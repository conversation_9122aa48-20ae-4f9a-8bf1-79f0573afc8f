package jyhapp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
	jyhReq "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type PointsApi struct{}

// GetPointsRecords 获取积分明细
// @Tags 积分管理
// @Summary 获取积分明细
// @Security ApiKeyAuth
// @Produce application/json
// @Param data  query     jyhReq.GetPointsRecords  true  "获取积分明细"
// @Success 200 {object} response.Response{data=response.PageResult{list=[]jyhapp.JyhPointsRecord}} "获取成功"
// @Router /points/records [get]
func (api *PointsApi) GetPointsRecords(c *gin.Context) {
	var req jyhReq.GetPointsRecords

	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}

	records, total, err := jyhPointsService.GetPointsRecords(req)
	if err != nil {
		global.GVA_LOG.Error("获取积分明细失败",
			zap.Uint("userID", req.UserID),
			zap.Error(err))
		response.FailWithMessage("获取积分明细失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     records,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}

// GetPointsRules 获取积分规则
// @Tags 积分管理
// @Summary 获取积分规则
// @Security ApiKeyAuth
// @Produce application/json
// @Success 200 {object} response.Response{data=[]jyhapp.JyhPointsRule} "获取成功"
// @Router /points/rules [get]
func (api *PointsApi) GetPointsRules(c *gin.Context) {
	var req jyhReq.GetPointsRules
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}

	records, total, err := jyhPointsService.GetPointsRules(req)
	if err != nil {
		global.GVA_LOG.Error("获取积分规则失败", zap.Error(err))
		response.FailWithMessage("获取积分规则失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     records,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}

// GetPointsRuleByID 获取积分详情
// @Tags 积分管理
// @Summary 获取积分详情
// @Security ApiKeyAuth
// @Produce application/json
// @Param     id  path      uint  true  "会员等级ID"
// @Success 200 {object} response.Response{msg=string} "获取积分详情"
// @Router /points/rules/detail [get]
func (api *PointsApi) GetPointsRuleByID(c *gin.Context) {
	var req request.GetById
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if req.GetUintId() == 0 {
		response.FailWithMessage("缺少积分规则ID", c)
		return
	}
	if rules, err := jyhPointsService.GetPointsRuleByID(req.GetUintId()); err != nil {
		global.GVA_LOG.Error("获取积分规则失败", zap.Error(err))
		response.FailWithMessage("获取积分规则失败", c)
	} else {
		response.OkWithData(rules, c)
	}
}

// UpdatePointsRule 更新积分规则
// @Tags 积分管理(后台)
// @Summary 更新积分规则
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body jyhapp.JyhPointsRule true "积分规则"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /points/rule [put]
func (api *PointsApi) UpdatePointsRule(c *gin.Context) {
	var rule jyhapp.JyhPointsRule
	if err := c.ShouldBindJSON(&rule); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := jyhPointsService.UpdatePointsRule(&rule); err != nil {
		global.GVA_LOG.Error("更新积分规则失败", zap.Error(err))
		response.FailWithMessage("更新积分规则失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// GetPointsRecordsByUser 获取用户积分明细(后台)
// @Tags 积分管理(后台)
// @Summary 获取用户积分明细
// @Security ApiKeyAuth
// @Produce application/json
// @Param userId path uint true "用户ID"
// @Param page query int false "页码"
// @Param pageSize query int false "每页数量"
// @Success 200 {object} response.Response{data=response.PageResult{list=[]jyhapp.JyhPointsRecord}} "获取成功"
// @Router /points/user/records/ [get]
func (api *PointsApi) GetPointsRecordsByUser(c *gin.Context) {
	var req jyhReq.GetPointsRecords
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}
	// 获取用户ID
	userID := utils.GetUserID(c)
	if userID == 0 {
		response.FailWithMessage("未登录", c)
		return
	}
	req.UserID = userID

	records, total, err := jyhPointsService.GetUserPointsRecords(req)
	if err != nil {
		global.GVA_LOG.Error("获取积分明细失败",
			zap.Uint("userID", userID),
			zap.Error(err))
		response.FailWithMessage("获取积分明细失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     records,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}

// ExchangeMaterial 兑换素材
// @Tags 积分兑换
// @Summary 兑换商品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body jyhReq.PointsExchangeRequest true "兑换请求"
// @Success 200 {object} response.Response{data=jyhapp.JyhPointsExchange} "兑换成功"
// @Router /points/exchange [post]
func (api *PointsApi) ExchangeMaterial(c *gin.Context) {
	userID := utils.GetUserID(c)
	if userID == 0 {
		response.FailWithMessage("未登录", c)
		return
	}

	var req jyhReq.PointsExchangeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	exchange, err := jyhPointsService.ExchangeMaterial(
		userID, req.MaterialID, req.Quantity, req.Address)

	if err != nil {
		global.GVA_LOG.Error("兑换失败",
			zap.Uint("userID", userID),
			zap.Uint("materialID", req.MaterialID),
			zap.Error(err))
		response.FailWithMessage("兑换失败: "+err.Error(), c)
	} else {
		response.OkWithData(gin.H{"exchange": exchange}, c)
	}
}

// GetUserExchanges 获取用户兑换记录
// @Tags 积分兑换
// @Summary 获取用户兑换记录
// @Security ApiKeyAuth
// @Produce application/json
// @Param page query int false "页码"
// @Param pageSize query int false "每页数量"
// @Success 200 {object} response.Response{data=response.PageResult{list=[]jyhapp.JyhPointsExchange}} "获取成功"
// @Router /points/exchange/user/records [get]
func (api *PointsApi) GetUserExchanges(c *gin.Context) {
	userID := utils.GetUserID(c)
	if userID == 0 {
		response.FailWithMessage("未登录", c)
		return
	}

	var req jyhReq.GetUserExchanges
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}

	exchanges, total, err := jyhPointsService.GetUserExchanges(userID, req)
	if err != nil {
		global.GVA_LOG.Error("获取兑换记录失败",
			zap.Uint("userID", userID),
			zap.Error(err))
		response.FailWithMessage("获取兑换记录失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     exchanges,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}

// GetExchangeRecord 获取兑换记录
// @Tags 积分兑换
// @Summary 获取兑换记录
// @Security ApiKeyAuth
// @Produce application/json
// @Param page query int false "页码"
// @Param pageSize query int false "每页数量"
// @Success 200 {object} response.Response{data=response.PageResult{list=[]jyhapp.JyhPointsExchange}} "获取成功"
// @Router /points/exchange/records [get]
func (api *PointsApi) GetExchangeRecord(c *gin.Context) {
	var req jyhReq.GetUserExchanges
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}

	exchanges, total, err := jyhPointsService.GetExchangeRecord(req)
	if err != nil {
		global.GVA_LOG.Error("获取兑换记录失败",
			zap.Error(err))
		response.FailWithMessage("获取兑换记录失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     exchanges,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}

// CancelExchange 取消兑换
// @Tags 积分兑换
// @Summary 取消兑换
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.GetById true "兑换记录ID"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"取消成功"}"
// @Router /points/exchange/cancel [post]
func (api *PointsApi) CancelExchange(c *gin.Context) {
	userID := utils.GetUserID(c)
	if userID == 0 {
		response.FailWithMessage("未登录", c)
		return
	}

	var req request.GetById
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := jyhPointsService.CancelExchange(uint(req.ID)); err != nil {
		global.GVA_LOG.Error("取消失败",
			zap.Uint("userID", userID),
			zap.Uint("exchangeID", uint(req.ID)),
			zap.Error(err))
		response.FailWithMessage("取消失败: "+err.Error(), c)
	} else {
		response.OkWithMessage("取消成功", c)
	}
}

// CompleteExchange 完成兑换（后台）
// @Tags 积分兑换(后台)
// @Summary 完成兑换
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.GetById true "兑换记录ID"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"操作成功"}"
// @Router /points/exchange/complete [post]
func (api *PointsApi) CompleteExchange(c *gin.Context) {
	var req request.GetById
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := jyhPointsService.CompleteExchange(uint(req.ID)); err != nil {
		global.GVA_LOG.Error("操作失败",
			zap.Uint("exchangeID", uint(req.ID)),
			zap.Error(err))
		response.FailWithMessage("操作失败: "+err.Error(), c)
	} else {
		response.OkWithMessage("操作成功", c)
	}
}
