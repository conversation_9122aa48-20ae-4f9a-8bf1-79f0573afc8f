package jyhapp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"time"
)

type JyhArticleCategory struct {
	global.GVA_MODEL
	Name        string `gorm:"type:varchar(100);not null" json:"name"`
	ParentID    uint   `gorm:"default:0;index" json:"parentId"`
	Sort        int    `gorm:"default:0" json:"sort"`
	IsActive    bool   `gorm:"default:true" json:"isActive"`
	Description string `gorm:"type:varchar(255)" json:"description"`

	// 关联关系
	Parent   *JyhArticleCategory  `gorm:"foreignKey:ParentID" json:"parent"`
	Children []JyhArticleCategory `gorm:"foreignKey:ParentID" json:"children"`
	Articles []JyhArticle         `gorm:"foreignKey:CategoryID" json:"articles"`
}

// TableName 文章分类
func (JyhArticleCategory) TableName() string {
	return "jyh_article_category"
}

type JyhArticle struct {
	global.GVA_MODEL
	Title       string     `gorm:"type:varchar(255);not null" json:"title"`
	CategoryID  uint       `gorm:"not null;index" json:"categoryId"`
	Author      string     `gorm:"type:varchar(100)" json:"author"`
	Summary     string     `gorm:"type:varchar(500)" json:"summary"`
	Content     string     `gorm:"type:longtext;not null" json:"content"`
	CoverImage  string     `gorm:"type:varchar(500)" json:"coverImage"`
	Status      string     `gorm:"type:enum('draft','published','archived');default:'draft'" json:"status"`
	IsTop       bool       `gorm:"default:false" json:"isTop"`
	ViewCount   int        `gorm:"default:0" json:"viewCount"`
	PublishedAt *time.Time `gorm:"index" json:"publishedAt"`

	// 关联关系
	Category JyhArticleCategory `gorm:"foreignKey:CategoryID" json:"category"`
}

// TableName 文章管理
func (JyhArticle) TableName() string {
	return "jyh_article"
}
