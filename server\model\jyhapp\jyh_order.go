package jyhapp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/shopspring/decimal"
)

// JyhOrder 订单表（注意避开关键字，用大写开头）
type JyhOrder struct {
	global.GVA_MODEL
	UserID uint            `gorm:"not null;index" json:"userId"`
	Amount decimal.Decimal `gorm:"type:decimal(10,2)"`
	Status string          `gorm:"type:enum('pending', 'completed', 'cancelled');default:'pending'" json:"status"`

	// 关联关系
	User JyhUser `gorm:"foreignKey:UserID" json:"user"`
}

// TableName JyhOrder 表名
func (JyhOrder) TableName() string {
	return "jyh_order"
}

// JyhMcnOrderDetail MCN订单统计表
type JyhMcnOrderDetail struct {
	ID                     uint   `json:"id" gorm:"primaryKey;autoIncrement;comment:主键ID"`
	Date                   string `json:"date" gorm:"type:varchar(30);index:uidDate,unique;comment:统计日期"`
	TotalCount             int    `json:"total_count" gorm:"default:0;comment:总订单数"`
	StartTime              string `json:"start_time" gorm:"type:varchar(30);comment:统计开始时间"`
	EndTime                string `json:"end_time" gorm:"type:varchar(30);comment:统计结束时间"`
	BillId                 string `json:"bill_id" gorm:"type:varchar(100);comment:账单ID"`
	TotalAmount            int    `json:"total_amount" gorm:"default:0;comment:佣金"`
	TotalServiceFee        int    `json:"total_service_fee" gorm:"default:0;comment:技术服务费"`
	TotalSettleAmount      int    `json:"total_settle_amount" gorm:"default:0;comment:结算收入"`
	AuthorFee              int    `json:"author_fee" gorm:"default:0;comment:达人分成"`
	OrganizationFee        int    `json:"organization_fee" gorm:"default:0;comment:机构分成"`
	AuthorUid              string `json:"author_uid" gorm:"type:varchar(50);index:uidDate,unique;comment:达人UID"`
	PayType                string `json:"pay_type" gorm:"type:varchar(50);comment:支付方式"`
	TotalServiceFeeReturn  int    `json:"total_service_fee_return" gorm:"default:0;comment:总服务费返还"`
	AuthorFeeReturn        int    `json:"author_fee_return" gorm:"default:0;comment:达人分成返还"`
	OrganizationFeeReturn  int    `json:"organization_fee_return" gorm:"default:0;comment:机构分成返还"`
	AuthorName             string `json:"author_name" gorm:"type:varchar(100);comment:达人名称"`
	UidType                string `json:"uid_type" gorm:"type:varchar(50);comment:用户类型"`
	AppId                  int    `json:"app_id" gorm:"default:0;comment:应用ID"`
	CommissionAmount       int    `json:"commission_amount" gorm:"default:0;comment:总佣金"`
	SumServiceFee          int    `json:"sum_service_fee" gorm:"default:0;comment:总技术服务费"`
	SettleAmount           int    `json:"settle_amount" gorm:"default:0;comment:总结算收入"`
	SettleAmountReturn     int    `json:"settle_amount_return" gorm:"default:0;comment:结算金额返还"`
	CommissionAmountReturn int    `json:"commission_amount_return" gorm:"default:0;comment:佣金金额返还"`
	SumOrganizationFee     int    `json:"sum_organization_fee" gorm:"default:0;comment:总机构分成"`
	SumAuthorFee           int    `json:"sum_author_fee" gorm:"default:0;comment:总达人分成"`
	ColonelFee             int    `json:"colonel_fee" gorm:"default:0;comment:总军团分成"`
	AuthorModeCount        int    `json:"author_mode_count" gorm:"default:0;comment:达人模式订单数"`
	ShopModeCount          int    `json:"shop_mode_count" gorm:"default:0;comment:商户模式订单数"`
	DouCustomerFee         int    `json:"dou_customer_fee" gorm:"default:0;comment:抖音客户分成"`
	DouCustomerFeeReturn   int    `json:"dou_customer_fee_return" gorm:"default:0;comment:抖音客户分成返还"`
}

// TableName JyhMcnOrderDetail 表名
func (JyhMcnOrderDetail) TableName() string {
	return "jyh_mcn_order_detail"
}
