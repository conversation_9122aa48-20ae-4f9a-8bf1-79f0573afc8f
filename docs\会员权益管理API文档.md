# 会员权益管理系统API文档

## 业务逻辑说明

### 核心概念

1. **权益类型（JyhUserBenefit）**：系统预设的权益配置，如"分佣比例"、"素材条数"等
2. **会员等级（JyhUserShipLevel）**：管理员配置的不同会员等级
3. **等级权益映射（JyhUserShipLevelBenefit）**：每个等级对应的权益值配置
4. **用户等级记录（JyhUserLevel）**：用户开通的等级记录
5. **用户权益快照（JyhUserBenefitSnapshot）**：用户开通时的权益配置快照

### 关键业务流程

**权益快照的作用**：
- 用户开通等级时，系统自动快照当时的权益配置
- 即使后续管理员调整等级权益，已开通用户的权益仍按快照执行
- 确保用户权益的稳定性和一致性

## API接口

### 1. 权益类型管理（预设数据）

#### 1.1 获取权益类型列表
- **接口**：`GET /user-benefit/benefits`
- **说明**：主要用于等级配置时选择预设权益
- **参数**：
  ```json
  {
    "page": 1,
    "pageSize": 10,
    "key": "commission_rate",
    "name": "分佣比例",
    "description": "用户推广获得的分佣比例"
  }
  ```

### 2. 会员等级管理（主要管理界面）

#### 2.1 获取会员等级列表
- **接口**：`GET /user-benefit/levels`
- **说明**：查询已配置的会员等级

#### 2.2 创建会员等级
- **接口**：`POST /user-benefit/levels`
- **说明**：创建等级时同时配置权益，使用事务保证一致性
- **请求体**：
  ```json
  {
    "name": "VIP1",
    "code": "vip1",
    "priceCents": 9900,
    "durationDays": 30,
    "sort": 1,
    "description": "初级VIP会员",
    "benefits": [
      {
        "benefitId": 1,
        "value": "5%",
        "condition": ""
      },
      {
        "benefitId": 2,
        "value": "100",
        "condition": ""
      }
    ]
  }
  ```

#### 2.3 获取会员等级详情
- **接口**：`GET /user-benefit/levels/{id}`
- **说明**：获取等级信息及其权益配置

#### 2.4 更新会员等级
- **接口**：`PUT /user-benefit/levels/{id}`
- **说明**：更新等级信息和权益配置

#### 2.5 删除会员等级
- **接口**：`DELETE /user-benefit/levels/{id}`
- **说明**：删除等级（需检查是否被用户使用）

### 3. 用户等级记录管理（用户开通等级）

#### 3.1 获取用户等级记录列表
- **接口**：`GET /user-benefit/user-levels`
- **说明**：查询用户的等级开通记录

#### 3.2 用户开通等级
- **接口**：`POST /user-benefit/user-levels`
- **说明**：用户开通等级时创建记录并自动快照权益
- **请求体**：
  ```json
  {
    "userId": 123,
    "levelId": 1,
    "orderId": "ORDER123456",
    "startAt": "2024-01-01T00:00:00Z",
    "endAt": "2024-01-31T23:59:59Z",
    "status": "active"
  }
  ```

#### 3.3 更新用户等级记录
- **接口**：`PUT /user-benefit/user-levels/{id}`

#### 3.4 删除用户等级记录
- **接口**：`DELETE /user-benefit/user-levels/{id}`

### 4. 用户权益快照查询

#### 4.1 获取权益快照列表
- **接口**：`GET /user-benefit/user-benefit-snapshots`
- **说明**：查询所有用户的权益快照

#### 4.2 查询指定用户的权益快照
- **接口**：`GET /user-benefit/users/{userId}/benefit-snapshots`
- **说明**：查询特定用户的权益配置快照

## 数据流示例

### 场景：用户购买VIP1会员

1. **管理员配置等级**：
   ```bash
   POST /user-benefit/levels
   {
     "name": "VIP1",
     "code": "vip1",
     "priceCents": 9900,
     "benefits": [
       {"benefitId": 1, "value": "5%"},    // 分佣比例5%
       {"benefitId": 2, "value": "100"}    // 素材条数100条
     ]
   }
   ```

2. **用户购买会员**：
   ```bash
   POST /user-benefit/user-levels
   {
     "userId": 123,
     "levelId": 1,
     "orderId": "ORDER123456",
     "startAt": "2024-01-01T00:00:00Z",
     "endAt": "2024-01-31T23:59:59Z"
   }
   ```

3. **系统自动创建权益快照**：
   - 在同一事务中创建用户等级记录
   - 快照当前等级的权益配置（分佣比例5%，素材条数100条）

4. **管理员调整等级权益**：
   ```bash
   PUT /user-benefit/levels/1
   {
     "benefits": [
       {"benefitId": 1, "value": "8%"},    // 调整为8%
       {"benefitId": 2, "value": "200"}    // 调整为200条
     ]
   }
   ```

5. **结果**：
   - 已开通的用户123仍享受原快照权益（5%, 100条）
   - 新购买的用户将享受新的权益配置（8%, 200条）

## 技术实现要点

### 事务处理
- 等级创建/更新：等级信息和权益配置在同一事务中处理
- 用户开通：用户等级记录和权益快照在同一事务中处理

### 数据一致性
- 权益快照确保用户权益不受后续调整影响
- 删除检查：删除等级前检查是否被用户使用
- 外键约束：确保数据关联的完整性

### 性能优化
- 查询接口不使用操作记录中间件
- 分页查询支持条件筛选
- 索引优化：用户ID、等级ID、时间范围等常用查询字段

## 状态说明

### 用户等级记录状态
- `active`：有效状态
- `expired`：已过期
- `cancelled`：已取消

### 时间字段
- `startAt`：权益开始时间
- `endAt`：权益结束时间
- `createdAt`：记录创建时间
- `updatedAt`：记录更新时间 