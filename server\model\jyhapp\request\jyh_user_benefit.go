package request

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/shopspring/decimal"
	"gorm.io/datatypes"
)

// ===== 权益类型相关 =====

// BenefitListReq 权益类型列表请求
type BenefitListReq struct {
	request.PageInfo
	Key         string `json:"key" form:"key"`                 // 权益唯一键
	Name        string `json:"name" form:"name"`               // 权益名称
	Description string `json:"description" form:"description"` // 权益描述
}

// BenefitCreateReq 创建权益类型请求
type BenefitCreateReq struct {
	Key         string `json:"key" binding:"required"`  // 唯一英文编码
	Name        string `json:"name" binding:"required"` // 展示名
	Description string `json:"description"`             // 运营备注
}

// BenefitUpdateReq 更新权益类型请求
type BenefitUpdateReq struct {
	ID          uint   `json:"id" binding:"required"`   // 权益ID
	Key         string `json:"key" binding:"required"`  // 唯一英文编码
	Name        string `json:"name" binding:"required"` // 展示名
	Description string `json:"description"`             // 运营备注
}

// ===== 会员等级相关 =====

// UserShipLevelListReq 会员等级列表请求
type UserShipLevelListReq struct {
	request.PageInfo
	Name string `json:"name" form:"name"` // 等级名称
	Code string `json:"code" form:"code"` // 等级编码
}

// LevelBenefitConfigItem 等级权益配置项
type LevelBenefitConfigItem struct {
	BenefitID uint            `json:"benefit_id" validate:"required"` // 权益类型ID
	Value     decimal.Decimal `json:"value" validate:"required"`      // 权益值
	Condition datatypes.JSON  `json:"condition"`                      // 权益条件
}

// UserShipLevelCreateReq 创建会员等级请求
type UserShipLevelCreateReq struct {
	Name                   string                   `json:"name" validate:"required,max=50"`              // 等级名称
	Code                   string                   `json:"code" validate:"required,max=50"`              // 等级编码
	Icon                   string                   `json:"icon" validate:"max=255"`                      // 图标
	PriceCents             uint64                   `json:"price_cents" validate:"min=0"`                 // 价格（分）
	DurationDays           uint                     `json:"duration_days" validate:"min=1"`               // 有效期（天）
	Sort                   int                      `json:"sort" validate:"min=0"`                        // 排序权重
	Description            string                   `json:"description" validate:"max=200"`               // 等级描述
	Benefits               []LevelBenefitConfigItem `json:"benefits" validate:"required,dive"`            // 权益配置列表
	Hide                   bool                     `json:"hide"`                                         // 是否隐藏（仅管理员可见）
	CommissionRate         decimal.Decimal          `json:"commission_rate" validate:"required"`          // 佣金比例（0-1之间的小数，0表示不支持佣金）
	PlatformCommissionRate decimal.Decimal          `json:"platform_commission_rate" validate:"required"` // 平台佣金比例（0-1之间的小数，0表示不支持平台佣金）
}

// UserShipLevelUpdateReq 更新会员等级请求
type UserShipLevelUpdateReq struct {
	ID                     uint                     `json:"id" validate:"required"`                       // 等级ID
	Name                   string                   `json:"name" validate:"required,max=50"`              // 等级名称
	Code                   string                   `json:"code" validate:"required,max=50"`              // 等级编码
	Icon                   string                   `json:"icon" validate:"max=255"`                      // 图标
	PriceCents             int                      `json:"price_cents" validate:"min=0"`                 // 价格（分）
	DurationDays           int                      `json:"duration_days" validate:"min=1"`               // 有效期（天）
	Sort                   int                      `json:"sort" validate:"min=0"`                        // 排序权重
	Description            string                   `json:"description" validate:"max=200"`               // 等级描述
	Benefits               []LevelBenefitConfigItem `json:"benefits" validate:"required,dive"`            // 权益配置列表
	Hide                   bool                     `json:"hide"`                                         // 是否隐藏（仅管理员可见）
	CommissionRate         decimal.Decimal          `json:"commission_rate" validate:"required"`          // 佣金比例（0-1之间的小数，0表示不支持佣金）
	PlatformCommissionRate decimal.Decimal          `json:"platform_commission_rate" validate:"required"` // 平台佣金比例（0-1之间的小数，0表示不支持平台佣金）
}

// ===== 等级-权益映射相关 =====

// LevelBenefitListReq 等级权益映射列表请求
type LevelBenefitListReq struct {
	request.PageInfo
	LevelID   uint `json:"level_id" form:"level_id"`     // 会员等级ID
	BenefitID uint `json:"benefit_id" form:"benefit_id"` // 权益类型ID
}

// LevelBenefitCreateReq 创建等级权益映射请求
type LevelBenefitCreateReq struct {
	LevelID   uint            `json:"level_id" binding:"required"`   // 所属会员等级ID
	BenefitID uint            `json:"benefit_id" binding:"required"` // 权益类型ID
	Value     decimal.Decimal `json:"value" binding:"required"`      // 权益值
	Condition datatypes.JSON  `json:"condition"`                     // 附加条件
}

// LevelBenefitUpdateReq 更新等级权益映射请求
type LevelBenefitUpdateReq struct {
	ID        uint            `json:"id" binding:"required"`         // 映射ID
	LevelID   uint            `json:"level_id" binding:"required"`   // 所属会员等级ID
	BenefitID uint            `json:"benefit_id" binding:"required"` // 权益类型ID
	Value     decimal.Decimal `json:"value" binding:"required"`      // 权益值
	Condition datatypes.JSON  `json:"condition"`                     // 附加条件
}

// LevelBenefitBatchCreateReq 批量创建等级权益映射请求
type LevelBenefitBatchCreateReq struct {
	LevelID  uint                    `json:"level_id" binding:"required"` // 会员等级ID
	Benefits []LevelBenefitCreateReq `json:"benefits" binding:"required"` // 权益列表
}

// ===== 用户等级记录相关 =====

// UserLevelListReq 用户等级记录列表请求
type UserLevelListReq struct {
	request.PageInfo
	UserID    uint   `json:"user_id" form:"user_id"`       // 用户ID
	LevelID   uint   `json:"level_id" form:"level_id"`     // 等级ID
	Status    string `json:"status" form:"status"`         // 状态筛选
	StartTime string `json:"start_time" form:"start_time"` // 申请开始时间
	EndTime   string `json:"end_time" form:"end_time"`     // 申请结束时间
}

// UserLevelReq 创建用户等级记录请求
type UserLevelReq struct {
	ID      uint      `json:"id" validate:"required"`       // 记录ID
	UserID  uint      `json:"user_id" validate:"required"`  // 用户ID
	LevelID uint      `json:"level_id" validate:"required"` // 等级ID
	OrderID string    `json:"order_id"`                     // 订单ID
	StartAt time.Time `json:"start_at" validate:"required"` // 开始时间
	EndAt   time.Time `json:"end_at" validate:"required"`   // 结束时间
	Status  string    `json:"status"`                       // 状态：active=有效，expired=过期，cancelled=取消
}

// UserLevelAuditReq 用户等级审核请求
type UserLevelAuditReq struct {
	ID     uint   `json:"id" validate:"required"`     // 用户等级记录ID
	Status string `json:"status" validate:"required"` // 审核结果：active=通过，rejected=拒绝
	Remark string `json:"remark"`                     // 审核备注
}

// ===== 用户权益快照相关 =====

// UserBenefitSnapshotListReq 用户权益快照列表请求
type UserBenefitSnapshotListReq struct {
	request.PageInfo
	UserID    uint `json:"user_id" form:"user_id"`       // 用户ID
	LevelID   uint `json:"level_id" form:"level_id"`     // 等级ID
	BenefitID uint `json:"benefit_id" form:"benefit_id"` // 权益类型ID
}
