package jyhapp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type ExternalApi struct {
}

// MaterialSync 同步素材
func (m *ExternalApi) MaterialSync(c *gin.Context) {
	var req request.MaterialSync
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	resp, err := materialSyncService.Sync(&req)
	if err != nil {
		global.GVA_LOG.Error("创建素材失败!", zap.Error(err))
		response.FailWithMessage("创建素材失败:"+err.<PERSON><PERSON><PERSON>(), c)
		return
	}
	response.OkWithData(resp, c)
}
