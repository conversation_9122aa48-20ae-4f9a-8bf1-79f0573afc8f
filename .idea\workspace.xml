<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="ALL" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="4c79a5e6-a9ef-489b-b6a3-dfb1b3d6e32f" name="Changes" comment="fix: si">
      <change beforePath="$PROJECT_DIR$/server/config.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/server/config.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/server/docs/docs.go" beforeDir="false" afterPath="$PROJECT_DIR$/server/docs/docs.go" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Go File" />
      </list>
    </option>
  </component>
  <component name="GOROOT" url="file://$PROJECT_DIR$/../../../server/go1.22.2" />
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="main" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="UPDATE_TYPE" value="REBASE" />
  </component>
  <component name="Make.Settings">
    <option name="path" value="C:\Program Files\mingw64\bin\make.exe" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2xiXmvaPX9hXVpSGiwSwkQ9sUAL" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "DefaultGoTemplateProperty": "Go File",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.go.formatter.settings.were.checked": "true",
    "RunOnceActivity.go.migrated.go.modules.settings": "true",
    "RunOnceActivity.go.modules.automatic.dependencies.download": "true",
    "RunOnceActivity.go.modules.go.list.on.any.changes.was.set": "true",
    "WebServerToolWindowFactoryState": "false",
    "git-widget-placeholder": "feature/1.0",
    "go.import.settings.migrated": "true",
    "go.sdk.automatically.set": "true",
    "last_opened_file_path": "D:/go/code/insbuy-code/douyin.srv",
    "node.js.detected.package.eslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "go.sdk"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "mysql"
    ]
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\go\code\insbuy-code\douyin.srv\server\api\v1\jyhapp" />
      <recent name="D:\go\code\insbuy-code\douyin.srv\server\service\jyhapp" />
      <recent name="D:\go\code\insbuy-code\douyin.srv\server\plugin" />
      <recent name="D:\go\code\insbuy-code\douyin.srv\server" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\go\code\insbuy-code\douyin.srv\server\model\jyhapp" />
    </key>
  </component>
  <component name="RunManager" selected="Go Build.go build github.com/flipped-aurora/gin-vue-admin/server">
    <configuration name="go build github.com/flipped-aurora/gin-vue-admin/server" type="GoApplicationRunConfiguration" factoryName="Go Application" temporary="true" nameIsGenerated="true">
      <module name="douyin.srv" />
      <working_directory value="$PROJECT_DIR$/server" />
      <kind value="PACKAGE" />
      <package value="github.com/flipped-aurora/gin-vue-admin/server" />
      <directory value="$PROJECT_DIR$" />
      <filePath value="$PROJECT_DIR$/server/main.go" />
      <method v="2" />
    </configuration>
    <configuration name="doc" type="MAKEFILE_TARGET_RUN_CONFIGURATION" factoryName="Makefile" temporary="true">
      <makefile filename="$PROJECT_DIR$/server/Makefile" target="doc" workingDirectory="" arguments="">
        <envs />
      </makefile>
      <method v="2" />
    </configuration>
    <configuration name="gen" type="MAKEFILE_TARGET_RUN_CONFIGURATION" factoryName="Makefile" temporary="true">
      <makefile filename="$PROJECT_DIR$/server/Makefile" target="gen" workingDirectory="" arguments="">
        <envs />
      </makefile>
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Go Build.go build github.com/flipped-aurora/gin-vue-admin/server" />
        <item itemvalue="Makefile Target.doc" />
        <item itemvalue="Makefile Target.gen" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ID_ORDER">
      <list>
        <option value="Default.Root" />
        <option value="Default.Author" />
        <option value="Default.Date" />
        <option value="Default.Subject" />
        <option value="Space.CommitStatus" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="fix: 获取客服基础信息接口调整" />
    <MESSAGE value="fix: 分配客服" />
    <MESSAGE value="refactor: 将 Limit 参数统一替换为 PageSize&#10;&#10;- 在 tagApi.go 文件中，将请求参数中的 Limit 统一替换为 PageSize&#10;- 此修改提高了代码的一致性和可读性，统一了分页参数的命名" />
    <MESSAGE value="fix: 获取客服信息-区分用户端和后台" />
    <MESSAGE value="fix: 用户tag外键修改" />
    <MESSAGE value="fix: 优化分配客服逻辑" />
    <MESSAGE value="refactor: 重构获取客服信息逻辑&#10;&#10;- 重构了 GetKefuInfo 函数，优化了逻辑结构和代码可读性" />
    <MESSAGE value="feat: 优化转接功能并添加新接口&#10;&#10;- 重构转接流程，增加转接优先级和类型字段&#10;- 添加取消转接、批量转接、清理过期转接等功能- 新增转接指标统计接口&#10;- 优化转接列表和详情接口&#10;- 增加WebSocket通知机制，实现转接实时通知" />
    <MESSAGE value="refactor: 重构客服系统&#10;&#10;- 移除工单和排队相关代码&#10;- 更新转接功能，使用会话ID替代工单ID&#10;- 优化用户连接和断开连接的处理逻辑&#10;- 删除不必要的函数和结构体" />
    <MESSAGE value="refactor: 统一分布规则接口路径格式" />
    <MESSAGE value="fix: 移除 GetKefuInfo 中的冗余用户 ID 检查" />
    <MESSAGE value="fix: 修复转接服务中会话查询条件错误&#10;&#10;- 将查询条件从 &quot;session_id&quot; 修改为 &quot;id&quot;，以匹配正确的会话标识字段" />
    <MESSAGE value="fix: 修复转接记录更新错误" />
    <MESSAGE value="fix: 修改服务分发规则状态代码&#10;&#10;-将服务分发规则的禁用状态代码从 0 修改为2&#10;- 更新了相关的常量定义和注释" />
    <MESSAGE value="fix: 将 session_id 类型从 string 改为 uint&#10;&#10;- 修改了多个文件中的 session_id 字段类型，将其从 string 改为 uint&#10;- 更新了相关的数据库模型、API接口和服务逻辑&#10;- 优化了数据类型，提高了数据一致性和安全性" />
    <MESSAGE value="fix: 使用上下文获取service_id" />
    <MESSAGE value="fix:调整转接管理接口路由- 将 privateRouter 更改为 plugServiceRouter，统一路由前缀- 移除了 GetTransferList函数中未使用的 service_id 查询参数" />
    <MESSAGE value="fix: 移除拒绝转接时的拒绝原因字段" />
    <MESSAGE value="fix: 优化用户账号绑定逻辑&#10;&#10;- 添加对特定错误消息的处理，忽略特定的绑定失败情况&#10;- 移除提取绑定 ID 的代码，简化绑定流程" />
    <MESSAGE value="refactor: 移除结算账号字段的必填验证" />
    <MESSAGE value="fix: 移除了对 pid 参数的校验，以适应可能的业务需求变化" />
    <MESSAGE value="feat: 添加用户扩展信息并优化用户信息响应" />
    <MESSAGE value="fix(user): 修复用户信息中扩展字段反序列化问题" />
    <MESSAGE value="fix: si" />
    <MESSAGE value="refactor(server): 优化绑定结算账号错误处理&#10;&#10;- 移除了错误消息中的冗余文本，直接返回原始错误信息&#10;- 提高了错误信息的可读性和一致性" />
    <option name="LAST_COMMIT_MESSAGE" value="refactor(server): 优化绑定结算账号错误处理&#10;&#10;- 移除了错误消息中的冗余文本，直接返回原始错误信息&#10;- 提高了错误信息的可读性和一致性" />
  </component>
  <component name="VgoProject">
    <settings-migrated>true</settings-migrated>
  </component>
</project>