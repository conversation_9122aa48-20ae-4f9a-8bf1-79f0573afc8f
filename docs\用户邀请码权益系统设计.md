# 用户邀请码权益系统设计文档

## 需求分析

### 1. 邀请码获取方式
- **平台直接给予**：平台生成的邀请码，用户使用后获得对应等级
- **用户邀请码**：基于用户当前等级权益生成的邀请码

### 2. 等级权益示例（星辰达人）
- **免费权益**：6个"炬火达人"名额推荐码（1个月内核销完）
- **购买权益**：
  - 1个"流星达人"推荐码：119元
  - 10个"流星达人"名额包：1090元（3个月内核销完）
  - 1个"炬火达人"推荐码：价格待定
  - 10个"炬火达人"名额包：12990元

### 3. 业务流程
1. 用户使用平台邀请码注册，获得对应等级
2. 根据等级自动分配免费邀请码额度
3. 用户可购买额外的邀请码和名额包
4. 邀请码有有效期限制，过期作废

## 数据库设计

### 1. 用户邀请码额度表 (jyh_invite_code)
```
type JyhInviteCode struct {
	global.GVA_MODEL
	Type      string     `gorm:"type:varchar(20);not null;default:'user';index;comment:邀请码类型：user-用户生成 platform-平台生成" json:"type"`
	UserID    *uint      `gorm:"index;comment:生成邀请码的用户ID（平台生成时为空）" json:"user_id"`
	Code      string     `gorm:"type:varchar(36);unique;not null;comment:唯一邀请码" json:"code"`
	LevelID   uint       `gorm:"type:int unsigned;not null;index;comment:会员等级ID" json:"level_id"`
	SalePrice uint64     `gorm:"type:bigint unsigned;not null;default:0;comment:出售价格(单位分)" json:"sale_price"`
	UsedAt    *time.Time `gorm:"type:datetime;comment:使用时间" json:"used_at"`
	UsedByUID *uint      `gorm:"type:int unsigned;index;comment:使用者用户ID" json:"used_by_uid"`
	Status    int        `gorm:"type:int unsigned;not null;default:0;comment:状态：0 未使用 1 已使用 2 已过期" json:"status"`
	IsUsed    bool       `gorm:"default:false;comment:是否已被使用" json:"is_used"`
	ExpiredAt *time.Time `gorm:"type:datetime;index;comment:过期时间" json:"expired_at"`

	// 关联关系
	User JyhUser `gorm:"foreignKey:UserID" json:"user"`
}
```

### 2. 邀请码权益配置表 (jyh_invite_code_benefit)
```sql
CREATE TABLE jyh_invite_code_benefit (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    created_at DATETIME(3) NULL,
    updated_at DATETIME(3) NULL,
    deleted_at DATETIME(3) NULL,
    user_level_id INT UNSIGNED NOT NULL COMMENT '用户等级ID',
    target_level_id INT UNSIGNED NOT NULL COMMENT '可生成的目标等级ID',
    benefit_type VARCHAR(20) NOT NULL COMMENT '权益类型：free-免费 purchase-可购买',
    quota_count INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '额度数量',
    price_cents BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '购买价格（分）',
    validity_days INT UNSIGNED NOT NULL DEFAULT 30 COMMENT '有效期（天）',
    is_package BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否为名额包',
    package_name VARCHAR(100) NULL COMMENT '名额包名称',
    description TEXT NULL COMMENT '权益描述',
    sort_order INT NOT NULL DEFAULT 0 COMMENT '排序',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    INDEX idx_user_level (user_level_id),
    INDEX idx_target_level (target_level_id),
    INDEX idx_benefit_type (benefit_type)
);
```


## 业务流程设计

### 1. 用户注册流程
1. 用户使用邀请码注册
2. 验证邀请码有效性
3. 分配对应等级
4. 根据等级配置自动分配免费邀请码额度
5. 记录邀请关系

### 2. 邀请码生成流程
1. 检查用户剩余额度
2. 验证目标等级权限
3. 生成邀请码
4. 扣减用户额度
5. 记录邀请码信息

### 3. 邀请码购买流程
1. 查询用户可购买的权益配置
2. 创建购买订单
3. 处理支付
4. 支付成功后分配额度
5. 记录购买记录

### 4. 权益分配流程
1. 用户等级变更时触发
2. 查询等级对应的免费权益
3. 自动分配邀请码额度
4. 设置有效期

这个设计方案可以完整支持您描述的邀请码权益系统，包括免费权益分配、付费权益购买、邀请码生成和管理等功能。 