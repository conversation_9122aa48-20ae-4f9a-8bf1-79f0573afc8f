package jyhapp

import (
	v1 "github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type JyhInviteCodeRouter struct{}

// InitJyhInviteCodeRouter 初始化邀请码路由信息
func (s *JyhInviteCodeRouter) InitJyhInviteCodeRouter(Router *gin.RouterGroup, JyhappGroup *gin.RouterGroup) {
	inviteCodeRouter := Router.Group("admin/invite_code").Use(middleware.OperationRecord())
	inviteCodeApi := v1.ApiGroupApp.JyhApiGroup.JyhInviteCodeApi

	{
		// 后台管理接口（需要管理员权限）
		{
			inviteCodeRouter.POST("code", inviteCodeApi.CreateInviteCode)        // 创建邀请码
			inviteCodeRouter.POST("batch", inviteCodeApi.BatchCreateInviteCodes) // 批量创建邀请码
			inviteCodeRouter.DELETE("code/:id", inviteCodeApi.DeleteInviteCode)  // 删除邀请码
			inviteCodeRouter.PUT("status", inviteCodeApi.UpdateInviteCodeStatus) // 更新邀请码状态
			inviteCodeRouter.POST("transfer", inviteCodeApi.TransferInviteCode)  // 转让邀请码
		}
		inviteCodeNoRecordRouter := Router.Group("admin/invite_code")
		{
			inviteCodeNoRecordRouter.GET("list", inviteCodeApi.GetInviteCodeList)                   // 获取邀请码列表
			inviteCodeNoRecordRouter.GET("statistics", inviteCodeApi.GetInviteCodeStatistics)       // 获取邀请码统计信息
			inviteCodeNoRecordRouter.POST("transfer/list", inviteCodeApi.GetInviteCodeTransferList) // 获取转让记录列表

		}
		// 用户端接口
		inviteCodeRouter.GET("validate", inviteCodeApi.ValidateInviteCode) // 验证邀请码
	}

	// 客户端接口（用户）
	userInviteCodeRouter := JyhappGroup.Group("jyh/invite_code")
	{
		userInviteCodeRouter.GET("list", inviteCodeApi.GetUserInviteCodesByLevel) // 获取用户邀请码列表（按等级分类）
	}
}
