// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
)

func newJyhUserShipLevelBenefit(db *gorm.DB, opts ...gen.DOOption) jyhUserShipLevelBenefit {
	_jyhUserShipLevelBenefit := jyhUserShipLevelBenefit{}

	_jyhUserShipLevelBenefit.jyhUserShipLevelBenefitDo.UseDB(db, opts...)
	_jyhUserShipLevelBenefit.jyhUserShipLevelBenefitDo.UseModel(&jyhapp.JyhUserShipLevelBenefit{})

	tableName := _jyhUserShipLevelBenefit.jyhUserShipLevelBenefitDo.TableName()
	_jyhUserShipLevelBenefit.ALL = field.NewAsterisk(tableName)
	_jyhUserShipLevelBenefit.ID = field.NewUint(tableName, "id")
	_jyhUserShipLevelBenefit.CreatedAt = field.NewTime(tableName, "created_at")
	_jyhUserShipLevelBenefit.UpdatedAt = field.NewTime(tableName, "updated_at")
	_jyhUserShipLevelBenefit.LevelID = field.NewUint(tableName, "level_id")
	_jyhUserShipLevelBenefit.BenefitID = field.NewUint(tableName, "benefit_id")
	_jyhUserShipLevelBenefit.Value = field.NewField(tableName, "value")
	_jyhUserShipLevelBenefit.Condition = field.NewField(tableName, "condition")

	_jyhUserShipLevelBenefit.fillFieldMap()

	return _jyhUserShipLevelBenefit
}

type jyhUserShipLevelBenefit struct {
	jyhUserShipLevelBenefitDo

	ALL       field.Asterisk
	ID        field.Uint
	CreatedAt field.Time
	UpdatedAt field.Time
	LevelID   field.Uint
	BenefitID field.Uint
	Value     field.Field
	Condition field.Field

	fieldMap map[string]field.Expr
}

func (j jyhUserShipLevelBenefit) Table(newTableName string) *jyhUserShipLevelBenefit {
	j.jyhUserShipLevelBenefitDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j jyhUserShipLevelBenefit) As(alias string) *jyhUserShipLevelBenefit {
	j.jyhUserShipLevelBenefitDo.DO = *(j.jyhUserShipLevelBenefitDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *jyhUserShipLevelBenefit) updateTableName(table string) *jyhUserShipLevelBenefit {
	j.ALL = field.NewAsterisk(table)
	j.ID = field.NewUint(table, "id")
	j.CreatedAt = field.NewTime(table, "created_at")
	j.UpdatedAt = field.NewTime(table, "updated_at")
	j.LevelID = field.NewUint(table, "level_id")
	j.BenefitID = field.NewUint(table, "benefit_id")
	j.Value = field.NewField(table, "value")
	j.Condition = field.NewField(table, "condition")

	j.fillFieldMap()

	return j
}

func (j *jyhUserShipLevelBenefit) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *jyhUserShipLevelBenefit) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 7)
	j.fieldMap["id"] = j.ID
	j.fieldMap["created_at"] = j.CreatedAt
	j.fieldMap["updated_at"] = j.UpdatedAt
	j.fieldMap["level_id"] = j.LevelID
	j.fieldMap["benefit_id"] = j.BenefitID
	j.fieldMap["value"] = j.Value
	j.fieldMap["condition"] = j.Condition
}

func (j jyhUserShipLevelBenefit) clone(db *gorm.DB) jyhUserShipLevelBenefit {
	j.jyhUserShipLevelBenefitDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j jyhUserShipLevelBenefit) replaceDB(db *gorm.DB) jyhUserShipLevelBenefit {
	j.jyhUserShipLevelBenefitDo.ReplaceDB(db)
	return j
}

type jyhUserShipLevelBenefitDo struct{ gen.DO }

func (j jyhUserShipLevelBenefitDo) Debug() *jyhUserShipLevelBenefitDo {
	return j.withDO(j.DO.Debug())
}

func (j jyhUserShipLevelBenefitDo) WithContext(ctx context.Context) *jyhUserShipLevelBenefitDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jyhUserShipLevelBenefitDo) ReadDB() *jyhUserShipLevelBenefitDo {
	return j.Clauses(dbresolver.Read)
}

func (j jyhUserShipLevelBenefitDo) WriteDB() *jyhUserShipLevelBenefitDo {
	return j.Clauses(dbresolver.Write)
}

func (j jyhUserShipLevelBenefitDo) Session(config *gorm.Session) *jyhUserShipLevelBenefitDo {
	return j.withDO(j.DO.Session(config))
}

func (j jyhUserShipLevelBenefitDo) Clauses(conds ...clause.Expression) *jyhUserShipLevelBenefitDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jyhUserShipLevelBenefitDo) Returning(value interface{}, columns ...string) *jyhUserShipLevelBenefitDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jyhUserShipLevelBenefitDo) Not(conds ...gen.Condition) *jyhUserShipLevelBenefitDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jyhUserShipLevelBenefitDo) Or(conds ...gen.Condition) *jyhUserShipLevelBenefitDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jyhUserShipLevelBenefitDo) Select(conds ...field.Expr) *jyhUserShipLevelBenefitDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jyhUserShipLevelBenefitDo) Where(conds ...gen.Condition) *jyhUserShipLevelBenefitDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jyhUserShipLevelBenefitDo) Order(conds ...field.Expr) *jyhUserShipLevelBenefitDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jyhUserShipLevelBenefitDo) Distinct(cols ...field.Expr) *jyhUserShipLevelBenefitDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jyhUserShipLevelBenefitDo) Omit(cols ...field.Expr) *jyhUserShipLevelBenefitDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jyhUserShipLevelBenefitDo) Join(table schema.Tabler, on ...field.Expr) *jyhUserShipLevelBenefitDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jyhUserShipLevelBenefitDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jyhUserShipLevelBenefitDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jyhUserShipLevelBenefitDo) RightJoin(table schema.Tabler, on ...field.Expr) *jyhUserShipLevelBenefitDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jyhUserShipLevelBenefitDo) Group(cols ...field.Expr) *jyhUserShipLevelBenefitDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jyhUserShipLevelBenefitDo) Having(conds ...gen.Condition) *jyhUserShipLevelBenefitDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jyhUserShipLevelBenefitDo) Limit(limit int) *jyhUserShipLevelBenefitDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jyhUserShipLevelBenefitDo) Offset(offset int) *jyhUserShipLevelBenefitDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jyhUserShipLevelBenefitDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jyhUserShipLevelBenefitDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jyhUserShipLevelBenefitDo) Unscoped() *jyhUserShipLevelBenefitDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jyhUserShipLevelBenefitDo) Create(values ...*jyhapp.JyhUserShipLevelBenefit) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jyhUserShipLevelBenefitDo) CreateInBatches(values []*jyhapp.JyhUserShipLevelBenefit, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jyhUserShipLevelBenefitDo) Save(values ...*jyhapp.JyhUserShipLevelBenefit) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jyhUserShipLevelBenefitDo) First() (*jyhapp.JyhUserShipLevelBenefit, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserShipLevelBenefit), nil
	}
}

func (j jyhUserShipLevelBenefitDo) Take() (*jyhapp.JyhUserShipLevelBenefit, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserShipLevelBenefit), nil
	}
}

func (j jyhUserShipLevelBenefitDo) Last() (*jyhapp.JyhUserShipLevelBenefit, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserShipLevelBenefit), nil
	}
}

func (j jyhUserShipLevelBenefitDo) Find() ([]*jyhapp.JyhUserShipLevelBenefit, error) {
	result, err := j.DO.Find()
	return result.([]*jyhapp.JyhUserShipLevelBenefit), err
}

func (j jyhUserShipLevelBenefitDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*jyhapp.JyhUserShipLevelBenefit, err error) {
	buf := make([]*jyhapp.JyhUserShipLevelBenefit, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jyhUserShipLevelBenefitDo) FindInBatches(result *[]*jyhapp.JyhUserShipLevelBenefit, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jyhUserShipLevelBenefitDo) Attrs(attrs ...field.AssignExpr) *jyhUserShipLevelBenefitDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jyhUserShipLevelBenefitDo) Assign(attrs ...field.AssignExpr) *jyhUserShipLevelBenefitDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jyhUserShipLevelBenefitDo) Joins(fields ...field.RelationField) *jyhUserShipLevelBenefitDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jyhUserShipLevelBenefitDo) Preload(fields ...field.RelationField) *jyhUserShipLevelBenefitDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jyhUserShipLevelBenefitDo) FirstOrInit() (*jyhapp.JyhUserShipLevelBenefit, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserShipLevelBenefit), nil
	}
}

func (j jyhUserShipLevelBenefitDo) FirstOrCreate() (*jyhapp.JyhUserShipLevelBenefit, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserShipLevelBenefit), nil
	}
}

func (j jyhUserShipLevelBenefitDo) FindByPage(offset int, limit int) (result []*jyhapp.JyhUserShipLevelBenefit, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jyhUserShipLevelBenefitDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jyhUserShipLevelBenefitDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jyhUserShipLevelBenefitDo) Delete(models ...*jyhapp.JyhUserShipLevelBenefit) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jyhUserShipLevelBenefitDo) withDO(do gen.Dao) *jyhUserShipLevelBenefitDo {
	j.DO = *do.(*gen.DO)
	return j
}
