package response

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
	"time"
)

type MaterialResponse struct {
	global.GVA_MODEL
	Name             string                  `json:"name"`
	CategoryID       uint                    `json:"categoryId"`
	CatName          string                  `json:"catName"` // 分类名称
	Price            float64                 `json:"price"`
	Status           uint                    `json:"status"` // 状态: 0-未发布, 1-已发布, 2-已下架
	Description      string                  `json:"description"`
	Copywriting      string                  `json:"copywriting"`      // 推荐文案
	DouyinProductUrl string                  `json:"douyinProductUrl"` // 抖音商品链接
	MusicUrl         string                  `json:"musicUrl"`         // 背景音乐链接
	FileUrl          string                  `json:"fileUrl"`          // 素材首图URL
	CreatedAt        time.Time               `json:"createdAt"`        // 创建时间
	TagList          []TagsResponse          `gorm:"-"json:"tagList"`  // 标签列表
	Files            []MaterialFilesResponse `gorm:"-"json:"files"`    // 素材文件列表
	MaterialId       uint                    `json:"materialId"`       // 素材ID
	IsSend           bool                    `json:"isSend"`           // 是否已下发
	Type             string                  `json:"type"`             // 素材类型 'stream','video','image'
	IsClaimed        uint                    `json:"isClaimed"`        // 是否已领取
	IsRecognized     uint                    `json:"isRecognized"`     // 是否已识别
}

type MaterialFilesResponse struct {
	FileUrl   string `json:"fileUrl"`   // 素材文件URL
	FileType  string `json:"fileType"`  // 文件类型（'image','video','music','document'）
	IsPrimary bool   `json:"isPrimary"` // 是否为主图
	FileName  string `json:"fileName"`  // 文件名称
}

type MaterialDataResponse struct {
	MaterialId       uint           `json:"materialId"`       // 素材ID
	Name             string         `json:"name"`             // 素材名称
	CategoryId       uint           `json:"categoryId"`       // 分类ID
	CatName          string         `json:"catName"`          // 分类名称
	Description      string         `json:"description"`      // 素材描述
	Copywriting      string         `json:"copywriting"`      // 推荐文案
	DouyinProductUrl string         `json:"douyinProductUrl"` // 抖音商品链接
	MusicUrl         string         `json:"musicUrl"`         // 背景音乐链接
	CreatedAt        time.Time      `json:"createdAt"`        // 创建时间
	TagList          []TagsResponse `gorm:"-"json:"tagList"`  // 标签列表
	Status           uint           `json:"status"`           // 素材状态
	IsRecognized     uint           `json:"isRecognized"`     // 是否已识别
	FileUrl          string         `json:"fileUrl"`          // 素材首图
	MaterialUserId   uint           `json:"materialUserId"`   // 素材用户ID
}

// CustomMaterialResponse 自定义素材列表
type CustomMaterialResponse struct {
	jyhapp.Model1
	Username    string    `json:"username"`    // 用户名称
	MaterialUrl string    `json:"materialUrl"` // 素材链接
	UserID      uint      `json:"userId"`      // 用户ID
	IsSend      bool      `json:"isSend"`      // 是否已下发
	SendAt      time.Time `json:"sendAt"`      // 下发时间
	MaterialID  uint      `json:"materialId"`  // 素材ID
}

type MaterialSendRecordResponse struct {
	MaterialId  uint           `json:"materialId"`      // 素材ID
	Name        string         `json:"name"`            // 素材名称
	CategoryId  uint           `json:"categoryId"`      // 分类ID
	CatName     string         `json:"catName"`         // 分类名称
	DouyinUrl   string         `json:"douyinUrl"`       // 抖音链接
	Copywriting string         `json:"copywriting"`     // 文案
	TagList     []TagsResponse `gorm:"-"json:"tagList"` // 标签列表
	Status      uint           `json:"status"`          // 领取状态
	FileUrl     string         `json:"fileUrl"`         // 素材首图
	Username    string         `json:"username"`        // 用户名
	SendAt      time.Time      `json:"sendAt"`          // 下发时间
	ClaimedAt   time.Time      `json:"claimedAt"`       // 领取时间
}

type MaterialRuleConfigResponse struct {
	ID            uint   `json:"id"`            // 规则ID
	MinFans       int    `json:"minFans"`       // 最小粉丝数
	MaxFans       int    `json:"maxFans"`       // 最大粉丝数
	MinValidFans  int    `json:"minValidFans"`  // 最小有效粉丝数
	MaxValidFans  int    `json:"maxValidFans"`  // 最大有效粉丝数
	FrequencyType string `json:"frequencyType"` // 领取频率类型 'daily','weekly'
	SelectionType string `json:"selectionType"` // 选择类型 'fixed','flexible', // 固定或组合
	MaterialCount int    `json:"materialCount"` // 素材总数量（每天/每周）
	StreamCount   int    `json:"streamCount"`   // 拉流素材数量
	VideoCount    int    `json:"videoCount" `   // 视频素材数量
	ImageCount    int    `json:"imageCount"`    // 图文素材数量
	Remark        string `json:"remark"`        // 备注说明
}
