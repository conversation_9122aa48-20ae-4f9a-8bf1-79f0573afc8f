package jyhapp

import (
	v1 "github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/gin-gonic/gin"
)

type OrderRouter struct{}

func (s *OrderRouter) InitJyhOrderRouter(Router *gin.RouterGroup) {
	//OrderRouter := Router.Group("article").Use(middleware.OperationRecord())
	OrderRouterWithoutRecord := Router.Group("order")
	var orderApi = v1.ApiGroupApp.JyhApiGroup.OrderApi
	{
		OrderRouterWithoutRecord.GET("statistics", orderApi.GetOrderStatistics) // 统计用户的订单收益
		OrderRouterWithoutRecord.GET("fee", orderApi.GetOrderDetails)           //获取订单佣金明细
	}
}
