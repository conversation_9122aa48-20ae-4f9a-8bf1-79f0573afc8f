package request

// SendContractVerificationCodeReq 发送签约验证码请求
type SendContractVerificationCodeReq struct {
	Phone string `json:"phone" binding:"required"` // 手机号
}

// VerifyContractCodeReq 验证签约验证码请求
type VerifyContractCodeReq struct {
	Code string `json:"code" binding:"required"` // 验证码
}

// GetUserInfoByTicketReq 根据ticket获取用户信息请求
type GetUserInfoByTicketReq struct {
	Phone  string `json:"phone"`
	Ticket string `json:"ticket" binding:"required"` // 票据
}

// AddAccountBindReq 绑定用户账号到机构请求
type AddAccountBindReq struct {
	Phone  string `json:"phone"`
	UserID string `json:"user_id" binding:"required"` // 用户ID
}

// AddUserBindPidReq 绑定结算请求
type AddUserBindPidReq struct {
	UserID string `json:"user_id" binding:"required"` // 用户ID
	Pid    string `json:"pid"`                        // 结算账号
}
