// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
)

func newJyhUserTagRelation(db *gorm.DB, opts ...gen.DOOption) jyhUserTagRelation {
	_jyhUserTagRelation := jyhUserTagRelation{}

	_jyhUserTagRelation.jyhUserTagRelationDo.UseDB(db, opts...)
	_jyhUserTagRelation.jyhUserTagRelationDo.UseModel(&jyhapp.JyhUserTagRelation{})

	tableName := _jyhUserTagRelation.jyhUserTagRelationDo.TableName()
	_jyhUserTagRelation.ALL = field.NewAsterisk(tableName)
	_jyhUserTagRelation.ID = field.NewUint(tableName, "id")
	_jyhUserTagRelation.UserID = field.NewUint(tableName, "user_id")
	_jyhUserTagRelation.TagID = field.NewUint(tableName, "tag_id")

	_jyhUserTagRelation.fillFieldMap()

	return _jyhUserTagRelation
}

type jyhUserTagRelation struct {
	jyhUserTagRelationDo

	ALL    field.Asterisk
	ID     field.Uint
	UserID field.Uint
	TagID  field.Uint

	fieldMap map[string]field.Expr
}

func (j jyhUserTagRelation) Table(newTableName string) *jyhUserTagRelation {
	j.jyhUserTagRelationDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j jyhUserTagRelation) As(alias string) *jyhUserTagRelation {
	j.jyhUserTagRelationDo.DO = *(j.jyhUserTagRelationDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *jyhUserTagRelation) updateTableName(table string) *jyhUserTagRelation {
	j.ALL = field.NewAsterisk(table)
	j.ID = field.NewUint(table, "id")
	j.UserID = field.NewUint(table, "user_id")
	j.TagID = field.NewUint(table, "tag_id")

	j.fillFieldMap()

	return j
}

func (j *jyhUserTagRelation) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *jyhUserTagRelation) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 3)
	j.fieldMap["id"] = j.ID
	j.fieldMap["user_id"] = j.UserID
	j.fieldMap["tag_id"] = j.TagID
}

func (j jyhUserTagRelation) clone(db *gorm.DB) jyhUserTagRelation {
	j.jyhUserTagRelationDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j jyhUserTagRelation) replaceDB(db *gorm.DB) jyhUserTagRelation {
	j.jyhUserTagRelationDo.ReplaceDB(db)
	return j
}

type jyhUserTagRelationDo struct{ gen.DO }

func (j jyhUserTagRelationDo) Debug() *jyhUserTagRelationDo {
	return j.withDO(j.DO.Debug())
}

func (j jyhUserTagRelationDo) WithContext(ctx context.Context) *jyhUserTagRelationDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jyhUserTagRelationDo) ReadDB() *jyhUserTagRelationDo {
	return j.Clauses(dbresolver.Read)
}

func (j jyhUserTagRelationDo) WriteDB() *jyhUserTagRelationDo {
	return j.Clauses(dbresolver.Write)
}

func (j jyhUserTagRelationDo) Session(config *gorm.Session) *jyhUserTagRelationDo {
	return j.withDO(j.DO.Session(config))
}

func (j jyhUserTagRelationDo) Clauses(conds ...clause.Expression) *jyhUserTagRelationDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jyhUserTagRelationDo) Returning(value interface{}, columns ...string) *jyhUserTagRelationDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jyhUserTagRelationDo) Not(conds ...gen.Condition) *jyhUserTagRelationDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jyhUserTagRelationDo) Or(conds ...gen.Condition) *jyhUserTagRelationDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jyhUserTagRelationDo) Select(conds ...field.Expr) *jyhUserTagRelationDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jyhUserTagRelationDo) Where(conds ...gen.Condition) *jyhUserTagRelationDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jyhUserTagRelationDo) Order(conds ...field.Expr) *jyhUserTagRelationDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jyhUserTagRelationDo) Distinct(cols ...field.Expr) *jyhUserTagRelationDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jyhUserTagRelationDo) Omit(cols ...field.Expr) *jyhUserTagRelationDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jyhUserTagRelationDo) Join(table schema.Tabler, on ...field.Expr) *jyhUserTagRelationDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jyhUserTagRelationDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jyhUserTagRelationDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jyhUserTagRelationDo) RightJoin(table schema.Tabler, on ...field.Expr) *jyhUserTagRelationDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jyhUserTagRelationDo) Group(cols ...field.Expr) *jyhUserTagRelationDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jyhUserTagRelationDo) Having(conds ...gen.Condition) *jyhUserTagRelationDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jyhUserTagRelationDo) Limit(limit int) *jyhUserTagRelationDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jyhUserTagRelationDo) Offset(offset int) *jyhUserTagRelationDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jyhUserTagRelationDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jyhUserTagRelationDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jyhUserTagRelationDo) Unscoped() *jyhUserTagRelationDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jyhUserTagRelationDo) Create(values ...*jyhapp.JyhUserTagRelation) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jyhUserTagRelationDo) CreateInBatches(values []*jyhapp.JyhUserTagRelation, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jyhUserTagRelationDo) Save(values ...*jyhapp.JyhUserTagRelation) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jyhUserTagRelationDo) First() (*jyhapp.JyhUserTagRelation, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserTagRelation), nil
	}
}

func (j jyhUserTagRelationDo) Take() (*jyhapp.JyhUserTagRelation, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserTagRelation), nil
	}
}

func (j jyhUserTagRelationDo) Last() (*jyhapp.JyhUserTagRelation, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserTagRelation), nil
	}
}

func (j jyhUserTagRelationDo) Find() ([]*jyhapp.JyhUserTagRelation, error) {
	result, err := j.DO.Find()
	return result.([]*jyhapp.JyhUserTagRelation), err
}

func (j jyhUserTagRelationDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*jyhapp.JyhUserTagRelation, err error) {
	buf := make([]*jyhapp.JyhUserTagRelation, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jyhUserTagRelationDo) FindInBatches(result *[]*jyhapp.JyhUserTagRelation, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jyhUserTagRelationDo) Attrs(attrs ...field.AssignExpr) *jyhUserTagRelationDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jyhUserTagRelationDo) Assign(attrs ...field.AssignExpr) *jyhUserTagRelationDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jyhUserTagRelationDo) Joins(fields ...field.RelationField) *jyhUserTagRelationDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jyhUserTagRelationDo) Preload(fields ...field.RelationField) *jyhUserTagRelationDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jyhUserTagRelationDo) FirstOrInit() (*jyhapp.JyhUserTagRelation, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserTagRelation), nil
	}
}

func (j jyhUserTagRelationDo) FirstOrCreate() (*jyhapp.JyhUserTagRelation, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserTagRelation), nil
	}
}

func (j jyhUserTagRelationDo) FindByPage(offset int, limit int) (result []*jyhapp.JyhUserTagRelation, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jyhUserTagRelationDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jyhUserTagRelationDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jyhUserTagRelationDo) Delete(models ...*jyhapp.JyhUserTagRelation) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jyhUserTagRelationDo) withDO(do gen.Dao) *jyhUserTagRelationDo {
	j.DO = *do.(*gen.DO)
	return j
}
