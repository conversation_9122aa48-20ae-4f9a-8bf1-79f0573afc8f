package mcn

import (
	"github.com/flipped-aurora/gin-vue-admin/server/core"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/initialize"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/mcn"
	"go.uber.org/zap"
	"testing"
)

func TestMcn(t *testing.T) {
	t.Setenv("GVA_CONFIG", "/Users/<USER>/Work/yingxiong/yxgit/douyin.srv/server/config.yaml") // 设置测试环境为debug模式
	// 初始化系统
	initializeSystem()
	client := mcn.NewMCNClient()

	type dTo struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data struct {
			Description string `json:"description"`
			ErrorCode   int    `json:"error_code"`
			Data        string `json:"data"`
			Ticket      string `json:"ticket"`
		} `json:"data"`
	}

	//登录
	login, err2 := client.Login("", "")
	if err2 != nil {
		t.Errorf("Failed to login: %v", err2)
	} else {
		t.Logf("Response from Login: %s", login)
	}
	return

	// 第一步： 获取验证码
	// Test sending verification code
	mobile := "15989399354" // 替换为实际的手机号
	response, err := client.SendVerificationCode(mobile)
	if err != nil {
		t.Errorf("Failed to send verification code: %v", err)
	} else {
		t.Logf("Response from SendVerificationCode: %s", response)
	}
	return

	// 第二步：校验验证码
	// Test verifying code
	/*code := "1067"
	  verifyResponse, err := client.VerifyCode(code, mobile)
	  if err != nil {
	  	t.Errorf("Failed to verify code: %v", err)
	  } else {
	  	t.Logf("Response from VerifyCode: %s", verifyResponse)
	  }

	  // 解析返回的JSON数据
	  var responseData dTo
	  if err := json.Unmarshal([]byte(verifyResponse), &responseData); err != nil {
	  	t.Errorf("Failed to parse JSON response: %v", err)
	  	return
	  }*/

	// 第三步：获取用户信息
	// 获取用户信息
	/*userInfoResponse, err := client.GetUserInfoByTicket(mobile, responseData.Data.Ticket)
	  //userInfoResponse, err := client.GetUserInfoByTicket(mobile, "VTIDEFQJRAREYGS2PQK6P4E5YKP6GARH5GF26Y_hl")
	  if err != nil {
	      t.Errorf("Failed to get user info: %v", err)
	  } else {
	      t.Logf("Response from GetUserInfoByTicket: %s", userInfoResponse)
	  }
	  return*/

	// 第四步：绑定账号
	//return Test binding account
	/*bindResponse, err := client.AddAccountBind(mobile, "***********")
	  if err != nil {
	  	t.Errorf("Failed to bind account: %v", err)
	  } else {
	  	t.Logf("Response from BindAccount: %s", bindResponse)
	  }*/
}

func TestCommissionBill(t *testing.T) {
	t.Setenv("GVA_CONFIG", "/Users/<USER>/Work/yingxiong/yxgit/douyin.srv/server/config.yaml") // 设置测试环境为debug模式
	// 初始化系统
	initializeSystem()
	client := mcn.NewMCNClient()

	// 获取佣金账单
	commissionBill, err := client.FetchDailyCommissionBill("2025-06-19 00:00:00", "2025-06-19 23:59:59", 1, 10)
	if err != nil {
		t.Errorf("Failed to get commission bill: %v", err)
	} else {
		t.Logf("Response from GetCommissionBill: %s", commissionBill)
	}
}

// initializeSystem 初始化系统所有组件
// 提取为单独函数以便于系统重载时调用
func initializeSystem() {
	global.GVA_VP = core.Viper() // 初始化Viper
	initialize.OtherInit()
	global.GVA_LOG = core.Zap() // 初始化zap日志库
	zap.ReplaceGlobals(global.GVA_LOG)
	if global.GVA_REDIS == nil {
		initialize.Redis()
	}

}
