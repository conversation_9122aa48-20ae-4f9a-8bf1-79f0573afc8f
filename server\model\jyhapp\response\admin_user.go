package response

// AdminUserListItem 后台用户列表项
type AdminUserListItem struct {
	ID             uint          `json:"id"`              // 用户ID
	Username       string        `json:"user_name"`       // 用户名
	Phone          string        `json:"phone"`           // 手机号(脱敏)
	InviteCode     string        `json:"invite_code"`     // 邀请码
	InvitedBy      uint          `json:"invited_by"`      // 邀请人ID
	InviterName    string        `json:"inviter_name"`    // 邀请人用户名
	IsAgent        bool          `json:"is_agent"`        // 是否代理
	Status         int           `json:"status"`          // 状态
	UserType       string        `json:"user_type"`       // 用户类型
	CanLiveStream  bool          `json:"can_live_stream"` // 是否可以直播
	ContractStatus int           `json:"contract_status"` // 签约状态 0-未签约 1-已签约
	CreatedAt      string        `json:"created_at"`      // 创建时间
	Tags           []UserTagItem `json:"tags" gorm:"-"`   // 用户标签列表
	UserExtInfo
}

// AdminUserCreateResp 后台创建用户响应
type AdminUserCreateResp struct {
	ID         uint   `json:"id"`          // 新创建的用户ID
	InviteCode string `json:"invite_code"` // 系统生成的邀请码
}

// UserBasicInfo 用户基本信息
type UserBasicInfo struct {
	ID             uint   `json:"id"`              // 用户ID
	Username       string `json:"user_name"`       // 用户名
	Phone          string `json:"phone"`           // 手机号(脱敏)
	InviteCode     string `json:"invite_code"`     // 邀请码
	InvitedBy      uint   `json:"invited_by"`      // 邀请人ID
	InviterName    string `json:"inviter_name"`    // 邀请人用户名
	IsAgent        bool   `json:"is_agent"`        // 是否代理
	Status         int    `json:"status"`          // 状态
	UserType       string `json:"user_type"`       // 用户类型
	CanLiveStream  bool   `json:"can_live_stream"` // 是否可以直播
	ContractStatus int    `json:"contract_status"` // 签约状态 0-未签约 1-已签约
	CreatedAt      string `json:"created_at"`      // 创建时间
	CurrentLevelID *uint  `json:"level_id"`        // 当前生效的等级ID
	UserExtInfo
}

// UserStatistics 用户统计数据
type UserStatistics struct {
	InvitedCount int64 `json:"invited_count"` // 邀请用户数
	OrderCount   int64 `json:"order_count"`   // 订单数
	TotalAmount  int64 `json:"total_amount"`  // 总消费金额
}

// InvitedUserItem 被邀请用户项
type InvitedUserItem struct {
	ID        uint   `json:"id"`         // 用户ID
	Username  string `json:"user_name"`  // 用户名
	CreatedAt string `json:"created_at"` // 创建时间
}

// AdminUserDetailResp 用户详情响应
type AdminUserDetailResp struct {
	BasicInfo    *UserBasicInfo     `json:"basic_info"`    // 基本信息
	Statistics   *UserStatistics    `json:"statistics"`    // 统计数据
	InvitedUsers []*InvitedUserItem `json:"invited_users"` // 被邀请用户列表
}
