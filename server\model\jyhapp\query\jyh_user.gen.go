// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
)

func newJyhUser(db *gorm.DB, opts ...gen.DOOption) jyhUser {
	_jyhUser := jyhUser{}

	_jyhUser.jyhUserDo.UseDB(db, opts...)
	_jyhUser.jyhUserDo.UseModel(&jyhapp.JyhUser{})

	tableName := _jyhUser.jyhUserDo.TableName()
	_jyhUser.ALL = field.NewAsterisk(tableName)
	_jyhUser.ID = field.NewUint(tableName, "id")
	_jyhUser.CreatedAt = field.NewTime(tableName, "created_at")
	_jyhUser.UpdatedAt = field.NewTime(tableName, "updated_at")
	_jyhUser.DeletedAt = field.NewField(tableName, "deleted_at")
	_jyhUser.Username = field.NewString(tableName, "username")
	_jyhUser.Phone = field.NewString(tableName, "phone")
	_jyhUser.Password = field.NewString(tableName, "password")
	_jyhUser.InvitedBy = field.NewUint(tableName, "invited_by")
	_jyhUser.IsAgent = field.NewBool(tableName, "is_agent")
	_jyhUser.Avatar = field.NewString(tableName, "avatar")
	_jyhUser.RegisteredAt = field.NewTime(tableName, "registered_at")
	_jyhUser.LastLoginAt = field.NewTime(tableName, "last_login_at")
	_jyhUser.Status = field.NewUint(tableName, "status")
	_jyhUser.UserType = field.NewString(tableName, "user_type")
	_jyhUser.InviteCode = field.NewString(tableName, "invite_code")
	_jyhUser.ContractStatus = field.NewInt(tableName, "contract_status")
	_jyhUser.CanLiveStream = field.NewBool(tableName, "can_live_stream")
	_jyhUser.Ext = field.NewField(tableName, "ext")
	_jyhUser.DouYinExt = field.NewField(tableName, "dou_yin_ext")
	_jyhUser.DouYinId = field.NewString(tableName, "dou_yin_id")
	_jyhUser.Points = field.NewInt(tableName, "points")
	_jyhUser.JyhUserExt = jyhUserHasOneJyhUserExt{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("JyhUserExt", "jyhapp.JyhUserExt"),
	}

	_jyhUser.Invitees = jyhUserHasManyInvitees{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Invitees", "jyhapp.JyhUser"),
		Inviter: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Invitees.Inviter", "jyhapp.JyhUser"),
		},
		JyhUserExt: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Invitees.JyhUserExt", "jyhapp.JyhUserExt"),
		},
		Invitees: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Invitees.Invitees", "jyhapp.JyhUser"),
		},
		Tags: struct {
			field.RelationField
			Users struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Invitees.Tags", "jyhapp.JyhUserTag"),
			Users: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Invitees.Tags.Users", "jyhapp.JyhUser"),
			},
		},
	}

	_jyhUser.Inviter = jyhUserBelongsToInviter{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Inviter", "jyhapp.JyhUser"),
	}

	_jyhUser.Tags = jyhUserManyToManyTags{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Tags", "jyhapp.JyhUserTag"),
	}

	_jyhUser.fillFieldMap()

	return _jyhUser
}

type jyhUser struct {
	jyhUserDo

	ALL            field.Asterisk
	ID             field.Uint
	CreatedAt      field.Time
	UpdatedAt      field.Time
	DeletedAt      field.Field
	Username       field.String
	Phone          field.String
	Password       field.String
	InvitedBy      field.Uint
	IsAgent        field.Bool
	Avatar         field.String
	RegisteredAt   field.Time
	LastLoginAt    field.Time
	Status         field.Uint
	UserType       field.String
	InviteCode     field.String
	ContractStatus field.Int
	CanLiveStream  field.Bool
	Ext            field.Field
	DouYinExt      field.Field
	DouYinId       field.String
	Points         field.Int
	JyhUserExt     jyhUserHasOneJyhUserExt

	Invitees jyhUserHasManyInvitees

	Inviter jyhUserBelongsToInviter

	Tags jyhUserManyToManyTags

	fieldMap map[string]field.Expr
}

func (j jyhUser) Table(newTableName string) *jyhUser {
	j.jyhUserDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j jyhUser) As(alias string) *jyhUser {
	j.jyhUserDo.DO = *(j.jyhUserDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *jyhUser) updateTableName(table string) *jyhUser {
	j.ALL = field.NewAsterisk(table)
	j.ID = field.NewUint(table, "id")
	j.CreatedAt = field.NewTime(table, "created_at")
	j.UpdatedAt = field.NewTime(table, "updated_at")
	j.DeletedAt = field.NewField(table, "deleted_at")
	j.Username = field.NewString(table, "username")
	j.Phone = field.NewString(table, "phone")
	j.Password = field.NewString(table, "password")
	j.InvitedBy = field.NewUint(table, "invited_by")
	j.IsAgent = field.NewBool(table, "is_agent")
	j.Avatar = field.NewString(table, "avatar")
	j.RegisteredAt = field.NewTime(table, "registered_at")
	j.LastLoginAt = field.NewTime(table, "last_login_at")
	j.Status = field.NewUint(table, "status")
	j.UserType = field.NewString(table, "user_type")
	j.InviteCode = field.NewString(table, "invite_code")
	j.ContractStatus = field.NewInt(table, "contract_status")
	j.CanLiveStream = field.NewBool(table, "can_live_stream")
	j.Ext = field.NewField(table, "ext")
	j.DouYinExt = field.NewField(table, "dou_yin_ext")
	j.DouYinId = field.NewString(table, "dou_yin_id")
	j.Points = field.NewInt(table, "points")

	j.fillFieldMap()

	return j
}

func (j *jyhUser) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *jyhUser) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 25)
	j.fieldMap["id"] = j.ID
	j.fieldMap["created_at"] = j.CreatedAt
	j.fieldMap["updated_at"] = j.UpdatedAt
	j.fieldMap["deleted_at"] = j.DeletedAt
	j.fieldMap["username"] = j.Username
	j.fieldMap["phone"] = j.Phone
	j.fieldMap["password"] = j.Password
	j.fieldMap["invited_by"] = j.InvitedBy
	j.fieldMap["is_agent"] = j.IsAgent
	j.fieldMap["avatar"] = j.Avatar
	j.fieldMap["registered_at"] = j.RegisteredAt
	j.fieldMap["last_login_at"] = j.LastLoginAt
	j.fieldMap["status"] = j.Status
	j.fieldMap["user_type"] = j.UserType
	j.fieldMap["invite_code"] = j.InviteCode
	j.fieldMap["contract_status"] = j.ContractStatus
	j.fieldMap["can_live_stream"] = j.CanLiveStream
	j.fieldMap["ext"] = j.Ext
	j.fieldMap["dou_yin_ext"] = j.DouYinExt
	j.fieldMap["dou_yin_id"] = j.DouYinId
	j.fieldMap["points"] = j.Points

}

func (j jyhUser) clone(db *gorm.DB) jyhUser {
	j.jyhUserDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j jyhUser) replaceDB(db *gorm.DB) jyhUser {
	j.jyhUserDo.ReplaceDB(db)
	return j
}

type jyhUserHasOneJyhUserExt struct {
	db *gorm.DB

	field.RelationField
}

func (a jyhUserHasOneJyhUserExt) Where(conds ...field.Expr) *jyhUserHasOneJyhUserExt {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a jyhUserHasOneJyhUserExt) WithContext(ctx context.Context) *jyhUserHasOneJyhUserExt {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a jyhUserHasOneJyhUserExt) Session(session *gorm.Session) *jyhUserHasOneJyhUserExt {
	a.db = a.db.Session(session)
	return &a
}

func (a jyhUserHasOneJyhUserExt) Model(m *jyhapp.JyhUser) *jyhUserHasOneJyhUserExtTx {
	return &jyhUserHasOneJyhUserExtTx{a.db.Model(m).Association(a.Name())}
}

type jyhUserHasOneJyhUserExtTx struct{ tx *gorm.Association }

func (a jyhUserHasOneJyhUserExtTx) Find() (result *jyhapp.JyhUserExt, err error) {
	return result, a.tx.Find(&result)
}

func (a jyhUserHasOneJyhUserExtTx) Append(values ...*jyhapp.JyhUserExt) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a jyhUserHasOneJyhUserExtTx) Replace(values ...*jyhapp.JyhUserExt) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a jyhUserHasOneJyhUserExtTx) Delete(values ...*jyhapp.JyhUserExt) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a jyhUserHasOneJyhUserExtTx) Clear() error {
	return a.tx.Clear()
}

func (a jyhUserHasOneJyhUserExtTx) Count() int64 {
	return a.tx.Count()
}

type jyhUserHasManyInvitees struct {
	db *gorm.DB

	field.RelationField

	Inviter struct {
		field.RelationField
	}
	JyhUserExt struct {
		field.RelationField
	}
	Invitees struct {
		field.RelationField
	}
	Tags struct {
		field.RelationField
		Users struct {
			field.RelationField
		}
	}
}

func (a jyhUserHasManyInvitees) Where(conds ...field.Expr) *jyhUserHasManyInvitees {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a jyhUserHasManyInvitees) WithContext(ctx context.Context) *jyhUserHasManyInvitees {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a jyhUserHasManyInvitees) Session(session *gorm.Session) *jyhUserHasManyInvitees {
	a.db = a.db.Session(session)
	return &a
}

func (a jyhUserHasManyInvitees) Model(m *jyhapp.JyhUser) *jyhUserHasManyInviteesTx {
	return &jyhUserHasManyInviteesTx{a.db.Model(m).Association(a.Name())}
}

type jyhUserHasManyInviteesTx struct{ tx *gorm.Association }

func (a jyhUserHasManyInviteesTx) Find() (result []*jyhapp.JyhUser, err error) {
	return result, a.tx.Find(&result)
}

func (a jyhUserHasManyInviteesTx) Append(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a jyhUserHasManyInviteesTx) Replace(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a jyhUserHasManyInviteesTx) Delete(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a jyhUserHasManyInviteesTx) Clear() error {
	return a.tx.Clear()
}

func (a jyhUserHasManyInviteesTx) Count() int64 {
	return a.tx.Count()
}

type jyhUserBelongsToInviter struct {
	db *gorm.DB

	field.RelationField
}

func (a jyhUserBelongsToInviter) Where(conds ...field.Expr) *jyhUserBelongsToInviter {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a jyhUserBelongsToInviter) WithContext(ctx context.Context) *jyhUserBelongsToInviter {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a jyhUserBelongsToInviter) Session(session *gorm.Session) *jyhUserBelongsToInviter {
	a.db = a.db.Session(session)
	return &a
}

func (a jyhUserBelongsToInviter) Model(m *jyhapp.JyhUser) *jyhUserBelongsToInviterTx {
	return &jyhUserBelongsToInviterTx{a.db.Model(m).Association(a.Name())}
}

type jyhUserBelongsToInviterTx struct{ tx *gorm.Association }

func (a jyhUserBelongsToInviterTx) Find() (result *jyhapp.JyhUser, err error) {
	return result, a.tx.Find(&result)
}

func (a jyhUserBelongsToInviterTx) Append(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a jyhUserBelongsToInviterTx) Replace(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a jyhUserBelongsToInviterTx) Delete(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a jyhUserBelongsToInviterTx) Clear() error {
	return a.tx.Clear()
}

func (a jyhUserBelongsToInviterTx) Count() int64 {
	return a.tx.Count()
}

type jyhUserManyToManyTags struct {
	db *gorm.DB

	field.RelationField
}

func (a jyhUserManyToManyTags) Where(conds ...field.Expr) *jyhUserManyToManyTags {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a jyhUserManyToManyTags) WithContext(ctx context.Context) *jyhUserManyToManyTags {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a jyhUserManyToManyTags) Session(session *gorm.Session) *jyhUserManyToManyTags {
	a.db = a.db.Session(session)
	return &a
}

func (a jyhUserManyToManyTags) Model(m *jyhapp.JyhUser) *jyhUserManyToManyTagsTx {
	return &jyhUserManyToManyTagsTx{a.db.Model(m).Association(a.Name())}
}

type jyhUserManyToManyTagsTx struct{ tx *gorm.Association }

func (a jyhUserManyToManyTagsTx) Find() (result []*jyhapp.JyhUserTag, err error) {
	return result, a.tx.Find(&result)
}

func (a jyhUserManyToManyTagsTx) Append(values ...*jyhapp.JyhUserTag) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a jyhUserManyToManyTagsTx) Replace(values ...*jyhapp.JyhUserTag) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a jyhUserManyToManyTagsTx) Delete(values ...*jyhapp.JyhUserTag) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a jyhUserManyToManyTagsTx) Clear() error {
	return a.tx.Clear()
}

func (a jyhUserManyToManyTagsTx) Count() int64 {
	return a.tx.Count()
}

type jyhUserDo struct{ gen.DO }

func (j jyhUserDo) Debug() *jyhUserDo {
	return j.withDO(j.DO.Debug())
}

func (j jyhUserDo) WithContext(ctx context.Context) *jyhUserDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jyhUserDo) ReadDB() *jyhUserDo {
	return j.Clauses(dbresolver.Read)
}

func (j jyhUserDo) WriteDB() *jyhUserDo {
	return j.Clauses(dbresolver.Write)
}

func (j jyhUserDo) Session(config *gorm.Session) *jyhUserDo {
	return j.withDO(j.DO.Session(config))
}

func (j jyhUserDo) Clauses(conds ...clause.Expression) *jyhUserDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jyhUserDo) Returning(value interface{}, columns ...string) *jyhUserDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jyhUserDo) Not(conds ...gen.Condition) *jyhUserDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jyhUserDo) Or(conds ...gen.Condition) *jyhUserDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jyhUserDo) Select(conds ...field.Expr) *jyhUserDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jyhUserDo) Where(conds ...gen.Condition) *jyhUserDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jyhUserDo) Order(conds ...field.Expr) *jyhUserDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jyhUserDo) Distinct(cols ...field.Expr) *jyhUserDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jyhUserDo) Omit(cols ...field.Expr) *jyhUserDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jyhUserDo) Join(table schema.Tabler, on ...field.Expr) *jyhUserDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jyhUserDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jyhUserDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jyhUserDo) RightJoin(table schema.Tabler, on ...field.Expr) *jyhUserDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jyhUserDo) Group(cols ...field.Expr) *jyhUserDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jyhUserDo) Having(conds ...gen.Condition) *jyhUserDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jyhUserDo) Limit(limit int) *jyhUserDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jyhUserDo) Offset(offset int) *jyhUserDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jyhUserDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jyhUserDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jyhUserDo) Unscoped() *jyhUserDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jyhUserDo) Create(values ...*jyhapp.JyhUser) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jyhUserDo) CreateInBatches(values []*jyhapp.JyhUser, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jyhUserDo) Save(values ...*jyhapp.JyhUser) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jyhUserDo) First() (*jyhapp.JyhUser, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUser), nil
	}
}

func (j jyhUserDo) Take() (*jyhapp.JyhUser, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUser), nil
	}
}

func (j jyhUserDo) Last() (*jyhapp.JyhUser, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUser), nil
	}
}

func (j jyhUserDo) Find() ([]*jyhapp.JyhUser, error) {
	result, err := j.DO.Find()
	return result.([]*jyhapp.JyhUser), err
}

func (j jyhUserDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*jyhapp.JyhUser, err error) {
	buf := make([]*jyhapp.JyhUser, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jyhUserDo) FindInBatches(result *[]*jyhapp.JyhUser, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jyhUserDo) Attrs(attrs ...field.AssignExpr) *jyhUserDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jyhUserDo) Assign(attrs ...field.AssignExpr) *jyhUserDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jyhUserDo) Joins(fields ...field.RelationField) *jyhUserDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jyhUserDo) Preload(fields ...field.RelationField) *jyhUserDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jyhUserDo) FirstOrInit() (*jyhapp.JyhUser, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUser), nil
	}
}

func (j jyhUserDo) FirstOrCreate() (*jyhapp.JyhUser, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUser), nil
	}
}

func (j jyhUserDo) FindByPage(offset int, limit int) (result []*jyhapp.JyhUser, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jyhUserDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jyhUserDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jyhUserDo) Delete(models ...*jyhapp.JyhUser) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jyhUserDo) withDO(do gen.Dao) *jyhUserDo {
	j.DO = *do.(*gen.DO)
	return j
}
