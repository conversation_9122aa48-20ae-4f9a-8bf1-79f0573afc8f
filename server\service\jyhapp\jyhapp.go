package jyhapp

import (
	"database/sql"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/errno"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/query"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/jgorm"
	"math"
	"regexp"
	"strconv"
	"strings"
	"time"
)

// 辅助工具
//
// jason.liao 2023.08.02

var timeFormats = []string{
	"20060102",
	"200601021504",
	"20060102150405",
	"2006-01-02",
	"2006-01-02 15",
	"2006-01-02 15:04",
	"2006-01-02 15:04:05",
	"2006-01-02T15:04:05",  // iso8601 without timezone
	"2006-01-02T15:04:05Z", // iso8601 with UTC timezone
	"2006-01-02 15:04:05.000",
}

func DateTime4Day0(d time.Time) time.Time {
	if d.<PERSON>() {
		return time.Time{}
	}
	_y, _m, _d := d.Date()
	return time.Date(_y, _m, _d, 0, 0, 0, 0, time.Local)
}

func ParseDatetime(s string) time.Time {
	if s == "" {
		return time.Time{}
	}
	for _, dateType := range timeFormats {
		if d, e := time.ParseInLocation(dateType, s, time.Local); e == nil {
			return d
		}
	}
	return time.Time{}
}

var SelectAppend = jgorm.SelectAppend

var rePhone = regexp.MustCompile(`^1[3456789]\d{9}$`)

// IsPhoneNumber 判断是否是手机号
func IsPhoneNumber(s string) bool {
	return len(s) == 11 && rePhone.MatchString(s)
}

// HideIdent 匹配 手机号,邮箱,中文,身份证等等 进行脱敏处理
func HideIdent(s string) string {
	if len(s) == 0 {
		return s
	}
	// 匹配手机号
	if IsPhoneNumber(s) {
		return s[:3] + "****" + s[7:]
	}
	// 匹配邮箱
	if ok, _ := regexp.MatchString(`^(\w)+(\.\w+)*@(\w)+((\.\w+)+)$`, s); ok {
		p := strings.IndexRune(s, '@')
		if p < 3 {
			return "***" + s[p:]
		} else {
			return s[:3] + "***" + s[p:]
		}
	}
	// 匹配中文
	if ok, _ := regexp.MatchString(`^[\u4e00-\u9fa5]{0,}$`, s); ok {
		return s[:1] + "**"
	}
	// 匹配身份证
	if ok, _ := regexp.MatchString(`^(\d{15}$|^\d{18}$|^\d{17}(\d|X|x))$`, s); ok {
		return s[:3] + "***********" + s[14:]
	}
	nameRune := []rune(s)
	lens := len(nameRune)
	if lens <= 1 {
		return "****"
	} else if lens == 2 {
		return string(nameRune[:1]) + "*"
	} else if lens == 3 {
		return string(nameRune[:1]) + "*" + string(nameRune[2:3])
	} else if lens == 4 {
		return string(nameRune[:1]) + "**" + string(nameRune[lens-1:lens])
	} else {
		return string(nameRune[:2]) + "***" + string(nameRune[lens-2:lens])
	}
}

func TryTransaction(fn func(tx *query.Query) error, opts ...*sql.TxOptions) error {
	var q = query.Q
	if q == nil {
		if global.GVA_DB != nil {
			q = query.Use(global.GVA_DB)
		}
	}
	if q == nil {
		return errno.QueryFailed.WithMsg("数据库连接失败")
	}
	return q.Transaction(fn, opts...)
}

// 解析时间为本地时区
func ParseTimeInLocal(t time.Time) time.Time {
	shLocation, _ := time.LoadLocation("Asia/Shanghai")
	return t.In(shLocation)
}

func GetInterfaceToInt(t1 interface{}) int {
	var t2 int
	switch t1.(type) {
	case uint:
		t2 = int(t1.(uint))
		break
	case int8:
		t2 = int(t1.(int8))
		break
	case uint8:
		t2 = int(t1.(uint8))
		break
	case int16:
		t2 = int(t1.(int16))
		break
	case uint16:
		t2 = int(t1.(uint16))
		break
	case int32:
		t2 = int(t1.(int32))
		break
	case uint32:
		t2 = int(t1.(uint32))
		break
	case int64:
		t2 = int(t1.(int64))
		break
	case uint64:
		t2 = int(t1.(uint64))
		break
	case float32:
		t2 = int(t1.(float32))
		break
	case float64:
		t2 = int(t1.(float64))
		break
	case string:
		t2, _ = strconv.Atoi(t1.(string))
		break
	default:
		t2 = t1.(int)
		break
	}
	return t2
}

// DaysFromNow 计算目标时间距离当前时间的天数
// 正数表示未来天数，负数表示过去天数
func DaysFromNow(target time.Time) int {
	now := time.Now().Local()

	// 获取本地时区信息
	loc := now.Location()

	// 将当前时间和目标时间都转换为本地时区的同一天0点
	normalize := func(t time.Time) time.Time {
		return time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, loc)
	}

	currentDate := normalize(now)
	targetDate := normalize(target.In(loc)) // 确保目标时间在本地时区

	// 计算小时差并四舍五入到整天
	hours := targetDate.Sub(currentDate).Hours()
	days := int(math.Round(hours / 24))

	return days
}
