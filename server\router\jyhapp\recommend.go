package jyhapp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type RecommendPositionRouter struct{}

func (s *RecommendPositionRouter) InitRecommendPositionRouter(Router *gin.RouterGroup) {
	recommendPositionRouter := Router.Group("recommend").Use(middleware.OperationRecord())
	recommendPositionWithoutRecord := Router.Group("recommend")
	recommendPositionApi := v1.ApiGroupApp.JyhApiGroup.RecommendPositionApi

	{
		recommendPositionRouter.POST("create", recommendPositionApi.CreateRecommendPosition)
		recommendPositionRouter.PUT("update", recommendPositionApi.UpdateRecommendPosition)
		recommendPositionRouter.DELETE("delete", recommendPositionApi.DeleteRecommendPosition)
	}
	{
		recommendPositionWithoutRecord.GET("detail", recommendPositionApi.GetRecommendPositionDetail)
		recommendPositionWithoutRecord.GET("list", recommendPositionApi.GetRecommendPositionList)
	}
	{
		recommendPositionRouter.POST("item/create", recommendPositionApi.CreateRecommendItem)
		recommendPositionRouter.PUT("item/update", recommendPositionApi.UpdateRecommendItem)
		recommendPositionRouter.DELETE("item/delete", recommendPositionApi.DeleteRecommendItem)
	}
	{
		recommendPositionWithoutRecord.GET("item/detail", recommendPositionApi.GetRecommendItemDetail)
		recommendPositionWithoutRecord.GET("item/list", recommendPositionApi.GetRecommendItemList)
	}
}
