// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
)

func newJyhUserAccountTransaction(db *gorm.DB, opts ...gen.DOOption) jyhUserAccountTransaction {
	_jyhUserAccountTransaction := jyhUserAccountTransaction{}

	_jyhUserAccountTransaction.jyhUserAccountTransactionDo.UseDB(db, opts...)
	_jyhUserAccountTransaction.jyhUserAccountTransactionDo.UseModel(&jyhapp.JyhUserAccountTransaction{})

	tableName := _jyhUserAccountTransaction.jyhUserAccountTransactionDo.TableName()
	_jyhUserAccountTransaction.ALL = field.NewAsterisk(tableName)
	_jyhUserAccountTransaction.TransactionID = field.NewUint(tableName, "transaction_id")
	_jyhUserAccountTransaction.AccountID = field.NewUint(tableName, "account_id")
	_jyhUserAccountTransaction.Amount = field.NewField(tableName, "amount")
	_jyhUserAccountTransaction.TransactionType = field.NewString(tableName, "transaction_type")
	_jyhUserAccountTransaction.SourceType = field.NewString(tableName, "source_type")
	_jyhUserAccountTransaction.ChangeType = field.NewInt(tableName, "change_type")
	_jyhUserAccountTransaction.RelatedBusinessID = field.NewUint(tableName, "related_business_id")
	_jyhUserAccountTransaction.TransactionDescription = field.NewString(tableName, "transaction_description")
	_jyhUserAccountTransaction.TransactionStatus = field.NewString(tableName, "transaction_status")
	_jyhUserAccountTransaction.CreatedAt = field.NewTime(tableName, "created_at")
	_jyhUserAccountTransaction.CompletedAt = field.NewTime(tableName, "completed_at")
	_jyhUserAccountTransaction.BalanceBefore = field.NewField(tableName, "balance_before")
	_jyhUserAccountTransaction.BalanceAfter = field.NewField(tableName, "balance_after")
	_jyhUserAccountTransaction.Account = jyhUserAccountTransactionHasOneAccount{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Account", "jyhapp.JyhUserAccount"),
		User: struct {
			field.RelationField
			Inviter struct {
				field.RelationField
			}
			JyhUserExt struct {
				field.RelationField
			}
			Invitees struct {
				field.RelationField
			}
			Tags struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Account.User", "jyhapp.JyhUser"),
			Inviter: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Account.User.Inviter", "jyhapp.JyhUser"),
			},
			JyhUserExt: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Account.User.JyhUserExt", "jyhapp.JyhUserExt"),
			},
			Invitees: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Account.User.Invitees", "jyhapp.JyhUser"),
			},
			Tags: struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Account.User.Tags", "jyhapp.JyhUserTag"),
				Users: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Account.User.Tags.Users", "jyhapp.JyhUser"),
				},
			},
		},
		Transactions: struct {
			field.RelationField
			Account struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Account.Transactions", "jyhapp.JyhUserAccountTransaction"),
			Account: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Account.Transactions.Account", "jyhapp.JyhUserAccount"),
			},
		},
	}

	_jyhUserAccountTransaction.fillFieldMap()

	return _jyhUserAccountTransaction
}

type jyhUserAccountTransaction struct {
	jyhUserAccountTransactionDo

	ALL                    field.Asterisk
	TransactionID          field.Uint
	AccountID              field.Uint
	Amount                 field.Field
	TransactionType        field.String
	SourceType             field.String
	ChangeType             field.Int
	RelatedBusinessID      field.Uint
	TransactionDescription field.String
	TransactionStatus      field.String
	CreatedAt              field.Time
	CompletedAt            field.Time
	BalanceBefore          field.Field
	BalanceAfter           field.Field
	Account                jyhUserAccountTransactionHasOneAccount

	fieldMap map[string]field.Expr
}

func (j jyhUserAccountTransaction) Table(newTableName string) *jyhUserAccountTransaction {
	j.jyhUserAccountTransactionDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j jyhUserAccountTransaction) As(alias string) *jyhUserAccountTransaction {
	j.jyhUserAccountTransactionDo.DO = *(j.jyhUserAccountTransactionDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *jyhUserAccountTransaction) updateTableName(table string) *jyhUserAccountTransaction {
	j.ALL = field.NewAsterisk(table)
	j.TransactionID = field.NewUint(table, "transaction_id")
	j.AccountID = field.NewUint(table, "account_id")
	j.Amount = field.NewField(table, "amount")
	j.TransactionType = field.NewString(table, "transaction_type")
	j.SourceType = field.NewString(table, "source_type")
	j.ChangeType = field.NewInt(table, "change_type")
	j.RelatedBusinessID = field.NewUint(table, "related_business_id")
	j.TransactionDescription = field.NewString(table, "transaction_description")
	j.TransactionStatus = field.NewString(table, "transaction_status")
	j.CreatedAt = field.NewTime(table, "created_at")
	j.CompletedAt = field.NewTime(table, "completed_at")
	j.BalanceBefore = field.NewField(table, "balance_before")
	j.BalanceAfter = field.NewField(table, "balance_after")

	j.fillFieldMap()

	return j
}

func (j *jyhUserAccountTransaction) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *jyhUserAccountTransaction) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 14)
	j.fieldMap["transaction_id"] = j.TransactionID
	j.fieldMap["account_id"] = j.AccountID
	j.fieldMap["amount"] = j.Amount
	j.fieldMap["transaction_type"] = j.TransactionType
	j.fieldMap["source_type"] = j.SourceType
	j.fieldMap["change_type"] = j.ChangeType
	j.fieldMap["related_business_id"] = j.RelatedBusinessID
	j.fieldMap["transaction_description"] = j.TransactionDescription
	j.fieldMap["transaction_status"] = j.TransactionStatus
	j.fieldMap["created_at"] = j.CreatedAt
	j.fieldMap["completed_at"] = j.CompletedAt
	j.fieldMap["balance_before"] = j.BalanceBefore
	j.fieldMap["balance_after"] = j.BalanceAfter

}

func (j jyhUserAccountTransaction) clone(db *gorm.DB) jyhUserAccountTransaction {
	j.jyhUserAccountTransactionDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j jyhUserAccountTransaction) replaceDB(db *gorm.DB) jyhUserAccountTransaction {
	j.jyhUserAccountTransactionDo.ReplaceDB(db)
	return j
}

type jyhUserAccountTransactionHasOneAccount struct {
	db *gorm.DB

	field.RelationField

	User struct {
		field.RelationField
		Inviter struct {
			field.RelationField
		}
		JyhUserExt struct {
			field.RelationField
		}
		Invitees struct {
			field.RelationField
		}
		Tags struct {
			field.RelationField
			Users struct {
				field.RelationField
			}
		}
	}
	Transactions struct {
		field.RelationField
		Account struct {
			field.RelationField
		}
	}
}

func (a jyhUserAccountTransactionHasOneAccount) Where(conds ...field.Expr) *jyhUserAccountTransactionHasOneAccount {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a jyhUserAccountTransactionHasOneAccount) WithContext(ctx context.Context) *jyhUserAccountTransactionHasOneAccount {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a jyhUserAccountTransactionHasOneAccount) Session(session *gorm.Session) *jyhUserAccountTransactionHasOneAccount {
	a.db = a.db.Session(session)
	return &a
}

func (a jyhUserAccountTransactionHasOneAccount) Model(m *jyhapp.JyhUserAccountTransaction) *jyhUserAccountTransactionHasOneAccountTx {
	return &jyhUserAccountTransactionHasOneAccountTx{a.db.Model(m).Association(a.Name())}
}

type jyhUserAccountTransactionHasOneAccountTx struct{ tx *gorm.Association }

func (a jyhUserAccountTransactionHasOneAccountTx) Find() (result *jyhapp.JyhUserAccount, err error) {
	return result, a.tx.Find(&result)
}

func (a jyhUserAccountTransactionHasOneAccountTx) Append(values ...*jyhapp.JyhUserAccount) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a jyhUserAccountTransactionHasOneAccountTx) Replace(values ...*jyhapp.JyhUserAccount) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a jyhUserAccountTransactionHasOneAccountTx) Delete(values ...*jyhapp.JyhUserAccount) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a jyhUserAccountTransactionHasOneAccountTx) Clear() error {
	return a.tx.Clear()
}

func (a jyhUserAccountTransactionHasOneAccountTx) Count() int64 {
	return a.tx.Count()
}

type jyhUserAccountTransactionDo struct{ gen.DO }

func (j jyhUserAccountTransactionDo) Debug() *jyhUserAccountTransactionDo {
	return j.withDO(j.DO.Debug())
}

func (j jyhUserAccountTransactionDo) WithContext(ctx context.Context) *jyhUserAccountTransactionDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jyhUserAccountTransactionDo) ReadDB() *jyhUserAccountTransactionDo {
	return j.Clauses(dbresolver.Read)
}

func (j jyhUserAccountTransactionDo) WriteDB() *jyhUserAccountTransactionDo {
	return j.Clauses(dbresolver.Write)
}

func (j jyhUserAccountTransactionDo) Session(config *gorm.Session) *jyhUserAccountTransactionDo {
	return j.withDO(j.DO.Session(config))
}

func (j jyhUserAccountTransactionDo) Clauses(conds ...clause.Expression) *jyhUserAccountTransactionDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jyhUserAccountTransactionDo) Returning(value interface{}, columns ...string) *jyhUserAccountTransactionDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jyhUserAccountTransactionDo) Not(conds ...gen.Condition) *jyhUserAccountTransactionDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jyhUserAccountTransactionDo) Or(conds ...gen.Condition) *jyhUserAccountTransactionDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jyhUserAccountTransactionDo) Select(conds ...field.Expr) *jyhUserAccountTransactionDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jyhUserAccountTransactionDo) Where(conds ...gen.Condition) *jyhUserAccountTransactionDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jyhUserAccountTransactionDo) Order(conds ...field.Expr) *jyhUserAccountTransactionDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jyhUserAccountTransactionDo) Distinct(cols ...field.Expr) *jyhUserAccountTransactionDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jyhUserAccountTransactionDo) Omit(cols ...field.Expr) *jyhUserAccountTransactionDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jyhUserAccountTransactionDo) Join(table schema.Tabler, on ...field.Expr) *jyhUserAccountTransactionDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jyhUserAccountTransactionDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jyhUserAccountTransactionDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jyhUserAccountTransactionDo) RightJoin(table schema.Tabler, on ...field.Expr) *jyhUserAccountTransactionDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jyhUserAccountTransactionDo) Group(cols ...field.Expr) *jyhUserAccountTransactionDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jyhUserAccountTransactionDo) Having(conds ...gen.Condition) *jyhUserAccountTransactionDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jyhUserAccountTransactionDo) Limit(limit int) *jyhUserAccountTransactionDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jyhUserAccountTransactionDo) Offset(offset int) *jyhUserAccountTransactionDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jyhUserAccountTransactionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jyhUserAccountTransactionDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jyhUserAccountTransactionDo) Unscoped() *jyhUserAccountTransactionDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jyhUserAccountTransactionDo) Create(values ...*jyhapp.JyhUserAccountTransaction) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jyhUserAccountTransactionDo) CreateInBatches(values []*jyhapp.JyhUserAccountTransaction, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jyhUserAccountTransactionDo) Save(values ...*jyhapp.JyhUserAccountTransaction) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jyhUserAccountTransactionDo) First() (*jyhapp.JyhUserAccountTransaction, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserAccountTransaction), nil
	}
}

func (j jyhUserAccountTransactionDo) Take() (*jyhapp.JyhUserAccountTransaction, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserAccountTransaction), nil
	}
}

func (j jyhUserAccountTransactionDo) Last() (*jyhapp.JyhUserAccountTransaction, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserAccountTransaction), nil
	}
}

func (j jyhUserAccountTransactionDo) Find() ([]*jyhapp.JyhUserAccountTransaction, error) {
	result, err := j.DO.Find()
	return result.([]*jyhapp.JyhUserAccountTransaction), err
}

func (j jyhUserAccountTransactionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*jyhapp.JyhUserAccountTransaction, err error) {
	buf := make([]*jyhapp.JyhUserAccountTransaction, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jyhUserAccountTransactionDo) FindInBatches(result *[]*jyhapp.JyhUserAccountTransaction, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jyhUserAccountTransactionDo) Attrs(attrs ...field.AssignExpr) *jyhUserAccountTransactionDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jyhUserAccountTransactionDo) Assign(attrs ...field.AssignExpr) *jyhUserAccountTransactionDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jyhUserAccountTransactionDo) Joins(fields ...field.RelationField) *jyhUserAccountTransactionDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jyhUserAccountTransactionDo) Preload(fields ...field.RelationField) *jyhUserAccountTransactionDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jyhUserAccountTransactionDo) FirstOrInit() (*jyhapp.JyhUserAccountTransaction, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserAccountTransaction), nil
	}
}

func (j jyhUserAccountTransactionDo) FirstOrCreate() (*jyhapp.JyhUserAccountTransaction, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserAccountTransaction), nil
	}
}

func (j jyhUserAccountTransactionDo) FindByPage(offset int, limit int) (result []*jyhapp.JyhUserAccountTransaction, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jyhUserAccountTransactionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jyhUserAccountTransactionDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jyhUserAccountTransactionDo) Delete(models ...*jyhapp.JyhUserAccountTransaction) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jyhUserAccountTransactionDo) withDO(do gen.Dao) *jyhUserAccountTransactionDo {
	j.DO = *do.(*gen.DO)
	return j
}
