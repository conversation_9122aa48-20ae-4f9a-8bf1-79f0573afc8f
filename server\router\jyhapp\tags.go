package jyhapp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type TagsRouter struct {
}

// InitTagsRouter 初始化 tags 路由信息
func (s *TagsRouter) InitTagsRouter(Router *gin.RouterGroup, JyhRouter *gin.RouterGroup) {
	tagsRouter := Router.Group("tags").Use(middleware.OperationRecord())
	tagsRouterWithoutRecord := Router.Group("tags")
	var tagsApi = v1.ApiGroupApp.JyhApiGroup.TagsApi
	{
		// 标签相关
		tagsRouter.POST("create", tagsApi.Create)   // 新建标签
		tagsRouter.DELETE("delete", tagsApi.Delete) // 删除标签
		tagsRouter.PUT("update", tagsApi.Update)    // 更新标签
	}
	{
		tagsRouterWithoutRecord.GET("detail", tagsApi.GetDetail) // 根据ID获取标签
		tagsRouterWithoutRecord.GET("list", tagsApi.GetList)     // 获取标签列表
		tagsRouterWithoutRecord.GET("getTags", tagsApi.GetTags)  // 获取所有标签列表
	}
	{

	}
}
