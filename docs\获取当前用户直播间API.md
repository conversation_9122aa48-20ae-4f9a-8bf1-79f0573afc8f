# 获取当前用户直播间API

## 接口概述

获取当前登录用户的直播房间信息，包含保利威认证信息。

## 接口信息

- **请求方式**：GET
- **请求路径**：`/api/jyhUserLiveRoom/current`
- **需要认证**：是（需要JWT Token）

## 请求示例

```bash
curl -X GET "http://localhost:8888/api/jyhUserLiveRoom/current" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

## 响应格式

### 1. 有开播权限且有直播房间

```json
{
  "code": 0,
  "data": {
    "hasPermission": true,
    "hasRoom": true,
    "room": {
      "id": 1,
      "channel_id": 123456,
      "channel_name": "我的直播间",
      "polyv_user_id": "abcd1234",
      "scene": "topclass",
      "channel_passwd": "password123",
      "seminar_host_password": null,
      "seminar_attendee_password": null,
      "status": "active",
      "authInfo": {
        "data": "base64加密的认证信息",
        "publicKey": "RSA公钥base64格式",
        "timestamp": 1234567890
      }
    }
  },
  "msg": "获取成功"
}
```

### 2. 有开播权限但无直播房间

```json
{
  "code": 0,
  "data": {
    "hasPermission": true,
    "hasRoom": false,
    "message": "您还没有直播房间，请先创建"
  },
  "msg": "暂无直播房间"
}
```

### 3. 无开播权限

```json
{
  "code": 0,
  "data": {
    "hasPermission": false,
    "hasRoom": false,
    "message": "您暂无开播权限，请联系管理员"
  },
  "msg": "用户无开播权限"
}
```

### 4. 错误响应

```json
{
  "code": 7,
  "data": {},
  "msg": "获取直播房间失败: 错误详情"
}
```

## 响应字段说明

### 成功响应 data 字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| hasPermission | boolean | 是否有开播权限 |
| hasRoom | boolean | 是否有直播房间 |
| message | string | 提示信息（无房间或无权限时） |
| room | object | 直播房间信息（有房间时） |

### room 字段详情

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int | 房间ID |
| channel_id | int64 | 保利威频道ID |
| channel_name | string | 频道名称 |
| polyv_user_id | string | 保利威用户ID |
| scene | string | 直播场景 |
| channel_passwd | string | 频道密码 |
| seminar_host_password | string\|null | 研讨会主持人密码 |
| seminar_attendee_password | string\|null | 研讨会参会人密码 |
| status | string | 房间状态 |
| authInfo | object | 加密的认证信息 |

### authInfo 字段详情

| 字段名 | 类型 | 说明 |
|--------|------|------|
| data | string | RSA加密后的认证信息（base64格式） |
| publicKey | string | RSA公钥（base64格式） |
| timestamp | int64 | 加密时间戳 |

## 业务逻辑

1. **权限检查**：首先验证用户是否有开播权限
2. **房间查询**：查询用户是否有可用的直播房间
3. **认证信息生成**：为有房间的用户生成保利威认证信息
4. **使用时间更新**：更新房间的最后使用时间

## 注意事项

1. **认证信息有效期**：authInfo 中的认证信息有30分钟有效期
2. **权限控制**：只有 `can_live_stream` 为 true 的用户才能获取房间信息
3. **房间状态**：只返回状态为 "active" 的房间
4. **自动更新**：每次调用会自动更新房间的最后使用时间

## 前端使用示例

```javascript
// 获取当前用户直播间
async function getCurrentLiveRoom() {
  try {
    const response = await fetch('/api/jyhUserLiveRoom/current', {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + localStorage.getItem('token'),
        'Content-Type': 'application/json'
      }
    });
    
    const result = await response.json();
    
    if (result.code === 0) {
      const { hasPermission, hasRoom, room, message } = result.data;
      
      if (!hasPermission) {
        console.log('无开播权限:', message);
        return null;
      }
      
      if (!hasRoom) {
        console.log('无直播房间:', message);
        return null;
      }
      
      // 有直播房间，可以使用 room 数据
      console.log('直播房间信息:', room);
      console.log('保利威认证信息:', room.authInfo);
      
      return room;
    } else {
      console.error('获取失败:', result.msg);
      return null;
    }
  } catch (error) {
    console.error('请求失败:', error);
    return null;
  }
}

// 使用示例
getCurrentLiveRoom().then(room => {
  if (room) {
    // 可以进入直播界面
    console.log('频道ID:', room.channel_id);
    console.log('频道密码:', room.channel_passwd);
    
    // 使用认证信息初始化保利威SDK
    if (room.authInfo) {
      // 解密认证信息（需要RSA私钥）
      // const authData = decryptAuthInfo(room.authInfo.data);
    }
  } else {
    // 显示无权限或需要创建房间的提示
  }
});
```

## 与其他接口的区别

| 接口 | 用途 | 返回认证信息 |
|------|------|-------------|
| `/api/jyhUserLiveRoom/current` | 获取当前用户直播间 | ✅ |
| `/api/jyhUserLiveRoom/getUserAvailableRoom` | 获取可用房间（管理员） | ❌ |
| `/api/live/getMyLiveRoom` | 旧版获取房间接口 | ❌ |

建议前端使用新的 `/api/jyhUserLiveRoom/current` 接口，它提供了更完整的信息和更好的权限控制。 