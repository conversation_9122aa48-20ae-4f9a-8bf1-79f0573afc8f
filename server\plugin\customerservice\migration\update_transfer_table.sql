-- 更新客服转接表结构
-- 添加新字段以支持增强的转接功能

-- 1. 添加新的状态字段（如果不存在）
ALTER TABLE `sys_service_transfer` 
ADD COLUMN IF NOT EXISTS `priority` tinyint(1) unsigned NOT NULL DEFAULT '2' COMMENT '转接优先级(1:低 2:中 3:高)' AFTER `status`,
ADD COLUMN IF NOT EXISTS `transfer_type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '转接类型(1:主动转接 2:智能转接 3:负载均衡)' AFTER `priority`,
ADD COLUMN IF NOT EXISTS `expire_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '过期时间' AFTER `transfer_type`,
ADD COLUMN IF NOT EXISTS `cancel_reason` varchar(500) DEFAULT NULL COMMENT '取消原因' AFTER `reject_reason`,
ADD COLUMN IF NOT EXISTS `user_tags` text COMMENT '用户标签JSON' AFTER `cancel_reason`,
ADD COLUMN IF NOT EXISTS `from_service_info` text COMMENT '转出客服信息JSON' AFTER `user_tags`,
ADD COLUMN IF NOT EXISTS `to_service_info` text COMMENT '转入客服信息JSON' AFTER `from_service_info`;

-- 2. 修改现有字段长度
ALTER TABLE `sys_service_transfer` 
MODIFY COLUMN `reason` varchar(500) DEFAULT NULL COMMENT '转接原因',
MODIFY COLUMN `reject_reason` varchar(500) DEFAULT NULL COMMENT '拒绝原因';

-- 3. 更新状态字段注释以包含新状态
ALTER TABLE `sys_service_transfer` 
MODIFY COLUMN `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '状态(1:待接收 2:已接收 3:已拒绝 4:已取消 5:已超时)';

-- 4. 添加索引以提高查询性能
CREATE INDEX IF NOT EXISTS `idx_transfer_status_expire` ON `sys_service_transfer` (`status`, `expire_time`);
CREATE INDEX IF NOT EXISTS `idx_transfer_priority` ON `sys_service_transfer` (`priority`);
CREATE INDEX IF NOT EXISTS `idx_transfer_type` ON `sys_service_transfer` (`transfer_type`);
CREATE INDEX IF NOT EXISTS `idx_transfer_from_service` ON `sys_service_transfer` (`from_service_id`, `status`);
CREATE INDEX IF NOT EXISTS `idx_transfer_to_service` ON `sys_service_transfer` (`to_service_id`, `status`);
CREATE INDEX IF NOT EXISTS `idx_transfer_user` ON `sys_service_transfer` (`user_id`);
CREATE INDEX IF NOT EXISTS `idx_transfer_ticket` ON `sys_service_transfer` (`ticket_id`);

-- 5. 创建转接统计视图（可选）
CREATE OR REPLACE VIEW `v_transfer_statistics` AS
SELECT 
    DATE(FROM_UNIXTIME(transfer_time)) as transfer_date,
    from_service_id,
    to_service_id,
    status,
    priority,
    transfer_type,
    COUNT(*) as transfer_count,
    AVG(CASE WHEN accept_time > 0 THEN accept_time - transfer_time ELSE NULL END) as avg_response_time
FROM `sys_service_transfer`
WHERE transfer_time > 0
GROUP BY transfer_date, from_service_id, to_service_id, status, priority, transfer_type;

-- 6. 创建过期转接清理存储过程
DELIMITER $$

CREATE PROCEDURE IF NOT EXISTS `CleanupExpiredTransfers`()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE transfer_id INT;
    DECLARE cur CURSOR FOR 
        SELECT id FROM sys_service_transfer 
        WHERE status = 1 AND expire_time > 0 AND expire_time < UNIX_TIMESTAMP();
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    START TRANSACTION;
    
    OPEN cur;
    read_loop: LOOP
        FETCH cur INTO transfer_id;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- 更新为过期状态
        UPDATE sys_service_transfer 
        SET status = 5 
        WHERE id = transfer_id;
        
    END LOOP;
    CLOSE cur;
    
    COMMIT;
    
    -- 返回处理的记录数
    SELECT ROW_COUNT() as expired_count;
END$$

DELIMITER ;

-- 7. 创建转接性能监控表（可选）
CREATE TABLE IF NOT EXISTS `sys_transfer_metrics` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `date` date NOT NULL COMMENT '统计日期',
  `service_id` bigint(20) NOT NULL COMMENT '客服ID',
  `total_transfers` int(11) NOT NULL DEFAULT '0' COMMENT '总转接数',
  `accepted_transfers` int(11) NOT NULL DEFAULT '0' COMMENT '接受转接数',
  `rejected_transfers` int(11) NOT NULL DEFAULT '0' COMMENT '拒绝转接数',
  `canceled_transfers` int(11) NOT NULL DEFAULT '0' COMMENT '取消转接数',
  `expired_transfers` int(11) NOT NULL DEFAULT '0' COMMENT '过期转接数',
  `avg_response_time` decimal(10,2) DEFAULT NULL COMMENT '平均响应时间(秒)',
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_metrics_date_service` (`date`, `service_id`),
  KEY `idx_metrics_date` (`date`),
  KEY `idx_metrics_service` (`service_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='转接性能指标表';

-- 8. 示例数据（可选，用于测试）
-- INSERT INTO `sys_service_transfer` 
-- (`ticket_id`, `from_service_id`, `to_service_id`, `user_id`, `reason`, `status`, `transfer_time`, `priority`, `transfer_type`, `expire_time`) 
-- VALUES 
-- (1, 1, 2, 1001, '技术问题需要专业客服处理', 1, UNIX_TIMESTAMP(), 3, 1, UNIX_TIMESTAMP() + 300),
-- (2, 2, 3, 1002, 'VIP客户需要高级客服', 2, UNIX_TIMESTAMP() - 600, 3, 1, UNIX_TIMESTAMP() - 300),
-- (3, 1, 3, 1003, '负载均衡转接', 1, UNIX_TIMESTAMP(), 2, 3, UNIX_TIMESTAMP() + 300);

-- 9. 权限设置（根据实际需求调整）
-- GRANT SELECT, INSERT, UPDATE ON sys_service_transfer TO 'customerservice_user'@'%';
-- GRANT EXECUTE ON PROCEDURE CleanupExpiredTransfers TO 'customerservice_admin'@'%';

-- 完成提示
SELECT 'Transfer table structure updated successfully!' as message;
