package jyhapp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	jyhReq "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type RecommendPositionApi struct{}

// CreateRecommendPosition 创建推荐位
// @Tags RecommendPosition
// @Summary 创建推荐位
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body jyhReq.RecommendPositionCreate true "创建推荐位"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /recommend/create [post]
func (api *RecommendPositionApi) CreateRecommendPosition(c *gin.Context) {
	var req jyhReq.RecommendPositionCreate
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := recommendPositionService.CreateRecommendPosition(req); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败: "+err.Error(), c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// UpdateRecommendPosition 更新推荐位
// @Tags RecommendPosition
// @Summary 更新推荐位
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body jyhReq.RecommendPositionUpdate true "更新推荐位"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /recommend/update [put]
func (api *RecommendPositionApi) UpdateRecommendPosition(c *gin.Context) {
	var req jyhReq.RecommendPositionUpdate
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := recommendPositionService.UpdateRecommendPosition(&req); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败: "+err.Error(), c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// DeleteRecommendPosition 删除推荐位
// @Tags RecommendPosition
// @Summary 删除推荐位
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.GetById true "删除推荐位"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /recommend/delete [delete]
func (api *RecommendPositionApi) DeleteRecommendPosition(c *gin.Context) {
	var req request.GetById
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := recommendPositionService.DeleteRecommendPosition(uint(req.ID)); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败: "+err.Error(), c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// GetRecommendPositionDetail 获取推荐位详情
// @Tags RecommendPosition
// @Summary 获取推荐位详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.GetById true "推荐位ID"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /recommend/detail [get]
func (api *RecommendPositionApi) GetRecommendPositionDetail(c *gin.Context) {
	var req request.GetById
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if position, err := recommendPositionService.GetRecommendPositionDetail(uint(req.ID)); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(gin.H{"position": position}, c)
	}
}

// GetRecommendPositionList 分页获取推荐位列表
// @Tags RecommendPosition
// @Summary 分页获取推荐位列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query jyhReq.RecommendPositionSearch true "分页获取推荐位列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /recommend/list [get]
func (api *RecommendPositionApi) GetRecommendPositionList(c *gin.Context) {
	var req jyhReq.RecommendPositionSearch
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	list, total, err := recommendPositionService.GetRecommendPositionList(&req)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}

// CreateRecommendItem 创建推荐内容项
// @Tags RecommendPosition
// @Summary 创建推荐内容项
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body jyhReq.RecommendItemCreate true "创建推荐内容项"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /recommend/item/create [post]
func (api *RecommendPositionApi) CreateRecommendItem(c *gin.Context) {
	var req jyhReq.RecommendItemCreate
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := recommendPositionService.CreateRecommendItem(req); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败: "+err.Error(), c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// UpdateRecommendItem 更新推荐内容项
// @Tags RecommendPosition
// @Summary 更新推荐内容项
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body jyhReq.RecommendItemUpdate true "更新推荐内容项"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /recommend/item/update [put]
func (api *RecommendPositionApi) UpdateRecommendItem(c *gin.Context) {
	var req jyhReq.RecommendItemUpdate
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := recommendPositionService.UpdateRecommendItem(&req); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败: "+err.Error(), c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// DeleteRecommendItem 删除推荐内容项
// @Tags RecommendPosition
// @Summary 删除推荐内容项
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.GetById true "删除推荐内容项"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /recommend/item/delete [delete]
func (api *RecommendPositionApi) DeleteRecommendItem(c *gin.Context) {
	var req request.GetById
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := recommendPositionService.DeleteRecommendItem(uint(req.ID)); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败: "+err.Error(), c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// GetRecommendItemDetail 获取推荐内容项详情
// @Tags RecommendPosition
// @Summary 获取推荐内容项详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.GetById true "推荐内容项ID"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /recommend/item/detail [get]
func (api *RecommendPositionApi) GetRecommendItemDetail(c *gin.Context) {
	var req request.GetById
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if item, err := recommendPositionService.GetRecommendItemDetail(uint(req.ID)); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(gin.H{"item": item}, c)
	}
}

// GetRecommendItemList 分页获取推荐内容项列表
// @Tags RecommendPosition
// @Summary 分页获取推荐内容项列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query jyhReq.RecommendItemSearch true "分页获取推荐内容项列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /recommend/item/list [get]
func (api *RecommendPositionApi) GetRecommendItemList(c *gin.Context) {
	var req jyhReq.RecommendItemSearch
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	list, total, err := recommendPositionService.GetRecommendItemList(&req)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}
