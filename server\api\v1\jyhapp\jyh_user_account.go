package jyhapp

import (
	jyhResp "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/response"
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	jyhReq "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type JyhUserAccountApi struct{}

// ================================ 用户端接口 ================================

// GetUserAccountSummary 用户端余额汇总接口
// @Tags JyhUserAccount
// @Summary 获取用户余额汇总
// @Description 用户查看自己的余额总览，包括各项余额、统计信息等
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=jyhResp.UserAccountDetail,msg=string} "获取成功"
// @Failure 401 {object} response.Response{msg=string} "未授权"
// @Failure 500 {object} response.Response{msg=string} "服务器内部错误"
// @Router /jyh/account/summary [get]
func (api *JyhUserAccountApi) GetUserAccountSummary(c *gin.Context) {
	// 获取当前用户ID
	userID := utils.GetUserID(c)
	if userID == 0 {
		response.FailWithMessage("获取用户信息失败", c)
		return
	}
	summary := &jyhResp.UserAccountDetail{}
	summary, err := jyhUserAccountService.GetUserAccountSummary(userID)
	if err != nil {
		global.GVA_LOG.Error("获取用户余额汇总失败", zap.Error(err))
		response.FailWithMessage("获取余额汇总失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(summary, "获取余额汇总成功", c)
}

// GetUserTransactionList 用户端余额交易明细接口
// @Tags JyhUserAccount
// @Summary 获取用户交易明细列表
// @Description 用户查看自己的交易记录，支持分页和多种筛选条件
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param page query int false "页码" default(1)
// @Param pageSize query int false "每页大小" default(20)
// @Success 200 {object} response.Response{data=jyhResp.TransactionListResp,msg=string} "获取成功"
// @Router /jyh/account/transactions [get]
func (api *JyhUserAccountApi) GetUserTransactionList(c *gin.Context) {
	// 获取当前用户ID
	userID := utils.GetUserID(c)
	if userID == 0 {
		response.FailWithMessage("获取用户信息失败", c)
		return
	}

	var req jyhReq.TransactionListReq
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	list, err := jyhUserAccountService.GetUserTransactionList(userID, &req)
	if err != nil {
		global.GVA_LOG.Error("获取用户交易明细失败", zap.Error(err))
		response.FailWithMessage("获取交易明细失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(list, "获取交易明细成功", c)
}

// ProcessWithdrawal 提现
// @Tags JyhUserAccount
// @Summary 管理端处理提现
// @Description 管理员为用户处理提现申请
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body jyhReq.ProcessWithdrawalReq true "处理提现请求"
// @Success 200 {object} response.Response{data=jyhResp.ProcessWithdrawalResp,msg=string} "处理成功"
// @Router /jyh/account/withdrawal [post]
func (api *JyhUserAccountApi) ProcessWithdrawal(c *gin.Context) {
	var req jyhReq.ProcessWithdrawalReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	userID := utils.GetUserID(c)
	if userID == 0 {
		response.FailWithMessage("获取用户信息失败", c)
		return
	}
	err := jyhUserAccountService.ProcessWithdrawal(userID, req.Amount, req.RelatedBusinessID, req.Description)
	if err != nil {
		global.GVA_LOG.Error("处理提现失败", zap.Error(err))
		response.FailWithMessage("处理提现失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("处理提现成功", c)
}

// ================================ 管理端接口 ================================

// GetAdminUserAccountDetail 管理端查看用户余额明细
// @Tags JyhUserAccount
// @Summary 管理端查看用户余额明细
// @Description 管理员查看指定用户的详细余额信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "用户ID"
// @Success 200 {object} response.Response{data=jyhResp.UserAccountDetail,msg=string} "获取成功"
// @Failure 400 {object} response.Response{msg=string} "请求参数错误"
// @Failure 404 {object} response.Response{msg=string} "用户不存在"
// @Failure 500 {object} response.Response{msg=string} "服务器内部错误"
// @Router /admin/user/accounts/{id} [get]
func (api *JyhUserAccountApi) GetAdminUserAccountDetail(c *gin.Context) {
	idStr := c.Param("id")
	userID, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("用户ID格式错误", c)
		return
	}

	detail, err := jyhUserAccountService.GetUserAccountDetail(uint(userID))
	if err != nil {
		global.GVA_LOG.Error("获取用户余额明细失败", zap.Error(err))
		response.FailWithMessage("获取用户余额明细失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(detail, "获取用户余额明细成功", c)
}

// GetAdminUserTransactionList 管理端查看用户交易记录
// @Tags JyhUserAccount
// @Summary 管理端查看用户交易记录
// @Description 管理员查看指定用户的交易记录列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "用户ID"
// @Param page query int false "页码" default(1)
// @Param pageSize query int false "每页大小" default(20)
// @Param transaction_type query string false "交易类型"
// @Param source_type query string false "资金来源"
// @Param transaction_status query string false "交易状态"
// @Param start_time query string false "开始时间"
// @Param end_time query string false "结束时间"
// @Success 200 {object} response.Response{data=jyhResp.TransactionListResp,msg=string} "获取成功"
// @Router /admin/user/accounts/transactions/{id} [get]
func (api *JyhUserAccountApi) GetAdminUserTransactionList(c *gin.Context) {
	idStr := c.Param("id")
	userID, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("用户ID格式错误", c)
		return
	}

	var req jyhReq.TransactionListReq
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	list, err := jyhUserAccountService.GetUserTransactionList(uint(userID), &req)
	if err != nil {
		global.GVA_LOG.Error("获取用户交易记录失败", zap.Error(err))
		response.FailWithMessage("获取用户交易记录失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(list, "获取用户交易记录成功", c)
}

// AdjustUserBalance 管理端调整用户余额
// @Tags JyhUserAccount
// @Summary 管理端调整用户余额
// @Description 管理员手动调整用户余额（增加或减少）
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body jyhReq.AdjustBalanceReq true "调整余额请求"
// @Success 200 {object} response.Response{msg=string} "调整成功"
// @Router /admin/user/accounts/adjust [post]
func (api *JyhUserAccountApi) AdjustUserBalance(c *gin.Context) {
	var req jyhReq.AdjustBalanceReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 根据金额正负判断是增加还是减少
	if req.Amount.IsPositive() {
		// 正数 - 增加余额
		err := jyhUserAccountService.AddCommissionIncome(req.UserID, req.Amount, req.SourceType, nil, req.Description)
		if err != nil {
			global.GVA_LOG.Error("调整用户余额失败", zap.Error(err))
			response.FailWithMessage("调整用户余额失败: "+err.Error(), c)
			return
		}
	} else {
		// 负数 - 减少余额（通过提现方式实现）
		positiveAmount := req.Amount.Abs()
		err := jyhUserAccountService.ProcessWithdrawal(req.UserID, positiveAmount, nil, req.Description)
		if err != nil {
			global.GVA_LOG.Error("调整用户余额失败", zap.Error(err))
			response.FailWithMessage("调整用户余额失败: "+err.Error(), c)
			return
		}
	}

	response.OkWithMessage("调整用户余额成功", c)
}

// GetAccountStatistics 管理端获取账户统计信息
// @Tags JyhUserAccount
// @Summary 管理端获取账户统计信息
// @Description 管理员查看整体账户统计数据
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=jyhResp.UserAccountStatistics,msg=string} "获取成功"
// @Failure 500 {object} response.Response{msg=string} "服务器内部错误"
// @Router /admin/user/accounts/statistics [get]
func (api *JyhUserAccountApi) GetAccountStatistics(c *gin.Context) {
	statistics, err := jyhUserAccountService.GetAccountStatistics()
	if err != nil {
		global.GVA_LOG.Error("获取账户统计信息失败", zap.Error(err))
		response.FailWithMessage("获取账户统计信息失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(statistics, "获取账户统计信息成功", c)
}
