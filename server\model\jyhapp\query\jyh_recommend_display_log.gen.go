// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
)

func newJyhRecommendDisplayLog(db *gorm.DB, opts ...gen.DOOption) jyhRecommendDisplayLog {
	_jyhRecommendDisplayLog := jyhRecommendDisplayLog{}

	_jyhRecommendDisplayLog.jyhRecommendDisplayLogDo.UseDB(db, opts...)
	_jyhRecommendDisplayLog.jyhRecommendDisplayLogDo.UseModel(&jyhapp.JyhRecommendDisplayLog{})

	tableName := _jyhRecommendDisplayLog.jyhRecommendDisplayLogDo.TableName()
	_jyhRecommendDisplayLog.ALL = field.NewAsterisk(tableName)
	_jyhRecommendDisplayLog.ID = field.NewUint(tableName, "id")
	_jyhRecommendDisplayLog.ItemID = field.NewUint(tableName, "item_id")
	_jyhRecommendDisplayLog.PositionID = field.NewUint(tableName, "position_id")
	_jyhRecommendDisplayLog.DisplayTime = field.NewTime(tableName, "display_time")
	_jyhRecommendDisplayLog.ClickTime = field.NewTime(tableName, "click_time")
	_jyhRecommendDisplayLog.UserAgent = field.NewString(tableName, "user_agent")
	_jyhRecommendDisplayLog.IPAddress = field.NewString(tableName, "ip_address")
	_jyhRecommendDisplayLog.Referer = field.NewString(tableName, "referer")
	_jyhRecommendDisplayLog.DeviceType = field.NewString(tableName, "device_type")
	_jyhRecommendDisplayLog.Item = jyhRecommendDisplayLogBelongsToItem{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Item", "jyhapp.JyhRecommendItem"),
		Position: struct {
			field.RelationField
			Items struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Item.Position", "jyhapp.JyhRecommendPosition"),
			Items: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Item.Position.Items", "jyhapp.JyhRecommendItem"),
			},
		},
	}

	_jyhRecommendDisplayLog.Position = jyhRecommendDisplayLogBelongsToPosition{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Position", "jyhapp.JyhRecommendPosition"),
	}

	_jyhRecommendDisplayLog.fillFieldMap()

	return _jyhRecommendDisplayLog
}

type jyhRecommendDisplayLog struct {
	jyhRecommendDisplayLogDo

	ALL         field.Asterisk
	ID          field.Uint
	ItemID      field.Uint
	PositionID  field.Uint
	DisplayTime field.Time
	ClickTime   field.Time
	UserAgent   field.String
	IPAddress   field.String
	Referer     field.String
	DeviceType  field.String
	Item        jyhRecommendDisplayLogBelongsToItem

	Position jyhRecommendDisplayLogBelongsToPosition

	fieldMap map[string]field.Expr
}

func (j jyhRecommendDisplayLog) Table(newTableName string) *jyhRecommendDisplayLog {
	j.jyhRecommendDisplayLogDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j jyhRecommendDisplayLog) As(alias string) *jyhRecommendDisplayLog {
	j.jyhRecommendDisplayLogDo.DO = *(j.jyhRecommendDisplayLogDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *jyhRecommendDisplayLog) updateTableName(table string) *jyhRecommendDisplayLog {
	j.ALL = field.NewAsterisk(table)
	j.ID = field.NewUint(table, "id")
	j.ItemID = field.NewUint(table, "item_id")
	j.PositionID = field.NewUint(table, "position_id")
	j.DisplayTime = field.NewTime(table, "display_time")
	j.ClickTime = field.NewTime(table, "click_time")
	j.UserAgent = field.NewString(table, "user_agent")
	j.IPAddress = field.NewString(table, "ip_address")
	j.Referer = field.NewString(table, "referer")
	j.DeviceType = field.NewString(table, "device_type")

	j.fillFieldMap()

	return j
}

func (j *jyhRecommendDisplayLog) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *jyhRecommendDisplayLog) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 11)
	j.fieldMap["id"] = j.ID
	j.fieldMap["item_id"] = j.ItemID
	j.fieldMap["position_id"] = j.PositionID
	j.fieldMap["display_time"] = j.DisplayTime
	j.fieldMap["click_time"] = j.ClickTime
	j.fieldMap["user_agent"] = j.UserAgent
	j.fieldMap["ip_address"] = j.IPAddress
	j.fieldMap["referer"] = j.Referer
	j.fieldMap["device_type"] = j.DeviceType

}

func (j jyhRecommendDisplayLog) clone(db *gorm.DB) jyhRecommendDisplayLog {
	j.jyhRecommendDisplayLogDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j jyhRecommendDisplayLog) replaceDB(db *gorm.DB) jyhRecommendDisplayLog {
	j.jyhRecommendDisplayLogDo.ReplaceDB(db)
	return j
}

type jyhRecommendDisplayLogBelongsToItem struct {
	db *gorm.DB

	field.RelationField

	Position struct {
		field.RelationField
		Items struct {
			field.RelationField
		}
	}
}

func (a jyhRecommendDisplayLogBelongsToItem) Where(conds ...field.Expr) *jyhRecommendDisplayLogBelongsToItem {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a jyhRecommendDisplayLogBelongsToItem) WithContext(ctx context.Context) *jyhRecommendDisplayLogBelongsToItem {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a jyhRecommendDisplayLogBelongsToItem) Session(session *gorm.Session) *jyhRecommendDisplayLogBelongsToItem {
	a.db = a.db.Session(session)
	return &a
}

func (a jyhRecommendDisplayLogBelongsToItem) Model(m *jyhapp.JyhRecommendDisplayLog) *jyhRecommendDisplayLogBelongsToItemTx {
	return &jyhRecommendDisplayLogBelongsToItemTx{a.db.Model(m).Association(a.Name())}
}

type jyhRecommendDisplayLogBelongsToItemTx struct{ tx *gorm.Association }

func (a jyhRecommendDisplayLogBelongsToItemTx) Find() (result *jyhapp.JyhRecommendItem, err error) {
	return result, a.tx.Find(&result)
}

func (a jyhRecommendDisplayLogBelongsToItemTx) Append(values ...*jyhapp.JyhRecommendItem) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a jyhRecommendDisplayLogBelongsToItemTx) Replace(values ...*jyhapp.JyhRecommendItem) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a jyhRecommendDisplayLogBelongsToItemTx) Delete(values ...*jyhapp.JyhRecommendItem) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a jyhRecommendDisplayLogBelongsToItemTx) Clear() error {
	return a.tx.Clear()
}

func (a jyhRecommendDisplayLogBelongsToItemTx) Count() int64 {
	return a.tx.Count()
}

type jyhRecommendDisplayLogBelongsToPosition struct {
	db *gorm.DB

	field.RelationField
}

func (a jyhRecommendDisplayLogBelongsToPosition) Where(conds ...field.Expr) *jyhRecommendDisplayLogBelongsToPosition {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a jyhRecommendDisplayLogBelongsToPosition) WithContext(ctx context.Context) *jyhRecommendDisplayLogBelongsToPosition {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a jyhRecommendDisplayLogBelongsToPosition) Session(session *gorm.Session) *jyhRecommendDisplayLogBelongsToPosition {
	a.db = a.db.Session(session)
	return &a
}

func (a jyhRecommendDisplayLogBelongsToPosition) Model(m *jyhapp.JyhRecommendDisplayLog) *jyhRecommendDisplayLogBelongsToPositionTx {
	return &jyhRecommendDisplayLogBelongsToPositionTx{a.db.Model(m).Association(a.Name())}
}

type jyhRecommendDisplayLogBelongsToPositionTx struct{ tx *gorm.Association }

func (a jyhRecommendDisplayLogBelongsToPositionTx) Find() (result *jyhapp.JyhRecommendPosition, err error) {
	return result, a.tx.Find(&result)
}

func (a jyhRecommendDisplayLogBelongsToPositionTx) Append(values ...*jyhapp.JyhRecommendPosition) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a jyhRecommendDisplayLogBelongsToPositionTx) Replace(values ...*jyhapp.JyhRecommendPosition) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a jyhRecommendDisplayLogBelongsToPositionTx) Delete(values ...*jyhapp.JyhRecommendPosition) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a jyhRecommendDisplayLogBelongsToPositionTx) Clear() error {
	return a.tx.Clear()
}

func (a jyhRecommendDisplayLogBelongsToPositionTx) Count() int64 {
	return a.tx.Count()
}

type jyhRecommendDisplayLogDo struct{ gen.DO }

func (j jyhRecommendDisplayLogDo) Debug() *jyhRecommendDisplayLogDo {
	return j.withDO(j.DO.Debug())
}

func (j jyhRecommendDisplayLogDo) WithContext(ctx context.Context) *jyhRecommendDisplayLogDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jyhRecommendDisplayLogDo) ReadDB() *jyhRecommendDisplayLogDo {
	return j.Clauses(dbresolver.Read)
}

func (j jyhRecommendDisplayLogDo) WriteDB() *jyhRecommendDisplayLogDo {
	return j.Clauses(dbresolver.Write)
}

func (j jyhRecommendDisplayLogDo) Session(config *gorm.Session) *jyhRecommendDisplayLogDo {
	return j.withDO(j.DO.Session(config))
}

func (j jyhRecommendDisplayLogDo) Clauses(conds ...clause.Expression) *jyhRecommendDisplayLogDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jyhRecommendDisplayLogDo) Returning(value interface{}, columns ...string) *jyhRecommendDisplayLogDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jyhRecommendDisplayLogDo) Not(conds ...gen.Condition) *jyhRecommendDisplayLogDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jyhRecommendDisplayLogDo) Or(conds ...gen.Condition) *jyhRecommendDisplayLogDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jyhRecommendDisplayLogDo) Select(conds ...field.Expr) *jyhRecommendDisplayLogDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jyhRecommendDisplayLogDo) Where(conds ...gen.Condition) *jyhRecommendDisplayLogDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jyhRecommendDisplayLogDo) Order(conds ...field.Expr) *jyhRecommendDisplayLogDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jyhRecommendDisplayLogDo) Distinct(cols ...field.Expr) *jyhRecommendDisplayLogDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jyhRecommendDisplayLogDo) Omit(cols ...field.Expr) *jyhRecommendDisplayLogDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jyhRecommendDisplayLogDo) Join(table schema.Tabler, on ...field.Expr) *jyhRecommendDisplayLogDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jyhRecommendDisplayLogDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jyhRecommendDisplayLogDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jyhRecommendDisplayLogDo) RightJoin(table schema.Tabler, on ...field.Expr) *jyhRecommendDisplayLogDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jyhRecommendDisplayLogDo) Group(cols ...field.Expr) *jyhRecommendDisplayLogDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jyhRecommendDisplayLogDo) Having(conds ...gen.Condition) *jyhRecommendDisplayLogDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jyhRecommendDisplayLogDo) Limit(limit int) *jyhRecommendDisplayLogDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jyhRecommendDisplayLogDo) Offset(offset int) *jyhRecommendDisplayLogDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jyhRecommendDisplayLogDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jyhRecommendDisplayLogDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jyhRecommendDisplayLogDo) Unscoped() *jyhRecommendDisplayLogDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jyhRecommendDisplayLogDo) Create(values ...*jyhapp.JyhRecommendDisplayLog) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jyhRecommendDisplayLogDo) CreateInBatches(values []*jyhapp.JyhRecommendDisplayLog, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jyhRecommendDisplayLogDo) Save(values ...*jyhapp.JyhRecommendDisplayLog) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jyhRecommendDisplayLogDo) First() (*jyhapp.JyhRecommendDisplayLog, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhRecommendDisplayLog), nil
	}
}

func (j jyhRecommendDisplayLogDo) Take() (*jyhapp.JyhRecommendDisplayLog, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhRecommendDisplayLog), nil
	}
}

func (j jyhRecommendDisplayLogDo) Last() (*jyhapp.JyhRecommendDisplayLog, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhRecommendDisplayLog), nil
	}
}

func (j jyhRecommendDisplayLogDo) Find() ([]*jyhapp.JyhRecommendDisplayLog, error) {
	result, err := j.DO.Find()
	return result.([]*jyhapp.JyhRecommendDisplayLog), err
}

func (j jyhRecommendDisplayLogDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*jyhapp.JyhRecommendDisplayLog, err error) {
	buf := make([]*jyhapp.JyhRecommendDisplayLog, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jyhRecommendDisplayLogDo) FindInBatches(result *[]*jyhapp.JyhRecommendDisplayLog, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jyhRecommendDisplayLogDo) Attrs(attrs ...field.AssignExpr) *jyhRecommendDisplayLogDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jyhRecommendDisplayLogDo) Assign(attrs ...field.AssignExpr) *jyhRecommendDisplayLogDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jyhRecommendDisplayLogDo) Joins(fields ...field.RelationField) *jyhRecommendDisplayLogDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jyhRecommendDisplayLogDo) Preload(fields ...field.RelationField) *jyhRecommendDisplayLogDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jyhRecommendDisplayLogDo) FirstOrInit() (*jyhapp.JyhRecommendDisplayLog, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhRecommendDisplayLog), nil
	}
}

func (j jyhRecommendDisplayLogDo) FirstOrCreate() (*jyhapp.JyhRecommendDisplayLog, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhRecommendDisplayLog), nil
	}
}

func (j jyhRecommendDisplayLogDo) FindByPage(offset int, limit int) (result []*jyhapp.JyhRecommendDisplayLog, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jyhRecommendDisplayLogDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jyhRecommendDisplayLogDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jyhRecommendDisplayLogDo) Delete(models ...*jyhapp.JyhRecommendDisplayLog) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jyhRecommendDisplayLogDo) withDO(do gen.Dao) *jyhRecommendDisplayLogDo {
	j.DO = *do.(*gen.DO)
	return j
}
