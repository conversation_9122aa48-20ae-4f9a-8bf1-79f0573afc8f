# 邀请码服务重构说明

## 重构目标

将邀请码生成逻辑从用户服务(`JyhUserService`)迁移到邀请码服务(`JyhInviteCodeService`)，实现更清晰的服务职责分离。

## 重构内容

### 1. 迁移的方法

从 `server/service/jyhapp/jyhuser.go` 迁移到 `server/service/jyhapp/jyh_invite_code.go`：

#### 主要方法：
- `processInviteCodeBenefit()` → `ProcessInviteCodeBenefit()`
- `generateUniqueInviteCodeInTx()` → `generateUniqueInviteCodeInTx()`
- `generateRandomInviteCode()` → `generateRandomInviteCode()`

#### 全局封装方法：
```go
// 新增全局封装函数
func ProcessInviteCodeBenefit(tx *query.Query, userID uint, levelID uint, benefitEndAt time.Time) error {
	return gSrv.JyhInviteCodeService.ProcessInviteCodeBenefit(tx, userID, levelID, benefitEndAt)
}
```

### 2. 调用方式更新

在 `jyhuser.go` 的 `assignUserLevel()` 方法中：

**重构前**：
```go
// 检查并处理邀请码生成权益
err = s.processInviteCodeBenefit(tx, userID, levelID, endAt)
```

**重构后**：
```go
// 检查并处理邀请码生成权益
err = ProcessInviteCodeBenefit(tx, userID, levelID, endAt)
```

### 3. 服务职责划分

#### JyhUserService（用户服务）
**负责**：
- 用户注册、登录、信息管理
- 用户等级分配和管理
- 用户权益快照创建
- 用户业务逻辑协调

**不再负责**：
- ❌ 邀请码生成算法
- ❌ 邀请码唯一性检查
- ❌ 邀请码数据库操作

#### JyhInviteCodeService（邀请码服务）
**负责**：
- 邀请码生成和管理
- 邀请码验证和使用
- 邀请码权益处理
- 邀请码相关的所有业务逻辑

**新增职责**：
- ✅ 权益邀请码自动生成
- ✅ 邀请码生成算法封装
- ✅ 邀请码业务逻辑集中管理

## 架构优势

### 1. 职责单一原则
- 每个服务只负责自己领域的业务逻辑
- 邀请码相关的所有操作都在邀请码服务中
- 用户服务专注于用户相关业务

### 2. 代码复用性
- 邀请码生成算法可以被其他场景复用
- 统一的邀请码管理逻辑
- 减少代码重复

### 3. 维护性提升
- 邀请码相关的bug只需要在一个服务中修复
- 邀请码功能的扩展更加容易
- 服务边界清晰，降低维护复杂度

### 4. 测试便利性
- 可以独立测试邀请码生成逻辑
- 用户服务和邀请码服务可以分别进行单元测试
- 更容易模拟和替换依赖

## 兼容性保证

### 1. API接口不变
- 用户注册等外部接口保持不变
- 调用方式对外部完全透明

### 2. 数据结构不变
- 数据库表结构无变化
- 数据模型定义保持一致

### 3. 业务逻辑不变
- 邀请码生成的业务规则完全一致
- 权益处理逻辑保持原有行为

## 使用方式

### 1. 在用户服务中调用
```go
// 在用户等级分配时自动处理邀请码权益
err = ProcessInviteCodeBenefit(tx, userID, levelID, endAt)
if err != nil {
    global.GVA_LOG.Error("处理邀请码权益失败", zap.Error(err))
    // 不中断流程，只记录错误
}
```

### 2. 直接调用邀请码服务
```go
// 直接使用邀请码服务实例
inviteCodeService := &JyhInviteCodeService{}
err := inviteCodeService.ProcessInviteCodeBenefit(tx, userID, levelID, endAt)
```

### 3. 通过服务组调用
```go
// 通过全局服务组调用
err := gSrv.JyhInviteCodeService.ProcessInviteCodeBenefit(tx, userID, levelID, endAt)
```

## 扩展性

### 1. 邀请码类型扩展
现在可以在邀请码服务中方便地添加新的邀请码类型和生成策略。

### 2. 权益规则扩展
可以在邀请码服务中添加更复杂的权益处理规则，如：
- 分层权益（不同等级生成不同数量）
- 时间限制权益（特定时间段内有效）
- 条件权益（满足特定条件才生成）

### 3. 监控和统计
可以在邀请码服务中统一添加：
- 邀请码生成统计
- 使用率分析
- 性能监控

## 注意事项

### 1. 事务一致性
- 所有邀请码操作都在同一事务中执行
- 确保数据一致性

### 2. 错误处理
- 邀请码生成失败不影响用户等级分配主流程
- 详细的错误日志记录

### 3. 性能考虑
- 批量生成邀请码时的性能优化
- 唯一性检查的效率

这次重构提升了代码的组织结构和可维护性，同时保持了功能的完整性和兼容性。 