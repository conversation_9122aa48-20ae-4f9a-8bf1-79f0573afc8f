package jyhapp

import (
	"errors"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/query"
	jyhReq "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/request"
	jyhRes "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/response"
	"time"
)

type JyhCourseService struct{}

// Create 创建课程
func (s *JyhCourseService) Create(req jyhReq.CourseCreateReq) (err error) {
	dbCourse := query.JyhCourse

	course := &jyhapp.JyhCourse{
		Title:      req.Title,
		CourseDate: ParseDatetime(req.CourseDate),
		ViewCount:  req.ViewCount,
		ImageUrl:   req.ImageUrl,
		VideoUrl:   req.VideoUrl,
		Detail:     req.Detail,
		Duration:   req.Duration,
		Teacher:    req.Teacher,
		Status:     req.Status,
		CategoryID: req.CategoryId,
	}

	return dbCourse.Create(course)
}

// Update 更新课程
func (s *JyhCourseService) Update(req *jyhReq.CourseUpdateReq) (err error) {
	dbCourse := query.JyhCourse

	// 检查课程是否存在
	_, err = dbCourse.Where(dbCourse.ID.Eq(req.ID)).First()
	if err != nil {
		return errors.New("课程不存在")
	}

	// 更新课程信息
	updates := jyhapp.JyhCourse{
		Title:      req.Title,
		CourseDate: ParseDatetime(req.CourseDate), // 需要转换为时间类型
		ViewCount:  req.ViewCount,
		ImageUrl:   req.ImageUrl,
		VideoUrl:   req.VideoUrl,
		Detail:     req.Detail,
		Duration:   req.Duration,
		Teacher:    req.Teacher,
		Status:     req.Status,
		CategoryID: req.CategoryId,
	}

	_, err = dbCourse.Where(dbCourse.ID.Eq(req.ID)).Updates(updates)
	return err
}

// Delete 删除课程
func (s *JyhCourseService) Delete(id uint) (err error) {
	dbCourse := query.JyhCourse

	_, err = dbCourse.Where(dbCourse.ID.Eq(id)).Delete()
	return err
}

// GetDetail 获取课程详情
func (s *JyhCourseService) GetDetail(id uint) (course *jyhRes.CourseDetailRes, err error) {
	var (
		dbCourse         = query.JyhCourse
		dbCourseCategory = query.JyhCourseCategory
	)
	err = dbCourse.
		Select(dbCourse.ALL, dbCourseCategory.Name.As("category_name")).
		LeftJoin(dbCourseCategory, dbCourse.CategoryID.EqCol(dbCourseCategory.ID)).
		Where(dbCourse.ID.Eq(id)).Scan(&course)
	if err != nil {
		return nil, errors.New("课程不存在")
	}
	return
}

// GetList 获取课程列表
func (s *JyhCourseService) GetList(req *jyhReq.CourseSearchReq) (list []*jyhapp.JyhCourse, total int64, err error) {
	dbCourse := query.JyhCourse
	dbCourseCategory := query.JyhCourseCategory

	queryBuilder := dbCourse.WithContext(global.GVA_DB.Statement.Context).
		LeftJoin(dbCourseCategory, dbCourse.CategoryID.EqCol(dbCourseCategory.ID))

	// 构建查询条件
	if req.Title != "" {
		queryBuilder = queryBuilder.Where(dbCourse.Title.Like("%" + req.Title + "%"))
	}
	if req.CourseDate != "" {
		courseDate, err := time.ParseInLocation("2006-01-02", req.CourseDate, time.Local)
		if err != nil {
			return nil, 0, errors.New("课程日期格式错误，必须为YYYY-MM-DD")
		}
		queryBuilder = queryBuilder.Where(dbCourse.CourseDate.Between(courseDate, courseDate.AddDate(0, 0, 1)))
	}
	if req.CategoryId > 0 {
		queryBuilder = queryBuilder.Where(dbCourse.CategoryID.Eq(req.CategoryId))
	}

	// 获取总数
	total, err = queryBuilder.Count()
	if err != nil {
		return
	}

	if req.Page == 0 && req.PageSize == -1 {
		list, err = queryBuilder.
			Select(dbCourse.ALL, dbCourseCategory.Name.As("category_name")).
			Order(dbCourse.ID).Find()
		return
	}

	// 分页查询
	queryBuilder = queryBuilder.
		Select(dbCourse.ALL, dbCourseCategory.Name.As("category_name")).
		Limit(req.PageSize).Offset((req.Page - 1) * req.PageSize)
	list, err = queryBuilder.Order(dbCourse.ID).Find()
	return
}

// IncrementViewCount 增加课程观看人数
func (s *JyhCourseService) IncrementViewCount(id uint) (err error) {
	dbCourse := query.JyhCourse

	// 检查课程是否存在
	course, err := dbCourse.Where(dbCourse.ID.Eq(id)).First()
	if err != nil {
		return errors.New("课程不存在")
	}

	// 增加观看人数
	course.ViewCount++
	_, err = dbCourse.Where(dbCourse.ID.Eq(id)).Updates(map[string]interface{}{
		"view_count": course.ViewCount,
	})
	return err
}
