<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="dataSourceStorageLocal" created-in="GO-232.9559.64">
    <data-source name="@cd-ins.ultrasdk.com" uuid="de747785-ff71-429c-a357-47f42969e063">
      <database-info product="MySQL" version="8.0.33" jdbc-version="4.2" driver-name="MySQL Connector/J" driver-version="mysql-connector-j-8.2.0 (Revision: 06a1f724497fd81c6a659131fda822c9e5085b6c)" dbms="MYSQL" exact-version="8.0.33" exact-driver-version="8.2">
        <extra-name-characters>#@</extra-name-characters>
        <identifier-quote-string>`</identifier-quote-string>
      </database-info>
      <case-sensitivity plain-identifiers="lower" quoted-identifiers="lower" />
      <secret-storage>master_key</secret-storage>
      <user-name>douyin-dev</user-name>
      <schema-mapping>
        <introspection-scope>
          <node kind="schema">
            <name qname="@" />
            <name qname="douyin-dev" />
          </node>
        </introspection-scope>
      </schema-mapping>
    </data-source>
  </component>
</project>