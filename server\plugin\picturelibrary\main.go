package picturelibrary

import (
	gvaGlobal "github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/picturelibrary/model"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/picturelibrary/router"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/plugin-tool/utils"
	"github.com/gin-gonic/gin"
)

type PictureLibraryPlugin struct {
}

func CreatePictureLibraryPlug() *PictureLibraryPlugin {

	gvaGlobal.GVA_DB.AutoMigrate(model.SysAttachment{}, model.SysAttachmentCategory{}) // 此处可以把插件依赖的数据库结构体自动创建表 需要填写对应的结构体

	// 下方会自动注册菜单 第一个参数为菜单一级路由信息一般为定义好的组名 第二个参数为真实使用的web页面路由信息
	//utils.RegisterMenus(
	//	system.SysBaseMenu{
	//		Path:      "picturelibrary",
	//		Name:      "picturelibrary",
	//		Hidden:    false,
	//		Component: "plugin/picturelibrary/view/index.vue",
	//		Sort:      0,
	//		Meta: system.Meta{
	//			Title: "图片库",
	//			Icon:  "folder",
	//		},
	//	},
	//)

	// 下方会自动注册api 以下格式为示例格式，请按照实际情况修改
	utils.RegisterApis(
		system.SysApi{
			Path:        "/pic/pic_library/list",
			Description: "图片列表",
			ApiGroup:    "图片库",
			Method:      "POST",
		},
		system.SysApi{
			Path:        "/pic/pic_library/cat_list",
			Description: "图片分类列表",
			ApiGroup:    "图片库",
			Method:      "POST",
		},
		system.SysApi{
			Path:        "/pic/pic_library/add_cat",
			Description: "添加分类",
			ApiGroup:    "图片库",
			Method:      "POST",
		},
		system.SysApi{
			Path:        "/pic/pic_library/upload_handler",
			Description: "上传文件",
			ApiGroup:    "图片库",
			Method:      "POST",
		},
		system.SysApi{
			Path:        "/pic/pic_library/delete_file",
			Description: "删除文件",
			ApiGroup:    "图片库",
			Method:      "POST",
		},
	)

	return &PictureLibraryPlugin{}
}

func (*PictureLibraryPlugin) Register(group *gin.RouterGroup) {
	router.RouterGroupApp.InitPictureLibraryRouter(group)
}

func (*PictureLibraryPlugin) RouterPath() string {
	return "pic"
}
