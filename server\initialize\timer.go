package initialize

import (
	"fmt"
	"github.com/flipped-aurora/gin-vue-admin/server/task"

	"github.com/robfig/cron/v3"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

func Timer() {
	go func() {
		var option []cron.Option
		option = append(option, cron.WithSeconds())
		// 清理DB定时任务
		_, err := global.GVA_Timer.AddTaskByFunc("ClearDB", "@daily", func() {
			err := task.ClearTable(global.GVA_DB) // 定时任务方法定在task文件包中
			if err != nil {
				fmt.Println("timer error:", err)
			}
		}, "定时清理数据库【日志，黑名单】内容", option...)
		if err != nil {
			fmt.Println("add timer error:", err)
		}

		// 其他定时任务定在这里 参考上方使用方法

		//_, err := global.GVA_Timer.AddTaskByFunc("定时任务标识", "corn表达式", func() {
		//	具体执行内容...
		//  ......
		//}, option...)
		//if err != nil {
		//	fmt.Println("add timer error:", err)
		//}

		// MCN相关的定时任务
		/*_, err = global.GVA_Timer.AddTaskByFunc("MCNLogin", "@every 10s", func() {
		  	err := task.MCNLogin(global.GVA_REDIS)
		  	if err != nil {
		  		fmt.Println("timer error:", err)
		  	}
		  }, "定时设置MCN登录状态", option...)
		  if err != nil {
		  	fmt.Println("add timer error:", err)
		  }*/

		// 只有正式环境才运行
		if global.GVA_CONFIG.System.Env == "develop" {
			_, err = global.GVA_Timer.AddTaskByFunc("MCNFetchCommission", "@daily", func() {
				err := task.MCNFetchCommission(global.GVA_DB, global.GVA_REDIS)
				if err != nil {
					fmt.Println("timer error:", err)
				}
			}, "定时抓取MCN佣金", option...)
			if err != nil {
				fmt.Println("add timer error:", err)
			}
		}

		//定时发送素材给用户
		_, err = global.GVA_Timer.AddTaskByFunc("SendMaterial", "@daily", func() {
			err := task.SendMaterial(global.GVA_DB, "daily")
			if err != nil {
				fmt.Println("timer error:", err)
			}
		}, "每天定时发送素材给用户", option...)
		if err != nil {
			fmt.Println("add timer error:", err)
		}

		_, err = global.GVA_Timer.AddTaskByFunc("SendMaterial", "@weekly", func() {
			err := task.SendMaterial(global.GVA_DB, "weekly")
			if err != nil {
				fmt.Println("timer error:", err)
			}
		}, "每天定时发送素材给用户", option...)
		if err != nil {
			fmt.Println("add timer error:", err)
		}

	}()
}
