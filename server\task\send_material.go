package task

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
	"gorm.io/gorm"
	"time"
)

//@author: [peter]
//@function: SendMaterial
//@description: 定时发放素材
//@param: db(数据库对象) *gorm.DB, FrequencyType("daily" or "weekly") string
//@return: error

func SendMaterial(db *gorm.DB, frequencyType string) error {
	// 你想根据 jyh_user_ext 表中的 粉丝数 和 有效粉丝数 字段，自动匹配 jyh_material_rule_config 表中定义的规则，获取每日或每周应发放的素材数量和类型（视频、图文、拉流）
	var userExts []jyhapp.JyhUserExt
	if err := db.Where("fans_count > 0 AND valid_fans_count > 0").Find(&userExts).Error; err != nil {
		return err
	}
	for _, userExt := range userExts {
		var materialRule jyhapp.JyhMaterialRuleConfig
		// 根据粉丝数和有效粉丝数查询规则,  jyh_material_rule_config 表中 粉丝数区间 min_fans 和 max_fans 字段, 有效粉丝数区间min_valid_fans 和 max_valid_fans 字段
		if err := db.Debug().
			Where("? BETWEEN min_fans AND max_fans AND ? BETWEEN min_valid_fans AND max_valid_fans",
				userExt.FansCount, userExt.ValidFansCount).
			Where("frequency_type = ? ", frequencyType).
			First(&materialRule).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				continue // 如果没有找到规则，则跳过
			}
			return err // 其他错误直接返回
		}

		// 根据规则发放素材（这里假设有一个发放素材的函数）
		if err := distributeMaterial(db, userExt.UserID, materialRule); err != nil {
			return err
		}
	}

	return nil
}

// distributeMaterial 发放素材的具体实现
func distributeMaterial(db *gorm.DB, userID uint, rule jyhapp.JyhMaterialRuleConfig) error {
	// 这里可以根据规则生成素材记录
	// 假设 jyh_material 是存储素材的表, 获取对应类型的素材， （视频、图文、拉流）， 将素材记录保存到 jyh_material_user 表中
	if rule.ImageCount > 0 {
		unsentMaterials, err := GetUnsentMaterials(db, "image")
		if err != nil {
			return err // 返回查询错误
		}
		for _, material := range unsentMaterials {
			DistributeMaterial(db, userID, material)
		}
	}
	if rule.VideoCount > 0 {
		unsentMaterials, err := GetUnsentMaterials(db, "video")
		if err != nil {
			return err // 返回查询错误
		}
		for _, material := range unsentMaterials {
			DistributeMaterial(db, userID, material)
		}
	}
	if rule.StreamCount > 0 {
		unsentMaterials, err := GetUnsentMaterials(db, "stream")
		if err != nil {
			return err // 返回查询错误
		}
		for _, material := range unsentMaterials {
			DistributeMaterial(db, userID, material)
		}
	}
	return nil
}

// GetUnsentMaterials 通过素材类型获取为发放的素材数据
func GetUnsentMaterials(db *gorm.DB, materialType string) ([]jyhapp.JyhMaterial, error) {
	var materials []jyhapp.JyhMaterial
	err := db.Where("type = ? AND is_send = ?", materialType, false).Find(&materials).Error
	if err != nil {
		return nil, err // 返回查询错误
	}
	return materials, nil // 返回未发放的素材列表
}

// 发放素材
func DistributeMaterial(db *gorm.DB, userID uint, material jyhapp.JyhMaterial) error {
	materialUser := jyhapp.JyhMaterialUser{
		UserID:     userID,
		MaterialID: material.ID,
		SendAt:     time.Now(),
		Status:     0, // 未领取状态
	}

	if err := db.Omit("claimed_at").Create(&materialUser).Error; err != nil {
		return err // 返回创建错误
	}

	// 更新素材状态为已发放
	if err := db.Model(&material).Where("id = ?", material.ID).Update("is_send", true).Error; err != nil {
		return err // 返回更新错误
	}

	return nil // 成功发放素材
}
