package jyhapp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type CourseCategoryRouter struct {
}

// InitCourseCategoryRouter 初始化 courseCategory 路由信息
func (s *CourseCategoryRouter) InitCourseCategoryRouter(Router *gin.RouterGroup, JyhRouter *gin.RouterGroup) {
	courseRouterRouter := Router.Group("courseCategory").Use(middleware.OperationRecord())
	courseRouterRouterWithoutRecord := Router.Group("courseCategory")
	var courseRouterApi = v1.ApiGroupApp.JyhApiGroup.CourseCategoryApi
	{
		// 课程分类相关
		courseRouterRouter.POST("create", courseRouterApi.Create)   // 新建课程分类
		courseRouterRouter.DELETE("delete", courseRouterApi.Delete) // 删除课程分类
		//courseRouterRouter.DELETE("deleteByIds", courseRouterApi.DeleteMaterialCategoryByIds) // 批量删除课程分类
		courseRouterRouter.PUT("update", courseRouterApi.Update) // 更新课程分类
	}
	{
		courseRouterRouterWithoutRecord.GET("detail", courseRouterApi.GetDetail) // 根据ID获取课程分类
		courseRouterRouterWithoutRecord.GET("list", courseRouterApi.GetList)     // 获取课程分类列表
	}
	{

	}
}
