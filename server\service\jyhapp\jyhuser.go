package jyhapp

import (
	"context"
	"encoding/json"
	"github.com/shopspring/decimal"

	"errors"
	"fmt"
	"math/rand"
	"regexp"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/utils/jgorm"

	"github.com/xtulnx/jkit-go/jtime"
	"gorm.io/gen"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/errno"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/query"
	jyhReq "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/request"
	jyhResp "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/response"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// 短信验证码场景
const (
	SmsSceneRegister  = "register" // 注册
	SmsSceneLogin     = "login"    // 登录
	SmsSceneResetPwd  = "reset"    // 重置密码
	SmsSceneBindPhone = "bind"     // 绑定手机号
)

// 短信验证码配置
const (
	SmsCodeLength   = 6      // 验证码长度
	SmsCodeExpire   = 300    // 验证码有效期（秒）
	SmsSendInterval = 60     // 发送间隔（秒）
	SmsMaxSendTimes = 10     // 每天最大发送次数
	SmsTestCode     = "1111" // 测试环境验证码
)

type JyhUserService struct{}

// Register 用户注册
func (s *JyhUserService) Register(req *jyhReq.UserRegister) (err error) {
	dbUser := query.JyhUser

	// 验证手机号格式
	if !isValidPhoneNumber(req.Phone) {
		return errors.New("无效的手机号码格式")
	}

	// 检查手机号是否已注册
	exists, err := dbUser.Where(dbUser.Phone.Eq(req.Phone)).Count()
	if err != nil {
		global.GVA_LOG.Error("查询用户失败", zap.Error(err))
		return err
	}
	if exists > 0 {
		return errors.New("手机号已注册")
	}

	// 验证验证码
	if err = s.validateVerificationCode(req.Phone, req.SmsCode, SmsSceneRegister); err != nil {
		return err
	}

	// 验证新的消耗型邀请码
	inviteCodeInfo, err := s.validateNewInviteCode(req.InviteCode)
	if err != nil {
		return err
	}

	// 生成新的邀请码（保留旧的邀请码字段用于兼容性）
	inviteCode, err := s.generateInviteCode()
	if err != nil {
		return err
	}

	// 创建用户
	user := &jyhapp.JyhUser{
		Phone:      req.Phone,
		Username:   HideIdent(req.Phone),  // 使用脱敏的手机号作为用户名
		InviteCode: inviteCode,            // 生成的新邀请码（保留字段）
		InvitedBy:  0,                     // 邀请人ID（暂时设为0，系统支持平台邀请码）
		IsAgent:    false,                 // 默认非代理
		Status:     1,                     // 默认启用
		UserType:   jyhapp.JyhAccountUser, // 默认普通用户
	}

	// 如果是用户生成的邀请码，设置邀请人
	if inviteCodeInfo.UserID != nil {
		user.InvitedBy = *inviteCodeInfo.UserID
	}

	var usedInviteCode *jyhapp.JyhInviteCode

	// 使用事务同时创建用户和用户扩展信息
	err = TryTransaction(func(tx *query.Query) (err error) {
		// 创建用户
		if err := tx.JyhUser.Create(user); err != nil {
			return err
		}

		// 使用邀请码（标记为已使用）
		usedInviteCode, err = gSrv.UseInviteCodeTx(tx, req.InviteCode, user.ID)
		if err != nil {
			return err
		}

		// 创建用户扩展信息
		userExt := &jyhapp.JyhUserExt{
			UserID: user.ID,
			Age:    0,   // 默认值
			Sex:    "男", // 默认值
		}

		if err := tx.JyhUserExt.Omit(tx.JyhUserExt.CarOwnerTime).Create(userExt); err != nil {
			return err
		}
		//用户层级关系
		err = gSrv.MaintainUserClosure(tx, user.ID, user.InvitedBy)
		if err != nil {
			return
		}

		// 根据邀请码绑定的等级自动为用户分配会员等级
		if usedInviteCode != nil && usedInviteCode.LevelID > 0 {
			err = s.assignUserLevel(tx, user.ID, usedInviteCode.LevelID)
			if err != nil {
				global.GVA_LOG.Error("分配用户等级失败", zap.Error(err))
				// 不中断注册流程，只记录错误
			}
		}

		return nil
	})
	if err != nil {
		global.GVA_LOG.Error("创建用户失败", zap.Error(err))
		err = errno.InternalServerError.WithMsg("创建用户失败")
		return
	}

	return nil
}

// generateInviteCode 生成邀请码
func (s *JyhUserService) generateInviteCode() (string, error) {
	// 设置随机数种子
	rand.Seed(time.Now().UnixNano())

	for i := 0; i < 10; i++ { // 最多尝试10次
		// 生成6位随机数
		code := fmt.Sprintf("%06d", rand.Intn(1000000))

		// 检查邀请码是否已存在
		dbUser := query.JyhUser
		exists, err := dbUser.Where(dbUser.InviteCode.Eq(code)).Count()
		if err != nil {
			return "", err
		}
		if exists == 0 {
			return code, nil
		}
	}

	return "", errors.New("无法生成唯一邀请码")
}

// validateInviteCode 验证邀请码并返回邀请人ID
func (s *JyhUserService) validateInviteCode(code string) (uint, error) {
	if code == "" {
		return 0, errors.New("邀请码不能为空")
	}

	// 查询用户表中是否存在该邀请码
	dbUser := query.JyhUser
	inviter, err := dbUser.Where(dbUser.InviteCode.Eq(code)).First()
	if err != nil {
		global.GVA_LOG.Error("查询邀请码失败", zap.Error(err))
		return 0, errors.New("验证邀请码失败")
	}
	if inviter == nil {
		return 0, errors.New("无效的邀请码")
	}

	return inviter.ID, nil
}

// isValidPhoneNumber 验证手机号格式
func isValidPhoneNumber(phone string) bool {
	pattern := `^1[3-9]\d{9}$`
	matched, _ := regexp.MatchString(pattern, phone)
	return matched
}

// validateVerificationCode 验证验证码
func (s *JyhUserService) validateVerificationCode(phone, code, scene string) error {
	if code == "" {
		return errors.New("验证码不能为空")
	}

	// 测试环境验证
	if global.GVA_CONFIG.System.Env != "public" && code == SmsTestCode {
		return nil
	}

	// 从Redis获取验证码
	savedCode, err := s.getSmsCode(context.Background(), phone, scene)
	if err != nil {
		if err == redis.Nil {
			return errors.New("验证码已过期")
		}
		return errors.New("验证码错误")
	}

	if savedCode != code {
		return errors.New("验证码错误")
	}

	// 验证成功后删除验证码
	s.deleteSmsCode(context.Background(), phone, scene)
	return nil
}

// Login 用户登录
func (s *JyhUserService) Login(req *jyhReq.UserLogin, token func(user *jyhapp.JyhUser) (token string, expiresAt int64, err error)) (*jyhResp.JyhUserResp, error) {
	dbUser := query.JyhUser
	// 验证手机号格式
	if !isValidPhoneNumber(req.Phone) {
		return nil, errors.New("无效的手机号码格式")
	}
	// 验证验证码
	if err := s.validateVerificationCode(req.Phone, req.SmsCode, SmsSceneLogin); err != nil {
		return nil, err
	}
	// 获取用户信息
	user, err := dbUser.Where(dbUser.Phone.Eq(req.Phone)).First()
	if err != nil {
		return nil, errors.New("用户不存在")
	}
	jwtToken, at, err := token(user)
	if err != nil {
		return nil, errno.InternalServerError.CombineErrorMsg("获取token失败!", err)
	}
	// 查询用户当前生效的等级信息
	var currentLevelInfo *jyhResp.UserLevelInfo
	dbUserLevel := query.JyhUserLevel
	dbLevel := query.JyhUserShipLevel

	userLevel, err := dbUserLevel.Where(
		dbUserLevel.UserID.Eq(user.ID),
		dbUserLevel.Status.Eq("active"),
		dbUserLevel.StartAt.Lte(time.Now()),
		dbUserLevel.EndAt.Gt(time.Now()),
	).First()

	if err == nil {
		// 获取等级详情
		level, levelErr := dbLevel.Where(dbLevel.ID.Eq(userLevel.LevelID)).First()
		if levelErr == nil {
			currentLevelInfo = &jyhResp.UserLevelInfo{
				ID:           userLevel.ID,
				LevelID:      userLevel.LevelID,
				LevelName:    level.Name,
				LevelCode:    level.Code,
				LevelIcon:    level.Icon,
				PriceCents:   level.PriceCents,
				DurationDays: level.DurationDays,
				StartAt:      userLevel.StartAt.Format("2006-01-02 15:04:05"),
				EndAt:        userLevel.EndAt.Format("2006-01-02 15:04:05"),
				Status:       userLevel.Status,
			}
		}
	}
	dbTransaction := query.JyhUserAccountTransaction
	dbAccount := query.JyhUserAccount
	var totalIncomeAmount decimal.Decimal
	// 计算总收入（佣金收入 + 退款）
	err = dbAccount.Join(dbTransaction, dbAccount.AccountID.EqCol(dbTransaction.AccountID)).
		Where(
			dbAccount.UserID.Eq(user.ID),
			dbTransaction.TransactionType.In(jyhapp.TransactionTypeCommissionIncome, jyhapp.TransactionTypeRefund),
			dbTransaction.TransactionStatus.Eq(jyhapp.TransactionStatusSuccess),
		).Select(dbTransaction.Amount.Sum().As("total_income")).Scan(&totalIncomeAmount)
	if err != nil {
		totalIncomeAmount = decimal.Zero
	}
	dbClosure := query.JyhUserClosure
	inviteNum, err := dbClosure.Where(dbClosure.AncestorID.Eq(user.ID), dbClosure.Depth.Gt(0)).Count()
	if err != nil {
		err = errno.InternalServerError.WithMsg("获取邀请人数失败")
		return nil, err
	}
	info := jyhResp.JyhUserInfo{
		Id:                user.ID,
		Username:          user.Username,
		Phone:             HideIdent(user.Phone),
		Avatar:            user.Avatar,
		Status:            user.Status,
		UserType:          user.UserType,
		IsAgent:           user.IsAgent,
		InvitedBy:         user.InvitedBy,
		InviteCode:        user.InviteCode,
		CurrentLevel:      currentLevelInfo,
		CanLiveStream:     user.CanLiveStream,
		ContractStatus:    user.ContractStatus,
		TotalIncomeAmount: totalIncomeAmount,
		InviteNum:         inviteNum,
	}
	ext := jyhResp.Ext{}
	_ = json.Unmarshal(user.Ext, &ext)
	info.Ext = &ext
	resp := &jyhResp.JyhUserResp{
		JyhUserInfo: info,
		Token:       jwtToken,
		ExpiresAt:   at,
	}
	return resp, nil
}

// GetUserInfo 获取用户信息
func (s *JyhUserService) GetUserInfo(userID uint) (*jyhResp.JyhUserInfo, error) {
	dbUser := query.JyhUser
	dbUserLevel := query.JyhUserLevel
	dbLevel := query.JyhUserShipLevel

	// 查询用户信息，并预加载扩展信息
	user, err := dbUser.Where(dbUser.ID.Eq(userID)).First()
	if err != nil {
		return nil, errors.New("用户不存在")
	}

	// 查询用户扩展信息
	var userExt *jyhapp.JyhUserExt
	err = global.GVA_DB.Where("user_id = ?", userID).First(&userExt).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		global.GVA_LOG.Error("获取用户扩展信息失败", zap.Error(err))
	}

	dbTransaction := query.JyhUserAccountTransaction
	dbAccount := query.JyhUserAccount
	var totalIncomeAmount decimal.Decimal
	// 计算总收入（佣金收入 + 退款）
	err = dbAccount.Join(dbTransaction, dbAccount.AccountID.EqCol(dbTransaction.AccountID)).
		Where(
			dbAccount.UserID.Eq(userID),
			dbTransaction.TransactionType.In(jyhapp.TransactionTypeCommissionIncome, jyhapp.TransactionTypeRefund),
			dbTransaction.TransactionStatus.Eq(jyhapp.TransactionStatusSuccess),
		).Select(dbTransaction.Amount.Sum().As("total_income")).Scan(&totalIncomeAmount)
	if err != nil {
		totalIncomeAmount = decimal.Zero
	}
	dbClosure := query.JyhUserClosure
	inviteNum, err := dbClosure.Where(dbClosure.AncestorID.Eq(userID), dbClosure.Depth.Gt(0)).Count()
	if err != nil {
		err = errno.InternalServerError.WithMsg("获取邀请人数失败")
		return nil, err
	}
	// 查询用户当前生效的等级信息
	var currentLevelInfo *jyhResp.UserLevelInfo
	userLevel, err := dbUserLevel.Where(
		dbUserLevel.UserID.Eq(userID),
		dbUserLevel.Status.In("active", "pending"),
		dbUserLevel.StartAt.Lte(time.Now()),
		dbUserLevel.EndAt.Gt(time.Now()),
	).First()

	if err == nil {
		// 获取等级详情
		level, levelErr := dbLevel.Where(dbLevel.ID.Eq(userLevel.LevelID)).First()
		if levelErr == nil {
			currentLevelInfo = &jyhResp.UserLevelInfo{
				ID:           userLevel.ID,
				LevelID:      userLevel.LevelID,
				LevelName:    level.Name,
				LevelCode:    level.Code,
				LevelIcon:    level.Icon,
				PriceCents:   level.PriceCents,
				DurationDays: level.DurationDays,
				StartAt:      userLevel.StartAt.Format("2006-01-02 15:04:05"),
				EndAt:        userLevel.EndAt.Format("2006-01-02 15:04:05"),
				Status:       userLevel.Status,
			}
		}
	}

	// 创建响应对象
	resp := &jyhResp.JyhUserInfo{
		Id:                user.ID,
		Username:          user.Username,
		Phone:             HideIdent(user.Phone),
		Avatar:            user.Avatar,
		Status:            user.Status,
		UserType:          user.UserType,
		IsAgent:           user.IsAgent,
		InvitedBy:         user.InvitedBy,
		InviteCode:        user.InviteCode,
		CurrentLevel:      currentLevelInfo,
		ContractStatus:    user.ContractStatus,
		CanLiveStream:     user.CanLiveStream,
		TotalIncomeAmount: totalIncomeAmount,
		InviteNum:         inviteNum,
	}
	ext := jyhResp.Ext{}
	_ = json.Unmarshal(user.Ext, &ext)
	resp.Ext = &ext
	// 添加用户扩展信息（如果存在），并对敏感信息进行脱敏
	if userExt != nil {
		resp.UserExt = &jyhResp.UserExtInfo{
			IdCard:         HideIdent(userExt.IdCard),
			Age:            userExt.Age,
			Sex:            userExt.Sex,
			City:           userExt.City,
			Occupation:     userExt.Occupation,
			Address:        HideIdent(userExt.Address),
			WeChat:         userExt.WeChat,
			WeChatNickname: userExt.WeChatNickname,
			DouYin:         userExt.DouYin,
			DouYinNickname: userExt.DouYinNickname,
			DouYinPhone:    HideIdent(userExt.DouYinPhone),
			QQ:             userExt.QQ,
			TrackType:      userExt.TrackType,
			IsCarOwner:     userExt.IsCarOwner,
			FansCount:      userExt.FansCount,
			ValidFansCount: userExt.ValidFansCount,
		}
		if userExt.IsCarOwner == true && !userExt.CarOwnerTime.IsZero() {
			resp.UserExt.CarOwnerDay = DaysFromNow(userExt.CarOwnerTime)
		}
	}

	return resp, nil
}

// SendVerificationCode 发送验证码
func (s *JyhUserService) SendVerificationCode(phone, scene string) error {
	if global.GVA_CONFIG.System.Env != "public" {
		return nil
	}
	if !isValidPhoneNumber(phone) {
		return errors.New("无效的手机号码格式")
	}

	if !isValidSmsScene(scene) {
		return errors.New("无效的短信场景")
	}

	// 检查发送频率
	if err := s.checkSmsFrequency(phone, scene); err != nil {
		return err
	}

	// 生成验证码
	code := s.generateSmsCode()

	// 保存验证码到Redis
	if err := s.saveSmsCode(context.Background(), phone, scene, code); err != nil {
		global.GVA_LOG.Error("保存验证码失败", zap.Error(err))
		return errors.New("发送验证码失败")
	}

	// TODO: 根据环境判断是否真实发送短信
	/*if !global.GVA_CONFIG.System.IsProd {
		global.GVA_LOG.Info("测试环境短信验证码", zap.String("phone", phone), zap.String("code", code))
		return nil
	}*/

	// 调用短信服务发送验证码
	if err := s.sendSms(phone, scene, code); err != nil {
		global.GVA_LOG.Error("发送短信失败", zap.Error(err))
		return errors.New("发送验证码失败")
	}

	return nil
}

// 生成验证码
func (s *JyhUserService) generateSmsCode() string {
	rand.Seed(time.Now().UnixNano())
	return fmt.Sprintf("%06d", rand.Intn(1000000))
}

// 检查短信发送频率
func (s *JyhUserService) checkSmsFrequency(phone, scene string) error {
	key := fmt.Sprintf("sms:interval:%s:%s", phone, scene)
	if global.GVA_REDIS == nil && global.GVA_CONFIG.System.Env == "public" {
		return errors.New("redis未初始化")
	}
	exists, _ := global.GVA_REDIS.Exists(context.Background(), key).Result()
	if exists == 1 {
		return errors.New("发送太频繁，请稍后再试")
	}

	// 检查每天发送次数
	dayKey := fmt.Sprintf("sms:count:%s:%s", phone, time.Now().Format("20060102"))
	count, _ := global.GVA_REDIS.Get(context.Background(), dayKey).Int()
	if count >= SmsMaxSendTimes {
		return errors.New("今日发送次数已达上限")
	}

	return nil
}

// 保存验证码到Redis
func (s *JyhUserService) saveSmsCode(ctx context.Context, phone, scene, code string) error {
	// 保存验证码
	codeKey := fmt.Sprintf("sms:code:%s:%s", phone, scene)
	if global.GVA_REDIS == nil {
		return errors.New("redis未初始化")
	}
	err := global.GVA_REDIS.Set(ctx, codeKey, code, time.Second*SmsCodeExpire).Err()
	if err != nil {
		return err
	}

	// 设置发送间隔
	intervalKey := fmt.Sprintf("sms:interval:%s:%s", phone, scene)
	err = global.GVA_REDIS.Set(ctx, intervalKey, 1, time.Second*SmsSendInterval).Err()
	if err != nil {
		return err
	}

	// 更新每日发送次数
	dayKey := fmt.Sprintf("sms:count:%s:%s", phone, time.Now().Format("20060102"))
	err = global.GVA_REDIS.Incr(ctx, dayKey).Err()
	if err != nil {
		return err
	}
	// 设置每日计数器过期时间
	global.GVA_REDIS.Expire(ctx, dayKey, time.Hour*24)

	return nil
}

// 从Redis获取验证码
func (s *JyhUserService) getSmsCode(ctx context.Context, phone, scene string) (string, error) {
	key := fmt.Sprintf("sms:code:%s:%s", phone, scene)
	if global.GVA_REDIS == nil {
		return "", errors.New("redis未初始化")
	}
	return global.GVA_REDIS.Get(ctx, key).Result()
}

// 删除Redis中的验证码
func (s *JyhUserService) deleteSmsCode(ctx context.Context, phone, scene string) {
	key := fmt.Sprintf("sms:code:%s:%s", phone, scene)
	if global.GVA_REDIS == nil {
		return
	}
	global.GVA_REDIS.Del(ctx, key)
}

// 发送短信
func (s *JyhUserService) sendSms(phone, scene, code string) error {
	// TODO: 实现具体的短信发送逻辑
	// 1. 根据不同场景选择不同的短信模板
	// 2. 调用短信服务商的API
	return nil
}

// 验证短信场景是否有效
func isValidSmsScene(scene string) bool {
	switch scene {
	case SmsSceneRegister, SmsSceneLogin, SmsSceneResetPwd, SmsSceneBindPhone:
		return true
	default:
		return false
	}
}

// GetUserList 管理员获取用户列表
func (s *JyhUserService) GetUserList(req *jyhReq.AdminUserListReq) ([]*jyhResp.AdminUserListItem, int64, error) {

	var (
		dbUser        = query.JyhUser
		dbInvitedUser = query.JyhUser.As("invited_user")
		dbUserExt     = query.JyhUserExt
	)
	// 构建查询条件
	q := dbUser.WithContext(context.Background())
	var conditions []gen.Condition
	{
		if req.Keyword != "" {
			conditions = append(conditions, dbUser.Where(dbUser.Username.Like("%"+req.Keyword+"%")).Or(dbUser.Phone.Like("%"+req.Keyword+"%")))
		}
		if req.Status != nil {
			conditions = append(conditions, dbUser.Status.Eq(uint(*req.Status)))
		}
		if req.UserType != nil {
			if *req.UserType == "user" {
				conditions = append(conditions, dbUser.UserType.Eq(jyhapp.JyhAccountUser))
			} else if *req.UserType == "admin" {
				conditions = append(conditions, dbUser.UserType.Eq(jyhapp.JyhAccountAdmin))
			}
		}
		if req.IsAgent != nil {
			conditions = append(conditions, dbUser.IsAgent.Is(*req.IsAgent))
		}

		// 时间范围筛选
		if startTime := jtime.Str2Time(req.StartTime); !startTime.IsZero() {
			conditions = append(conditions, dbUser.CreatedAt.Gte(startTime))
		}
		if endTime := jtime.Str2Time(req.EndTime); !endTime.IsZero() {
			conditions = append(conditions, dbUser.CreatedAt.Lte(endTime))
		}

		// 标签筛选
		if len(req.TagIDs) > 0 || req.TagName != "" {
			// 根据标签筛选用户，需要先找到符合条件的用户ID
			userIDs, err := s.getUserIDsByTagFilter(req.TagIDs, req.TagName)
			if err != nil {
				global.GVA_LOG.Error("根据标签筛选用户失败", zap.Error(err))
				return nil, 0, err
			}
			if len(userIDs) == 0 {
				// 没有符合条件的用户，直接返回空结果
				return []*jyhResp.AdminUserListItem{}, 0, nil
			}
			conditions = append(conditions, dbUser.ID.In(userIDs...))
		}
	}
	dao1 := q.Where(conditions...).
		LeftJoin(dbInvitedUser, dbUser.InvitedBy.EqCol(dbInvitedUser.ID)).
		LeftJoin(dbUserExt, dbUser.ID.EqCol(dbUserExt.UserID))
	jgorm.SelectAppend(&dao1.DO,
		dbUser.ID,
		dbUser.Username,
		dbUser.Phone,
		dbUser.InvitedBy,
		dbUser.InviteCode,
		dbInvitedUser.Username.As("inviter_name"),
		dbUser.IsAgent,
		dbUser.Status,
		dbUser.UserType,
		dbUser.CanLiveStream,
		dbUser.ContractStatus,
		dbUser.CreatedAt.DateFormat("%Y-%m-%d %H:%i:%s").As("created_at"),
		dbUserExt.IdCard,
		dbUserExt.Age,
		dbUserExt.Sex,
		dbUserExt.City,
		dbUserExt.Occupation,
		dbUserExt.Address,
		dbUserExt.WeChat,
		dbUserExt.WeChatNickname,
		dbUserExt.DouYin,
		dbUserExt.DouYinNickname,
		dbUserExt.DouYinPhone,
		dbUserExt.QQ,
		dbUserExt.TrackType,
		dbUserExt.IsCarOwner,
		dbUserExt.CarOwnerTime,
		dbUserExt.FansCount,
		dbUserExt.ValidFansCount,
	)
	// 获取总数
	total, err := dao1.Count()
	if err != nil {
		global.GVA_LOG.Error("获取用户列表总数失败", zap.Error(err))
		return nil, 0, err
	}
	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	dao1 = dao1.Offset(offset).
		Limit(req.PageSize).
		Order(dbUser.CreatedAt.Desc())
	var result []*jyhResp.AdminUserListItem
	err = dao1.Scan(&result)
	if err != nil {
		global.GVA_LOG.Error("获取用户列表失败", zap.Error(err))
		return nil, 0, err
	}
	// 批量获取用户标签
	userTagMap, err := s.batchGetUserTags(result)
	if err != nil {
		global.GVA_LOG.Error("批量获取用户标签失败", zap.Error(err))
		// 如果获取标签失败，给所有用户设置空标签数组
		userTagMap = make(map[uint][]jyhResp.UserTagItem)
	}

	// 构建响应数据
	for i, v := range result {
		// 转换用户类型
		userType := "user" // 默认普通用户
		if v.UserType == jyhapp.JyhAccountAdmin {
			userType = "admin"
		}
		result[i].UserType = userType
		// 获取用户扩展信息
		result[i].Phone = HideIdent(v.Phone)
		result[i].IdCard = HideIdent(v.IdCard)
		result[i].Address = HideIdent(v.Address)
		result[i].DouYinPhone = HideIdent(v.DouYinPhone)

		// 从map中获取用户标签
		result[i].Tags = make([]jyhResp.UserTagItem, 0)
		if tags, exists := userTagMap[v.ID]; exists {
			result[i].Tags = tags
		}
	}
	return result, total, nil
}

// batchGetUserTags 批量获取用户标签
func (s *JyhUserService) batchGetUserTags(users []*jyhResp.AdminUserListItem) (map[uint][]jyhResp.UserTagItem, error) {
	if len(users) == 0 {
		return make(map[uint][]jyhResp.UserTagItem), nil
	}

	// 提取所有用户ID
	var userIDs []uint
	for _, user := range users {
		userIDs = append(userIDs, user.ID)
	}

	dbTag := query.JyhUserTag
	dbRelation := query.JyhUserTagRelation

	// 批量查询所有用户的标签关系
	relations, err := dbRelation.Where(dbRelation.UserID.In(userIDs...)).Find()
	if err != nil {
		return nil, err
	}

	if len(relations) == 0 {
		// 没有任何标签关系，返回空map
		result := make(map[uint][]jyhResp.UserTagItem)
		for _, userID := range userIDs {
			result[userID] = []jyhResp.UserTagItem{}
		}
		return result, nil
	}

	// 提取所有标签ID
	var tagIDs []uint
	tagIDSet := make(map[uint]bool)
	for _, relation := range relations {
		if !tagIDSet[relation.TagID] {
			tagIDs = append(tagIDs, relation.TagID)
			tagIDSet[relation.TagID] = true
		}
	}

	// 批量查询标签详情
	tags, err := dbTag.Where(dbTag.ID.In(tagIDs...)).Find()
	if err != nil {
		return nil, err
	}

	// 构建标签ID到标签详情的映射
	tagMap := make(map[uint]jyhResp.UserTagItem)
	for _, tag := range tags {
		tagMap[tag.ID] = jyhResp.UserTagItem{
			ID:          tag.ID,
			Name:        tag.Name,
			Description: tag.Description,
			Color:       tag.Color,
			Status:      tag.Status,
			Sort:        tag.Sort,
			CreatedAt:   tag.CreatedAt.Format("2006-01-02 15:04:05"),
		}
	}

	// 构建用户ID到标签列表的映射
	userTagMap := make(map[uint][]jyhResp.UserTagItem)

	// 初始化所有用户的标签数组
	for _, userID := range userIDs {
		userTagMap[userID] = []jyhResp.UserTagItem{}
	}

	// 填充用户标签数据
	for _, relation := range relations {
		if tagItem, exists := tagMap[relation.TagID]; exists {
			userTagMap[relation.UserID] = append(userTagMap[relation.UserID], tagItem)
		}
	}

	return userTagMap, nil
}

// getUserTagsByUserID 获取用户标签（内部辅助方法）
func (s *JyhUserService) getUserTagsByUserID(userID uint) ([]jyhResp.UserTagItem, error) {
	dbTag := query.JyhUserTag
	dbRelation := query.JyhUserTagRelation

	// 查询用户的所有标签关系
	relations, err := dbRelation.Where(dbRelation.UserID.Eq(userID)).Find()
	if err != nil {
		return nil, err
	}

	if len(relations) == 0 {
		return []jyhResp.UserTagItem{}, nil
	}

	var tagIDs []uint
	for _, relation := range relations {
		tagIDs = append(tagIDs, relation.TagID)
	}

	// 查询标签详情
	tags, err := dbTag.Where(dbTag.ID.In(tagIDs...)).Find()
	if err != nil {
		return nil, err
	}

	var tagItems []jyhResp.UserTagItem
	for _, tag := range tags {
		tagItems = append(tagItems, jyhResp.UserTagItem{
			ID:          tag.ID,
			Name:        tag.Name,
			Description: tag.Description,
			Color:       tag.Color,
			Status:      tag.Status,
			Sort:        tag.Sort,
			CreatedAt:   tag.CreatedAt.Format("2006-01-02 15:04:05"),
		})
	}

	return tagItems, nil
}

// getUserIDsByTagFilter 根据标签筛选获取用户ID列表
func (s *JyhUserService) getUserIDsByTagFilter(tagIDs []uint, tagName string) ([]uint, error) {
	dbTag := query.JyhUserTag
	dbRelation := query.JyhUserTagRelation

	var userIDs []uint

	// 如果有标签ID筛选
	if len(tagIDs) > 0 {
		// 查询拥有指定标签的用户ID
		relations, err := dbRelation.Where(dbRelation.TagID.In(tagIDs...)).Find()
		if err != nil {
			return nil, err
		}

		// 如果需要用户同时拥有所有指定标签（AND关系）
		if len(tagIDs) > 1 {
			// 统计每个用户拥有的标签数量
			userTagCount := make(map[uint]int)
			for _, relation := range relations {
				userTagCount[relation.UserID]++
			}

			// 只返回拥有所有指定标签的用户
			for userID, count := range userTagCount {
				if count == len(tagIDs) {
					userIDs = append(userIDs, userID)
				}
			}
		} else {
			// 单个标签，直接返回所有用户ID
			for _, relation := range relations {
				userIDs = append(userIDs, relation.UserID)
			}
		}
	}

	// 如果有标签名称筛选
	if tagName != "" {
		// 先根据名称找到标签ID
		tags, err := dbTag.Where(dbTag.Name.Like("%" + tagName + "%")).Find()
		if err != nil {
			return nil, err
		}

		if len(tags) == 0 {
			// 没有匹配的标签，返回空结果
			return []uint{}, nil
		}

		var nameTagIDs []uint
		for _, tag := range tags {
			nameTagIDs = append(nameTagIDs, tag.ID)
		}

		// 查询拥有这些标签的用户ID
		relations, err := dbRelation.Where(dbRelation.TagID.In(nameTagIDs...)).Find()
		if err != nil {
			return nil, err
		}

		var nameUserIDs []uint
		for _, relation := range relations {
			nameUserIDs = append(nameUserIDs, relation.UserID)
		}

		// 如果同时有tagIDs和tagName筛选，需要取交集
		if len(tagIDs) > 0 {
			userIDs = intersectUserIDs(userIDs, nameUserIDs)
		} else {
			userIDs = nameUserIDs
		}
	}

	return userIDs, nil
}

// intersectUserIDs 计算两个用户ID切片的交集
func intersectUserIDs(ids1, ids2 []uint) []uint {
	idMap := make(map[uint]bool)
	for _, id := range ids1 {
		idMap[id] = true
	}

	var result []uint
	for _, id := range ids2 {
		if idMap[id] {
			result = append(result, id)
		}
	}

	return result
}

// AdminCreateUser 管理员创建用户
func (s *JyhUserService) AdminCreateUser(req *jyhReq.AdminUserReq) (*jyhResp.AdminUserCreateResp, error) {
	dbUser := query.JyhUser

	// 验证手机号格式
	if !isValidPhoneNumber(req.Phone) {
		return nil, errors.New("无效的手机号码格式")
	}

	// 检查手机号是否已注册
	exists, err := dbUser.Where(dbUser.Phone.Eq(req.Phone)).Count()
	if err != nil {
		global.GVA_LOG.Error("查询用户失败", zap.Error(err))
		return nil, err
	}
	if exists > 0 {
		return nil, errors.New("手机号已注册")
	}

	// 验证邀请码并获取邀请人ID（如果提供了邀请码）
	var inviterID uint
	if req.InviteCode != "" {
		inviterID, err = s.validateInviteCode(req.InviteCode)
		if err != nil {
			return nil, err
		}
	}

	// 生成新的邀请码
	inviteCode, err := s.generateInviteCode()
	if err != nil {
		return nil, err
	}

	// 设置默认值
	username := req.Username
	if username == "" {
		username = HideIdent(req.Phone) // 使用脱敏的手机号作为用户名
	}

	isAgent := false
	if req.IsAgent != nil {
		isAgent = *req.IsAgent
	}
	status := uint(1) // 默认启用
	if req.Status != nil {
		status = uint(*req.Status)
	}
	userType := jyhapp.JyhAccountUser // 默认普通用户
	if req.UserType != "" {
		userType = req.UserType
	}

	// 设置默认直播权限
	canLiveStream := false
	if req.CanLiveStream != nil {
		canLiveStream = *req.CanLiveStream
	}

	// 设置默认签约状态
	contractStatus := 0 // 默认未签约
	if req.ContractStatus != nil {
		contractStatus = *req.ContractStatus
	}

	// 创建用户
	user := &jyhapp.JyhUser{
		Phone:          req.Phone,
		Username:       username,
		InviteCode:     inviteCode,
		InvitedBy:      inviterID,
		IsAgent:        isAgent,
		Status:         status,
		UserType:       userType,
		CanLiveStream:  canLiveStream,
		ContractStatus: contractStatus,
	}

	// 使用事务同时创建用户和用户扩展信息
	err = TryTransaction(func(tx *query.Query) error {
		// 创建用户
		if err := tx.JyhUser.Create(user); err != nil {
			return err
		}

		// 用户层级关系（如果有邀请人）
		if inviterID > 0 {
			err = gSrv.MaintainUserClosure(tx, user.ID, inviterID)
			if err != nil {
				return err
			}
		}

		// 创建用户扩展信息
		userExt := &jyhapp.JyhUserExt{
			UserID:         user.ID,
			FansCount:      req.FansCount,      // 使用原生的int字段
			ValidFansCount: req.ValidFansCount, // 使用原生的int字段
		}

		// 设置UserExtUpdateInfo中的指针字段（安全取值）
		if req.Age != nil {
			userExt.Age = *req.Age
		}
		if req.Sex != nil {
			userExt.Sex = *req.Sex
		}
		if req.IdCard != nil {
			userExt.IdCard = *req.IdCard
		}
		if req.City != nil {
			userExt.City = *req.City
		}
		if req.Occupation != nil {
			userExt.Occupation = *req.Occupation
		}
		if req.Address != nil {
			userExt.Address = *req.Address
		}
		if req.WeChat != nil {
			userExt.WeChat = *req.WeChat
		}
		if req.WeChatNickname != nil {
			userExt.WeChatNickname = *req.WeChatNickname
		}
		if req.DouYin != nil {
			userExt.DouYin = *req.DouYin
		}
		if req.DouYinNickname != nil {
			userExt.DouYinNickname = *req.DouYinNickname
		}
		if req.DouYinPhone != nil {
			userExt.DouYinPhone = *req.DouYinPhone
		}
		if req.QQ != nil {
			userExt.QQ = *req.QQ
		}
		if req.TrackType != nil {
			userExt.TrackType = *req.TrackType
		}
		if req.IsCarOwner != nil {
			userExt.IsCarOwner = *req.IsCarOwner
		}
		if req.CarOwnerTime != nil {
			userExt.CarOwnerTime = jtime.Str2Time(*req.CarOwnerTime)
		}

		if err := tx.JyhUserExt.Omit(tx.JyhUserExt.CarOwnerTime).Create(userExt); err != nil {
			return err
		}

		// 如果指定了等级，则分配等级
		if req.LevelID != nil && *req.LevelID > 0 {
			err = s.assignUserLevel(tx, user.ID, *req.LevelID)
			if err != nil {
				global.GVA_LOG.Error("分配用户等级失败", zap.Error(err))
				return err
			}
		}

		return nil
	})

	if err != nil {
		global.GVA_LOG.Error("创建用户失败", zap.Error(err))
		return nil, err
	}

	resp := &jyhResp.AdminUserCreateResp{
		ID:         user.ID,
		InviteCode: inviteCode,
	}

	return resp, nil
}

// AdminUpdateUser 管理员更新用户
func (s *JyhUserService) AdminUpdateUser(req *jyhReq.AdminUserReq) error {
	dbUser := query.JyhUser
	// 检查用户是否存在
	var user jyhapp.JyhUser
	err := dbUser.Where(dbUser.ID.Eq(req.ID)).Scan(&user)
	if err != nil {
		return errors.New("用户不存在")
	}
	if user.ID == 0 {
		return errors.New("用户不存在")
	}

	// 使用事务同时更新用户和用户扩展信息
	return TryTransaction(func(tx *query.Query) error {
		dbUser := tx.JyhUser

		// 构建用户基本信息更新字段
		updates := make(map[string]interface{})

		if req.Username != "" {
			updates["username"] = req.Username
		}

		if req.IsAgent != nil {
			updates["is_agent"] = *req.IsAgent
		}

		if req.Status != nil {
			updates["status"] = uint(*req.Status)
		}

		if req.UserType != "" {
			updates["user_type"] = req.UserType
		}

		if req.CanLiveStream != nil {
			updates["can_live_stream"] = *req.CanLiveStream
		}

		if req.ContractStatus != nil {
			updates["contract_status"] = *req.ContractStatus
		}

		// 执行用户基本信息更新（如果有更新字段）
		if len(updates) > 0 {
			_, err := dbUser.Where(dbUser.ID.Eq(req.ID)).Updates(updates)
			if err != nil {
				global.GVA_LOG.Error("更新用户基本信息失败", zap.Error(err))
				return err
			}
		}
		dbUserExt := tx.JyhUserExt

		// 查询用户扩展信息是否存在
		count, err := dbUserExt.Where(dbUserExt.UserID.Eq(req.ID)).Count()
		if err != nil {
			global.GVA_LOG.Error("查询用户扩展信息失败", zap.Error(err))
			return err
		}

		if count > 0 {
			// 存在则更新
			updatesExt := make(map[string]interface{})

			// 只更新传入的非null字段（包括空字符串）
			if req.Sex != nil {
				updatesExt["sex"] = *req.Sex
			}
			if req.Age != nil {
				updatesExt["age"] = *req.Age
			}
			if req.IdCard != nil {
				updatesExt["id_card"] = *req.IdCard
			}
			if req.City != nil {
				updatesExt["city"] = *req.City
			}
			if req.Occupation != nil {
				updatesExt["occupation"] = *req.Occupation
			}
			if req.Address != nil {
				updatesExt["address"] = *req.Address
			}
			if req.WeChat != nil {
				updatesExt["we_chat"] = *req.WeChat
			}
			if req.WeChatNickname != nil {
				updatesExt["we_chat_nickname"] = *req.WeChatNickname
			}
			if req.DouYin != nil {
				updatesExt["dou_yin"] = *req.DouYin
			}
			if req.DouYinNickname != nil {
				updatesExt["dou_yin_nickname"] = *req.DouYinNickname
			}
			if req.DouYinPhone != nil {
				updatesExt["dou_yin_phone"] = *req.DouYinPhone
			}
			if req.QQ != nil {
				updatesExt["qq"] = *req.QQ
			}
			if req.TrackType != nil {
				updatesExt["track_type"] = *req.TrackType
			}
			if req.IsCarOwner != nil {
				updatesExt["is_car_owner"] = *req.IsCarOwner
			}

			// 更新原生的粉丝数字段
			updatesExt["fans_count"] = req.FansCount
			updatesExt["valid_fans_count"] = req.ValidFansCount

			if req.CarOwnerTime != nil && req.IsCarOwner != nil && *req.IsCarOwner == true {
				updatesExt["car_owner_time"] = jtime.Str2Time(*req.CarOwnerTime)
			}

			if len(updatesExt) > 0 {
				_, err = dbUserExt.Where(dbUserExt.UserID.Eq(req.ID)).Updates(updatesExt)
				if err != nil {
					global.GVA_LOG.Error("更新用户扩展信息失败", zap.Error(err))
					return err
				}
			}
		} else {
			// 不存在则创建
			ext := jyhapp.JyhUserExt{
				UserID:         req.ID,
				FansCount:      req.FansCount,      // 使用原生的int字段
				ValidFansCount: req.ValidFansCount, // 使用原生的int字段
			}

			// 设置UserExtUpdateInfo中的指针字段（安全取值）
			if req.Age != nil {
				ext.Age = *req.Age
			}
			if req.Sex != nil {
				ext.Sex = *req.Sex
			}
			if req.IdCard != nil {
				ext.IdCard = *req.IdCard
			}
			if req.City != nil {
				ext.City = *req.City
			}
			if req.Occupation != nil {
				ext.Occupation = *req.Occupation
			}
			if req.Address != nil {
				ext.Address = *req.Address
			}
			if req.WeChat != nil {
				ext.WeChat = *req.WeChat
			}
			if req.WeChatNickname != nil {
				ext.WeChatNickname = *req.WeChatNickname
			}
			if req.DouYin != nil {
				ext.DouYin = *req.DouYin
			}
			if req.DouYinNickname != nil {
				ext.DouYinNickname = *req.DouYinNickname
			}
			if req.DouYinPhone != nil {
				ext.DouYinPhone = *req.DouYinPhone
			}
			if req.QQ != nil {
				ext.QQ = *req.QQ
			}
			if req.TrackType != nil {
				ext.TrackType = *req.TrackType
			}
			if req.IsCarOwner != nil {
				ext.IsCarOwner = *req.IsCarOwner
			}
			if req.CarOwnerTime != nil {
				ext.CarOwnerTime = jtime.Str2Time(*req.CarOwnerTime)
			}

			err = dbUserExt.Omit(dbUserExt.CarOwnerTime).Create(&ext)
			if err != nil {
				global.GVA_LOG.Error("创建用户扩展信息失败", zap.Error(err))
				return err
			}
		}

		// 处理等级分配/切换逻辑
		if req.LevelID != nil && *req.LevelID > 0 {
			// 验证等级是否存在
			levelDb := tx.JyhUserShipLevel
			_, err = levelDb.Where(levelDb.ID.Eq(*req.LevelID)).First()
			if err != nil {
				global.GVA_LOG.Error("指定的会员等级不存在", zap.Uint("levelID", *req.LevelID))
				return errors.New("指定的会员等级不存在")
			}

			// 切换用户等级（如果用户有当前等级会自动停止，然后分配新等级）
			err = s.switchUserLevel(tx, req.ID, *req.LevelID)
			if err != nil {
				global.GVA_LOG.Error("切换用户等级失败", zap.Error(err))
				return err
			}
		}

		return nil
	})
}

// AdminGetUserDetail 管理员获取用户详情
func (s *JyhUserService) AdminGetUserDetail(userID uint) (*jyhResp.AdminUserDetailResp, error) {
	dbUser := query.JyhUser

	// 获取用户基本信息
	user, err := dbUser.Where(dbUser.ID.Eq(userID)).First()
	if err != nil {
		return nil, errors.New("用户不存在")
	}

	// 查询用户扩展信息
	var userExt *jyhapp.JyhUserExt
	err = global.GVA_DB.Where("user_id = ?", userID).First(&userExt).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		global.GVA_LOG.Error("获取用户扩展信息失败", zap.Error(err))
	}

	// 获取邀请人信息
	var inviterName string
	if user.InvitedBy > 0 {
		inviter, err := dbUser.Where(dbUser.ID.Eq(user.InvitedBy)).First()
		if err == nil && inviter != nil {
			inviterName = inviter.Username
		}
	}

	// 转换用户类型
	userType := "user" // 默认普通用户
	if user.UserType == jyhapp.JyhAccountAdmin {
		userType = "admin"
	}

	// 获取用户当前等级ID（简化版本）
	currentLevelID := s.getCurrentUserLevelID(userID)

	// 构建基本信息
	basicInfo := &jyhResp.UserBasicInfo{
		ID:             user.ID,
		Username:       user.Username,
		Phone:          HideIdent(user.Phone),
		InviteCode:     user.InviteCode,
		InvitedBy:      user.InvitedBy,
		InviterName:    inviterName,
		IsAgent:        user.IsAgent,
		Status:         int(user.Status),
		UserType:       userType,
		CanLiveStream:  user.CanLiveStream,
		ContractStatus: user.ContractStatus,
		CreatedAt:      user.CreatedAt.Format("2006-01-02 15:04:05"),
		CurrentLevelID: currentLevelID,
	}

	// 添加用户扩展信息（如果存在），并对敏感信息进行脱敏
	if userExt != nil {
		basicInfo.IdCard = HideIdent(userExt.IdCard)
		basicInfo.Age = userExt.Age
		basicInfo.Sex = userExt.Sex
		basicInfo.City = userExt.City
		basicInfo.Occupation = userExt.Occupation
		basicInfo.Address = HideIdent(userExt.Address)
		basicInfo.WeChat = userExt.WeChat
		basicInfo.WeChatNickname = userExt.WeChatNickname
		basicInfo.DouYin = userExt.DouYin
		basicInfo.DouYinNickname = userExt.DouYinNickname
		basicInfo.DouYinPhone = HideIdent(userExt.DouYinPhone)
		basicInfo.QQ = userExt.QQ
		basicInfo.TrackType = userExt.TrackType
		basicInfo.IsCarOwner = userExt.IsCarOwner
		basicInfo.FansCount = userExt.FansCount
		basicInfo.ValidFansCount = userExt.ValidFansCount
		if !userExt.CarOwnerTime.IsZero() {
			basicInfo.CarOwnerTime = userExt.CarOwnerTime.Format("2006-01-02 15:04:05")
		}
		basicInfo.FansCount = userExt.FansCount
		basicInfo.ValidFansCount = userExt.ValidFansCount
	}

	// 获取统计数据
	// 邀请用户数
	invitedCount, _ := dbUser.Where(dbUser.InvitedBy.Eq(userID)).Count()

	// TODO: 获取订单数和总消费金额，需要订单表
	statistics := &jyhResp.UserStatistics{
		InvitedCount: invitedCount,
		OrderCount:   0, // 暂时设为0，需要订单表支持
		TotalAmount:  0, // 暂时设为0，需要订单表支持
	}

	// 获取被邀请用户列表（最近10个）
	invitedUsers, _ := dbUser.Where(dbUser.InvitedBy.Eq(userID)).Limit(10).Order(dbUser.CreatedAt.Desc()).Find()
	var invitedUserList []*jyhResp.InvitedUserItem
	for _, invitedUser := range invitedUsers {
		item := &jyhResp.InvitedUserItem{
			ID:        invitedUser.ID,
			Username:  invitedUser.Username,
			CreatedAt: invitedUser.CreatedAt.Format("2006-01-02 15:04:05"),
		}
		invitedUserList = append(invitedUserList, item)
	}

	resp := &jyhResp.AdminUserDetailResp{
		BasicInfo:    basicInfo,
		Statistics:   statistics,
		InvitedUsers: invitedUserList,
	}

	return resp, nil
}

// AdminToggleUserStatus 管理员切换用户状态
func (s *JyhUserService) AdminToggleUserStatus(userID uint, status int) error {
	dbUser := query.JyhUser

	// 检查用户是否存在
	_, err := dbUser.Where(dbUser.ID.Eq(userID)).First()
	if err != nil {
		return errors.New("用户不存在")
	}

	// 更新状态
	_, err = dbUser.Where(dbUser.ID.Eq(userID)).UpdateSimple(dbUser.Status.Value(uint(status)))
	if err != nil {
		global.GVA_LOG.Error("更新用户状态失败", zap.Error(err))
		return err
	}

	return nil
}

// UpdateUserInfo 更新用户社交媒体信息
func (s *JyhUserService) UpdateUserInfo(userID uint, req *jyhReq.UserExtUpdateInfo) error {
	dbUserExt := query.JyhUserExt

	// 检查用户是否存在
	userExt, err := dbUserExt.Where(dbUserExt.UserID.Eq(userID)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			userExt = &jyhapp.JyhUserExt{}
		} else {
			global.GVA_LOG.Error("查询用户扩展信息失败", zap.Error(err))
			return err
		}
	}

	// 保存更新
	var saveErr error
	if userExt.UserID == 0 {
		// 新记录，只设置传入的非null字段
		newUserExt := &jyhapp.JyhUserExt{
			UserID: userID,
		}

		// 只设置传入的非null字段
		if req.WeChatNickname != nil {
			newUserExt.WeChatNickname = *req.WeChatNickname
		}
		if req.WeChat != nil {
			newUserExt.WeChat = *req.WeChat
		}
		if req.DouYinNickname != nil {
			newUserExt.DouYinNickname = *req.DouYinNickname
		}
		if req.DouYin != nil {
			newUserExt.DouYin = *req.DouYin
		}
		if req.DouYinPhone != nil {
			newUserExt.DouYinPhone = *req.DouYinPhone
		}
		if req.QQ != nil {
			newUserExt.QQ = *req.QQ
		}
		if req.IdCard != nil {
			newUserExt.IdCard = *req.IdCard
		}
		if req.Sex != nil {
			newUserExt.Sex = *req.Sex
		}
		if req.City != nil {
			newUserExt.City = *req.City
		}
		if req.Occupation != nil {
			newUserExt.Occupation = *req.Occupation
		}
		if req.Address != nil {
			newUserExt.Address = *req.Address
		}
		if req.Age != nil {
			newUserExt.Age = *req.Age
		}
		if req.FansCount != nil {
			newUserExt.FansCount = *req.FansCount
		}
		if req.ValidFansCount != nil {
			newUserExt.ValidFansCount = *req.ValidFansCount
		}
		if req.TrackType != nil {
			newUserExt.TrackType = *req.TrackType
		}
		if req.IsCarOwner != nil {
			newUserExt.IsCarOwner = *req.IsCarOwner
		}

		saveErr = dbUserExt.Omit(dbUserExt.CarOwnerTime).Create(newUserExt)
	} else {
		// 更新现有记录，使用GORM的Updates方法进行动态更新
		updates := make(map[string]interface{})

		// 只更新传入的非null字段（包括空字符串，因为用户可能想清空数据）
		if req.WeChatNickname != nil {
			updates["we_chat_nickname"] = *req.WeChatNickname
		}
		if req.WeChat != nil {
			updates["we_chat"] = *req.WeChat
		}
		if req.DouYinNickname != nil {
			updates["dou_yin_nickname"] = *req.DouYinNickname
		}
		if req.DouYin != nil {
			updates["dou_yin"] = *req.DouYin
		}
		if req.DouYinPhone != nil {
			updates["dou_yin_phone"] = *req.DouYinPhone
		}
		if req.QQ != nil {
			updates["qq"] = *req.QQ
		}
		if req.IdCard != nil {
			updates["id_card"] = *req.IdCard
		}
		if req.Sex != nil {
			updates["sex"] = *req.Sex
		}
		if req.City != nil {
			updates["city"] = *req.City
		}
		if req.Occupation != nil {
			updates["occupation"] = *req.Occupation
		}
		if req.Address != nil {
			updates["address"] = *req.Address
		}
		if req.Age != nil {
			updates["age"] = *req.Age
		}
		if req.FansCount != nil {
			updates["fans_count"] = *req.FansCount
		}
		if req.ValidFansCount != nil {
			updates["valid_fans_count"] = *req.ValidFansCount
		}
		if req.TrackType != nil {
			updates["track_type"] = *req.TrackType
		}
		if req.IsCarOwner != nil {
			updates["is_car_owner"] = *req.IsCarOwner
		}

		// 如果有字段需要更新，则执行更新
		if len(updates) > 0 {
			_, saveErr = dbUserExt.Where(dbUserExt.UserID.Eq(userID)).Updates(updates)
			if saveErr != nil {
				global.GVA_LOG.Error("更新用户扩展信息失败", zap.Error(saveErr))
				return saveErr
			}
		}
	}
	return nil
}

// GetJyhUser 根据用户ID获取用户实体
func (s *JyhUserService) GetJyhUser(userID uint) (*jyhapp.JyhUser, error) {
	dbUser := query.JyhUser

	user, err := dbUser.Where(dbUser.ID.Eq(userID)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户不存在")
		}
		global.GVA_LOG.Error("查询用户失败", zap.Error(err), zap.Uint("userID", userID))
		return nil, err
	}

	return user, nil
}

// validateNewInviteCode 验证新的消耗型邀请码
func (s *JyhUserService) validateNewInviteCode(code string) (*jyhapp.JyhInviteCode, error) {
	if code == "" {
		return nil, errors.New("邀请码不能为空")
	}

	// 使用新的邀请码服务验证
	return gSrv.JyhInviteCodeService.ValidateInviteCode(code)
}

// getCurrentUserLevel 获取用户当前生效的等级信息
func (s *JyhUserService) getCurrentUserLevel(userID uint) (*jyhResp.UserLevelInfo, error) {
	dbUserLevel := query.JyhUserLevel
	dbLevel := query.JyhUserShipLevel

	// 查询用户当前生效的等级记录
	userLevel, err := dbUserLevel.Where(
		dbUserLevel.UserID.Eq(userID),
		dbUserLevel.Status.In("active", "pending"),
		dbUserLevel.StartAt.Lte(time.Now()),
		dbUserLevel.EndAt.Gt(time.Now()),
	).First()

	if err != nil {
		// 用户没有当前生效的等级
		return nil, nil
	}

	// 获取等级详情
	level, err := dbLevel.Where(dbLevel.ID.Eq(userLevel.LevelID)).First()
	if err != nil {
		global.GVA_LOG.Error("获取等级详情失败", zap.Error(err))
		return nil, err
	}

	return &jyhResp.UserLevelInfo{
		ID:           userLevel.ID,
		LevelID:      userLevel.LevelID,
		LevelName:    level.Name,
		LevelCode:    level.Code,
		LevelIcon:    level.Icon,
		PriceCents:   level.PriceCents,
		DurationDays: level.DurationDays,
		StartAt:      userLevel.StartAt.Format("2006-01-02 15:04:05"),
		EndAt:        userLevel.EndAt.Format("2006-01-02 15:04:05"),
		Status:       userLevel.Status,
	}, nil
}

// getCurrentUserLevelID 获取用户当前生效的等级ID（简化版本）
func (s *JyhUserService) getCurrentUserLevelID(userID uint) *uint {
	dbUserLevel := query.JyhUserLevel

	// 查询用户当前生效的等级记录
	userLevel, err := dbUserLevel.Where(
		dbUserLevel.UserID.Eq(userID),
		dbUserLevel.Status.In("active", "pending"),
		dbUserLevel.StartAt.Lte(time.Now()),
		dbUserLevel.EndAt.Gt(time.Now()),
	).First()

	if err != nil {
		// 用户没有当前生效的等级
		return nil
	}

	return &userLevel.LevelID
}

// assignUserLevel 为用户分配等级
func (s *JyhUserService) assignUserLevel(tx *query.Query, userID uint, levelID uint) error {
	userLevelDb := tx.JyhUserLevel
	levelDb := tx.JyhUserShipLevel

	// 检查是否已开通相同等级的有效记录
	existingCount, err := userLevelDb.Where(
		userLevelDb.UserID.Eq(userID),
		userLevelDb.LevelID.Eq(levelID),
		userLevelDb.Status.In("active", "pending"),
	).Count()
	if err != nil {
		global.GVA_LOG.Error("检查用户等级记录失败", zap.Error(err))
		return err
	}
	if existingCount > 0 {
		global.GVA_LOG.Info("用户已拥有该等级，跳过分配", zap.Uint("userID", userID), zap.Uint("levelID", levelID))
		return nil
	}

	// 查询等级信息
	level, err := levelDb.Where(levelDb.ID.Eq(levelID)).First()
	if err != nil {
		return errors.New("会员等级不存在")
	}

	// 计算开始时间和结束时间
	startAt := time.Now()
	var endAt time.Time

	// 根据等级有效期计算结束时间
	if level.DurationDays == 0 {
		// 0表示永久有效，设置为一个很远的日期
		endAt = time.Date(2099, 12, 31, 23, 59, 59, 0, time.Local)
	} else {
		// 按天数计算结束时间
		endAt = startAt.AddDate(0, 0, int(level.DurationDays))
	}

	// 创建用户等级记录（直接设置为active状态，无需审核）
	userLevel := &jyhapp.JyhUserLevel{
		UserID:  userID,
		LevelID: levelID,
		StartAt: startAt,
		EndAt:   endAt,
		Status:  "active", // 直接激活，无需审核
	}

	err = userLevelDb.Create(userLevel)
	if err != nil {
		global.GVA_LOG.Error("创建用户等级记录失败", zap.Error(err))
		return err
	}

	// 创建用户权益快照（使用权益服务的方法）
	err = gSrv.CreateUserBenefitSnapshotTx(tx, userLevel.ID, userID, levelID, startAt, endAt)
	if err != nil {
		global.GVA_LOG.Error("创建用户权益快照失败", zap.Error(err))
		return err
	}

	// 检查并处理邀请码生成权益
	err = gSrv.ProcessInviteCodeBenefit(tx, userID, levelID, endAt)
	if err != nil {
		global.GVA_LOG.Error("处理邀请码权益失败", zap.Error(err))
		// 不中断流程，只记录错误
	}

	global.GVA_LOG.Info("用户等级分配成功", zap.Uint("userID", userID), zap.Uint("levelID", levelID))
	return nil
}

// switchUserLevel 切换用户等级（停止旧等级，激活新等级）
func (s *JyhUserService) switchUserLevel(tx *query.Query, userID uint, newLevelID uint) error {
	userLevelDb := tx.JyhUserLevel

	// 查找用户当前有效的等级记录
	currentLevels, err := userLevelDb.Where(
		userLevelDb.UserID.Eq(userID),
		userLevelDb.Status.In("active", "pending"),
		userLevelDb.StartAt.Lte(time.Now()),
		userLevelDb.EndAt.Gt(time.Now()),
	).Find()
	if err != nil {
		global.GVA_LOG.Error("查询用户当前等级失败", zap.Error(err))
		return err
	}

	// 停止所有当前有效的等级
	for _, currentLevel := range currentLevels {
		if currentLevel.LevelID == newLevelID {
			// 如果目标等级与当前等级相同，不需要切换
			global.GVA_LOG.Info("用户已拥有目标等级，无需切换",
				zap.Uint("userID", userID),
				zap.Uint("levelID", newLevelID))
			return nil
		}

		// 停止当前等级（设置结束时间为当前时间）
		_, err = userLevelDb.Where(userLevelDb.ID.Eq(currentLevel.ID)).
			UpdateSimple(userLevelDb.Status.Value("cancelled"), userLevelDb.EndAt.Value(time.Now()))
		if err != nil {
			global.GVA_LOG.Error("停止用户当前等级失败", zap.Error(err))
			return err
		}

		global.GVA_LOG.Info("已停止用户等级",
			zap.Uint("userID", userID),
			zap.Uint("oldLevelID", currentLevel.LevelID),
			zap.Uint("userLevelID", currentLevel.ID))
	}

	// 分配新等级
	err = s.assignUserLevel(tx, userID, newLevelID)
	if err != nil {
		global.GVA_LOG.Error("分配新等级失败", zap.Error(err))
		return err
	}

	global.GVA_LOG.Info("用户等级切换成功",
		zap.Uint("userID", userID),
		zap.Uint("newLevelID", newLevelID))

	return nil
}

// ReviewCertificate 用户粉丝审核凭证
func (s *JyhUserService) ReviewCertificate(userID uint, req *jyhReq.CertificateReview) (err error) {
	err = TryTransaction(func(tx *query.Query) (err error) {
		var dbUserCertificate = tx.JyhUserCertificate

		// 检查提交的凭证记录是否存在
		if req.CertificateID == 0 {
			return errors.New("凭证ID不能为空")
		}
		if req.Status != "approved" && req.Status != "rejected" {
			return errors.New("审核状态必须为 approved 或 rejected")
		}
		// 查询凭证记录
		userCertificate, err := dbUserCertificate.Where(dbUserCertificate.ID.Eq(req.CertificateID)).First()
		if err != nil {
			global.GVA_LOG.Error("查询凭证记录失败", zap.Error(err), zap.Uint("certificateID", req.CertificateID))
			return errors.New("凭证记录不存在")
		}
		if userCertificate.ID == 0 {
			return errors.New("凭证记录不存在")
		}
		/*// 检查用户是否有权限审核
		  if !gSrv.IsUserAdmin(userID) {
		  	global.GVA_LOG.Error("用户没有权限审核凭证", zap.Uint("userID", userID))
		  	return errors.New("您没有权限审核凭证")
		  }*/
		// 检查状态
		if userCertificate.Status != "pending" {
			return errors.New("该凭证已审核")
		}

		// 更新审核信息
		now := time.Now()
		updates := map[string]interface{}{
			"status":      req.Status,
			"remark":      req.Remark,
			"reviewer_id": userID,
			"reviewed_at": &now,
		}
		_, err = dbUserCertificate.Where(dbUserCertificate.ID.Eq(req.CertificateID)).Updates(updates)
		if err != nil {
			global.GVA_LOG.Error("更新凭证审核状态失败", zap.Error(err), zap.Uint("certificateID", req.CertificateID))
			return errors.New("更新凭证审核状态失败")
		}

		// 如果审核通过，更新用户扩展信息(粉丝数和有效粉丝数)
		if req.Status == "approved" {
			_, err = tx.JyhUserExt.Where(tx.JyhUserExt.UserID.Eq(userCertificate.UserID)).Updates(map[string]interface{}{
				"fans_count":       userCertificate.FansCount,
				"valid_fans_count": userCertificate.ValidFansCount,
				"track_type":       jyhapp.TrackTypeHangCar,
			})
			if err != nil {
				global.GVA_LOG.Error("更新用户扩展信息失败", zap.Error(err), zap.Uint("userID", userCertificate.UserID))
				return errors.New("更新用户扩展信息失败")
			}
		}
		return
	})
	return
}

// GetAllCertificates 获取所有用户粉丝凭证列表（后台管理）
func (s *JyhUserService) GetAllCertificates(req *jyhReq.CertificateSearch) ([]jyhapp.JyhUserCertificate, int64, error) {
	db := global.GVA_DB

	query := db.Model(&jyhapp.JyhUserCertificate{})

	// 添加查询条件
	if req.UserID != nil {
		query = query.Where("user_id = ?", *req.UserID)
	}
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}
	if req.ReviewerID != nil {
		query = query.Where("reviewer_id = ?", *req.ReviewerID)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页处理
	if req.Page > 0 && req.PageSize > 0 {
		offset := (req.Page - 1) * req.PageSize
		query = query.Offset(offset).Limit(req.PageSize)
	}

	// 排序
	query = query.Order("created_at DESC")

	// 查询结果
	var certificates []jyhapp.JyhUserCertificate
	if err := query.Preload("User").Preload("Reviewer").Find(&certificates).Error; err != nil {
		return nil, 0, err
	}

	return certificates, total, nil
}

// GetCertificateDetail 获取凭证详情
func (s *JyhUserService) GetCertificateDetail(certificateID uint) (*jyhapp.JyhUserCertificate, error) {
	db := global.GVA_DB

	var certificate jyhapp.JyhUserCertificate
	if err := db.Preload("User").First(&certificate, certificateID).Error; err != nil {
		return nil, err
	}

	return &certificate, nil
}

// CreateCertificate 创建凭证记录
func (s *JyhUserService) CreateCertificate(userID uint, req jyhReq.UploadCertificateReq) error {
	dbUserCertificate := query.JyhUserCertificate

	// 创建凭证记录
	certificate := &jyhapp.JyhUserCertificate{
		UserID:          userID,
		FansCount:       req.FansCount,
		ValidFansCount:  req.ValidFansCount,
		CertificateFile: req.CertificateFile,
	}

	err := dbUserCertificate.Create(certificate)
	if err != nil {
		global.GVA_LOG.Error("创建用户粉丝凭证失败", zap.Error(err), zap.Uint("userID", userID))
		return err
	}
	// 发送通知给管理员
	/*if err := gSrv.SendCertificateNotification(certificate.ID, userID); err != nil {
		global.GVA_LOG.Error("发送凭证通知失败", zap.Error(err), zap.Uint("certificateID", certificate.ID))
		return err
	}*/
	return nil
}
