package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/shopspring/decimal"
)

// AddCommissionIncomeReq 添加佣金收入请求
type AddCommissionIncomeReq struct {
	UserID            uint            `json:"user_id" binding:"required" validate:"required"`                               // 用户ID
	Amount            decimal.Decimal `json:"amount" binding:"required" validate:"required,gt=0"`                           // 佣金金额
	SourceType        string          `json:"source_type" binding:"required,oneof=dy jyh" validate:"required,oneof=dy jyh"` // 资金来源: dy-抖音佣金, jyh-JYH佣金
	RelatedBusinessID *uint           `json:"related_business_id"`                                                          // 相关业务ID
	Description       string          `json:"description" validate:"max=255"`                                               // 交易描述
}

// ProcessWithdrawalReq 处理提现请求
type ProcessWithdrawalReq struct {
	Amount            decimal.Decimal `json:"amount" binding:"required" validate:"required,gt=0"` // 提现金额
	RelatedBusinessID *uint           `json:"related_business_id"`                                // 相关业务ID
	Description       string          `json:"description" validate:"max=255"`                     // 交易描述
}

// UserAccountListReq 用户账户列表请求
type UserAccountListReq struct {
	request.PageInfo
	UserID     *uint            `json:"user_id" form:"user_id"`         // 用户ID
	Username   string           `json:"username" form:"username"`       // 用户名（模糊搜索）
	Phone      string           `json:"phone" form:"phone"`             // 手机号（模糊搜索）
	MinBalance *decimal.Decimal `json:"min_balance" form:"min_balance"` // 最小余额
	MaxBalance *decimal.Decimal `json:"max_balance" form:"max_balance"` // 最大余额
}

// TransactionListReq 交易记录列表请求
type TransactionListReq struct {
	request.PageInfo
	AccountID         *uint            `json:"account_id" form:"account_id"`                 // 账户ID
	UserID            *uint            `json:"user_id" form:"user_id"`                       // 用户ID
	TransactionType   string           `json:"transaction_type" form:"transaction_type"`     // 交易类型
	SourceType        string           `json:"source_type" form:"source_type"`               // 资金来源
	TransactionStatus string           `json:"transaction_status" form:"transaction_status"` // 交易状态
	StartTime         string           `json:"start_time" form:"start_time"`                 // 开始时间
	EndTime           string           `json:"end_time" form:"end_time"`                     // 结束时间
	MinAmount         *decimal.Decimal `json:"min_amount" form:"min_amount"`                 // 最小金额
	MaxAmount         *decimal.Decimal `json:"max_amount" form:"max_amount"`                 // 最大金额
}

// AdjustBalanceReq 调整余额请求（管理员功能）
type AdjustBalanceReq struct {
	UserID      uint            `json:"user_id" binding:"required" validate:"required"`                               // 用户ID
	Amount      decimal.Decimal `json:"amount" binding:"required" validate:"required"`                                // 调整金额（可为负数）
	SourceType  string          `json:"source_type" binding:"required,oneof=dy jyh" validate:"required,oneof=dy jyh"` // 影响的余额类型
	Description string          `json:"description" binding:"required" validate:"required,max=255"`                   // 调整原因
}

// ValidateBalanceReq 验证余额一致性请求
type ValidateBalanceReq struct {
	AccountID *uint `json:"account_id" form:"account_id"` // 账户ID，为空时验证所有账户
}
