package jyhapp

import (
	"encoding/json"
	"errors"
	"fmt"
	"gorm.io/datatypes"
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/query"
	jyhReq "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/request"
	jyhResp "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/mcn"
	"go.uber.org/zap"
)

type JyhUserContractService struct{}

// 签约场景常量
const (
	SmsSceneContract = "contract" // 签约
)

// SendContractVerificationCode 发送签约验证码
func (s *JyhUserContractService) SendContractVerificationCode(uid uint) error {
	dbUser := query.JyhUser
	user, err := dbUser.Where(dbUser.ID.Eq(uid)).First()
	if err != nil {
		return errors.New("用户不存在")
	}
	// 验证手机号格式
	if !isValidPhoneNumber(user.Phone) {
		return errors.New("无效的手机号码格式")
	}
	// 创建MCN客户端
	mcnClient := mcn.NewMCNClient()
	// 调用MCN发送验证码接口
	result, err := mcnClient.SendVerificationCode(user.Phone)
	if err != nil {
		global.GVA_LOG.Error("发送签约验证码失败", zap.Error(err))
		return errors.New("发送签约验证码失败: " + err.Error())
	}

	// 解析响应
	var resp map[string]interface{}
	if err := json.Unmarshal([]byte(result), &resp); err != nil {
		global.GVA_LOG.Error("解析签约验证码响应失败", zap.Error(err))
		return errors.New("解析签约验证码响应失败")
	}

	// 检查响应状态
	if code, ok := resp["code"].(float64); !ok || code != 0 {
		msg := "未知错误"
		if m, ok := resp["msg"].(string); ok {
			msg = m
		}
		return errors.New("发送签约验证码失败: " + msg)
	}

	return nil
}

// VerifyContractCode 验证签约验证码
func (s *JyhUserContractService) VerifyContractCode(uid uint, req jyhReq.VerifyContractCodeReq) (*jyhResp.VerifyContractCodeResp, error) {
	dbUser := query.JyhUser
	user, err := dbUser.Where(dbUser.ID.Eq(uid)).First()
	if err != nil {
		return nil, errors.New("用户不存在")
	}
	// 验证手机号格式
	if !isValidPhoneNumber(user.Phone) {
		return nil, errors.New("无效的手机号码格式")
	}

	// 创建MCN客户端
	mcnClient := mcn.NewMCNClient()

	// 调用MCN验证验证码接口
	result, err := mcnClient.VerifyCode(req.Code, user.Phone)
	if err != nil {
		global.GVA_LOG.Error("验证签约验证码失败", zap.Error(err))
		return nil, errors.New("验证签约验证码失败: " + err.Error())
	}
	// 解析响应
	var resp map[string]interface{}
	if err := json.Unmarshal([]byte(result), &resp); err != nil {
		global.GVA_LOG.Error("解析验证签约验证码响应失败", zap.Error(err))
		return nil, errors.New("解析验证签约验证码响应失败")
	}

	// 检查响应状态
	if code, ok := resp["code"].(float64); !ok || code != 0 {
		msg := "未知错误"
		if m, ok := resp["msg"].(string); ok {
			msg = m
		}
		return nil, errors.New("验证签约验证码失败: " + msg)
	}
	fmt.Println(resp)
	// 提取ticket
	ticket := ""
	if data, ok := resp["data"].(map[string]interface{}); ok {
		if t, ok := data["ticket"].(string); ok {
			ticket = t
		}
	}

	if ticket == "" {
		return nil, errors.New("获取ticket失败")
	}

	return &jyhResp.VerifyContractCodeResp{
		Ticket: ticket,
	}, nil
}

// GetUserInfoByTicket 根据ticket获取用户信息
func (s *JyhUserContractService) GetUserInfoByTicket(uid uint, req *jyhReq.GetUserInfoByTicketReq) (*jyhResp.MCNUserInfo, error) {
	phone := req.Phone
	if phone == "" {
		dbUser := query.JyhUser
		user, err := dbUser.Where(dbUser.ID.Eq(uid)).First()
		if err != nil {
			return nil, errors.New("用户不存在")
		}
		// 验证手机号格式
		if !isValidPhoneNumber(user.Phone) {
			return nil, errors.New("无效的手机号码格式")
		}
		phone = user.Phone
	}
	// 创建MCN客户端
	mcnClient := mcn.NewMCNClient()

	// 调用MCN获取用户信息接口
	result, err := mcnClient.GetUserInfoByTicket(phone, req.Ticket)
	if err != nil {
		global.GVA_LOG.Error("获取用户信息失败", zap.Error(err))
		return nil, errors.New("获取用户信息失败: " + err.Error())
	}
	// 解析响应
	var res map[string]interface{}
	if err = json.Unmarshal([]byte(result), &res); err != nil {
		global.GVA_LOG.Error("获取用户信息失败", zap.Error(err))
		return nil, errors.New("获取用户信息失败")
	}

	// 检查响应状态
	if code, ok := res["code"].(float64); !ok || code != 0 {
		msg := "未知错误"
		if m, ok := res["msg"].(string); ok {
			msg = m
		}
		return nil, errors.New("获取用户信息失败: " + msg)
	}
	// 解析响应
	type Dto struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data struct {
			UserList []jyhResp.MCNUserInfo `json:"user_info_list"`
		} `json:"data"`
	}
	var resp Dto
	if err := json.Unmarshal([]byte(result), &resp); err != nil {
		global.GVA_LOG.Error("解析获取用户信息响应失败", zap.Error(err))
		return nil, errors.New("解析获取用户信息响应失败")
	}
	if resp.Code != 0 {
		return nil, errors.New(resp.Msg)
	}
	userInfo := &jyhResp.MCNUserInfo{}
	if len(resp.Data.UserList) > 0 {
		// 提取用户信息
		userInfo = &resp.Data.UserList[0]
	}
	if userInfo.UserId == "" {
		return nil, errors.New("获取用户ID失败")
	}
	//更新到用户表
	ext, _ := json.Marshal(userInfo)
	dbUser := query.JyhUser
	_, err = dbUser.Where(dbUser.ID.Eq(uid)).UpdateSimple(
		dbUser.Username.Value(userInfo.UserName),
		dbUser.Avatar.Value(userInfo.AvatarImg),
		dbUser.Ext.Value(datatypes.JSON(ext)),
		dbUser.DouYinId.Value(userInfo.UserId),
	)
	if err != nil {
		return nil, errors.New("更新用户信息失败")
	}
	return userInfo, nil
}

// AddAccountBind 绑定用户账号到机构
func (s *JyhUserContractService) AddAccountBind(uid uint, req *jyhReq.AddAccountBindReq) (*jyhResp.AddAccountBindResp, error) {
	phone := req.Phone
	if phone == "" {
		dbUser := query.JyhUser
		user, err := dbUser.Where(dbUser.ID.Eq(uid)).First()
		if err != nil {
			return nil, errors.New("用户不存在")
		}
		// 验证手机号格式
		if !isValidPhoneNumber(user.Phone) {
			return nil, errors.New("无效的手机号码格式")
		}
		phone = user.Phone
	}
	// 创建MCN客户端
	mcnClient := mcn.NewMCNClient()

	// 调用MCN绑定账号接口
	result, err := mcnClient.AddAccountBind(phone, req.UserID)
	if err != nil {
		global.GVA_LOG.Error("绑定用户账号失败", zap.Error(err))
		return nil, errors.New("绑定用户账号失败: " + err.Error())
	}

	// 解析响应
	var resp map[string]interface{}
	if err := json.Unmarshal([]byte(result), &resp); err != nil {
		global.GVA_LOG.Error("解析绑定用户账号响应失败", zap.Error(err))
		return nil, errors.New("解析绑定用户账号响应失败")
	}
	// 检查响应状态
	if code, ok := resp["code"].(float64); !ok || code != 0 {
		msg := "未知错误"
		if m, ok := resp["msg"].(string); ok {
			msg = m
		}
		if msg != "达人已与其他机构绑定，暂不支持绑定" {
			return nil, errors.New("绑定用户账号失败: " + msg)
		}
	}

	// 提取绑定ID
	bindID := ""
	dbUser := query.JyhUser
	_, err = dbUser.Where(dbUser.ID.Eq(uid)).UpdateSimple(
		dbUser.ContractStatus.Value(2),
	)
	if err != nil {
		return nil, errors.New("绑定用户到机构失败")
	}
	return &jyhResp.AddAccountBindResp{
		BindID: bindID,
	}, nil
}

// AddUserBindPid 绑定结算账号
func (s *JyhUserContractService) AddUserBindPid(req *jyhReq.AddUserBindPidReq) (*jyhResp.AddUserBindPidResp, error) {
	// 创建MCN客户端
	mcnClient := mcn.NewMCNClient()

	// 调用MCN绑定结算账号接口
	result, err := mcnClient.AddUserBindPid(req.UserID, req.Pid)
	if err != nil {
		global.GVA_LOG.Error("绑定结算账号失败", zap.Error(err))
		return nil, errors.New("绑定结算账号失败: " + err.Error())
	}

	// 解析响应
	var resp map[string]interface{}
	if err := json.Unmarshal([]byte(result), &resp); err != nil {
		global.GVA_LOG.Error("解析绑定结算账号响应失败", zap.Error(err))
		return nil, errors.New("解析绑定结算账号响应失败")
	}

	// 检查响应状态
	if code, ok := resp["code"].(float64); !ok || code != 0 {
		msg := "未知错误"
		if m, ok := resp["msg"].(string); ok {
			msg = m
		}
		return nil, errors.New("绑定结算账号失败: " + msg)
	}

	// 提取绑定ID
	bindID := ""
	if data, ok := resp["data"].(map[string]interface{}); ok {
		if id, ok := data["bind_id"].(string); ok {
			bindID = id
		}
	}

	// 更新用户签约状态
	// 从用户ID获取用户
	userID, _ := strconv.ParseUint(req.UserID, 10, 64)
	if userID > 0 {
		dbUser := query.JyhUser
		updates := map[string]interface{}{
			"contract_status": 1, // 设置为已签约
		}
		_, err = dbUser.Where(dbUser.ID.Eq(uint(userID))).Updates(updates)
		if err != nil {
			global.GVA_LOG.Error("更新用户签约状态失败", zap.Error(err))
			// 不返回错误，因为主要流程已完成
		}
	}

	return &jyhResp.AddUserBindPidResp{
		BindID: bindID,
	}, nil
}
