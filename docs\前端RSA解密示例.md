# 保利威认证信息RSA解密 - 前端使用指南

## 概述

本文档介绍如何在前端使用RSA非对称加密来解密保利威的认证信息（AppId和SecretKey）。

## 工作流程

1. **服务端**：使用RSA公钥加密保利威认证信息
2. **前端**：获取RSA私钥，用于解密认证信息
3. **前端**：解密后获得AppId和SecretKey，用于调用保利威API

## API接口

### 1. 获取RSA密钥对
```
GET /api/live/rsa_keys
```

响应：
```json
{
    "code": 0,
    "data": {
        "privateKey": "-----BEGIN RSA PRIVATE KEY-----\n...\n-----END RSA PRIVATE KEY-----",
        "publicKey": "-----BEGIN PUBLIC KEY-----\n...\n-----END PUBLIC KEY-----"
    },
    "msg": "操作成功"
}
```

### 2. 创建直播房间（包含加密认证信息）
```
POST /api/live/start_live
```

响应中包含：
```json
{
    "code": 0,
    "data": {
        "channelId": 123456,
        "authInfo": {
            "data": "base64加密的认证信息",
            "publicKey": "RSA公钥",
            "timestamp": 1234567890
        }
    }
}
```

## JavaScript解密示例

### 安装依赖

```bash
npm install node-rsa
```

### 解密代码示例

```javascript
const NodeRSA = require('node-rsa');

class PolyvAuthDecryptor {
    constructor() {
        this.privateKey = null;
        this.publicKey = null;
    }

    // 获取RSA密钥对
    async getRSAKeys() {
        try {
            const response = await fetch('/api/live/rsa_keys', {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + localStorage.getItem('token'),
                    'Content-Type': 'application/json'
                }
            });
            
            const result = await response.json();
            if (result.code === 0) {
                this.privateKey = result.data.privateKey;
                this.publicKey = result.data.publicKey;
                return true;
            }
            throw new Error(result.msg || '获取密钥失败');
        } catch (error) {
            console.error('获取RSA密钥失败:', error);
            return false;
        }
    }

    // 解密认证信息
    decryptAuthInfo(encryptedData) {
        if (!this.privateKey) {
            throw new Error('私钥未加载，请先调用getRSAKeys()');
        }

        try {
            // 创建RSA实例
            const key = new NodeRSA();
            key.importKey(this.privateKey, 'pkcs1-private-pem');
            
            // 设置解密选项
            key.setOptions({
                encryptionScheme: 'pkcs1'
            });

            // Base64解码
            const encryptedBuffer = Buffer.from(encryptedData, 'base64');
            
            // 分块解密
            let result = Buffer.alloc(0);
            let offset = 0;
            
            while (offset < encryptedBuffer.length) {
                // 读取块长度（2字节）
                if (offset + 2 > encryptedBuffer.length) break;
                
                const blockLen = (encryptedBuffer[offset] << 8) | encryptedBuffer[offset + 1];
                offset += 2;
                
                // 读取块数据
                if (offset + blockLen > encryptedBuffer.length) break;
                
                const blockData = encryptedBuffer.slice(offset, offset + blockLen);
                offset += blockLen;
                
                // 解密块
                const decryptedBlock = key.decrypt(blockData);
                result = Buffer.concat([result, decryptedBlock]);
            }
            
            // 解析JSON
            const authInfo = JSON.parse(result.toString('utf8'));
            
            // 检查时间戳（30分钟有效期）
            const now = Math.floor(Date.now() / 1000);
            if (now - authInfo.timestamp > 1800) {
                throw new Error('认证信息已过期');
            }
            
            return authInfo;
        } catch (error) {
            console.error('解密失败:', error);
            throw new Error('解密认证信息失败: ' + error.message);
        }
    }

    // 完整的工作流程示例
    async initializePolyv() {
        try {
            // 1. 获取密钥对
            const keysLoaded = await this.getRSAKeys();
            if (!keysLoaded) {
                throw new Error('无法获取RSA密钥');
            }

            // 2. 创建直播房间
            const response = await fetch('/api/live/start_live', {
                method: 'POST',
                headers: {
                    'Authorization': 'Bearer ' + localStorage.getItem('token'),
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    channelName: '我的直播间',
                    password: '123456'
                })
            });

            const result = await response.json();
            if (result.code !== 0) {
                throw new Error(result.msg || '创建直播房间失败');
            }

            // 3. 解密认证信息
            if (result.data.authInfo && result.data.authInfo.data) {
                const authInfo = this.decryptAuthInfo(result.data.authInfo.data);
                
                console.log('保利威认证信息:', {
                    appId: authInfo.appId,
                    secretKey: authInfo.secretKey,
                    timestamp: authInfo.timestamp
                });

                // 4. 使用认证信息初始化保利威SDK
                // 这里可以调用保利威的JavaScript SDK
                return {
                    channelInfo: result.data,
                    polyvAuth: authInfo
                };
            }

        } catch (error) {
            console.error('初始化失败:', error);
            throw error;
        }
    }
}

// 使用示例
const decryptor = new PolyvAuthDecryptor();

// 方式一：完整流程
decryptor.initializePolyv()
    .then(result => {
        console.log('初始化成功:', result);
        // 使用result.polyvAuth中的appId和secretKey
    })
    .catch(error => {
        console.error('初始化失败:', error);
    });

// 方式二：单独解密
decryptor.getRSAKeys()
    .then(() => {
        const encryptedData = "base64_encrypted_data_here";
        const authInfo = decryptor.decryptAuthInfo(encryptedData);
        console.log('解密成功:', authInfo);
    })
    .catch(error => {
        console.error('操作失败:', error);
    });
```

### 浏览器环境（使用Web Crypto API）

如果不想使用Node.js库，也可以使用浏览器原生的Web Crypto API：

```javascript
class WebCryptoDecryptor {
    constructor() {
        this.privateKey = null;
        this.publicKey = null;
    }

    // 将PEM格式转换为ArrayBuffer
    pemToArrayBuffer(pem, type) {
        const b64Lines = pem.replace(`-----BEGIN ${type}-----`, '')
                           .replace(`-----END ${type}-----`, '')
                           .replace(/\s/g, '');
        const binaryString = atob(b64Lines);
        const bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
            bytes[i] = binaryString.charCodeAt(i);
        }
        return bytes.buffer;
    }

    // 导入私钥
    async importPrivateKey(pemKey) {
        const keyData = this.pemToArrayBuffer(pemKey, 'RSA PRIVATE KEY');
        this.privateKey = await window.crypto.subtle.importKey(
            'pkcs8',
            keyData,
            {
                name: 'RSA-OAEP',
                hash: 'SHA-256',
            },
            false,
            ['decrypt']
        );
    }

    // 解密数据
    async decrypt(encryptedData) {
        if (!this.privateKey) {
            throw new Error('私钥未导入');
        }

        const encryptedBuffer = new Uint8Array(
            atob(encryptedData).split('').map(char => char.charCodeAt(0))
        );

        const decryptedBuffer = await window.crypto.subtle.decrypt(
            'RSA-OAEP',
            this.privateKey,
            encryptedBuffer
        );

        const decoder = new TextDecoder();
        return JSON.parse(decoder.decode(decryptedBuffer));
    }
}
```

## 注意事项

1. **安全性**：私钥应该安全存储，不要在代码中硬编码
2. **时效性**：认证信息有30分钟的有效期，过期需要重新获取
3. **错误处理**：应该妥善处理解密失败的情况
4. **网络请求**：在生产环境中应该使用HTTPS确保传输安全

## 常见问题

### Q: 解密失败怎么办？
A: 检查以下几点：
- 确保私钥格式正确
- 确认加密数据未被篡改
- 检查时间戳是否在有效期内

### Q: 可以缓存密钥对吗？
A: 可以，但建议定期更新以提高安全性

### Q: 前端直接获取私钥安全吗？
A: 这种方式主要是为了身份验证而非数据保密，实际应用中应该评估安全风险 