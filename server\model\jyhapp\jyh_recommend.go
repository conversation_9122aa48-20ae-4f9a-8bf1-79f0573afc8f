package jyhapp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"time"
)

// JyhRecommendPosition 推荐位管理
type JyhRecommendPosition struct {
	global.GVA_MODEL
	Name        string `gorm:"type:varchar(100);not null;comment:推荐位名称" json:"name"`
	PositionKey string `gorm:"type:varchar(50);uniqueIndex;not null;comment:推荐位KEY" json:"positionKey"`
	//PositionType string `gorm:"type:enum('banner','marquee','promotion');default:'banner'" json:"positionType"`
	Description string `gorm:"type:varchar(255);comment:推荐位描述" json:"description"`
	Width       int    `gorm:"comment:推荐位宽度" json:"width"`
	Height      int    `gorm:"comment:推荐位高度" json:"height"`
	IsEnabled   bool   `gorm:"default:true;comment:是否启用" json:"isEnabled"`
	MaxItems    int    `gorm:"default:10;comment:最大内容项数" json:"maxItems"`

	Items []JyhRecommendItem `gorm:"foreignKey:PositionID" json:"items"`
}

// TableName JyhRecommendPosition 推荐位管理
func (JyhRecommendPosition) TableName() string {
	return "jyh_recommend_position"
}

// JyhRecommendItem 推荐内容项
type JyhRecommendItem struct {
	global.GVA_MODEL
	PositionID   uint      `gorm:"not null;index;comment:推荐位ID" json:"positionID"`
	Title        string    `gorm:"type:varchar(100);not null;comment:标题" json:"title"`
	ContentType  string    `gorm:"type:enum('image','text','video');default:'image';comment:类型" json:"contentType"`
	ContentValue string    `gorm:"type:varchar(500);not null;comment:内容" json:"contentValue"`
	LinkUrl      string    `gorm:"type:varchar(500);comment:链接" json:"linkUrl"`
	BgColor      string    `gorm:"type:varchar(20);default:'#FFFFFF';comment:背景颜色" json:"bgColor"`
	TextColor    string    `gorm:"type:varchar(20);default:'#000000';comment:文本颜色" json:"textColor"`
	StartTime    time.Time `gorm:"index;comment:开始时间" json:"startTime"`
	EndTime      time.Time `gorm:"index;comment:结束时间" json:"endTime"`
	Sort         int       `gorm:"default:0;comment:排序号" json:"sort"`
	Status       int       `gorm:"default:0;comment:状态" json:"status"`

	Position JyhRecommendPosition `gorm:"foreignKey:PositionID" json:"position"`
}

// TableName JyhRecommendation 推荐位管理
func (JyhRecommendItem) TableName() string {
	return "jyh_recommend_item"
}

// JyhRecommendDisplayLog 推荐位展示记录
type JyhRecommendDisplayLog struct {
	ID          uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	ItemID      uint      `gorm:"not null;index" json:"itemID"`
	PositionID  uint      `gorm:"not null;index" json:"positionID"`
	DisplayTime time.Time `gorm:"index" json:"displayTime"`
	ClickTime   time.Time `json:"clickTime"`
	UserAgent   string    `gorm:"type:varchar(500)" json:"userAgent"`
	IPAddress   string    `gorm:"type:varchar(50)" json:"IPAddress"`
	Referer     string    `gorm:"type:varchar(500)" json:"referer"`
	DeviceType  string    `gorm:"type:enum('pc','mobile','tablet');default:'mobile'" json:"deviceType"`

	Item     JyhRecommendItem     `gorm:"foreignKey:ItemID" json:"item"`
	Position JyhRecommendPosition `gorm:"foreignKey:PositionID" json:"position"`
}

// TableName RecommendDisplayLog 推荐位展示记录
func (JyhRecommendDisplayLog) TableName() string {
	return "jyh_recommend_display_log"
}
