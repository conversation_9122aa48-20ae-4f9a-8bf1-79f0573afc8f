package request

import "github.com/flipped-aurora/gin-vue-admin/server/model/common/request"

type GetPointsRecords struct {
	UserID uint `json:"userID" form:"userID"` // 用户ID
	request.PageInfo
}

// PointsExchangeRequest 兑换请求
type PointsExchangeRequest struct {
	MaterialID uint                   `json:"materialID" binding:"required"`
	Quantity   int                    `json:"quantity" binding:"min=1"`
	Address    map[string]interface{} `json:"address"` // 收货地址信息
}

// PointsAddressInfo 地址信息结构示例
type PointsAddressInfo struct {
	Receiver   string `json:"receiver" binding:"required"`
	Phone      string `json:"phone" binding:"required"`
	Region     string `json:"region" binding:"required"` // 省市区
	Detail     string `json:"detail" binding:"required"` // 详细地址
	PostalCode string `json:"postalCode"`
}

// GetUserExchanges 获取用户兑换记录请求
type GetUserExchanges struct {
	UserID uint `json:"userID" form:"userID"` // 用户ID
	request.PageInfo
}

type GetPointsRules struct {
	IsEnable *bool `json:"isEnable" form:"isEnable"` // 是否启用
	request.PageInfo
}
