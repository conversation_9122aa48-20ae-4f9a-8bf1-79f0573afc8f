package jyhapp

import (
	"github.com/gin-gonic/gin"

	v1 "github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
)

type LiveRouter struct{}

// InitLiveRouter 初始化直播路由
func (r *LiveRouter) InitLiveRouter(Router *gin.RouterGroup) {
	liveRouter := Router.Group("live").Use(middleware.JWTAuth())

	liveApi := v1.ApiGroupApp.JyhApiGroup.LiveApi
	{
		// 获取频道详细信息列表
		liveRouter.GET("channels", liveApi.GetChannelDetailList) // 获取频道详细信息列表
		// 用户端开播相关接口
		liveRouter.POST("start_live", liveApi.StartLive)     // 用户自行开播
		liveRouter.GET("current", liveApi.UserLiveRoom)      // 获取当前用户的直播房间
		liveRouter.GET("auth_info", liveApi.GetLiveAuthInfo) // 获取直播加密参数
	}
}
