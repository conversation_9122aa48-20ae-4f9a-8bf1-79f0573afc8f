// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
)

func newJyhInviteCode(db *gorm.DB, opts ...gen.DOOption) jyhInviteCode {
	_jyhInviteCode := jyhInviteCode{}

	_jyhInviteCode.jyhInviteCodeDo.UseDB(db, opts...)
	_jyhInviteCode.jyhInviteCodeDo.UseModel(&jyhapp.JyhInviteCode{})

	tableName := _jyhInviteCode.jyhInviteCodeDo.TableName()
	_jyhInviteCode.ALL = field.NewAsterisk(tableName)
	_jyhInviteCode.ID = field.NewUint(tableName, "id")
	_jyhInviteCode.CreatedAt = field.NewTime(tableName, "created_at")
	_jyhInviteCode.UpdatedAt = field.NewTime(tableName, "updated_at")
	_jyhInviteCode.DeletedAt = field.NewField(tableName, "deleted_at")
	_jyhInviteCode.Type = field.NewString(tableName, "type")
	_jyhInviteCode.UserID = field.NewUint(tableName, "user_id")
	_jyhInviteCode.Code = field.NewString(tableName, "code")
	_jyhInviteCode.LevelID = field.NewUint(tableName, "level_id")
	_jyhInviteCode.SalePrice = field.NewUint64(tableName, "sale_price")
	_jyhInviteCode.UsedAt = field.NewTime(tableName, "used_at")
	_jyhInviteCode.UsedByUID = field.NewUint(tableName, "used_by_uid")
	_jyhInviteCode.Status = field.NewInt(tableName, "status")
	_jyhInviteCode.IsUsed = field.NewBool(tableName, "is_used")
	_jyhInviteCode.ExpiredAt = field.NewTime(tableName, "expired_at")
	_jyhInviteCode.User = jyhInviteCodeBelongsToUser{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("User", "jyhapp.JyhUser"),
		Inviter: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("User.Inviter", "jyhapp.JyhUser"),
		},
		JyhUserExt: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("User.JyhUserExt", "jyhapp.JyhUserExt"),
		},
		Invitees: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("User.Invitees", "jyhapp.JyhUser"),
		},
		Tags: struct {
			field.RelationField
			Users struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("User.Tags", "jyhapp.JyhUserTag"),
			Users: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("User.Tags.Users", "jyhapp.JyhUser"),
			},
		},
	}

	_jyhInviteCode.fillFieldMap()

	return _jyhInviteCode
}

type jyhInviteCode struct {
	jyhInviteCodeDo

	ALL       field.Asterisk
	ID        field.Uint
	CreatedAt field.Time
	UpdatedAt field.Time
	DeletedAt field.Field
	Type      field.String
	UserID    field.Uint
	Code      field.String
	LevelID   field.Uint
	SalePrice field.Uint64
	UsedAt    field.Time
	UsedByUID field.Uint
	Status    field.Int
	IsUsed    field.Bool
	ExpiredAt field.Time
	User      jyhInviteCodeBelongsToUser

	fieldMap map[string]field.Expr
}

func (j jyhInviteCode) Table(newTableName string) *jyhInviteCode {
	j.jyhInviteCodeDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j jyhInviteCode) As(alias string) *jyhInviteCode {
	j.jyhInviteCodeDo.DO = *(j.jyhInviteCodeDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *jyhInviteCode) updateTableName(table string) *jyhInviteCode {
	j.ALL = field.NewAsterisk(table)
	j.ID = field.NewUint(table, "id")
	j.CreatedAt = field.NewTime(table, "created_at")
	j.UpdatedAt = field.NewTime(table, "updated_at")
	j.DeletedAt = field.NewField(table, "deleted_at")
	j.Type = field.NewString(table, "type")
	j.UserID = field.NewUint(table, "user_id")
	j.Code = field.NewString(table, "code")
	j.LevelID = field.NewUint(table, "level_id")
	j.SalePrice = field.NewUint64(table, "sale_price")
	j.UsedAt = field.NewTime(table, "used_at")
	j.UsedByUID = field.NewUint(table, "used_by_uid")
	j.Status = field.NewInt(table, "status")
	j.IsUsed = field.NewBool(table, "is_used")
	j.ExpiredAt = field.NewTime(table, "expired_at")

	j.fillFieldMap()

	return j
}

func (j *jyhInviteCode) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *jyhInviteCode) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 15)
	j.fieldMap["id"] = j.ID
	j.fieldMap["created_at"] = j.CreatedAt
	j.fieldMap["updated_at"] = j.UpdatedAt
	j.fieldMap["deleted_at"] = j.DeletedAt
	j.fieldMap["type"] = j.Type
	j.fieldMap["user_id"] = j.UserID
	j.fieldMap["code"] = j.Code
	j.fieldMap["level_id"] = j.LevelID
	j.fieldMap["sale_price"] = j.SalePrice
	j.fieldMap["used_at"] = j.UsedAt
	j.fieldMap["used_by_uid"] = j.UsedByUID
	j.fieldMap["status"] = j.Status
	j.fieldMap["is_used"] = j.IsUsed
	j.fieldMap["expired_at"] = j.ExpiredAt

}

func (j jyhInviteCode) clone(db *gorm.DB) jyhInviteCode {
	j.jyhInviteCodeDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j jyhInviteCode) replaceDB(db *gorm.DB) jyhInviteCode {
	j.jyhInviteCodeDo.ReplaceDB(db)
	return j
}

type jyhInviteCodeBelongsToUser struct {
	db *gorm.DB

	field.RelationField

	Inviter struct {
		field.RelationField
	}
	JyhUserExt struct {
		field.RelationField
	}
	Invitees struct {
		field.RelationField
	}
	Tags struct {
		field.RelationField
		Users struct {
			field.RelationField
		}
	}
}

func (a jyhInviteCodeBelongsToUser) Where(conds ...field.Expr) *jyhInviteCodeBelongsToUser {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a jyhInviteCodeBelongsToUser) WithContext(ctx context.Context) *jyhInviteCodeBelongsToUser {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a jyhInviteCodeBelongsToUser) Session(session *gorm.Session) *jyhInviteCodeBelongsToUser {
	a.db = a.db.Session(session)
	return &a
}

func (a jyhInviteCodeBelongsToUser) Model(m *jyhapp.JyhInviteCode) *jyhInviteCodeBelongsToUserTx {
	return &jyhInviteCodeBelongsToUserTx{a.db.Model(m).Association(a.Name())}
}

type jyhInviteCodeBelongsToUserTx struct{ tx *gorm.Association }

func (a jyhInviteCodeBelongsToUserTx) Find() (result *jyhapp.JyhUser, err error) {
	return result, a.tx.Find(&result)
}

func (a jyhInviteCodeBelongsToUserTx) Append(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a jyhInviteCodeBelongsToUserTx) Replace(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a jyhInviteCodeBelongsToUserTx) Delete(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a jyhInviteCodeBelongsToUserTx) Clear() error {
	return a.tx.Clear()
}

func (a jyhInviteCodeBelongsToUserTx) Count() int64 {
	return a.tx.Count()
}

type jyhInviteCodeDo struct{ gen.DO }

func (j jyhInviteCodeDo) Debug() *jyhInviteCodeDo {
	return j.withDO(j.DO.Debug())
}

func (j jyhInviteCodeDo) WithContext(ctx context.Context) *jyhInviteCodeDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jyhInviteCodeDo) ReadDB() *jyhInviteCodeDo {
	return j.Clauses(dbresolver.Read)
}

func (j jyhInviteCodeDo) WriteDB() *jyhInviteCodeDo {
	return j.Clauses(dbresolver.Write)
}

func (j jyhInviteCodeDo) Session(config *gorm.Session) *jyhInviteCodeDo {
	return j.withDO(j.DO.Session(config))
}

func (j jyhInviteCodeDo) Clauses(conds ...clause.Expression) *jyhInviteCodeDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jyhInviteCodeDo) Returning(value interface{}, columns ...string) *jyhInviteCodeDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jyhInviteCodeDo) Not(conds ...gen.Condition) *jyhInviteCodeDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jyhInviteCodeDo) Or(conds ...gen.Condition) *jyhInviteCodeDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jyhInviteCodeDo) Select(conds ...field.Expr) *jyhInviteCodeDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jyhInviteCodeDo) Where(conds ...gen.Condition) *jyhInviteCodeDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jyhInviteCodeDo) Order(conds ...field.Expr) *jyhInviteCodeDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jyhInviteCodeDo) Distinct(cols ...field.Expr) *jyhInviteCodeDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jyhInviteCodeDo) Omit(cols ...field.Expr) *jyhInviteCodeDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jyhInviteCodeDo) Join(table schema.Tabler, on ...field.Expr) *jyhInviteCodeDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jyhInviteCodeDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jyhInviteCodeDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jyhInviteCodeDo) RightJoin(table schema.Tabler, on ...field.Expr) *jyhInviteCodeDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jyhInviteCodeDo) Group(cols ...field.Expr) *jyhInviteCodeDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jyhInviteCodeDo) Having(conds ...gen.Condition) *jyhInviteCodeDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jyhInviteCodeDo) Limit(limit int) *jyhInviteCodeDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jyhInviteCodeDo) Offset(offset int) *jyhInviteCodeDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jyhInviteCodeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jyhInviteCodeDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jyhInviteCodeDo) Unscoped() *jyhInviteCodeDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jyhInviteCodeDo) Create(values ...*jyhapp.JyhInviteCode) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jyhInviteCodeDo) CreateInBatches(values []*jyhapp.JyhInviteCode, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jyhInviteCodeDo) Save(values ...*jyhapp.JyhInviteCode) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jyhInviteCodeDo) First() (*jyhapp.JyhInviteCode, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhInviteCode), nil
	}
}

func (j jyhInviteCodeDo) Take() (*jyhapp.JyhInviteCode, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhInviteCode), nil
	}
}

func (j jyhInviteCodeDo) Last() (*jyhapp.JyhInviteCode, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhInviteCode), nil
	}
}

func (j jyhInviteCodeDo) Find() ([]*jyhapp.JyhInviteCode, error) {
	result, err := j.DO.Find()
	return result.([]*jyhapp.JyhInviteCode), err
}

func (j jyhInviteCodeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*jyhapp.JyhInviteCode, err error) {
	buf := make([]*jyhapp.JyhInviteCode, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jyhInviteCodeDo) FindInBatches(result *[]*jyhapp.JyhInviteCode, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jyhInviteCodeDo) Attrs(attrs ...field.AssignExpr) *jyhInviteCodeDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jyhInviteCodeDo) Assign(attrs ...field.AssignExpr) *jyhInviteCodeDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jyhInviteCodeDo) Joins(fields ...field.RelationField) *jyhInviteCodeDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jyhInviteCodeDo) Preload(fields ...field.RelationField) *jyhInviteCodeDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jyhInviteCodeDo) FirstOrInit() (*jyhapp.JyhInviteCode, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhInviteCode), nil
	}
}

func (j jyhInviteCodeDo) FirstOrCreate() (*jyhapp.JyhInviteCode, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhInviteCode), nil
	}
}

func (j jyhInviteCodeDo) FindByPage(offset int, limit int) (result []*jyhapp.JyhInviteCode, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jyhInviteCodeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jyhInviteCodeDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jyhInviteCodeDo) Delete(models ...*jyhapp.JyhInviteCode) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jyhInviteCodeDo) withDO(do gen.Dao) *jyhInviteCodeDo {
	j.DO = *do.(*gen.DO)
	return j
}
