package main

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
	"gorm.io/gen"
	"gorm.io/gorm"
)

func genQueryJyhApp(db *gorm.DB) {
	// specify the output directory (default: "./query")
	// ### if you want to query without context constrain, set mode gen.WithoutContext ###
	g := gen.NewGenerator(gen.Config{
		OutPath: "./model/jyhapp/query",
		Mode:    gen.WithoutContext | gen.WithDefaultQuery,
		/* Mode: gen.WithoutContext|gen.WithDefaultQuery*/
		//if you want the nullable field generation property to be pointer type, set FieldNullable true
		FieldNullable: true,
		//if you want to assign field which has default value in `Create` API, set FieldCoverable true, reference: https://gorm.io/docs/create.html#Default-Values
		/* FieldCoverable: true,*/
		// if you want generate field with unsigned integer type, set FieldSignable true
		/* FieldSignable: true,*/
		//if you want to generate index tags from database, set FieldWithIndexTag true
		/* FieldWithIndexTag: true,*/
		//if you want to generate type tags from database, set FieldWithTypeTag true
		/* FieldWithTypeTag: true,*/
		//if you need unit tests for query code, set WithUnitTest true
		/* WithUnitTest: true, */
	})

	g.UseDB(db)

	//g.WithDataTypeMap(map[string]func(detailType string) (dataType string){
	//	"date": func(detailType string) (dataType string) {
	//		return "time.Time"
	//	},
	//})

	gvaModels := []interface{}{}
	gvaModels = append(gvaModels, jyhapp.GenInitTables()...)

	// apply basic crud api on structs or table models which is specified by table name with function
	// GenerateModel/GenerateModelAs. And generator will generate table models' code when calling Excute.
	// 想对已有的model生成crud等基础方法可以直接指定model struct ，例如model.User{}
	// 如果是想直接生成表的model和crud方法，则可以指定标名称，例如g.GenerateModel("company")
	// 想自定义某个表生成特性，比如struct的名称/字段类型/tag等，可以指定opt，例如g.GenerateModel("company",gen.FieldIgnore("address")), g.GenerateModelAs("people", "Person", gen.FieldIgnore("address"))
	g.ApplyBasic(
		//access.SysDict{},
		//g.GenerateModel("company"),
		//g.GenerateModelAs("people", "Person", gen.FieldIgnore("address")),
		gvaModels...,
	)

	// apply diy interfaces on structs or table models
	// 如果想给某些表或者model生成自定义方法，可以用ApplyInterface，第一个参数是方法接口，可以参考DIY部分文档定义
	//g.ApplyInterface(
	//	func(method access.Method) {},
	//
	//	access.AllTables...,
	//)

	g.Execute()
}

func main() {
	var db *gorm.DB = nil
	genQueryJyhApp(db)
}
