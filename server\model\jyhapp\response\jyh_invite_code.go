package response

import "time"

// InviteCodeItem 邀请码列表项
type InviteCodeItem struct {
	ID         uint       `json:"id"`           // 邀请码ID
	Type       string     `json:"type"`         // 邀请码类型
	UserID     *uint      `json:"user_id"`      // 生成用户ID
	UserName   string     `json:"user_name"`    // 生成用户名
	UserPhone  string     `json:"user_phone"`   // 生成用户手机号(脱敏)
	Code       string     `json:"code"`         // 邀请码
	LevelID    uint       `json:"level_id"`     // 会员等级ID
	LevelName  string     `json:"level_name"`   // 会员等级名称
	SalePrice  uint64     `json:"sale_price"`   // 出售价格(单位分)
	UsedAt     *time.Time `json:"used_at"`      // 使用时间
	UsedByUID  *uint      `json:"used_by_uid"`  // 使用者用户ID
	UsedByName string     `json:"used_by_name"` // 使用者用户名
	Status     int        `json:"status"`       // 状态：0 未使用 1 已使用 2 已过期
	IsUsed     bool       `json:"is_used"`      // 是否已被使用
	ExpiredAt  *time.Time `json:"expired_at"`   // 过期时间
	CreatedAt  time.Time  `json:"created_at"`   // 创建时间
}

// InviteCodeListResp 邀请码列表响应
type InviteCodeListResp struct {
	List     []*InviteCodeItem `json:"list"`      // 邀请码列表
	Total    int64             `json:"total"`     // 总数
	Page     int               `json:"page"`      // 当前页
	PageSize int               `json:"page_size"` // 每页大小
}

// InviteCodeStatistics 邀请码统计信息
type InviteCodeStatistics struct {
	Total    int64 `json:"total"`    // 总邀请码数
	Used     int64 `json:"used"`     // 已使用数量
	Unused   int64 `json:"unused"`   // 未使用数量
	Expired  int64 `json:"expired"`  // 已过期数量
	Platform int64 `json:"platform"` // 平台邀请码数量
	User     int64 `json:"user"`     // 用户邀请码数量
}

// CreateInviteCodeResp 创建邀请码响应
type CreateInviteCodeResp struct {
	Code string `json:"code"` // 生成的邀请码
}

// BatchCreateInviteCodeResp 批量创建邀请码响应
type BatchCreateInviteCodeResp struct {
	Count int      `json:"count"` // 成功创建的数量
	Codes []string `json:"codes"` // 生成的邀请码列表
}

// UserInviteCodeItem 用户邀请码项
type UserInviteCodeItem struct {
	ID        uint       `json:"id"`         // 邀请码ID
	Code      string     `json:"code"`       // 邀请码
	SalePrice int64      `json:"sale_price"` // 售价（分）
	ExpiredAt *time.Time `json:"expired_at"` // 过期时间
	CreatedAt time.Time  `json:"created_at"` // 创建时间
}

// UserInviteCodeLevel 用户邀请码等级分组
type UserInviteCodeLevel struct {
	LevelID     uint                  `json:"level_id"`     // 等级ID
	LevelName   string                `json:"level_name"`   // 等级名称
	CodeCount   int                   `json:"code_count"`   // 邀请码数量
	InviteCodes []*UserInviteCodeItem `json:"invite_codes"` // 邀请码列表
}

// UserInviteCodeListResp 用户邀请码列表响应（按等级分类）
type UserInviteCodeListResp struct {
	TotalCount int                    `json:"total_count"` // 总邀请码数量
	Levels     []*UserInviteCodeLevel `json:"levels"`      // 按等级分类的邀请码
}

// InviteCodeTransferItem 邀请码转让记录项
type InviteCodeTransferItem struct {
	ID           uint      `json:"id"`             // 转让记录ID
	InviteCodeID uint      `json:"invite_code_id"` // 邀请码ID
	InviteCode   string    `json:"invite_code"`    // 邀请码
	FromUserID   *uint     `json:"from_user_id"`   // 原持有用户ID
	FromUserName string    `json:"from_user_name"` // 原持有用户名
	ToUserID     uint      `json:"to_user_id"`     // 新持有用户ID
	ToUserName   string    `json:"to_user_name"`   // 新持有用户名
	AdminID      uint      `json:"admin_id"`       // 操作管理员ID
	AdminName    string    `json:"admin_name"`     // 操作管理员名
	Reason       string    `json:"reason"`         // 转让原因
	TransferTime time.Time `json:"transfer_time"`  // 转让时间
	LevelID      uint      `json:"level_id"`       // 会员等级ID
	LevelName    string    `json:"level_name"`     // 会员等级名称
	SalePrice    uint64    `json:"sale_price"`     // 出售价格(单位分)
	CreatedAt    time.Time `json:"created_at"`     // 创建时间
}

// InviteCodeTransferListResp 邀请码转让记录列表响应
type InviteCodeTransferListResp struct {
	List     []*InviteCodeTransferItem `json:"list"`      // 转让记录列表
	Total    int64                     `json:"total"`     // 总数
	Page     int                       `json:"page"`      // 当前页
	PageSize int                       `json:"page_size"` // 每页大小
}

// TransferInviteCodeResp 转让邀请码响应
type TransferInviteCodeResp struct {
	TransferID uint `json:"transfer_id"` // 转让记录ID
}
