package request

import "github.com/flipped-aurora/gin-vue-admin/server/model/common/request"

// CourseCategorySearch 课程分类查询参数
type CourseCategorySearch struct {
	request.PageInfo
	Name     string `json:"name" form:"name"`         // 分类名称
	Sort     int    `json:"sort"`                     // 排序
	IsActive bool   `json:"isActive" form:"isActive"` // 状态
	CatDesc  string `json:"catDesc" form:"catDesc"`   // 分类描述
}

// CourseCategoryCreate 课程分类创建参数
type CourseCategoryCreate struct {
	Name     string `json:"name" binding:"required"`     // 分类名称
	Sort     int    `json:"sort"`                        // 排序
	IsActive bool   `json:"isActive" binding:"required"` // 状态
	CatDesc  string `json:"catDesc"`                     // 描述
}

// CourseCategoryUpdate 课程分类更新参数
type CourseCategoryUpdate struct {
	ID       uint   `json:"id" binding:"required"`   // ID
	Name     string `json:"name" binding:"required"` // 分类名称
	Sort     int    `json:"sort"`                    // 排序
	IsActive bool   `json:"isActive"`                // 状态
	CatDesc  string `json:"catDesc"`                 // 描述
}
