package jyhapp

import (
	"errors"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
	jyhRes "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/response"
	"go.uber.org/zap"
	"gorm.io/gen"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/query"
	jyhReq "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/request"
)

type TagsService struct{}

// Create 创建标签
func (s *TagsService) Create(req jyhReq.TagsCreate) (err error) {
	dbTag := query.JyhTag
	// 检查标签名称是否存在
	res := &jyhapp.JyhTag{}
	err = dbTag.Where(dbTag.Name.Eq(req.Name)).Scan(&res)
	if err != nil {
		global.GVA_LOG.Error("查询标签失败", zap.Error(err))
		return err
	}

	if res.ID > 0 {
		return errors.New("标签名称重复")
	}

	tag := &jyhapp.JyhTag{
		Name: req.Name,
		Type: req.Type,
	}

	return dbTag.Create(tag)
}

// Update 更新标签
func (s *TagsService) Update(req *jyhReq.TagsUpdate) (err error) {
	dbTag := query.JyhTag

	res := &jyhapp.JyhTag{}
	err = dbTag.Where(dbTag.Name.Eq(req.Name), dbTag.ID.Neq(req.ID)).Scan(&res)
	if err != nil {
		return err
	}
	if res.ID > 0 {
		return errors.New("标签名称重复")
	}

	_, err = dbTag.Where(dbTag.ID.Eq(req.ID)).First()
	if err != nil {
		return errors.New("标签不存在")
	}

	// 更新标签信息
	updates := map[string]interface{}{
		"name": req.Name,
		"type": req.Type,
	}

	_, err = dbTag.Select(dbTag.Name, dbTag.Type).Where(dbTag.ID.Eq(req.ID)).Updates(updates)
	return err
}

// Delete 删除标签
func (s *TagsService) Delete(id uint) (err error) {
	dbTag := query.JyhTag

	// 检查是否有关联的素材
	materialTagQuery := query.JyhMaterialTag
	materialCount, err := materialTagQuery.Where(materialTagQuery.TagID.Eq(id)).Count()
	if err != nil {
		return err
	}
	if materialCount > 0 {
		return errors.New("存在关联的素材，无法删除")
	}

	_, err = dbTag.Where(dbTag.ID.Eq(id)).Delete()
	return err
}

// GetDetail 获取标签详情
func (s *TagsService) GetDetail(id uint) (category *jyhapp.JyhTag, err error) {
	dbTag := query.JyhTag
	category, err = dbTag.Where(dbTag.ID.Eq(id)).First()
	return
}

// GetList 获取标签列表
func (s *TagsService) GetList(req *jyhReq.TagsSearch) (list []*jyhapp.JyhTag, total int64, err error) {
	dbTag := query.JyhTag
	queryBuilder := dbTag.WithContext(global.GVA_DB.Statement.Context)

	// 构建查询条件
	if req.Name != "" {
		queryBuilder = queryBuilder.Where(dbTag.Name.Like("%" + req.Name + "%"))
	}

	// 获取总数
	total, err = queryBuilder.Count()
	if err != nil {
		return
	}

	if req.Page == 0 && req.PageSize == -1 {
		list, err = queryBuilder.Order(dbTag.ID).Find()
		return
	}

	// 分页查询
	queryBuilder = queryBuilder.Limit(req.PageSize).Offset((req.Page - 1) * req.PageSize)
	list, err = queryBuilder.Order(dbTag.ID).Find()
	return
}

// GetTags 获取所有标签
func (s *TagsService) GetTags(req jyhReq.TagsFilterReq) (res []jyhRes.TagsResponse, err error) {
	var (
		dbTag = query.JyhTag
		cons  []gen.Condition
	)

	if req.Type != "" {
		cons = append(cons, dbTag.Type.Eq(req.Type))
	}
	if req.Name != "" {
		cons = append(cons, dbTag.Name.Like("%"+req.Name+"%"))
	}

	err = dbTag.Select(dbTag.ID.As("tag_id"), dbTag.Name).Where(cons...).Scan(&res)
	if err != nil {
		return nil, err
	}
	return res, nil
}
