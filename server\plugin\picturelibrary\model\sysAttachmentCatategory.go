package model

type SysAttachmentCategory struct {
	Id       uint                     `gorm:"primarykey" json:"id"` // 主键ID
	Name     string                   `json:"name" form:"name" gorm:"default:'';type:varchar(255);column:name;comment:分类名称;"`
	Pid      uint                     `json:"pid" form:"pid" gorm:"default:0;type:int;column:pid;comment:父节点ID;"`
	AddTime  int                      `json:"add_time" form:"add_time" gorm:"default:0;type:int;column:add_time;comment:添加时间;"`
	Children []*SysAttachmentCategory `json:"children" gorm:"-"`
}

func (SysAttachmentCategory) TableName() string {
	return "sys_attachment_category"
}
