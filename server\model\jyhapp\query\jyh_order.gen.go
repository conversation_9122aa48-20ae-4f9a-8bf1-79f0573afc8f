// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
)

func newJyhOrder(db *gorm.DB, opts ...gen.DOOption) jyhOrder {
	_jyhOrder := jyhOrder{}

	_jyhOrder.jyhOrderDo.UseDB(db, opts...)
	_jyhOrder.jyhOrderDo.UseModel(&jyhapp.JyhOrder{})

	tableName := _jyhOrder.jyhOrderDo.TableName()
	_jyhOrder.ALL = field.NewAsterisk(tableName)
	_jyhOrder.ID = field.NewUint(tableName, "id")
	_jyhOrder.CreatedAt = field.NewTime(tableName, "created_at")
	_jyhOrder.UpdatedAt = field.NewTime(tableName, "updated_at")
	_jyhOrder.DeletedAt = field.NewField(tableName, "deleted_at")
	_jyhOrder.UserID = field.NewUint(tableName, "user_id")
	_jyhOrder.Amount = field.NewField(tableName, "amount")
	_jyhOrder.Status = field.NewString(tableName, "status")
	_jyhOrder.User = jyhOrderBelongsToUser{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("User", "jyhapp.JyhUser"),
		Inviter: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("User.Inviter", "jyhapp.JyhUser"),
		},
		JyhUserExt: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("User.JyhUserExt", "jyhapp.JyhUserExt"),
		},
		Invitees: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("User.Invitees", "jyhapp.JyhUser"),
		},
		Tags: struct {
			field.RelationField
			Users struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("User.Tags", "jyhapp.JyhUserTag"),
			Users: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("User.Tags.Users", "jyhapp.JyhUser"),
			},
		},
	}

	_jyhOrder.fillFieldMap()

	return _jyhOrder
}

type jyhOrder struct {
	jyhOrderDo

	ALL       field.Asterisk
	ID        field.Uint
	CreatedAt field.Time
	UpdatedAt field.Time
	DeletedAt field.Field
	UserID    field.Uint
	Amount    field.Field
	Status    field.String
	User      jyhOrderBelongsToUser

	fieldMap map[string]field.Expr
}

func (j jyhOrder) Table(newTableName string) *jyhOrder {
	j.jyhOrderDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j jyhOrder) As(alias string) *jyhOrder {
	j.jyhOrderDo.DO = *(j.jyhOrderDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *jyhOrder) updateTableName(table string) *jyhOrder {
	j.ALL = field.NewAsterisk(table)
	j.ID = field.NewUint(table, "id")
	j.CreatedAt = field.NewTime(table, "created_at")
	j.UpdatedAt = field.NewTime(table, "updated_at")
	j.DeletedAt = field.NewField(table, "deleted_at")
	j.UserID = field.NewUint(table, "user_id")
	j.Amount = field.NewField(table, "amount")
	j.Status = field.NewString(table, "status")

	j.fillFieldMap()

	return j
}

func (j *jyhOrder) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *jyhOrder) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 8)
	j.fieldMap["id"] = j.ID
	j.fieldMap["created_at"] = j.CreatedAt
	j.fieldMap["updated_at"] = j.UpdatedAt
	j.fieldMap["deleted_at"] = j.DeletedAt
	j.fieldMap["user_id"] = j.UserID
	j.fieldMap["amount"] = j.Amount
	j.fieldMap["status"] = j.Status

}

func (j jyhOrder) clone(db *gorm.DB) jyhOrder {
	j.jyhOrderDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j jyhOrder) replaceDB(db *gorm.DB) jyhOrder {
	j.jyhOrderDo.ReplaceDB(db)
	return j
}

type jyhOrderBelongsToUser struct {
	db *gorm.DB

	field.RelationField

	Inviter struct {
		field.RelationField
	}
	JyhUserExt struct {
		field.RelationField
	}
	Invitees struct {
		field.RelationField
	}
	Tags struct {
		field.RelationField
		Users struct {
			field.RelationField
		}
	}
}

func (a jyhOrderBelongsToUser) Where(conds ...field.Expr) *jyhOrderBelongsToUser {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a jyhOrderBelongsToUser) WithContext(ctx context.Context) *jyhOrderBelongsToUser {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a jyhOrderBelongsToUser) Session(session *gorm.Session) *jyhOrderBelongsToUser {
	a.db = a.db.Session(session)
	return &a
}

func (a jyhOrderBelongsToUser) Model(m *jyhapp.JyhOrder) *jyhOrderBelongsToUserTx {
	return &jyhOrderBelongsToUserTx{a.db.Model(m).Association(a.Name())}
}

type jyhOrderBelongsToUserTx struct{ tx *gorm.Association }

func (a jyhOrderBelongsToUserTx) Find() (result *jyhapp.JyhUser, err error) {
	return result, a.tx.Find(&result)
}

func (a jyhOrderBelongsToUserTx) Append(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a jyhOrderBelongsToUserTx) Replace(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a jyhOrderBelongsToUserTx) Delete(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a jyhOrderBelongsToUserTx) Clear() error {
	return a.tx.Clear()
}

func (a jyhOrderBelongsToUserTx) Count() int64 {
	return a.tx.Count()
}

type jyhOrderDo struct{ gen.DO }

func (j jyhOrderDo) Debug() *jyhOrderDo {
	return j.withDO(j.DO.Debug())
}

func (j jyhOrderDo) WithContext(ctx context.Context) *jyhOrderDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jyhOrderDo) ReadDB() *jyhOrderDo {
	return j.Clauses(dbresolver.Read)
}

func (j jyhOrderDo) WriteDB() *jyhOrderDo {
	return j.Clauses(dbresolver.Write)
}

func (j jyhOrderDo) Session(config *gorm.Session) *jyhOrderDo {
	return j.withDO(j.DO.Session(config))
}

func (j jyhOrderDo) Clauses(conds ...clause.Expression) *jyhOrderDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jyhOrderDo) Returning(value interface{}, columns ...string) *jyhOrderDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jyhOrderDo) Not(conds ...gen.Condition) *jyhOrderDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jyhOrderDo) Or(conds ...gen.Condition) *jyhOrderDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jyhOrderDo) Select(conds ...field.Expr) *jyhOrderDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jyhOrderDo) Where(conds ...gen.Condition) *jyhOrderDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jyhOrderDo) Order(conds ...field.Expr) *jyhOrderDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jyhOrderDo) Distinct(cols ...field.Expr) *jyhOrderDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jyhOrderDo) Omit(cols ...field.Expr) *jyhOrderDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jyhOrderDo) Join(table schema.Tabler, on ...field.Expr) *jyhOrderDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jyhOrderDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jyhOrderDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jyhOrderDo) RightJoin(table schema.Tabler, on ...field.Expr) *jyhOrderDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jyhOrderDo) Group(cols ...field.Expr) *jyhOrderDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jyhOrderDo) Having(conds ...gen.Condition) *jyhOrderDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jyhOrderDo) Limit(limit int) *jyhOrderDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jyhOrderDo) Offset(offset int) *jyhOrderDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jyhOrderDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jyhOrderDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jyhOrderDo) Unscoped() *jyhOrderDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jyhOrderDo) Create(values ...*jyhapp.JyhOrder) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jyhOrderDo) CreateInBatches(values []*jyhapp.JyhOrder, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jyhOrderDo) Save(values ...*jyhapp.JyhOrder) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jyhOrderDo) First() (*jyhapp.JyhOrder, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhOrder), nil
	}
}

func (j jyhOrderDo) Take() (*jyhapp.JyhOrder, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhOrder), nil
	}
}

func (j jyhOrderDo) Last() (*jyhapp.JyhOrder, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhOrder), nil
	}
}

func (j jyhOrderDo) Find() ([]*jyhapp.JyhOrder, error) {
	result, err := j.DO.Find()
	return result.([]*jyhapp.JyhOrder), err
}

func (j jyhOrderDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*jyhapp.JyhOrder, err error) {
	buf := make([]*jyhapp.JyhOrder, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jyhOrderDo) FindInBatches(result *[]*jyhapp.JyhOrder, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jyhOrderDo) Attrs(attrs ...field.AssignExpr) *jyhOrderDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jyhOrderDo) Assign(attrs ...field.AssignExpr) *jyhOrderDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jyhOrderDo) Joins(fields ...field.RelationField) *jyhOrderDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jyhOrderDo) Preload(fields ...field.RelationField) *jyhOrderDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jyhOrderDo) FirstOrInit() (*jyhapp.JyhOrder, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhOrder), nil
	}
}

func (j jyhOrderDo) FirstOrCreate() (*jyhapp.JyhOrder, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhOrder), nil
	}
}

func (j jyhOrderDo) FindByPage(offset int, limit int) (result []*jyhapp.JyhOrder, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jyhOrderDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jyhOrderDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jyhOrderDo) Delete(models ...*jyhapp.JyhOrder) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jyhOrderDo) withDO(do gen.Dao) *jyhOrderDo {
	j.DO = *do.(*gen.DO)
	return j
}
