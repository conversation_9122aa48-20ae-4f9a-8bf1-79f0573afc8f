# 检查文件是否存在
ifneq ($(wildcard .env),)
include .env
endif

GITTAG ?= $(shell git log --pretty=format:"%h:%cd" --date=format:"%Y-%m-%dT%H:%M:%S" -1)
GITAUTHOR ?= $(shell git log -1 --pretty=format:'%an <%ae>' | tr -d "\"'" )
GITMESSAGE ?= $(shell git log -1 --pretty=format:'%B' | head -1 | tr -d "\"'" )
# | grep -E '(feat|fix|perf|refactor|build|chore)([\w\W]+)?\s?:'
GITLOG ?= $(shell echo "| 时间 | 提交人 | 修改信息 |<br/>|----|-----|-----|<br/>"; git log --pretty=format:'| %cd | %an <%ae> | %s |' --date=format:"%Y-%m-%dT%H:%M:%S" | grep -v '| Merge ' | head -6 | tr '\n' '\14' | sed 's;\xc;<br/>;g' | tr -d "\"'" )
BUILD_TIME ?= `TZ='Asia/Shanghai' date "+%Y-%m-%dT%H:%M:%S"`
BUILD_VERSION ?= $(or $(DRONE_TAG), 1.0)
CONFPREFIX ?= jyh
APP ?= douyin

BUILD_TAG ?= `date "+%Y%m%d%H%M"`

ENV_PKG=github.com/flipped-aurora/gin-vue-admin/server/global
DEVELOP_URL ?= https://cd-ins.ultrasdk.com/wh/dev-douyinsrv?token=Q1flPXCwXBoZvMU79nFpMIdww1XZYWoJ
RELEASE_URL ?= https://pos.mmweb.top/wh0/prod-douyinsrv?token=w2SCGWJ6DZTHjws9Xl8zsUANrtu1q2Pk

tags += \
        jsoniter

ldflagsWeb+=\
	-X '${ENV_PKG}.GitTag=${GITTAG}' \
	-X '${ENV_PKG}.GitAuthor=${GITAUTHOR}' \
	-X '${ENV_PKG}.GitCommitMsg=${GITMESSAGE}' \
	-X '${ENV_PKG}.GitCommitLog=${GITLOG}' \
	-X '${ENV_PKG}.Version=${BUILD_VERSION}' \
	-X '${ENV_PKG}.BuildTime=${BUILD_TIME}' \
	-X '${ENV_PKG}.ConfEnvPrefix=${CONFPREFIX}' \
	-w -s

.PHONY: all
all: run

clean:
	rm -f ${APP} ${APP}.gz ${APP}.exe ${APP}.tar.gz
run:
	go run -tags "${tags}" -ldflags "${ldflagsWeb}" . $(RUN_ARGS)

doc-kit:
	@which swag > /dev/null 2>&1; if [ $$? -ne 0 ]; then \
		echo "安装 swag"; \
		go install github.com/swaggo/swag/cmd/swag@latest; \
	fi

doc:
	swag init --parseInternal --parseDependency --parseDepth 1 -d . -g main.go -o ./docs -ot go

gen:
	@echo "从 model 生成 db 查询代码"
	go run cmd/generate/generate.go

proto:
	@echo "从 proto 生成 pb.go"
	protoc --go_out=./pkg/pb --go-grpc_out=./pkg/pb ./pkg/pb/*.proto

linux:
	GOOS="linux" GOARCH="amd64" go build -tags "${tags}" -ldflags "${ldflagsWeb}" -o ${APP} .

windows:
	GOOS="windows" GOARCH="amd64" go build -tags "${tags}" -ldflags "${ldflagsWeb}" -o ${APP} .

mac:
	GOOS="darwin" GOARCH="amd64" go build -tags "${tags}" -ldflags "${ldflagsWeb}" -o ${APP} .


update_dev: linux
	tar -czf ${APP}.tar.gz ${APP} config.docker.yaml ./resource
	curl "${DEVELOP_URL}" -F "pkg=@${APP}.tar.gz"

update_srv: linux
	tar -czf ${APP}.tar.gz ${APP} config.srv.yaml ./resource
	curl "${RELEASE_URL}" -F "pkg=@${APP}.tar.gz"

develop: clean doc update_dev
	echo "开发版本：${BUILD_VERSION}"

release: clean update_srv
	echo "正式版本：${BUILD_VERSION}"

init-go:
	@echo "初始化 go mod"
	@go env | grep GOPROXY || go env -w GOPROXY=https://proxy.golang.com.cn,direct
	go mod tidy

repo-release:
	@echo "发布版本"
	git tag -a ${BUILD_TAG} -m "发布版本 ${BUILD_TAG}"
	git push origin ${BUILD_TAG}

all_dev: init-go clean doc-kit doc linux update_dev
	@echo "开发版本：${BUILD_VERSION}"

all_srv: init-go clean doc-kit doc linux update_srv
	@echo "正式版本：${BUILD_VERSION}"

stat-srv:
	curl https://pos.mmweb.top/insbuy-api/kit/stat
