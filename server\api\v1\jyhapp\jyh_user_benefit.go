package jyhapp

import (
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	jyhReq "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type JyhUserBenefitApi struct{}

// ===== 权益类型接口（预设数据，极少管理接口） =====

// GetBenefitList 获取权益类型列表（主要用于等级配置时选择预设权益）
// @Tags      UserBenefit
// @Summary   获取权益类型列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     jyhReq.BenefitListReq  true  "获取权益类型列表请求"
// @Success   200   {object}  response.Response{data=response.PageResult,msg=string}  "获取权益类型列表"
// @Router    /admin/user_benefit/benefits [get]
func (a *JyhUserBenefitApi) GetBenefitList(c *gin.Context) {
	var req jyhReq.BenefitListReq
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 10
	}

	list, total, err := userBenefitService.GetBenefitList(&req)
	if err != nil {
		global.GVA_LOG.Error("获取权益类型列表失败！", zap.Error(err))
		response.FailWithMessage("获取权益类型列表失败:"+err.Error(), c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取权益类型列表成功", c)
}

// ===== 会员等级接口（主要管理界面，包含权益配置） =====

// GetUserShipLevelList 获取会员等级列表
// @Tags      UserBenefit
// @Summary   获取会员等级列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     jyhReq.UserShipLevelListReq  true  "获取会员等级列表请求"
// @Success   200   {object}  response.Response{data=response.PageResult,msg=string}  "获取会员等级列表"
// @Router    /jyh/user/levels [get]
func (a *JyhUserBenefitApi) GetUserShipLevelList(c *gin.Context) {
	var req jyhReq.UserShipLevelListReq
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 10
	}

	list, total, err := userBenefitService.GetUserShipLevelList(&req, true) // 用户端接口，过滤隐藏的等级
	if err != nil {
		global.GVA_LOG.Error("获取会员等级列表失败！", zap.Error(err))
		response.FailWithMessage("获取会员等级列表失败:"+err.Error(), c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取会员等级列表成功", c)
}

// GetAdminUserShipLevelList 获取会员等级列表（管理端）
// @Tags      UserBenefit
// @Summary   获取会员等级列表（管理端）
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     jyhReq.UserShipLevelListReq  true  "获取会员等级列表请求"
// @Success   200   {object}  response.Response{data=response.PageResult,msg=string}  "获取会员等级列表"
// @Router    /admin/user_benefit/levels [get]
func (a *JyhUserBenefitApi) GetAdminUserShipLevelList(c *gin.Context) {
	var req jyhReq.UserShipLevelListReq
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 10
	}

	// 管理端接口，显示所有等级（包括隐藏的）
	list, total, err := userBenefitService.GetUserShipLevelList(&req)
	if err != nil {
		global.GVA_LOG.Error("获取会员等级列表失败！", zap.Error(err))
		response.FailWithMessage("获取会员等级列表失败:"+err.Error(), c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取会员等级列表成功", c)
}

// CreateUserShipLevel 创建会员等级（包含权益配置，事务处理）
// @Tags      UserBenefit
// @Summary   创建会员等级
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      jyhReq.UserShipLevelCreateReq  true  "创建会员等级请求"
// @Success   200   {object}  response.Response{msg=string}  "创建会员等级"
// @Router    /admin/user_benefit/levels [post]
func (a *JyhUserBenefitApi) CreateUserShipLevel(c *gin.Context) {
	var req jyhReq.UserShipLevelCreateReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = userBenefitService.CreateUserShipLevel(&req)
	if err != nil {
		global.GVA_LOG.Error("创建会员等级失败！", zap.Error(err))
		response.FailWithMessage("创建会员等级失败:"+err.Error(), c)
		return
	}

	response.OkWithMessage("创建会员等级成功", c)
}

// GetUserShipLevelDetail 获取会员等级详情（包含权益配置）
// @Tags      UserBenefit
// @Summary   获取会员等级详情
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     id  path      uint  true  "会员等级ID"
// @Success   200 {object}  response.Response{data=response.UserShipLevelDetailResp,msg=string}  "获取会员等级详情"
// @Router    /admin/user_benefit/levels/{id} [get]
func (a *JyhUserBenefitApi) GetUserShipLevelDetail(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.FailWithMessage("无效的ID参数", c)
		return
	}

	resp, err := userBenefitService.GetUserShipLevelDetail(uint(id))
	if err != nil {
		global.GVA_LOG.Error("获取会员等级详情失败！", zap.Error(err))
		response.FailWithMessage("获取会员等级详情失败:"+err.Error(), c)
		return
	}

	response.OkWithDetailed(resp, "获取会员等级详情成功", c)
}

// UpdateUserShipLevel 更新会员等级（包含权益配置，事务处理）
// @Tags      UserBenefit
// @Summary   更新会员等级
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     id   path      uint                           true  "会员等级ID"
// @Param     data body      jyhReq.UserShipLevelUpdateReq  true  "更新会员等级请求"
// @Success   200  {object}  response.Response{msg=string}  "更新会员等级"
// @Router    /admin/user_benefit/levels [put]
func (a *JyhUserBenefitApi) UpdateUserShipLevel(c *gin.Context) {
	var req jyhReq.UserShipLevelUpdateReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = userBenefitService.UpdateUserShipLevel(&req)
	if err != nil {
		global.GVA_LOG.Error("更新会员等级失败！", zap.Error(err))
		response.FailWithMessage("更新会员等级失败:"+err.Error(), c)
		return
	}

	response.OkWithMessage("更新会员等级成功", c)
}

// DeleteUserShipLevel 删除会员等级
// @Tags      UserBenefit
// @Summary   删除会员等级
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     id  path      uint  true  "会员等级ID"
// @Success   200 {object}  response.Response{msg=string}  "删除会员等级"
// @Router    /admin/user_benefit/levels [delete]
func (a *JyhUserBenefitApi) DeleteUserShipLevel(c *gin.Context) {
	var req request.GetById
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = userBenefitService.DeleteUserShipLevel(req.GetUintId())
	if err != nil {
		global.GVA_LOG.Error("删除会员等级失败！", zap.Error(err))
		response.FailWithMessage("删除会员等级失败:"+err.Error(), c)
		return
	}

	response.OkWithMessage("删除会员等级成功", c)
}

// ===== 用户等级记录接口（用户开通等级） =====

// GetUserLevelList 获取用户等级记录列表（支持状态筛选，包含审核功能）
// @Tags      UserBenefit
// @Summary   获取用户等级记录列表（支持状态筛选，包含审核功能）
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     user_id    query     uint    false  "用户ID"
// @Param     level_id   query     uint    false  "等级ID"
// @Param     status     query     string  false  "状态筛选（pending=待审核，active=已生效，rejected=已拒绝，expired=已过期）"
// @Param     start_time query     string  false  "申请开始时间"
// @Param     end_time   query     string  false  "申请结束时间"
// @Param     page       query     int     true   "页码"
// @Param     page_size  query     int     true   "每页条数"
// @Success   200   {object}  response.Response{data=response.PageResult,msg=string}  "获取用户等级记录列表"
// @Router    /admin/user_benefit/user_levels [get]
func (a *JyhUserBenefitApi) GetUserLevelList(c *gin.Context) {
	var req jyhReq.UserLevelListReq
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 10
	}

	list, total, err := userBenefitService.GetUserLevelList(&req)
	if err != nil {
		global.GVA_LOG.Error("获取用户等级记录列表失败！", zap.Error(err))
		response.FailWithMessage("获取用户等级记录列表失败:"+err.Error(), c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取用户等级记录列表成功", c)
}

// CreateUserLevel 用户开通等级（创建用户等级记录并快照权益）
// @Tags      UserBenefit
// @Summary   用户开通等级
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      jyhReq.UserLevelReq  true  "用户开通等级请求"
// @Success   200   {object}  response.Response{msg=string}  "用户开通等级"
// @Router    /jyh/user/user_levels [post]
func (a *JyhUserBenefitApi) CreateUserLevel(c *gin.Context) {
	var req jyhReq.UserLevelReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = userBenefitService.CreateUserLevel(&req)
	if err != nil {
		global.GVA_LOG.Error("用户开通等级失败！", zap.Error(err))
		response.FailWithMessage("用户开通等级失败:"+err.Error(), c)
		return
	}

	response.OkWithMessage("用户开通等级成功", c)
}

// UpdateUserLevel 更新用户等级记录
// @Tags      UserBenefit
// @Summary   更新用户等级记录
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     id   path      uint                       true  "用户等级记录ID"
// @Param     data body      jyhReq.UserLevelReq  true  "更新用户等级记录请求"
// @Success   200  {object}  response.Response{msg=string}  "更新用户等级记录"
// @Router    /admin/user_benefit/user_levels/{id} [put]
func (a *JyhUserBenefitApi) UpdateUserLevel(c *gin.Context) {
	var req jyhReq.UserLevelReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = userBenefitService.UpdateUserLevel(&req)
	if err != nil {
		global.GVA_LOG.Error("更新用户等级记录失败！", zap.Error(err))
		response.FailWithMessage("更新用户等级记录失败:"+err.Error(), c)
		return
	}

	response.OkWithMessage("更新用户等级记录成功", c)
}

// AuditUserLevel 审核用户等级记录
// @Tags      UserBenefit
// @Summary   审核用户等级记录
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data body      jyhReq.UserLevelAuditReq  true  "审核用户等级记录请求"
// @Success   200  {object}  response.Response{msg=string}  "审核用户等级记录"
// @Router    /admin/user_benefit/audit [post]
func (a *JyhUserBenefitApi) AuditUserLevel(c *gin.Context) {
	var req jyhReq.UserLevelAuditReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = userBenefitService.AuditUserLevel(&req)
	if err != nil {
		global.GVA_LOG.Error("审核用户等级记录失败！", zap.Error(err))
		response.FailWithMessage("审核用户等级记录失败:"+err.Error(), c)
		return
	}

	var auditResult string
	if req.Status == "active" {
		auditResult = "审核通过"
	} else {
		auditResult = "审核拒绝"
	}

	response.OkWithMessage(auditResult+"成功", c)
}

// DeleteUserLevel 删除用户等级记录
// @Tags      UserBenefit
// @Summary   删除用户等级记录
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     id  path      uint  true  "用户等级记录ID"
// @Success   200 {object}  response.Response{msg=string}  "删除用户等级记录"
// @Router    /admin/user_benefit/user_levels/{id} [delete]
func (a *JyhUserBenefitApi) DeleteUserLevel(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.FailWithMessage("无效的ID参数", c)
		return
	}

	err = userBenefitService.DeleteUserLevel(uint(id))
	if err != nil {
		global.GVA_LOG.Error("删除用户等级记录失败！", zap.Error(err))
		response.FailWithMessage("删除用户等级记录失败:"+err.Error(), c)
		return
	}

	response.OkWithMessage("删除用户等级记录成功", c)
}

// ===== 用户权益快照接口（主要用于查询） =====

// GetUserBenefitSnapshotList 获取用户权益快照列表
// @Tags      UserBenefit
// @Summary   获取用户权益快照列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     jyhReq.UserBenefitSnapshotListReq  true  "获取用户权益快照列表请求"
// @Success   200   {object}  response.Response{data=response.PageResult,msg=string}  "获取用户权益快照列表"
// @Router    /admin/user_benefit/user_benefit_snapshots [get]
func (a *JyhUserBenefitApi) GetUserBenefitSnapshotList(c *gin.Context) {
	var req jyhReq.UserBenefitSnapshotListReq
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 10
	}

	list, total, err := userBenefitService.GetUserBenefitSnapshotList(&req)
	if err != nil {
		global.GVA_LOG.Error("获取用户权益快照列表失败！", zap.Error(err))
		response.FailWithMessage("获取用户权益快照列表失败:"+err.Error(), c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取用户权益快照列表成功", c)
}

// GetUserBenefitSnapshots 查询指定用户的权益快照
// @Tags      UserBenefit
// @Summary   查询指定用户的权益快照
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     userId  path      uint             true   "用户ID"
// @Param     data    query     request.PageInfo false  "分页参数"
// @Success   200     {object}  response.Response{data=response.PageResult,msg=string}  "获取用户权益快照"
// @Router    /admin/user_benefit/users/{userId}/benefit_snapshots [get]
func (a *JyhUserBenefitApi) GetUserBenefitSnapshots(c *gin.Context) {
	/*	userId, err := strconv.ParseUint(c.Param("userId"), 10, 32)
		if err != nil {
			response.FailWithMessage("无效的用户ID参数", c)
			return
		}

		var pageReq struct {
			Page     int `json:"page" form:"page"`
			PageSize int `json:"pageSize" form:"pageSize"`
		}
		err = c.ShouldBindQuery(&pageReq)
		if err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}

		if pageReq.Page == 0 {
			pageReq.Page = 1
		}
		if pageReq.PageSize == 0 {
			pageReq.PageSize = 10
		}

		req := &jyhReq.UserBenefitSnapshotListReq{
			PageInfo:
			UserID: uint(userId),
		}

		list, total, err := userBenefitService.GetUserBenefitSnapshotList(req)
		if err != nil {
			global.GVA_LOG.Error("获取用户权益快照失败！", zap.Error(err))
			response.FailWithMessage("获取用户权益快照失败:"+err.Error(), c)
			return
		}

		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取用户权益快照成功", c)*/
}
