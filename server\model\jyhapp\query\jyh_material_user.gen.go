// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
)

func newJyhMaterialUser(db *gorm.DB, opts ...gen.DOOption) jyhMaterialUser {
	_jyhMaterialUser := jyhMaterialUser{}

	_jyhMaterialUser.jyhMaterialUserDo.UseDB(db, opts...)
	_jyhMaterialUser.jyhMaterialUserDo.UseModel(&jyhapp.JyhMaterialUser{})

	tableName := _jyhMaterialUser.jyhMaterialUserDo.TableName()
	_jyhMaterialUser.ALL = field.NewAsterisk(tableName)
	_jyhMaterialUser.ID = field.NewUint(tableName, "id")
	_jyhMaterialUser.MaterialID = field.NewUint(tableName, "material_id")
	_jyhMaterialUser.UserID = field.NewUint(tableName, "user_id")
	_jyhMaterialUser.SendAt = field.NewTime(tableName, "send_at")
	_jyhMaterialUser.ClaimedAt = field.NewTime(tableName, "claimed_at")
	_jyhMaterialUser.Status = field.NewUint(tableName, "status")
	_jyhMaterialUser.IsRecognized = field.NewUint(tableName, "is_recognized")

	_jyhMaterialUser.fillFieldMap()

	return _jyhMaterialUser
}

type jyhMaterialUser struct {
	jyhMaterialUserDo

	ALL          field.Asterisk
	ID           field.Uint
	MaterialID   field.Uint
	UserID       field.Uint
	SendAt       field.Time
	ClaimedAt    field.Time
	Status       field.Uint
	IsRecognized field.Uint

	fieldMap map[string]field.Expr
}

func (j jyhMaterialUser) Table(newTableName string) *jyhMaterialUser {
	j.jyhMaterialUserDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j jyhMaterialUser) As(alias string) *jyhMaterialUser {
	j.jyhMaterialUserDo.DO = *(j.jyhMaterialUserDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *jyhMaterialUser) updateTableName(table string) *jyhMaterialUser {
	j.ALL = field.NewAsterisk(table)
	j.ID = field.NewUint(table, "id")
	j.MaterialID = field.NewUint(table, "material_id")
	j.UserID = field.NewUint(table, "user_id")
	j.SendAt = field.NewTime(table, "send_at")
	j.ClaimedAt = field.NewTime(table, "claimed_at")
	j.Status = field.NewUint(table, "status")
	j.IsRecognized = field.NewUint(table, "is_recognized")

	j.fillFieldMap()

	return j
}

func (j *jyhMaterialUser) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *jyhMaterialUser) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 7)
	j.fieldMap["id"] = j.ID
	j.fieldMap["material_id"] = j.MaterialID
	j.fieldMap["user_id"] = j.UserID
	j.fieldMap["send_at"] = j.SendAt
	j.fieldMap["claimed_at"] = j.ClaimedAt
	j.fieldMap["status"] = j.Status
	j.fieldMap["is_recognized"] = j.IsRecognized
}

func (j jyhMaterialUser) clone(db *gorm.DB) jyhMaterialUser {
	j.jyhMaterialUserDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j jyhMaterialUser) replaceDB(db *gorm.DB) jyhMaterialUser {
	j.jyhMaterialUserDo.ReplaceDB(db)
	return j
}

type jyhMaterialUserDo struct{ gen.DO }

func (j jyhMaterialUserDo) Debug() *jyhMaterialUserDo {
	return j.withDO(j.DO.Debug())
}

func (j jyhMaterialUserDo) WithContext(ctx context.Context) *jyhMaterialUserDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jyhMaterialUserDo) ReadDB() *jyhMaterialUserDo {
	return j.Clauses(dbresolver.Read)
}

func (j jyhMaterialUserDo) WriteDB() *jyhMaterialUserDo {
	return j.Clauses(dbresolver.Write)
}

func (j jyhMaterialUserDo) Session(config *gorm.Session) *jyhMaterialUserDo {
	return j.withDO(j.DO.Session(config))
}

func (j jyhMaterialUserDo) Clauses(conds ...clause.Expression) *jyhMaterialUserDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jyhMaterialUserDo) Returning(value interface{}, columns ...string) *jyhMaterialUserDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jyhMaterialUserDo) Not(conds ...gen.Condition) *jyhMaterialUserDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jyhMaterialUserDo) Or(conds ...gen.Condition) *jyhMaterialUserDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jyhMaterialUserDo) Select(conds ...field.Expr) *jyhMaterialUserDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jyhMaterialUserDo) Where(conds ...gen.Condition) *jyhMaterialUserDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jyhMaterialUserDo) Order(conds ...field.Expr) *jyhMaterialUserDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jyhMaterialUserDo) Distinct(cols ...field.Expr) *jyhMaterialUserDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jyhMaterialUserDo) Omit(cols ...field.Expr) *jyhMaterialUserDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jyhMaterialUserDo) Join(table schema.Tabler, on ...field.Expr) *jyhMaterialUserDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jyhMaterialUserDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jyhMaterialUserDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jyhMaterialUserDo) RightJoin(table schema.Tabler, on ...field.Expr) *jyhMaterialUserDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jyhMaterialUserDo) Group(cols ...field.Expr) *jyhMaterialUserDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jyhMaterialUserDo) Having(conds ...gen.Condition) *jyhMaterialUserDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jyhMaterialUserDo) Limit(limit int) *jyhMaterialUserDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jyhMaterialUserDo) Offset(offset int) *jyhMaterialUserDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jyhMaterialUserDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jyhMaterialUserDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jyhMaterialUserDo) Unscoped() *jyhMaterialUserDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jyhMaterialUserDo) Create(values ...*jyhapp.JyhMaterialUser) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jyhMaterialUserDo) CreateInBatches(values []*jyhapp.JyhMaterialUser, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jyhMaterialUserDo) Save(values ...*jyhapp.JyhMaterialUser) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jyhMaterialUserDo) First() (*jyhapp.JyhMaterialUser, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterialUser), nil
	}
}

func (j jyhMaterialUserDo) Take() (*jyhapp.JyhMaterialUser, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterialUser), nil
	}
}

func (j jyhMaterialUserDo) Last() (*jyhapp.JyhMaterialUser, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterialUser), nil
	}
}

func (j jyhMaterialUserDo) Find() ([]*jyhapp.JyhMaterialUser, error) {
	result, err := j.DO.Find()
	return result.([]*jyhapp.JyhMaterialUser), err
}

func (j jyhMaterialUserDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*jyhapp.JyhMaterialUser, err error) {
	buf := make([]*jyhapp.JyhMaterialUser, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jyhMaterialUserDo) FindInBatches(result *[]*jyhapp.JyhMaterialUser, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jyhMaterialUserDo) Attrs(attrs ...field.AssignExpr) *jyhMaterialUserDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jyhMaterialUserDo) Assign(attrs ...field.AssignExpr) *jyhMaterialUserDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jyhMaterialUserDo) Joins(fields ...field.RelationField) *jyhMaterialUserDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jyhMaterialUserDo) Preload(fields ...field.RelationField) *jyhMaterialUserDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jyhMaterialUserDo) FirstOrInit() (*jyhapp.JyhMaterialUser, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterialUser), nil
	}
}

func (j jyhMaterialUserDo) FirstOrCreate() (*jyhapp.JyhMaterialUser, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterialUser), nil
	}
}

func (j jyhMaterialUserDo) FindByPage(offset int, limit int) (result []*jyhapp.JyhMaterialUser, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jyhMaterialUserDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jyhMaterialUserDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jyhMaterialUserDo) Delete(models ...*jyhapp.JyhMaterialUser) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jyhMaterialUserDo) withDO(do gen.Dao) *jyhMaterialUserDo {
	j.DO = *do.(*gen.DO)
	return j
}
