// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
)

func newJyhMaterialUpload(db *gorm.DB, opts ...gen.DOOption) jyhMaterialUpload {
	_jyhMaterialUpload := jyhMaterialUpload{}

	_jyhMaterialUpload.jyhMaterialUploadDo.UseDB(db, opts...)
	_jyhMaterialUpload.jyhMaterialUploadDo.UseModel(&jyhapp.JyhMaterialUpload{})

	tableName := _jyhMaterialUpload.jyhMaterialUploadDo.TableName()
	_jyhMaterialUpload.ALL = field.NewAsterisk(tableName)
	_jyhMaterialUpload.ID = field.NewUint(tableName, "id")
	_jyhMaterialUpload.CreatedAt = field.NewTime(tableName, "created_at")
	_jyhMaterialUpload.UpdatedAt = field.NewTime(tableName, "updated_at")
	_jyhMaterialUpload.MaterialUserID = field.NewUint(tableName, "material_user_id")
	_jyhMaterialUpload.FileUrl = field.NewString(tableName, "file_url")
	_jyhMaterialUpload.UploadTime = field.NewTime(tableName, "upload_time")
	_jyhMaterialUpload.Status = field.NewUint(tableName, "status")
	_jyhMaterialUpload.Ret = field.NewString(tableName, "ret")

	_jyhMaterialUpload.fillFieldMap()

	return _jyhMaterialUpload
}

type jyhMaterialUpload struct {
	jyhMaterialUploadDo

	ALL            field.Asterisk
	ID             field.Uint
	CreatedAt      field.Time
	UpdatedAt      field.Time
	MaterialUserID field.Uint
	FileUrl        field.String
	UploadTime     field.Time
	Status         field.Uint
	Ret            field.String

	fieldMap map[string]field.Expr
}

func (j jyhMaterialUpload) Table(newTableName string) *jyhMaterialUpload {
	j.jyhMaterialUploadDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j jyhMaterialUpload) As(alias string) *jyhMaterialUpload {
	j.jyhMaterialUploadDo.DO = *(j.jyhMaterialUploadDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *jyhMaterialUpload) updateTableName(table string) *jyhMaterialUpload {
	j.ALL = field.NewAsterisk(table)
	j.ID = field.NewUint(table, "id")
	j.CreatedAt = field.NewTime(table, "created_at")
	j.UpdatedAt = field.NewTime(table, "updated_at")
	j.MaterialUserID = field.NewUint(table, "material_user_id")
	j.FileUrl = field.NewString(table, "file_url")
	j.UploadTime = field.NewTime(table, "upload_time")
	j.Status = field.NewUint(table, "status")
	j.Ret = field.NewString(table, "ret")

	j.fillFieldMap()

	return j
}

func (j *jyhMaterialUpload) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *jyhMaterialUpload) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 8)
	j.fieldMap["id"] = j.ID
	j.fieldMap["created_at"] = j.CreatedAt
	j.fieldMap["updated_at"] = j.UpdatedAt
	j.fieldMap["material_user_id"] = j.MaterialUserID
	j.fieldMap["file_url"] = j.FileUrl
	j.fieldMap["upload_time"] = j.UploadTime
	j.fieldMap["status"] = j.Status
	j.fieldMap["ret"] = j.Ret
}

func (j jyhMaterialUpload) clone(db *gorm.DB) jyhMaterialUpload {
	j.jyhMaterialUploadDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j jyhMaterialUpload) replaceDB(db *gorm.DB) jyhMaterialUpload {
	j.jyhMaterialUploadDo.ReplaceDB(db)
	return j
}

type jyhMaterialUploadDo struct{ gen.DO }

func (j jyhMaterialUploadDo) Debug() *jyhMaterialUploadDo {
	return j.withDO(j.DO.Debug())
}

func (j jyhMaterialUploadDo) WithContext(ctx context.Context) *jyhMaterialUploadDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jyhMaterialUploadDo) ReadDB() *jyhMaterialUploadDo {
	return j.Clauses(dbresolver.Read)
}

func (j jyhMaterialUploadDo) WriteDB() *jyhMaterialUploadDo {
	return j.Clauses(dbresolver.Write)
}

func (j jyhMaterialUploadDo) Session(config *gorm.Session) *jyhMaterialUploadDo {
	return j.withDO(j.DO.Session(config))
}

func (j jyhMaterialUploadDo) Clauses(conds ...clause.Expression) *jyhMaterialUploadDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jyhMaterialUploadDo) Returning(value interface{}, columns ...string) *jyhMaterialUploadDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jyhMaterialUploadDo) Not(conds ...gen.Condition) *jyhMaterialUploadDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jyhMaterialUploadDo) Or(conds ...gen.Condition) *jyhMaterialUploadDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jyhMaterialUploadDo) Select(conds ...field.Expr) *jyhMaterialUploadDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jyhMaterialUploadDo) Where(conds ...gen.Condition) *jyhMaterialUploadDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jyhMaterialUploadDo) Order(conds ...field.Expr) *jyhMaterialUploadDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jyhMaterialUploadDo) Distinct(cols ...field.Expr) *jyhMaterialUploadDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jyhMaterialUploadDo) Omit(cols ...field.Expr) *jyhMaterialUploadDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jyhMaterialUploadDo) Join(table schema.Tabler, on ...field.Expr) *jyhMaterialUploadDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jyhMaterialUploadDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jyhMaterialUploadDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jyhMaterialUploadDo) RightJoin(table schema.Tabler, on ...field.Expr) *jyhMaterialUploadDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jyhMaterialUploadDo) Group(cols ...field.Expr) *jyhMaterialUploadDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jyhMaterialUploadDo) Having(conds ...gen.Condition) *jyhMaterialUploadDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jyhMaterialUploadDo) Limit(limit int) *jyhMaterialUploadDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jyhMaterialUploadDo) Offset(offset int) *jyhMaterialUploadDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jyhMaterialUploadDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jyhMaterialUploadDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jyhMaterialUploadDo) Unscoped() *jyhMaterialUploadDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jyhMaterialUploadDo) Create(values ...*jyhapp.JyhMaterialUpload) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jyhMaterialUploadDo) CreateInBatches(values []*jyhapp.JyhMaterialUpload, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jyhMaterialUploadDo) Save(values ...*jyhapp.JyhMaterialUpload) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jyhMaterialUploadDo) First() (*jyhapp.JyhMaterialUpload, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterialUpload), nil
	}
}

func (j jyhMaterialUploadDo) Take() (*jyhapp.JyhMaterialUpload, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterialUpload), nil
	}
}

func (j jyhMaterialUploadDo) Last() (*jyhapp.JyhMaterialUpload, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterialUpload), nil
	}
}

func (j jyhMaterialUploadDo) Find() ([]*jyhapp.JyhMaterialUpload, error) {
	result, err := j.DO.Find()
	return result.([]*jyhapp.JyhMaterialUpload), err
}

func (j jyhMaterialUploadDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*jyhapp.JyhMaterialUpload, err error) {
	buf := make([]*jyhapp.JyhMaterialUpload, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jyhMaterialUploadDo) FindInBatches(result *[]*jyhapp.JyhMaterialUpload, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jyhMaterialUploadDo) Attrs(attrs ...field.AssignExpr) *jyhMaterialUploadDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jyhMaterialUploadDo) Assign(attrs ...field.AssignExpr) *jyhMaterialUploadDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jyhMaterialUploadDo) Joins(fields ...field.RelationField) *jyhMaterialUploadDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jyhMaterialUploadDo) Preload(fields ...field.RelationField) *jyhMaterialUploadDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jyhMaterialUploadDo) FirstOrInit() (*jyhapp.JyhMaterialUpload, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterialUpload), nil
	}
}

func (j jyhMaterialUploadDo) FirstOrCreate() (*jyhapp.JyhMaterialUpload, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterialUpload), nil
	}
}

func (j jyhMaterialUploadDo) FindByPage(offset int, limit int) (result []*jyhapp.JyhMaterialUpload, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jyhMaterialUploadDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jyhMaterialUploadDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jyhMaterialUploadDo) Delete(models ...*jyhapp.JyhMaterialUpload) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jyhMaterialUploadDo) withDO(do gen.Dao) *jyhMaterialUploadDo {
	j.DO = *do.(*gen.DO)
	return j
}
