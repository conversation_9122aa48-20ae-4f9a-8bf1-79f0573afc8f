// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
)

func newJyhRecommendItem(db *gorm.DB, opts ...gen.DOOption) jyhRecommendItem {
	_jyhRecommendItem := jyhRecommendItem{}

	_jyhRecommendItem.jyhRecommendItemDo.UseDB(db, opts...)
	_jyhRecommendItem.jyhRecommendItemDo.UseModel(&jyhapp.JyhRecommendItem{})

	tableName := _jyhRecommendItem.jyhRecommendItemDo.TableName()
	_jyhRecommendItem.ALL = field.NewAsterisk(tableName)
	_jyhRecommendItem.ID = field.NewUint(tableName, "id")
	_jyhRecommendItem.CreatedAt = field.NewTime(tableName, "created_at")
	_jyhRecommendItem.UpdatedAt = field.NewTime(tableName, "updated_at")
	_jyhRecommendItem.DeletedAt = field.NewField(tableName, "deleted_at")
	_jyhRecommendItem.PositionID = field.NewUint(tableName, "position_id")
	_jyhRecommendItem.Title = field.NewString(tableName, "title")
	_jyhRecommendItem.ContentType = field.NewString(tableName, "content_type")
	_jyhRecommendItem.ContentValue = field.NewString(tableName, "content_value")
	_jyhRecommendItem.LinkUrl = field.NewString(tableName, "link_url")
	_jyhRecommendItem.BgColor = field.NewString(tableName, "bg_color")
	_jyhRecommendItem.TextColor = field.NewString(tableName, "text_color")
	_jyhRecommendItem.StartTime = field.NewTime(tableName, "start_time")
	_jyhRecommendItem.EndTime = field.NewTime(tableName, "end_time")
	_jyhRecommendItem.Sort = field.NewInt(tableName, "sort")
	_jyhRecommendItem.Status = field.NewInt(tableName, "status")
	_jyhRecommendItem.Position = jyhRecommendItemBelongsToPosition{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Position", "jyhapp.JyhRecommendPosition"),
		Items: struct {
			field.RelationField
			Position struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Position.Items", "jyhapp.JyhRecommendItem"),
			Position: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Position.Items.Position", "jyhapp.JyhRecommendPosition"),
			},
		},
	}

	_jyhRecommendItem.fillFieldMap()

	return _jyhRecommendItem
}

type jyhRecommendItem struct {
	jyhRecommendItemDo

	ALL          field.Asterisk
	ID           field.Uint
	CreatedAt    field.Time
	UpdatedAt    field.Time
	DeletedAt    field.Field
	PositionID   field.Uint
	Title        field.String
	ContentType  field.String
	ContentValue field.String
	LinkUrl      field.String
	BgColor      field.String
	TextColor    field.String
	StartTime    field.Time
	EndTime      field.Time
	Sort         field.Int
	Status       field.Int
	Position     jyhRecommendItemBelongsToPosition

	fieldMap map[string]field.Expr
}

func (j jyhRecommendItem) Table(newTableName string) *jyhRecommendItem {
	j.jyhRecommendItemDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j jyhRecommendItem) As(alias string) *jyhRecommendItem {
	j.jyhRecommendItemDo.DO = *(j.jyhRecommendItemDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *jyhRecommendItem) updateTableName(table string) *jyhRecommendItem {
	j.ALL = field.NewAsterisk(table)
	j.ID = field.NewUint(table, "id")
	j.CreatedAt = field.NewTime(table, "created_at")
	j.UpdatedAt = field.NewTime(table, "updated_at")
	j.DeletedAt = field.NewField(table, "deleted_at")
	j.PositionID = field.NewUint(table, "position_id")
	j.Title = field.NewString(table, "title")
	j.ContentType = field.NewString(table, "content_type")
	j.ContentValue = field.NewString(table, "content_value")
	j.LinkUrl = field.NewString(table, "link_url")
	j.BgColor = field.NewString(table, "bg_color")
	j.TextColor = field.NewString(table, "text_color")
	j.StartTime = field.NewTime(table, "start_time")
	j.EndTime = field.NewTime(table, "end_time")
	j.Sort = field.NewInt(table, "sort")
	j.Status = field.NewInt(table, "status")

	j.fillFieldMap()

	return j
}

func (j *jyhRecommendItem) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *jyhRecommendItem) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 16)
	j.fieldMap["id"] = j.ID
	j.fieldMap["created_at"] = j.CreatedAt
	j.fieldMap["updated_at"] = j.UpdatedAt
	j.fieldMap["deleted_at"] = j.DeletedAt
	j.fieldMap["position_id"] = j.PositionID
	j.fieldMap["title"] = j.Title
	j.fieldMap["content_type"] = j.ContentType
	j.fieldMap["content_value"] = j.ContentValue
	j.fieldMap["link_url"] = j.LinkUrl
	j.fieldMap["bg_color"] = j.BgColor
	j.fieldMap["text_color"] = j.TextColor
	j.fieldMap["start_time"] = j.StartTime
	j.fieldMap["end_time"] = j.EndTime
	j.fieldMap["sort"] = j.Sort
	j.fieldMap["status"] = j.Status

}

func (j jyhRecommendItem) clone(db *gorm.DB) jyhRecommendItem {
	j.jyhRecommendItemDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j jyhRecommendItem) replaceDB(db *gorm.DB) jyhRecommendItem {
	j.jyhRecommendItemDo.ReplaceDB(db)
	return j
}

type jyhRecommendItemBelongsToPosition struct {
	db *gorm.DB

	field.RelationField

	Items struct {
		field.RelationField
		Position struct {
			field.RelationField
		}
	}
}

func (a jyhRecommendItemBelongsToPosition) Where(conds ...field.Expr) *jyhRecommendItemBelongsToPosition {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a jyhRecommendItemBelongsToPosition) WithContext(ctx context.Context) *jyhRecommendItemBelongsToPosition {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a jyhRecommendItemBelongsToPosition) Session(session *gorm.Session) *jyhRecommendItemBelongsToPosition {
	a.db = a.db.Session(session)
	return &a
}

func (a jyhRecommendItemBelongsToPosition) Model(m *jyhapp.JyhRecommendItem) *jyhRecommendItemBelongsToPositionTx {
	return &jyhRecommendItemBelongsToPositionTx{a.db.Model(m).Association(a.Name())}
}

type jyhRecommendItemBelongsToPositionTx struct{ tx *gorm.Association }

func (a jyhRecommendItemBelongsToPositionTx) Find() (result *jyhapp.JyhRecommendPosition, err error) {
	return result, a.tx.Find(&result)
}

func (a jyhRecommendItemBelongsToPositionTx) Append(values ...*jyhapp.JyhRecommendPosition) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a jyhRecommendItemBelongsToPositionTx) Replace(values ...*jyhapp.JyhRecommendPosition) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a jyhRecommendItemBelongsToPositionTx) Delete(values ...*jyhapp.JyhRecommendPosition) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a jyhRecommendItemBelongsToPositionTx) Clear() error {
	return a.tx.Clear()
}

func (a jyhRecommendItemBelongsToPositionTx) Count() int64 {
	return a.tx.Count()
}

type jyhRecommendItemDo struct{ gen.DO }

func (j jyhRecommendItemDo) Debug() *jyhRecommendItemDo {
	return j.withDO(j.DO.Debug())
}

func (j jyhRecommendItemDo) WithContext(ctx context.Context) *jyhRecommendItemDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jyhRecommendItemDo) ReadDB() *jyhRecommendItemDo {
	return j.Clauses(dbresolver.Read)
}

func (j jyhRecommendItemDo) WriteDB() *jyhRecommendItemDo {
	return j.Clauses(dbresolver.Write)
}

func (j jyhRecommendItemDo) Session(config *gorm.Session) *jyhRecommendItemDo {
	return j.withDO(j.DO.Session(config))
}

func (j jyhRecommendItemDo) Clauses(conds ...clause.Expression) *jyhRecommendItemDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jyhRecommendItemDo) Returning(value interface{}, columns ...string) *jyhRecommendItemDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jyhRecommendItemDo) Not(conds ...gen.Condition) *jyhRecommendItemDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jyhRecommendItemDo) Or(conds ...gen.Condition) *jyhRecommendItemDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jyhRecommendItemDo) Select(conds ...field.Expr) *jyhRecommendItemDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jyhRecommendItemDo) Where(conds ...gen.Condition) *jyhRecommendItemDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jyhRecommendItemDo) Order(conds ...field.Expr) *jyhRecommendItemDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jyhRecommendItemDo) Distinct(cols ...field.Expr) *jyhRecommendItemDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jyhRecommendItemDo) Omit(cols ...field.Expr) *jyhRecommendItemDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jyhRecommendItemDo) Join(table schema.Tabler, on ...field.Expr) *jyhRecommendItemDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jyhRecommendItemDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jyhRecommendItemDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jyhRecommendItemDo) RightJoin(table schema.Tabler, on ...field.Expr) *jyhRecommendItemDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jyhRecommendItemDo) Group(cols ...field.Expr) *jyhRecommendItemDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jyhRecommendItemDo) Having(conds ...gen.Condition) *jyhRecommendItemDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jyhRecommendItemDo) Limit(limit int) *jyhRecommendItemDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jyhRecommendItemDo) Offset(offset int) *jyhRecommendItemDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jyhRecommendItemDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jyhRecommendItemDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jyhRecommendItemDo) Unscoped() *jyhRecommendItemDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jyhRecommendItemDo) Create(values ...*jyhapp.JyhRecommendItem) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jyhRecommendItemDo) CreateInBatches(values []*jyhapp.JyhRecommendItem, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jyhRecommendItemDo) Save(values ...*jyhapp.JyhRecommendItem) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jyhRecommendItemDo) First() (*jyhapp.JyhRecommendItem, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhRecommendItem), nil
	}
}

func (j jyhRecommendItemDo) Take() (*jyhapp.JyhRecommendItem, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhRecommendItem), nil
	}
}

func (j jyhRecommendItemDo) Last() (*jyhapp.JyhRecommendItem, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhRecommendItem), nil
	}
}

func (j jyhRecommendItemDo) Find() ([]*jyhapp.JyhRecommendItem, error) {
	result, err := j.DO.Find()
	return result.([]*jyhapp.JyhRecommendItem), err
}

func (j jyhRecommendItemDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*jyhapp.JyhRecommendItem, err error) {
	buf := make([]*jyhapp.JyhRecommendItem, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jyhRecommendItemDo) FindInBatches(result *[]*jyhapp.JyhRecommendItem, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jyhRecommendItemDo) Attrs(attrs ...field.AssignExpr) *jyhRecommendItemDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jyhRecommendItemDo) Assign(attrs ...field.AssignExpr) *jyhRecommendItemDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jyhRecommendItemDo) Joins(fields ...field.RelationField) *jyhRecommendItemDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jyhRecommendItemDo) Preload(fields ...field.RelationField) *jyhRecommendItemDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jyhRecommendItemDo) FirstOrInit() (*jyhapp.JyhRecommendItem, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhRecommendItem), nil
	}
}

func (j jyhRecommendItemDo) FirstOrCreate() (*jyhapp.JyhRecommendItem, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhRecommendItem), nil
	}
}

func (j jyhRecommendItemDo) FindByPage(offset int, limit int) (result []*jyhapp.JyhRecommendItem, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jyhRecommendItemDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jyhRecommendItemDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jyhRecommendItemDo) Delete(models ...*jyhapp.JyhRecommendItem) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jyhRecommendItemDo) withDO(do gen.Dao) *jyhRecommendItemDo {
	j.DO = *do.(*gen.DO)
	return j
}
