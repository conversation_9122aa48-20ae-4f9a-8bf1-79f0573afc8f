package jyhapp

// GenInitTables 需要初始化的表
func GenInitTables() []interface{} {
	return []interface{}{
		// 会员相关
		JyhUser{},
		JyhUserExt{},
		JyhUserTag{},
		JyhUserTagRelation{},
		JyhUserClosure{},
		JyhOrder{},
		JyhUserLevel{},
		JyhUserBenefitSnapshot{},
		JyhUserShipLevel{},
		JyhUserBenefit{},
		JyhUserShipLevelBenefit{},
		JyhUserCertificate{}, // 用户粉丝凭证
		// 素材相关
		JyhMaterial{},
		JyhMaterialCategory{},
		JyhMaterialClaimRecord{},
		JyhMaterialFile{},
		JyhTag{},
		JyhMaterialTag{},
		JyhMaterialUser{},
		JyhMaterialCustom{},
		JyhMaterialUpload{},
		JyhMaterialRuleConfig{},
		// 邀请码相关
		JyhInviteCode{},
		JyhInviteRecord{},
		// 佣金相关
		JyhCommission{},
		JyhCommissionRate{},
		JyhMcnOrderDetail{},
		// 推荐位相关
		JyhRecommendPosition{},
		JyhRecommendItem{},
		JyhRecommendDisplayLog{},
		// 课程相关
		JyhCourse{},
		JyhCourseCategory{},
		// 文章相关
		JyhArticle{},
		JyhArticleCategory{},
		//直播
		JyhUserLiveRoom{},

		// 用户余额系统表
		JyhUserAccount{},
		JyhUserAccountTransaction{},
		// 用户积分系统表
		JyhPointsRule{},
		JyhPointsRecord{},
		JyhPointsExchange{},
	}
}
