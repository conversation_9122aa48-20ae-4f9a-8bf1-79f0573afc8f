package ws

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
	sysModel "github.com/flipped-aurora/gin-vue-admin/server/plugin/customerservice/model"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/customerservice/tools"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

type Message struct {
	Sender    string `json:"sender"`
	Receiver  string `json:"receiver"`
	Content   string `json:"content"`
	MsgType   string `json:"msg_type"` //对应msg表的msg_type
	Role      string `json:"role"`
	Timestamp int64  `json:"timestamp"`
	Nickname  string `json:"nickname"`
	AvatarUrl string `json:"avatar_url"`
	IsKf      int64  `json:"is_kf"`
}

type TypeMsg struct {
	Type string      `json:"type"`
	Data interface{} `json:"data,omitempty"`
}

// 消息类型常量
const (
	MsgTypeMessage  = "message"  // 普通消息
	MsgTypePing     = "ping"     // 心跳
	MsgTypePong     = "pong"     // 心跳回复
	MsgTypeOnline   = "online"   // 上线
	MsgTypeOffline  = "offline"  // 下线
	MsgTypeQueue    = "queue"    // 排队通知
	MsgTypeTransfer = "transfer" // 转接通知
	MsgTypeTicket   = "ticket"   // 工单状态
	MsgTypeAssign   = "assign"   // 分配客服
)

type Client struct {
	UserID       string
	Role         string
	Socket       *websocket.Conn
	Send         chan []byte
	LastPingTime time.Time

	// 新增字段
	ServiceID    int64  // 客服ID（对于客服角色）
	Tags         []uint // 客服标签（对于客服角色）
	CurrentUsers []uint // 当前服务的用户列表
	Status       string // 客服状态：idle/busy/offline
	TicketID     uint   // 当前工单ID
	QueueID      uint   // 当前排队ID
}

type ClientManager struct {
	Clients    map[string]*Client
	Broadcast  chan TypeMsg
	Register   chan *Client
	Unregister chan *Client
}

var Manager = ClientManager{
	Clients:    make(map[string]*Client),
	Broadcast:  make(chan TypeMsg),
	Register:   make(chan *Client),
	Unregister: make(chan *Client),
}

// 定时检查连接的活动状态
func (manager *ClientManager) CheckClientActivity() {
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		<-ticker.C
		now := time.Now()

		for ck, client := range manager.Clients {
			// 如果超过一定时间没有收到ping，则断开连接
			fmt.Println(ck)
			fmt.Println(now.Sub(client.LastPingTime))
			if now.Sub(client.LastPingTime) > 120*time.Second {
				client.Socket.Close()
				delete(manager.Clients, ck)
				//设置离线
				if client.Role == "user" {
					setUserOnline("offline", client.UserID)
				}
			}
		}
	}
}

func (manager *ClientManager) Start() {
	for {
		select {
		case conn := <-manager.Register:
			key := conn.Role + conn.UserID

			// 处理重复连接
			if existingConn, ok := manager.Clients[key]; ok {
				existingConn.Socket.Close()
				delete(manager.Clients, key)
			}

			manager.Clients[key] = conn
			fmt.Println(key)

			// 新增：连接建立后的处理
			if conn.Role == "user" {
				handleUserConnect(conn)
			} else if conn.Role == "kf" {
				handleServiceConnect(conn)
			}

		case conn := <-manager.Unregister:
			key := conn.Role + conn.UserID
			if existingConn, ok := manager.Clients[key]; ok && existingConn == conn {
				delete(manager.Clients, key)
			}
		case message := <-manager.Broadcast:
			// 根据消息类型进行智能路由
			routeMessage(message)
		}
	}
}

func (c *Client) Read() {
	defer func() {
		// 断开连接时的清理逻辑
		if c.Role == "user" {
			handleUserDisconnect(c)
		} else if c.Role == "kf" {
			handleServiceDisconnect(c)
		}
		Manager.Unregister <- c
		c.Socket.Close()
	}()
	c.Socket.SetReadLimit(512)

	for {
		_, message, err := c.Socket.ReadMessage()
		if err != nil {
			break
		}
		var msg TypeMsg
		if err := json.Unmarshal(message, &msg); err != nil {
			continue
		}
		switch msg.Type {
		case MsgTypePing:
			// 更新最后一次收到ping消息的时间
			c.LastPingTime = time.Now()

			// 回复pong消息
			pongMsg := TypeMsg{
				Type: MsgTypePong,
				Data: time.Now().Unix(),
			}
			pongStr, _ := json.Marshal(pongMsg)
			c.Send <- pongStr

		case MsgTypeMessage:
			//发送消息走的后台接口去触发广播，改成前端发送消息走这里
			handleMessage(c, msg)

		case MsgTypeTransfer:
			handleTransferRequest(c, msg)

		case "accept_transfer":
			handleTransferAccept(c, msg)

		case "reject_transfer":
			handleTransferReject(c, msg)
		}
	}
}

func (c *Client) Write() {
	defer func() {
		c.Socket.Close()
	}()
	for {
		select {
		case message, ok := <-c.Send:
			c.Socket.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				c.Socket.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}
			if err := c.Socket.WriteMessage(websocket.TextMessage, message); err != nil {
				return
			}
		}
	}
}

var Upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		return true
	},
}

func WsServe(ctx *gin.Context) {
	//token := ctx.Query("token")
	token := ctx.Query("token")
	j := utils.NewJWT()
	claims, err := j.ParseToken(token)
	if err != nil {
		if errors.Is(err, utils.TokenExpired) {
			http.NotFound(ctx.Writer, ctx.Request)
			return
		}
	}
	conn, err := Upgrader.Upgrade(ctx.Writer, ctx.Request, nil)
	if err != nil {
		http.NotFound(ctx.Writer, ctx.Request)
		return
	}
	uidStr := strconv.Itoa(int(claims.BaseClaims.ID))
	client := &Client{
		UserID:       uidStr,
		Role:         "user",
		Socket:       conn,
		Send:         make(chan []byte),
		LastPingTime: time.Now(),
	}

	Manager.Register <- client
	setUserOnline("online", uidStr)

	go client.Read()
	go client.Write()
}

func ServeWsForKefu(ctx *gin.Context) {
	token := ctx.Query("token")
	claims, err := tools.ValidateToken(token)
	if err != nil {
		response.FailWithMessage("token已失效", ctx)
		return
	}
	kfId := claims.ServiceId
	db := global.GVA_DB.Model(&sysModel.SysService{})
	var info sysModel.SysService
	err = db.Find(&info).Error
	if err != nil {
		response.FailWithMessage("客服不存在", ctx)
		return
	}
	conn, err2 := Upgrader.Upgrade(ctx.Writer, ctx.Request, nil)
	if err2 != nil {
		http.NotFound(ctx.Writer, ctx.Request)
		return
	}

	// 新增：获取客服标签和状态
	serviceTags := getServiceTags(kfId)

	client := &Client{
		UserID:       fmt.Sprintf("%v", kfId),
		Role:         "kf",
		Socket:       conn,
		Send:         make(chan []byte),
		LastPingTime: time.Now(),
		ServiceID:    kfId,
		Tags:         serviceTags,
		CurrentUsers: []uint{},
		Status:       "idle",
	}
	Manager.Register <- client

	//设置客服在线
	global.GVA_DB.Model(&sysModel.SysService{}).Where("id = ?", kfId).Update("online", 1)
	// 更新客服工作状态
	updateServiceStatus(kfId, "online")

	go client.Read()
	go client.Write()
}

func setUserOnline(cType string, Id string) {
	//给用户在record表里的客服广播此用户离线
	var list []sysModel.SysServiceRecord
	err := global.GVA_DB.Where("uid=?", Id).Find(&list).Error
	if err == nil && len(list) > 0 {
		for _, rec := range list {
			strSerId := strconv.FormatInt(rec.ServiceId, 10)
			roleKey := "kf" + strSerId
			fmt.Println(roleKey)
			serviceClient, ok := Manager.Clients[roleKey]
			if serviceClient != nil && ok {
				dataMsg := Message{
					MsgType:  "1",
					Sender:   Id,
					Receiver: strSerId,
					Role:     "user",
				}
				sendMsg := TypeMsg{
					Type: cType,
					Data: dataMsg,
				}
				str, _ := json.Marshal(sendMsg)
				serviceClient.Send <- str
			}
		}
	}
}

// ==================== 新增辅助函数 ====================

// getUserTags 获取用户标签
func getUserTags(userID uint) []uint {
	var tags []uint
	var user jyhapp.JyhUser
	err := global.GVA_DB.Preload("Tags").Where("id = ?", userID).First(&user).Error
	if err == nil {
		for _, tag := range user.Tags {
			tags = append(tags, tag.ID)
		}
	}
	return tags
}

// getServiceTags 获取客服标签
func getServiceTags(serviceID int64) []uint {
	var tags []uint
	var service sysModel.SysService
	err := global.GVA_DB.Preload("Tags").Where("id = ?", serviceID).First(&service).Error
	if err == nil {
		for _, tag := range service.Tags {
			tags = append(tags, tag.ID)
		}
	}
	return tags
}

// updateServiceStatus 更新客服状态
func updateServiceStatus(serviceID int64, status string) {
	var workStatus uint = sysModel.WorkStatusAvailable
	if status == "online" {
		workStatus = sysModel.WorkStatusAvailable
	} else if status == "busy" {
		workStatus = sysModel.WorkStatusBusy
	} else if status == "offline" {
		workStatus = sysModel.WorkStatusBreak
	}

	global.GVA_DB.Model(&sysModel.SysService{}).Where("id = ?", serviceID).
		Update("work_status", workStatus)
}

// handleUserDisconnect 处理用户断开连接
func handleUserDisconnect(client *Client) {
	setUserOnline("offline", client.UserID)
}

// handleServiceDisconnect 处理客服断开连接
func handleServiceDisconnect(client *Client) {
	if client.ServiceID > 0 {
		// 设置客服离线
		global.GVA_DB.Model(&sysModel.SysService{}).Where("id = ?", client.ServiceID).
			Update("online", 0)
		updateServiceStatus(client.ServiceID, "offline")
	}
}

// handleMessage 处理普通消息
func handleMessage(client *Client, msg TypeMsg) {
	Manager.Broadcast <- msg
}

// handleTransferRequest 处理转接请求
func handleTransferRequest(client *Client, msg TypeMsg) {
	data := msg.Data.(map[string]interface{})
	toServiceID := int64(data["to_service_id"].(float64))
	reason := data["reason"].(string)

	// 创建转接记录
	transfer := &sysModel.SysServiceTransfer{
		SessionID:     uint(client.ServiceID),
		FromServiceID: client.ServiceID,
		ToServiceID:   toServiceID,
		UserID:        uint(data["user_id"].(float64)),
		Reason:        reason,
		Status:        sysModel.TransferStatusPending,
		TransferTime:  time.Now().Unix(),
	}

	global.GVA_DB.Create(transfer)

	// 通知目标客服
	targetClient := findServiceClient(toServiceID)
	if targetClient != nil {
		transferMsg := TypeMsg{
			Type: MsgTypeTransfer,
			Data: map[string]interface{}{
				"transfer_id":  transfer.ID,
				"from_service": client.ServiceID,
				"user_id":      data["user_id"],
				"reason":       reason,
				"status":       "pending",
			},
		}
		transferData, _ := json.Marshal(transferMsg)
		targetClient.Send <- transferData
	}
}

// handleTransferAccept 处理转接接受
func handleTransferAccept(client *Client, msg TypeMsg) {
	data := msg.Data.(map[string]interface{})
	transferID := uint(data["transfer_id"].(float64))

	// 更新转接状态
	global.GVA_DB.Model(&sysModel.SysServiceTransfer{}).Where("id = ?", transferID).
		Updates(map[string]interface{}{
			"status":      sysModel.TransferRecordStatusRejected,
			"accept_time": time.Now().Unix(),
		})

	// 通知转接结果
	notifyTransferResult(transferID, "accepted")

	// 更新客服负载
	updateServiceLoad(client.ServiceID, "add")
}

// handleTransferReject 处理转接拒绝
func handleTransferReject(client *Client, msg TypeMsg) {
	data := msg.Data.(map[string]interface{})
	transferID := uint(data["transfer_id"].(float64))
	reason := data["reason"].(string)

	// 更新转接状态
	global.GVA_DB.Model(&sysModel.SysServiceTransfer{}).Where("id = ?", transferID).
		Updates(map[string]interface{}{
			"status":        sysModel.TransferRecordStatusRejected,
			"reject_reason": reason,
		})

	// 通知转接结果
	notifyTransferResult(transferID, "rejected")
}

// handleUserConnect 处理用户连接
func handleUserConnect(client *Client) {
	// 用户连接后的处理逻辑
	fmt.Printf("用户 %s 已连接\n", client.UserID)
}

// handleServiceConnect 处理客服连接
func handleServiceConnect(client *Client) {
	// 客服连接后的处理逻辑
	fmt.Printf("客服 %s 已连接\n", client.UserID)
}

// routeMessage 路由消息
func routeMessage(message TypeMsg) {
	switch message.Type {
	case MsgTypeMessage:
		routeNormalMessage(message)
	case MsgTypeQueue:
		routeQueueMessage(message)
	case MsgTypeTransfer:
		routeTransferMessage(message)
	case MsgTypeTicket:
		routeTicketMessage(message)
	default:
		// 默认路由逻辑
		routeNormalMessage(message)
	}
}

// ==================== 消息路由函数 ====================

// routeNormalMessage 路由普通消息
func routeNormalMessage(message TypeMsg) {
	data := message.Data.(map[string]interface{})
	receiver := data["receiver"].(string)
	receiverKey := "user" + receiver
	if data["role"].(string) == "user" {
		receiverKey = "kf" + receiver
	}
	if client, ok := Manager.Clients[receiverKey]; ok {
		str, _ := json.Marshal(message)
		client.Send <- str
	} else {
		fmt.Println(receiverKey + "链接不存在")
	}
}

// routeQueueMessage 路由排队消息
func routeQueueMessage(message TypeMsg) {
	// 排队消息路由逻辑
	data := message.Data.(map[string]interface{})
	if userID, ok := data["user_id"].(string); ok {
		userKey := "user" + userID
		if client, ok := Manager.Clients[userKey]; ok {
			str, _ := json.Marshal(message)
			client.Send <- str
		}
	}
}

// routeTransferMessage 路由转接消息
func routeTransferMessage(message TypeMsg) {
	// 转接消息路由逻辑
	data := message.Data.(map[string]interface{})
	if serviceID, ok := data["service_id"].(string); ok {
		serviceKey := "kf" + serviceID
		if client, ok := Manager.Clients[serviceKey]; ok {
			str, _ := json.Marshal(message)
			client.Send <- str
		}
	}
}

// routeTicketMessage 路由工单消息
func routeTicketMessage(message TypeMsg) {
	// 工单消息路由逻辑
	data := message.Data.(map[string]interface{})
	if targetID, ok := data["target_id"].(string); ok {
		targetKey := data["target_type"].(string) + targetID
		if client, ok := Manager.Clients[targetKey]; ok {
			str, _ := json.Marshal(message)
			client.Send <- str
		}
	}
}

// ==================== 辅助工具函数 ====================

// smartMatchService 智能匹配客服
func smartMatchService(userTags []uint) *sysModel.SysService {
	var service sysModel.SysService

	// 简单的匹配逻辑：找到第一个在线且有匹配标签的客服
	if len(userTags) > 0 {
		// 有标签的情况，找匹配的客服
		query := global.GVA_DB.Table("sys_service").
			Select("sys_service.*").
			Joins("LEFT JOIN sys_service_tag_relation ON sys_service.id = sys_service_tag_relation.service_id").
			Where("sys_service.online = 1 AND sys_service.work_status = ?", sysModel.WorkStatusAvailable).
			Where("sys_service_tag_relation.tag_id IN ?", userTags).
			Order("sys_service.current_user_count ASC")

		if err := query.First(&service).Error; err == nil {
			return &service
		}
	}

	// 没有匹配标签的客服或没有标签，分配默认客服
	err := global.GVA_DB.Where("online = 1 AND work_status = ?", sysModel.WorkStatusAvailable).
		Order("current_user_count ASC").First(&service).Error
	if err != nil {
		// 如果没有可用客服，随便找一个
		global.GVA_DB.Where("status = 1").Order("add_time DESC").First(&service)
	}

	return &service
}

// calculateWaitTime 计算等待时间
func calculateWaitTime(queueNo int) int64 {
	// 简单的等待时间计算：每个排队用户假设需要5分钟
	return int64(queueNo * 5 * 60)
}

// findServiceClient 查找客服客户端
func findServiceClient(serviceID int64) *Client {
	serviceKey := "kf" + strconv.FormatInt(serviceID, 10)
	if client, ok := Manager.Clients[serviceKey]; ok {
		return client
	}
	return nil
}

// findUserClient 查找用户客户端
func findUserClient(userID uint) *Client {
	userKey := "user" + strconv.FormatUint(uint64(userID), 10)
	if client, ok := Manager.Clients[userKey]; ok {
		return client
	}
	return nil
}

// notifyTransferResult 通知转接结果
func notifyTransferResult(transferID uint, result string) {
	var transfer sysModel.SysServiceTransfer
	global.GVA_DB.Where("id = ?", transferID).First(&transfer)

	// 通知原客服
	fromClient := findServiceClient(transfer.FromServiceID)
	if fromClient != nil {
		msg := TypeMsg{
			Type: MsgTypeTransfer,
			Data: map[string]interface{}{
				"transfer_id": transferID,
				"result":      result,
			},
		}
		data, _ := json.Marshal(msg)
		fromClient.Send <- data
	}

	// 通知用户
	userClient := findUserClient(transfer.UserID)
	if userClient != nil {
		msg := TypeMsg{
			Type: MsgTypeTransfer,
			Data: map[string]interface{}{
				"transfer_id": transferID,
				"result":      result,
			},
		}
		data, _ := json.Marshal(msg)
		userClient.Send <- data
	}
}

// updateServiceLoad 更新客服负载
func updateServiceLoad(serviceID int64, operation string) {
	if operation == "add" {
		global.GVA_DB.Model(&sysModel.SysService{}).Where("id = ?", serviceID).
			Update("current_user_count", global.GVA_DB.Raw("current_user_count + 1"))
	} else if operation == "remove" {
		global.GVA_DB.Model(&sysModel.SysService{}).Where("id = ?", serviceID).
			Update("current_user_count", global.GVA_DB.Raw("current_user_count - 1"))
	}
}
