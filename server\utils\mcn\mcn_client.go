package mcn

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"go.uber.org/zap"
	"io/ioutil"
	"net/http"
	"net/http/cookiejar"
	"net/url"
	"strings"
	"time"
)

// MC<PERSON>lient represents a client for interacting with the MCN API.
type MCNClient struct {
	client    *http.Client
	CookieKey string
}

// NewMCNClient creates a new MCNClient.
func NewMCNClient() *MCNClient {
	// 创建一个 cookie jar 来保存 cookies
	jar, err := cookiejar.New(nil)
	if err != nil {
		global.GVA_LOG.Error("Error creating cookie jar", zap.Error(err))
		return nil
	}
	if global.GVA_REDIS == nil {
		global.GVA_LOG.Error("Redis client is not initialized")
		return nil
	}
	return &MCNClient{
		client: &http.Client{
			Jar: jar,
		},
	}
}

const (
	MCNSendCodeUrl    = "https://buyin.jinritemai.com/passport/web/send_code/"
	MCNCheckCodeUrl   = "https://buyin.jinritemai.com/passport/web/mobile/check_code/"
	MCNBindAccountUrl = "https://buyin.jinritemai.com/api/bind/institutionApplyBind"
	MCNUserInfoUrl    = "https://buyin.jinritemai.com/ecom/mcn/common/get_user_info_by_ticket"
	MCNLoginUrl       = "https://buyin-institution-sso.jinritemai.com/account_login/v2/"
	MCNUserBindPid    = "https://buyin.jinritemai.com/api/bind/addUserBindPid"
	MCNDailyBillURL   = "https://buyin.jinritemai.com/buyin/v2/settlement/getOrganizationBill"

	// redis key
	MCNKEYPREFIX     = "mcn:"
	MCNCookiesKey    = "cookies"
	MCNXCsrfTokenKey = "x-secsdk-csrf-token"
)

type MCNResponse struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data"`
}

// Login 登录接口
func (m *MCNClient) Login(username, password string) (string, error) {
	// 模拟登录请求
	loginURL := MCNLoginUrl
	// 注意：这里的 username 和 password 需要替换为实际的用户名和密码
	loginPayload := strings.NewReader(`{"account": "31353c3535333632374574742b666a68","password": "614b54616e4e53546c75443d326e482","fp": "verify_mb4y3wah_DFJ1wmp8_BkRO_4qRw_Ai47_hb37WiMDGScg","aid": "2191","language": "zh","account_sdk_source": "web","mix_mode": "1","service": "https://buyin.jinritemai.com/mpa/account/institution-role-select","captcha_key": "","redirect_sso_to_login": "false"}`)

	req, err := http.NewRequest("POST", loginURL, loginPayload)
	if err != nil {
		return "", err
	}

	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Add("Accept", "application/json, text/plain, */*")
	req.Header.Add("origin", "https://buyin.jinritemai.com")
	req.Header.Add("Referer", "https://buyin.jinritemai.com/")

	resp, err := m.client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()
	// 返回登录成功的消息
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	m.SaveCookies(m.client.Jar.Cookies(req.URL))

	return m.handleResponse(body)
}

// SendVerificationCode 发送验证码接口
func (m *MCNClient) SendVerificationCode(mobile string) (string, error) {
	sendCodeURL := MCNSendCodeUrl
	data := url.Values{
		"aid":          {"1128"},
		"mobile":       {mobile},
		"channel":      {"App Store"},
		"type":         {"26"},
		"device_id":    {"23451113420923236"},
		"version_code": {"7.3.6"},
	}
	req, err := http.NewRequest("POST", sendCodeURL, bytes.NewBufferString(data.Encode()))
	if err != nil {
		return "", err
	}
	m.setHeaders(req)
	resp, err := m.client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	return m.handleResponse(body)
}

// VerifyCode 校验验证码接口
func (m *MCNClient) VerifyCode(code, mobile string) (string, error) {
	checkCodeURL := MCNCheckCodeUrl
	data := url.Values{
		"aid":          {"1128"},
		"code":         {code},
		"mobile":       {mobile},
		"channel":      {"App Store"},
		"type":         {"26"},
		"device_id":    {"23451113420923236"},
		"version_code": {"7.3.6"},
	}
	req, err := http.NewRequest("POST", checkCodeURL, bytes.NewBufferString(data.Encode()))
	if err != nil {
		return "", err
	}
	m.setHeaders(req)
	resp, err := m.client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}
	// 保存cookie到 Redis
	m.CookieKey = "verifyCode:" + mobile
	err = m.SaveCookies(m.client.Jar.Cookies(req.URL))
	if err != nil {
		return "", fmt.Errorf("failed to save cookies: %w", err)
	}
	return m.handleResponse(body)
}

// GetUserInfoByTicket 获取用户信息接口
func (m *MCNClient) GetUserInfoByTicket(mobile, ticket string) (string, error) {
	getUserInfoURL := MCNUserInfoUrl
	data := url.Values{
		"ticket": {ticket},
	}
	req, err := http.NewRequest("POST", getUserInfoURL, bytes.NewBufferString(data.Encode()))
	if err != nil {
		return "", err
	}
	//m.CookieKey = "verifyCode:" + mobile
	m.setHeaders(req)
	resp, err := m.client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	/*// 保存请求的结果
	  // 获取 x-secsdk-csrf-token
	  csrfToken := resp.Header.Get(MCNXCsrfTokenKey)
	  if csrfToken == "" {
	  	return "", fmt.Errorf(MCNXCsrfTokenKey + " not found in response headers")
	  }*/

	// 保存cookie到 Redis
	m.CookieKey = "userinfo:" + mobile
	err = m.SaveCookies(m.client.Jar.Cookies(req.URL))
	if err != nil {
		return "", fmt.Errorf("failed to save cookies: %w", err)
	}

	return m.handleResponse(body)
}

// AddAccount 添加账号接口 ， // userID: 用户ID，startTime: 绑定开始时间，以当前时间开始，endTime: 绑定结束时间 （最长3年）
func (m *MCNClient) AddAccountBind(mobile, userID string) (string, error) {

	if userID == "" {
		return "", fmt.Errorf("userID must not be empty")
	}

	// 设置开始时间为当前时间， 结束时间为三年后
	startTime := time.Now().Format("2006-01-02 15:04:05")
	endTime := time.Now().AddDate(3, 0, 0).Format("2006-01-02 15:04:05")

	addAccountURL := MCNBindAccountUrl
	data := url.Values{
		"app_id":          {"1128"},
		"user_id":         {userID},
		"bind_start_time": {startTime},
		"bind_end_time":   {endTime},
	}
	req, err := http.NewRequest("POST", addAccountURL, bytes.NewBufferString(data.Encode()))
	if err != nil {
		return "", err
	}
	//m.CookieKey = "userinfo:" + mobile
	m.setHeaders(req)
	resp, err := m.client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	// 保存请求的结果
	//ioutil.WriteFile("mcn_add_account_response.json", body, 0644)

	// 保存cookie到 Redis
	m.CookieKey = "bindAccount:" + mobile
	err = m.SaveCookies(m.client.Jar.Cookies(req.URL))
	if err != nil {
		return "", fmt.Errorf("failed to save cookies: %w", err)
	}

	return m.handleResponse(body)
}

// AddUserBindPid 添加用户绑定结算账号接口
func (m *MCNClient) AddUserBindPid(userID, pid string) (string, error) {
	if userID == "" {
		return "", fmt.Errorf("userID and pid must not be empty")
	}

	// bind_type=0&platform=6&user_id=***********&bind_remark=系统绑定&
	// bind_start_time=2025-06-04 00:00:00&bind_end_time=2025-06-30 23:59:59&
	// bind_pid=&user_profit_ratio=60&app_id=1128&invoice_status=1
	addUserBindPidURL := MCNUserBindPid
	data := url.Values{
		"bind_type":         {"0"},
		"platform":          {"6"},
		"bind_remark":       {"系统绑定"},
		"bind_start_time":   {time.Now().Format("2006-01-02 15:04:05")},
		"bind_end_time":     {time.Now().AddDate(3, 0, 0).Format("2006-01-02 15:04:05")},
		"bind_pid":          {""},
		"user_profit_ratio": {"60"},
		"invoice_status":    {"1"},
		"app_id":            {"1128"},
		"user_id":           {userID},
	}
	req, err := http.NewRequest("POST", addUserBindPidURL, bytes.NewBufferString(data.Encode()))
	if err != nil {
		return "", err
	}
	m.setHeaders(req)
	resp, err := m.client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	return m.handleResponse(body)
}

// FetchDailyCommissionBill 抓取每日佣金账单
// 请求参数： start_time=2025-06-16+00:00:00&end_time=2025-06-16+23:59:59&role_type=ORGANIZATION&author_uid=&page=3&size=20&pay_type=&bill_type=DAY
func (m *MCNClient) FetchDailyCommissionBill(startTime string, endTime string, page int, pageSize int) (string, error) {
	// 这里需要实现具体的抓取逻辑
	dailyBillURL := MCNDailyBillURL

	//拼装get请求参数
	params := url.Values{
		"start_time": {startTime},
		"end_time":   {endTime},
		"role_type":  {"ORGANIZATION"},
		"author_uid": {""}, // 可以根据需要设置
		"page":       {fmt.Sprintf("%d", page)},
		"size":       {fmt.Sprintf("%d", pageSize)},
		"pay_type":   {""}, // 可以根据需要设置
		"bill_type":  {"DAY"},
	}

	if len(params) > 0 {
		dailyBillURL += "?" + params.Encode()
	}

	//fmt.Printf("Fetching daily commission bill from URL: %s\n", dailyBillURL)
	req, err := http.NewRequest("GET", dailyBillURL, nil)
	if err != nil {
		return "", err
	}

	m.CookieKey = "bill:day"
	m.setHeaders(req)

	resp, err := m.client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	return m.handleResponse(body)
}

func (m *MCNClient) setHeaders(req *http.Request) {
	req.Header.Set("Accept", "application/json, text/plain, */*")
	req.Header.Set("Accept-Language", "zh-CN,zh;q=0.9")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Origin", "https://buyin.jinritemai.com")
	req.Header.Set("Referer", "https://buyin.jinritemai.com/dashboard/daren/account-manager")
	req.Header.Set("Sec-CH-UA", `"Chromium";v="135", "Not-A.Brand";v="8"`)
	req.Header.Set("Sec-CH-UA-Mobile", "?0")
	req.Header.Set("Sec-CH-UA-Platform", `"macOS"`)
	req.Header.Set("Sec-Fetch-Dest", "empty")
	req.Header.Set("Sec-Fetch-Mode", "cors")
	req.Header.Set("Sec-Fetch-Site", "same-origin")
	req.Header.Set("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36")

	// 设置 x-secsdk-csrf-token
	m.LoadCsrfTokenHeader(req)

	// 从默认的 cookie jar 中加载 cookies
	m.loadCookies(req)
}

// 处理验证码和用户相关的Body返回数据
func (m *MCNClient) handleResponse(responseBody []byte) (string, error) {
	// 检查responseBody
	if len(responseBody) == 0 {
		return "", fmt.Errorf("empty response body")
	}

	// 返回的数据格式可能是两种不同的格式，判断处理
	var ret MCNResponse
	if !strings.Contains(string(responseBody), `"code":`) {
		// dtoMCN
		type dtoMCN struct {
			Data struct {
				Description string `json:"description"`
				ErrorCode   int    `json:"error_code"`
				Data        string `json:"data"`
				Ticket      string `json:"ticket"`
			} `json:"data"`
			Message string `json:"message"`
		}

		// 解析JSON
		var response dtoMCN
		err := json.Unmarshal(responseBody, &response)
		if err != nil {
			return "", err
		}
		if response.Message == "success" {
			ret.Msg = response.Message
			ret.Code = 0
			ret.Data = response.Data
		} else {
			// 如果有错误信息，构建错误响应
			ret.Msg = response.Data.Description
			ret.Code = response.Data.ErrorCode
		}
	} else {
		// 处理MCNResponse格式
		err := json.Unmarshal(responseBody, &ret)
		if err != nil {
			return "", fmt.Errorf("failed to unmarshal response: %w", err)
		}
	}

	// 返回格式化的JSON字符串
	jsonBytes, err := json.Marshal(ret)
	if err != nil {
		return "", fmt.Errorf("failed to marshal response: %w", err)
	}

	return string(jsonBytes), nil
}

// SaveCookies 保存cookie
func (m *MCNClient) SaveCookies(cookies []*http.Cookie) error {
	if len(cookies) > 0 {
		var cookieStrs []string
		for _, cookie := range cookies {
			cookieStrs = append(cookieStrs, fmt.Sprintf("%s=%s", cookie.Name, cookie.Value))
		}

		// 将 cookies 保存到 Redis
		err := global.GVA_REDIS.Set(context.Background(), MCNKEYPREFIX+MCNCookiesKey+":"+m.CookieKey, strings.Join(cookieStrs, "; "), 0).Err()
		if err != nil {
			return fmt.Errorf("failed to save cookies to Redis: %w", err)
		}
	}
	return nil
}

// 获取cookie
func (m *MCNClient) GetCookiesFromRedis() (string, error) {
	cookieStr := global.GVA_REDIS.Get(context.Background(), MCNKEYPREFIX+MCNCookiesKey).Val()
	if cookieStr == "" {
		return "", fmt.Errorf("no cookies found in Redis")
	}
	return cookieStr, nil
}

// 打印cookies
func (m *MCNClient) PrintCookies(urlStr string) {
	u, err := url.Parse(urlStr)
	if err != nil {
		fmt.Printf("Error parsing URL: %v\n", err)
		return
	}
	cookies := m.client.Jar.Cookies(u)
	fmt.Printf("Cookies for %s:\n", urlStr)
	for i, cookie := range cookies {
		fmt.Printf("%d: %s=%s\n", i+1, cookie.Name, cookie.Value)
	}
}

// loadCookies 从 Redis 中加载 cookies
func (m *MCNClient) loadCookies(req *http.Request) {
	tKey := MCNKEYPREFIX + MCNCookiesKey

	cookieStr := global.GVA_REDIS.Get(context.Background(), tKey).Val()
	fmt.Printf("Loading cookies for key: %s, value: %s\n", tKey, cookieStr)
	if cookieStr != "" {
		// 将 cookies 字符串分割成单个 cookie
		cookies := strings.Split(cookieStr, "; ")
		for _, cookie := range cookies {
			parts := strings.Split(cookie, "=")
			if len(parts) == 2 {
				//req.AddCookie(&http.Cookie{Name: parts[0], Value: parts[1]})
			}
		}
	}

	if m.CookieKey != "" {
		tKey += ":" + m.CookieKey
	}
	cookieStr2 := global.GVA_REDIS.Get(context.Background(), tKey).Val()
	fmt.Printf("Loading cookies for key: %s, value: %s\n", tKey, cookieStr2)
	if cookieStr2 != "" {
		// 将 cookies 字符串分割成单个 cookie
		cookies := strings.Split(cookieStr2, "; ")
		for _, cookie := range cookies {
			parts := strings.Split(cookie, "=")
			if len(parts) == 2 {
				fmt.Printf("Adding cookie: %s=%s\n", parts[0], parts[1])
				req.AddCookie(&http.Cookie{Name: parts[0], Value: parts[1]})
			}
		}
	}
}

// LoadCsrfTokenHeader 获取x-secsdk-csrf-token的头，并且赋值
func (m *MCNClient) LoadCsrfTokenHeader(req *http.Request) {
	// 从 Redis 获取 x-secsdk-csrf-token
	csrfToken := global.GVA_REDIS.Get(context.Background(), MCNKEYPREFIX+MCNXCsrfTokenKey).Val()
	if csrfToken != "" {
		req.Header.Set(MCNXCsrfTokenKey, csrfToken)
	}
}

// SetCsrfTokenHeaderToRedis 获取x-secsdk-csrf-token的头，并且赋值
func (m *MCNClient) SetCsrfTokenHeaderToRedis(csrfToken string) error {
	err := global.GVA_REDIS.Set(context.Background(), MCNKEYPREFIX+MCNXCsrfTokenKey, csrfToken, 0).Err()
	if err != nil {
		return fmt.Errorf("failed to set x-secsdk-csrf-token in Redis: %w", err)
	}
	return nil
}
