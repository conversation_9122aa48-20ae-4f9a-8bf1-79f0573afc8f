package jyhapp

import (
	"errors"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/errno"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/query"
	jyhReq "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/request"
	jyhResp "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/response"
	"gorm.io/gorm"
	"strings"
)

type MaterialSyncService struct {
}

func (m *MaterialSyncService) ConfAutoCreateCategory() bool {
	return true
}
func (m *MaterialSyncService) ConfAutoCreateTag() bool {
	return true
}

// 添加一个多个分组
func (m *MaterialSyncService) MultiLevelCategoryCreateInner(q *query.Query, categories []string, autoCreate bool) (id uint, err error) {
	if len(categories) == 0 {
		return 0, errno.BadRequest
	}

	var (
		dbCategory = q.JyhMaterialCategory
	)

	var parentId uint
	for _, category := range categories {
		// 检查分类名称是否重复
		err = dbCategory.Where(dbCategory.CatName.Eq(category), dbCategory.ParentID.Eq(parentId)).Select(dbCategory.ID).Order(dbCategory.ID).Limit(1).Scan(&id)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return 0, err
		}

		if id == 0 {
			if !autoCreate {
				return 0, errors.New("分类名称不存在: " + category)
			}
			c := jyhapp.JyhMaterialCategory{
				CatName:  category,
				ParentID: parentId,
				IsActive: true,
			}
			err = dbCategory.Create(&c)
			if err != nil {
				return 0, errors.New("创建分类失败: " + err.Error())
			}
			id = c.ID
		}
		parentId = id
	}
	return
}

// 创建一组标签
func (m *MaterialSyncService) MultiTagCreateInner(q *query.Query, tags []string, autoCreate bool) (ids []uint, err error) {
	if len(tags) == 0 {
		return nil, errno.BadRequest
	}

	var (
		dbTag = q.JyhTag
	)

	var tagItem []*jyhapp.JyhTag
	err = dbTag.Where(dbTag.Name.In(tags...)).Select(dbTag.ID, dbTag.Name).Scan(&tagItem)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, errors.New("查询标签失败: " + err.Error())
	}

	var tagNew []*jyhapp.JyhTag
	var mapExists = make(map[string]uint, len(tagItem))
	for _, item := range tagItem {
		if item.ID > 0 {
			mapExists[item.Name] = item.ID
		}
	}
	for _, tag := range tags {
		if id, exists := mapExists[tag]; exists {
			ids = append(ids, id)
			continue
		}
		if !autoCreate {
			return nil, errors.New("标签名称不存在: " + tag)
		}
		t := jyhapp.JyhTag{
			Name: tag,
		}
		tagNew = append(tagNew, &t)
	}
	if len(tagNew) > 0 {
		err = dbTag.CreateInBatches(tagNew, len(tagNew))
		if err != nil {
			return nil, errors.New("创建标签失败: " + err.Error())
		}
		for _, item := range tagNew {
			ids = append(ids, item.ID)
		}
	}
	return
}

// Sync 同步素材数据: 保存或更新
func (m *MaterialSyncService) Sync(req *jyhReq.MaterialSync) (resp *jyhResp.MaterialSyncResponse, err error) {
	if req == nil {
		return nil, errno.BadRequest
	}
	if len(req.Files) == 0 {
		return nil, errno.BadRequest.WithMsg("至少需要上传一个文件")
	}

	var categories []string
	for _, c := range strings.Split(req.Category, "/") {
		c = strings.TrimSpace(c)
		if c == "" {
			continue
		}
		categories = append(categories, c)
	}
	if len(categories) == 0 {
		return nil, errno.BadRequest.WithMsg("分类不能为空")
	}

	var tags []string
	{
		_mt := make(map[string]struct{})
		for _, s1 := range tags {
			s1 = strings.TrimSpace(s1)
			if s1 == "" {
				continue
			}
			if _, exists := _mt[s1]; !exists {
				_mt[s1] = struct{}{}
				tags = append(tags, s1)
			}
		}
	}
	if len(tags) == 0 {
		//	return errors.New("至少需要选择一个标签")
	}

	var confAutoCreateCategory = m.ConfAutoCreateCategory()
	var confAutoCreateTag = m.ConfAutoCreateTag()

	resp = &jyhResp.MaterialSyncResponse{}
	err = query.Use(global.GVA_DB).Transaction(func(tx *query.Query) (err error) {
		//var (
		//	dbMaterial = q.JyhMaterial
		//)
		//
		// 检查素材名称是否重复
		//res := &jyhapp.JyhMaterial{}
		//err = dbMaterial.Where(dbMaterial.Name.Eq(req.Name)).Scan(&res)
		//if err != nil {
		//	return err
		//}
		//if res.ID > 0 {
		//	return errors.New("素材名称重复")
		//}

		categoryId, err := m.MultiLevelCategoryCreateInner(tx, categories, confAutoCreateCategory)
		if err != nil {
			return err
		}
		var tagIds []uint
		if len(tags) > 0 {
			tagIds, err = m.MultiTagCreateInner(tx, tags, confAutoCreateTag)
			if err != nil {
				return err
			}
		}
		material := &jyhapp.JyhMaterial{
			Name:             req.Name,
			CategoryID:       categoryId,
			Copywriting:      req.Copywriting,
			DouyinProductUrl: req.DouyinProductUrl,
			MusicUrl:         req.MusicUrl,
			Description:      req.Description,
			FileUrl:          req.FileUrl,
			//Status:           req.Status,
		}
		material.ID = req.MaterialId
		if material.ID > 0 {
			err = gSrv.MaterialService.UpdateInner(tx, material, tagIds, req.Files)
		} else {
			err = gSrv.MaterialService.CreateInner(tx, material, tagIds, req.Files)
		}

		if err == nil {
			resp.MaterialId = material.ID
		}
		return err
	})
	return
}
