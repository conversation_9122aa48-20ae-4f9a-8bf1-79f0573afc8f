# 客服智能分配逻辑说明

## 问题背景

原有的客服分配逻辑存在以下问题：
1. 规则只绑定了标签，但没有指定对应的客服
2. 分配时虽然找到了匹配标签的规则，但仍然是通过标签直接匹配客服，规则没有起到"指定特定客服"的作用

## 解决方案

### 1. 数据结构调整

新增了规则与客服的关联表：
```sql
CREATE TABLE `sys_service_rule_service_relation` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `rule_id` bigint(20) unsigned NOT NULL COMMENT '规则ID',
  `service_id` bigint(20) NOT NULL COMMENT '客服ID',
  `priority` int(11) NOT NULL DEFAULT '1' COMMENT '在该规则下的优先级',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_rule_service` (`rule_id`, `service_id`)
);
```

### 2. 分配逻辑优化

新的分配逻辑按以下优先级进行：

#### 2.1 有用户标签的情况
1. **查找匹配标签的规则**：根据用户标签找到最高优先级的规则
2. **优先分配规则指定的客服**：
   - 首先尝试分配规则关联的首选客服（优先级最高）
   - 如果首选客服不可用，依次尝试规则关联的其他客服
3. **回退到标签匹配**：如果规则关联的客服都不可用，回退到原有的标签匹配逻辑
4. **使用默认规则**：如果没有匹配的规则，使用默认规则

#### 2.2 无用户标签的情况
1. **使用默认规则**：优先从默认规则关联的客服中选择
2. **通用分配**：如果默认规则没有关联客服，使用通用分配逻辑

### 3. API接口更新

#### 3.1 创建/更新规则
```json
{
  "rule_name": "VIP客户规则",
  "priority": 1,
  "max_users": 5,
  "work_time_start": "09:00",
  "work_time_end": "18:00",
  "is_default": false,
  "tag_ids": [1, 2],        // 关联的标签ID
  "service_ids": [10, 11]   // 关联的客服ID（按优先级排序）
}
```

#### 3.2 API接口说明
- 创建/更新规则时直接处理客服绑定，无需额外接口
- `GET /api/customerservice/services/available` - 获取可分配的客服列表（用于前端选择）
- 规则列表和详情接口自动返回关联的客服信息

### 4. 使用示例

#### 4.1 创建规则并关联客服
```bash
# 创建一个VIP客户规则，关联特定的高级客服
curl -X POST /api/customerservice/rule \
  -H "Content-Type: application/json" \
  -d '{
    "rule_name": "VIP客户专属服务",
    "priority": 1,
    "max_users": 3,
    "work_time_start": "09:00",
    "work_time_end": "22:00",
    "tag_ids": [1],           // VIP标签
    "service_ids": [10, 11]   // 高级客服ID，按优先级排序
  }'
```

#### 4.2 分配逻辑示例
当一个带有VIP标签的用户进入客服系统时：
1. 系统找到"VIP客户专属服务"规则
2. 优先尝试分配客服ID=10（优先级最高）
3. 如果客服10不可用，尝试客服11
4. 如果规则关联的客服都不可用，回退到标签匹配其他有VIP标签的客服
5. 最后回退到默认分配逻辑

### 5. 优势

1. **精确控制**：可以为特定标签的用户指定特定的客服团队
2. **灵活配置**：支持多级回退机制，确保用户总能被分配到客服
3. **优先级管理**：在规则内部可以设置客服的优先级
4. **向后兼容**：保持原有的标签匹配逻辑作为回退方案

### 6. 注意事项

1. **数据迁移**：需要执行SQL脚本创建新的关联表
2. **配置更新**：现有规则需要重新配置客服关联
3. **性能考虑**：新增了数据库查询，但通过合理的索引优化性能
4. **监控建议**：建议监控分配成功率和回退情况
