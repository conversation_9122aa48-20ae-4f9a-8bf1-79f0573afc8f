package jyhapp

import (
	v1 "github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type ArticleRouter struct{}

func (s *ArticleRouter) InitJyhArticleRouter(Router *gin.RouterGroup) {
	articleRouter := Router.Group("article").Use(middleware.OperationRecord())
	articleRouterWithoutRecord := Router.Group("article")
	var articleApi = v1.ApiGroupApp.JyhApiGroup.ArticleApi
	{
		// 分类管理
		articleRouter.POST("category/create", articleApi.CreateCategory)
		articleRouter.PUT("category/update", articleApi.UpdateCategory)
		articleRouter.DELETE("category/delete", articleApi.DeleteCategory)
		// 文章管理
		articleRouter.POST("create", articleApi.CreateArticle)
		articleRouter.PUT("update", articleApi.UpdateArticle)
		articleRouter.DELETE("delete", articleApi.DeleteArticle)

		articleRouter.POST("publish", articleApi.PublishArticle)
		articleRouter.POST("archive", articleApi.ArchiveArticle)
		articleRouter.POST("setTop", articleApi.SetArticleTop)
	}
	{
		articleRouterWithoutRecord.GET("category/detail", articleApi.GetCategoryDetail)
		articleRouterWithoutRecord.GET("category/list", articleApi.GetCategoryList)
		articleRouterWithoutRecord.GET("category/tree", articleApi.GetCategoryTree)
		articleRouterWithoutRecord.GET("detail", articleApi.GetArticleDetail)
		articleRouterWithoutRecord.GET("list", articleApi.GetArticleList)
		articleRouterWithoutRecord.GET("detail/:id", articleApi.GetPublicArticleDetail)
	}
}
