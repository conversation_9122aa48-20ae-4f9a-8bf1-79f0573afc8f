package request

import "github.com/flipped-aurora/gin-vue-admin/server/model/common/request"

type CourseCreateReq struct {
	Title      string `json:"title" binding:"required"`      // 课程标题
	CourseDate string `json:"courseDate" binding:"required"` // 课程日期，格式为YYYY-MM-DD
	ViewCount  int    `json:"viewCount"`                     // 课程浏览量
	ImageUrl   string `json:"imageUrl"`                      // 课程封面图
	VideoUrl   string `json:"videoUrl" binding:"required"`   // 课程视频地址
	Detail     string `json:"detail"`                        // 课程详情
	Duration   int    `json:"duration" binding:"required"`   // 课程时长（分钟）
	Teacher    string `json:"teacher" binding:"required"`    // 讲师姓名
	Status     uint   `json:"status"`                        // 课程状态
	CategoryId uint   `json:"categoryId" binding:"required"` // 课程分类ID
}

type CourseUpdateReq struct {
	ID         uint   `json:"id" binding:"required"`         // 课程ID
	Title      string `json:"title" binding:"required"`      // 课程标题
	CourseDate string `json:"courseDate" binding:"required"` // 课程日期，格式为YYYY-MM-DD
	ViewCount  int    `json:"viewCount"`                     // 课程浏览量
	ImageUrl   string `json:"imageUrl"`                      // 课程封面图
	VideoUrl   string `json:"videoUrl" binding:"required"`   // 课程视频地址
	Detail     string `json:"detail"`                        // 课程详情
	Duration   int    `json:"duration" binding:"required"`   // 课程时长（分钟）
	Teacher    string `json:"teacher" binding:"required"`    // 讲师姓名
	Status     uint   `json:"status"`                        // 课程状态
	CategoryId uint   `json:"categoryId" binding:"required"` // 课程分类ID
}

type CourseSearchReq struct {
	request.PageInfo
	Title      string `json:"title" form:"title"`           // 课程标题
	CourseDate string `json:"course_date" form:"date"`      // 课程日期，格式为YYYY-MM-DD
	Teacher    string `json:"teacher" form:"teacher"`       // 讲师姓名
	Status     uint   `json:"status" form:"status"`         // 课程状态
	CategoryId uint   `json:"categoryId" form:"categoryId"` // 课程分类ID
}
