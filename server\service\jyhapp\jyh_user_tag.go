package jyhapp

import (
	"context"
	"errors"
	"gorm.io/gen"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/query"
	jyhReq "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/request"
	jyhResp "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/response"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type JyhUserTagService struct{}

// CreateUserTag 创建用户标签
func (s *JyhUserTagService) CreateUserTag(req *jyhReq.UserTagCreateReq) error {
	dbTag := query.JyhUserTag

	// 检查标签名称是否重复
	count, err := dbTag.Where(dbTag.Name.Eq(req.Name)).Count()
	if err != nil {
		global.GVA_LOG.Error("检查标签名称失败", zap.Error(err))
		return err
	}

	if count > 0 {
		return errors.New("标签名称已存在")
	}

	status := uint(1)
	if req.Status != nil {
		status = *req.Status
	}

	tag := &jyhapp.JyhUserTag{
		Name:        req.Name,
		Description: req.Description,
		Color:       req.Color,
		Status:      status,
		Sort:        req.Sort,
	}

	err = dbTag.Create(tag)
	if err != nil {
		global.GVA_LOG.Error("创建标签失败", zap.Error(err))
		return err
	}

	return nil
}

// UpdateUserTag 更新用户标签
func (s *JyhUserTagService) UpdateUserTag(req *jyhReq.UserTagUpdateReq) error {
	dbTag := query.JyhUserTag

	// 检查标签是否存在
	tag, err := dbTag.Where(dbTag.ID.Eq(req.ID)).First()
	if err != nil {
		global.GVA_LOG.Error("标签不存在", zap.Error(err))
		return errors.New("标签不存在")
	}

	// 检查名称是否重复
	if req.Name != "" && req.Name != tag.Name {
		count, err := dbTag.Where(dbTag.Name.Eq(req.Name), dbTag.ID.Neq(req.ID)).Count()
		if err != nil {
			global.GVA_LOG.Error("检查标签名称失败", zap.Error(err))
			return err
		}

		if count > 0 {
			return errors.New("标签名称已存在")
		}
	}

	// 构建更新字段
	updates := make(map[string]interface{})
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}
	if req.Color != "" {
		updates["color"] = req.Color
	}
	if req.Status != nil {
		updates["status"] = *req.Status
	}
	updates["sort"] = req.Sort

	_, err = dbTag.Where(dbTag.ID.Eq(req.ID)).Updates(updates)
	if err != nil {
		global.GVA_LOG.Error("更新标签失败", zap.Error(err))
		return err
	}

	return nil
}

// DeleteUserTag 删除用户标签
func (s *JyhUserTagService) DeleteUserTag(id uint) error {
	dbRelation := query.JyhUserTagRelation
	dbTag := query.JyhUserTag

	// 使用事务处理
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 先删除关联关系
		_, err := dbRelation.WithContext(context.Background()).Where(dbRelation.TagID.Eq(id)).Delete()
		if err != nil {
			global.GVA_LOG.Error("删除标签关联关系失败", zap.Error(err))
			return err
		}

		// 删除标签
		_, err = dbTag.WithContext(context.Background()).Where(dbTag.ID.Eq(id)).Delete()
		if err != nil {
			global.GVA_LOG.Error("删除标签失败", zap.Error(err))
			return err
		}

		return nil
	})
}

// GetUserTagList 获取用户标签列表
func (s *JyhUserTagService) GetUserTagList(req *jyhReq.UserTagListReq) ([]*jyhResp.UserTagItem, int64, error) {
	dbTag := query.JyhUserTag

	// 构建查询条件
	q := dbTag.WithContext(context.Background())
	var conditions []gen.Condition
	{
		if req.Name != "" {
			conditions = append(conditions, dbTag.Name.Like("%"+req.Name+"%"))
		}
		if req.Status != nil {
			conditions = append(conditions, dbTag.Status.Eq(uint(*req.Status)))
		}
	}
	dao1 := q.Where(conditions...)
	// 获取总数
	total, err := dao1.Count()
	if err != nil {
		global.GVA_LOG.Error("获取标签总数失败", zap.Error(err))
		return nil, 0, err
	}

	// 分页查询
	tags, err := dao1.Order(dbTag.Sort.Desc(), dbTag.ID.Desc()).
		Limit(req.PageSize).
		Offset((req.Page - 1) * req.PageSize).
		Find()

	if err != nil {
		global.GVA_LOG.Error("获取标签列表失败", zap.Error(err))
		return nil, 0, err
	}

	// 组装响应数据
	result := make([]*jyhResp.UserTagItem, 0)
	for _, tag := range tags {
		result = append(result, &jyhResp.UserTagItem{
			ID:          tag.ID,
			Name:        tag.Name,
			Description: tag.Description,
			Color:       tag.Color,
			Status:      tag.Status,
			Sort:        tag.Sort,
			CreatedAt:   tag.CreatedAt.Format("2006-01-02 15:04:05"),
		})
	}

	return result, total, nil
}

// GetUserTagDetail 获取用户标签详情
func (s *JyhUserTagService) GetUserTagDetail(id uint) (*jyhResp.UserTagDetail, error) {
	dbTag := query.JyhUserTag

	tag, err := dbTag.Where(dbTag.ID.Eq(id)).First()
	if err != nil {
		global.GVA_LOG.Error("获取标签详情失败", zap.Error(err))
		return nil, err
	}

	return &jyhResp.UserTagDetail{
		ID:          tag.ID,
		Name:        tag.Name,
		Description: tag.Description,
		Color:       tag.Color,
		Status:      tag.Status,
		Sort:        tag.Sort,
		CreatedAt:   tag.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:   tag.UpdatedAt.Format("2006-01-02 15:04:05"),
	}, nil
}

// BindUserTag 绑定用户标签
func (s *JyhUserTagService) BindUserTag(req *jyhReq.UserTagBindReq) error {
	dbUser := query.JyhUser
	dbTag := query.JyhUserTag
	dbRelation := query.JyhUserTagRelation

	// 检查用户是否存在
	user, err := dbUser.Where(dbUser.ID.Eq(req.UserID)).First()
	if err != nil {
		global.GVA_LOG.Error("用户不存在", zap.Error(err))
		return errors.New("用户不存在")
	}
	if user == nil {
		return errors.New("用户不存在")
	}

	// 检查标签是否存在
	count, err := dbTag.Where(dbTag.ID.In(req.TagIDs...)).Count()
	if err != nil {
		global.GVA_LOG.Error("检查标签失败", zap.Error(err))
		return err
	}
	if int64(len(req.TagIDs)) != count {
		return errors.New("部分标签不存在")
	}

	// 使用事务处理
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 先删除旧的关联关系
		_, err := dbRelation.WithContext(context.Background()).Where(dbRelation.UserID.Eq(req.UserID)).Unscoped().Delete()
		if err != nil {
			global.GVA_LOG.Error("删除旧标签关联关系失败", zap.Error(err))
			return err
		}

		// 添加新的关联关系
		for _, tagID := range req.TagIDs {
			relation := &jyhapp.JyhUserTagRelation{
				UserID: req.UserID,
				TagID:  tagID,
			}
			if err := tx.Create(relation).Error; err != nil {
				global.GVA_LOG.Error("创建标签关联关系失败", zap.Error(err))
				return err
			}
		}

		return nil
	})
}

// GetUserTags 获取用户标签
func (s *JyhUserTagService) GetUserTags(userID uint) (*jyhResp.UserWithTags, error) {
	dbUser := query.JyhUser
	dbTag := query.JyhUserTag
	dbRelation := query.JyhUserTagRelation

	// 检查用户是否存在
	user, err := dbUser.Where(dbUser.ID.Eq(userID)).First()
	if err != nil {
		global.GVA_LOG.Error("用户不存在", zap.Error(err))
		return nil, errors.New("用户不存在")
	}
	if user == nil {
		return nil, errors.New("用户不存在")
	}

	// 查询用户的所有标签关系
	relations, err := dbRelation.Where(dbRelation.UserID.Eq(userID)).Find()
	if err != nil {
		global.GVA_LOG.Error("查询用户标签关系失败", zap.Error(err))
		return nil, err
	}

	var tagIDs []uint
	for _, relation := range relations {
		tagIDs = append(tagIDs, relation.TagID)
	}

	var tags []*jyhapp.JyhUserTag
	if len(tagIDs) > 0 {
		tags, err = dbTag.Where(dbTag.ID.In(tagIDs...)).Find()
		if err != nil {
			global.GVA_LOG.Error("查询标签失败", zap.Error(err))
			return nil, err
		}
	}

	var tagItems []jyhResp.UserTagItem
	for _, tag := range tags {
		tagItems = append(tagItems, jyhResp.UserTagItem{
			ID:          tag.ID,
			Name:        tag.Name,
			Description: tag.Description,
			Color:       tag.Color,
			Status:      tag.Status,
			Sort:        tag.Sort,
			CreatedAt:   tag.CreatedAt.Format("2006-01-02 15:04:05"),
		})
	}

	return &jyhResp.UserWithTags{
		UserID: userID,
		Tags:   tagItems,
	}, nil
}
