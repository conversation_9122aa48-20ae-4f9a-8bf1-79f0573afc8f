package jyhapp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	commonReq "github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type CourseCategoryApi struct{}

// Create 创建课程分类
// @Tags      CourseCategory
// @Summary   创建课程分类
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.CourseCategoryCreate  true  "创建课程分类"
// @Success   200   {object}  response.Response{msg=string}  "创建课程分类"
// @Router    /courseCategory/create [post]
func (m *CourseCategoryApi) Create(c *gin.Context) {
	var req request.CourseCategoryCreate
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = courseCategoryService.Create(req)
	if err != nil {
		global.GVA_LOG.Error("创建课程分类失败!", zap.Error(err))
		response.FailWithMessage("创建课程分类失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建课程分类成功", c)
}

// Update 更新课程分类
// @Tags      CourseCategory
// @Summary   更新课程分类
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.CourseCategoryUpdate  true  "更新课程分类"
// @Success   200   {object}  response.Response{msg=string}  "更新课程分类"
// @Router    /courseCategory/update [put]
func (m *CourseCategoryApi) Update(c *gin.Context) {
	var req request.CourseCategoryUpdate
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = courseCategoryService.Update(&req)
	if err != nil {
		global.GVA_LOG.Error("更新课程分类失败!", zap.Error(err))
		response.FailWithMessage("更新课程分类失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新课程分类成功", c)
}

// Delete 删除课程分类
// @Tags      CourseCategory
// @Summary   删除课程分类
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      commonReq.GetById  true  "删除课程分类"
// @Success   200   {object}  response.Response{msg=string}  "删除课程分类"
// @Router    /courseCategory/delete [delete]
func (m *CourseCategoryApi) Delete(c *gin.Context) {
	var req commonReq.GetById
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = courseCategoryService.Delete(req.Uint())
	if err != nil {
		global.GVA_LOG.Error("删除课程分类失败!", zap.Error(err))
		response.FailWithMessage("删除课程分类失败: "+err.Error(), c)
		return
	}
	response.OkWithMessage("删除课程分类成功", c)
}

// GetDetail 获取课程分类详情
// @Tags      CourseCategory
// @Summary   获取课程分类详情
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     commonReq.GetById  true  "获取课程分类详情"
// @Success   200   {object}  response.Response{data=jyhapp.JyhCourseCategory,msg=string}  "获取课程分类详情"
// @Router    /courseCategory/detail [get]
func (m *CourseCategoryApi) GetDetail(c *gin.Context) {
	var req commonReq.GetById
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	category, err := courseCategoryService.GetDetail(req.Uint())
	if err != nil {
		global.GVA_LOG.Error("获取课程分类失败!", zap.Error(err))
		response.FailWithMessage("获取课程分类失败", c)
		return
	}
	response.OkWithData(category, c)
}

// GetList 获取课程分类列表
// @Tags      CourseCategory
// @Summary   获取课程分类列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     request.CourseCategorySearch  true  "获取课程分类列表"
// @Success   200   {object}  response.Response{data=response.PageResult,msg=string}  "获取课程分类列表"
// @Router    /courseCategory/list [get]
func (m *CourseCategoryApi) GetList(c *gin.Context) {
	var req request.CourseCategorySearch
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	list, total, err := courseCategoryService.GetList(&req)
	if err != nil {
		global.GVA_LOG.Error("获取课程分类失败!", zap.Error(err))
		response.FailWithMessage("获取课程分类失败", c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取课程分类成功", c)
}
