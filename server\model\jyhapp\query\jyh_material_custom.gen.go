// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
)

func newJyhMaterialCustom(db *gorm.DB, opts ...gen.DOOption) jyhMaterialCustom {
	_jyhMaterialCustom := jyhMaterialCustom{}

	_jyhMaterialCustom.jyhMaterialCustomDo.UseDB(db, opts...)
	_jyhMaterialCustom.jyhMaterialCustomDo.UseModel(&jyhapp.JyhMaterialCustom{})

	tableName := _jyhMaterialCustom.jyhMaterialCustomDo.TableName()
	_jyhMaterialCustom.ALL = field.NewAsterisk(tableName)
	_jyhMaterialCustom.ID = field.NewUint(tableName, "id")
	_jyhMaterialCustom.CreatedAt = field.NewTime(tableName, "created_at")
	_jyhMaterialCustom.UpdatedAt = field.NewTime(tableName, "updated_at")
	_jyhMaterialCustom.UserID = field.NewUint(tableName, "user_id")
	_jyhMaterialCustom.MaterialUrl = field.NewString(tableName, "material_url")
	_jyhMaterialCustom.IsSend = field.NewBool(tableName, "is_send")
	_jyhMaterialCustom.SendAt = field.NewTime(tableName, "send_at")
	_jyhMaterialCustom.MaterialID = field.NewUint(tableName, "material_id")

	_jyhMaterialCustom.fillFieldMap()

	return _jyhMaterialCustom
}

type jyhMaterialCustom struct {
	jyhMaterialCustomDo

	ALL         field.Asterisk
	ID          field.Uint
	CreatedAt   field.Time
	UpdatedAt   field.Time
	UserID      field.Uint
	MaterialUrl field.String
	IsSend      field.Bool
	SendAt      field.Time
	MaterialID  field.Uint

	fieldMap map[string]field.Expr
}

func (j jyhMaterialCustom) Table(newTableName string) *jyhMaterialCustom {
	j.jyhMaterialCustomDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j jyhMaterialCustom) As(alias string) *jyhMaterialCustom {
	j.jyhMaterialCustomDo.DO = *(j.jyhMaterialCustomDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *jyhMaterialCustom) updateTableName(table string) *jyhMaterialCustom {
	j.ALL = field.NewAsterisk(table)
	j.ID = field.NewUint(table, "id")
	j.CreatedAt = field.NewTime(table, "created_at")
	j.UpdatedAt = field.NewTime(table, "updated_at")
	j.UserID = field.NewUint(table, "user_id")
	j.MaterialUrl = field.NewString(table, "material_url")
	j.IsSend = field.NewBool(table, "is_send")
	j.SendAt = field.NewTime(table, "send_at")
	j.MaterialID = field.NewUint(table, "material_id")

	j.fillFieldMap()

	return j
}

func (j *jyhMaterialCustom) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *jyhMaterialCustom) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 8)
	j.fieldMap["id"] = j.ID
	j.fieldMap["created_at"] = j.CreatedAt
	j.fieldMap["updated_at"] = j.UpdatedAt
	j.fieldMap["user_id"] = j.UserID
	j.fieldMap["material_url"] = j.MaterialUrl
	j.fieldMap["is_send"] = j.IsSend
	j.fieldMap["send_at"] = j.SendAt
	j.fieldMap["material_id"] = j.MaterialID
}

func (j jyhMaterialCustom) clone(db *gorm.DB) jyhMaterialCustom {
	j.jyhMaterialCustomDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j jyhMaterialCustom) replaceDB(db *gorm.DB) jyhMaterialCustom {
	j.jyhMaterialCustomDo.ReplaceDB(db)
	return j
}

type jyhMaterialCustomDo struct{ gen.DO }

func (j jyhMaterialCustomDo) Debug() *jyhMaterialCustomDo {
	return j.withDO(j.DO.Debug())
}

func (j jyhMaterialCustomDo) WithContext(ctx context.Context) *jyhMaterialCustomDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jyhMaterialCustomDo) ReadDB() *jyhMaterialCustomDo {
	return j.Clauses(dbresolver.Read)
}

func (j jyhMaterialCustomDo) WriteDB() *jyhMaterialCustomDo {
	return j.Clauses(dbresolver.Write)
}

func (j jyhMaterialCustomDo) Session(config *gorm.Session) *jyhMaterialCustomDo {
	return j.withDO(j.DO.Session(config))
}

func (j jyhMaterialCustomDo) Clauses(conds ...clause.Expression) *jyhMaterialCustomDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jyhMaterialCustomDo) Returning(value interface{}, columns ...string) *jyhMaterialCustomDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jyhMaterialCustomDo) Not(conds ...gen.Condition) *jyhMaterialCustomDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jyhMaterialCustomDo) Or(conds ...gen.Condition) *jyhMaterialCustomDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jyhMaterialCustomDo) Select(conds ...field.Expr) *jyhMaterialCustomDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jyhMaterialCustomDo) Where(conds ...gen.Condition) *jyhMaterialCustomDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jyhMaterialCustomDo) Order(conds ...field.Expr) *jyhMaterialCustomDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jyhMaterialCustomDo) Distinct(cols ...field.Expr) *jyhMaterialCustomDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jyhMaterialCustomDo) Omit(cols ...field.Expr) *jyhMaterialCustomDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jyhMaterialCustomDo) Join(table schema.Tabler, on ...field.Expr) *jyhMaterialCustomDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jyhMaterialCustomDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jyhMaterialCustomDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jyhMaterialCustomDo) RightJoin(table schema.Tabler, on ...field.Expr) *jyhMaterialCustomDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jyhMaterialCustomDo) Group(cols ...field.Expr) *jyhMaterialCustomDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jyhMaterialCustomDo) Having(conds ...gen.Condition) *jyhMaterialCustomDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jyhMaterialCustomDo) Limit(limit int) *jyhMaterialCustomDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jyhMaterialCustomDo) Offset(offset int) *jyhMaterialCustomDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jyhMaterialCustomDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jyhMaterialCustomDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jyhMaterialCustomDo) Unscoped() *jyhMaterialCustomDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jyhMaterialCustomDo) Create(values ...*jyhapp.JyhMaterialCustom) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jyhMaterialCustomDo) CreateInBatches(values []*jyhapp.JyhMaterialCustom, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jyhMaterialCustomDo) Save(values ...*jyhapp.JyhMaterialCustom) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jyhMaterialCustomDo) First() (*jyhapp.JyhMaterialCustom, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterialCustom), nil
	}
}

func (j jyhMaterialCustomDo) Take() (*jyhapp.JyhMaterialCustom, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterialCustom), nil
	}
}

func (j jyhMaterialCustomDo) Last() (*jyhapp.JyhMaterialCustom, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterialCustom), nil
	}
}

func (j jyhMaterialCustomDo) Find() ([]*jyhapp.JyhMaterialCustom, error) {
	result, err := j.DO.Find()
	return result.([]*jyhapp.JyhMaterialCustom), err
}

func (j jyhMaterialCustomDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*jyhapp.JyhMaterialCustom, err error) {
	buf := make([]*jyhapp.JyhMaterialCustom, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jyhMaterialCustomDo) FindInBatches(result *[]*jyhapp.JyhMaterialCustom, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jyhMaterialCustomDo) Attrs(attrs ...field.AssignExpr) *jyhMaterialCustomDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jyhMaterialCustomDo) Assign(attrs ...field.AssignExpr) *jyhMaterialCustomDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jyhMaterialCustomDo) Joins(fields ...field.RelationField) *jyhMaterialCustomDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jyhMaterialCustomDo) Preload(fields ...field.RelationField) *jyhMaterialCustomDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jyhMaterialCustomDo) FirstOrInit() (*jyhapp.JyhMaterialCustom, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterialCustom), nil
	}
}

func (j jyhMaterialCustomDo) FirstOrCreate() (*jyhapp.JyhMaterialCustom, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterialCustom), nil
	}
}

func (j jyhMaterialCustomDo) FindByPage(offset int, limit int) (result []*jyhapp.JyhMaterialCustom, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jyhMaterialCustomDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jyhMaterialCustomDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jyhMaterialCustomDo) Delete(models ...*jyhapp.JyhMaterialCustom) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jyhMaterialCustomDo) withDO(do gen.Dao) *jyhMaterialCustomDo {
	j.DO = *do.(*gen.DO)
	return j
}
