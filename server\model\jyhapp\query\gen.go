// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

var (
	Q                         = new(Query)
	JyhArticle                *jyhArticle
	JyhArticleCategory        *jyhArticleCategory
	JyhCommission             *jyhCommission
	JyhCommissionRate         *jyhCommissionRate
	JyhCourse                 *jyhCourse
	JyhCourseCategory         *jyhCourseCategory
	JyhInviteCode             *jyhInviteCode
	JyhInviteRecord           *jyhInviteRecord
	JyhMaterial               *jyhMaterial
	JyhMaterialCategory       *jyhMaterialCategory
	JyhMaterialClaimRecord    *jyhMaterialClaimRecord
	JyhMaterialCustom         *jyhMaterialCustom
	JyhMaterialFile           *jyhMaterialFile
	JyhMaterialRuleConfig     *jyhMaterialRuleConfig
	JyhMaterialTag            *jyhMaterialTag
	JyhMaterialUpload         *jyhMaterialUpload
	JyhMaterialUser           *jyhMaterialUser
	JyhMcnOrderDetail         *jyhMcnOrderDetail
	JyhOrder                  *jyhOrder
	JyhPointsExchange         *jyhPointsExchange
	JyhPointsRecord           *jyhPointsRecord
	JyhPointsRule             *jyhPointsRule
	JyhRecommendDisplayLog    *jyhRecommendDisplayLog
	JyhRecommendItem          *jyhRecommendItem
	JyhRecommendPosition      *jyhRecommendPosition
	JyhTag                    *jyhTag
	JyhUser                   *jyhUser
	JyhUserAccount            *jyhUserAccount
	JyhUserAccountTransaction *jyhUserAccountTransaction
	JyhUserBenefit            *jyhUserBenefit
	JyhUserBenefitSnapshot    *jyhUserBenefitSnapshot
	JyhUserCertificate        *jyhUserCertificate
	JyhUserClosure            *jyhUserClosure
	JyhUserExt                *jyhUserExt
	JyhUserLevel              *jyhUserLevel
	JyhUserLiveRoom           *jyhUserLiveRoom
	JyhUserShipLevel          *jyhUserShipLevel
	JyhUserShipLevelBenefit   *jyhUserShipLevelBenefit
	JyhUserTag                *jyhUserTag
	JyhUserTagRelation        *jyhUserTagRelation
)

func SetDefault(db *gorm.DB, opts ...gen.DOOption) {
	*Q = *Use(db, opts...)
	JyhArticle = &Q.JyhArticle
	JyhArticleCategory = &Q.JyhArticleCategory
	JyhCommission = &Q.JyhCommission
	JyhCommissionRate = &Q.JyhCommissionRate
	JyhCourse = &Q.JyhCourse
	JyhCourseCategory = &Q.JyhCourseCategory
	JyhInviteCode = &Q.JyhInviteCode
	JyhInviteRecord = &Q.JyhInviteRecord
	JyhMaterial = &Q.JyhMaterial
	JyhMaterialCategory = &Q.JyhMaterialCategory
	JyhMaterialClaimRecord = &Q.JyhMaterialClaimRecord
	JyhMaterialCustom = &Q.JyhMaterialCustom
	JyhMaterialFile = &Q.JyhMaterialFile
	JyhMaterialRuleConfig = &Q.JyhMaterialRuleConfig
	JyhMaterialTag = &Q.JyhMaterialTag
	JyhMaterialUpload = &Q.JyhMaterialUpload
	JyhMaterialUser = &Q.JyhMaterialUser
	JyhMcnOrderDetail = &Q.JyhMcnOrderDetail
	JyhOrder = &Q.JyhOrder
	JyhPointsExchange = &Q.JyhPointsExchange
	JyhPointsRecord = &Q.JyhPointsRecord
	JyhPointsRule = &Q.JyhPointsRule
	JyhRecommendDisplayLog = &Q.JyhRecommendDisplayLog
	JyhRecommendItem = &Q.JyhRecommendItem
	JyhRecommendPosition = &Q.JyhRecommendPosition
	JyhTag = &Q.JyhTag
	JyhUser = &Q.JyhUser
	JyhUserAccount = &Q.JyhUserAccount
	JyhUserAccountTransaction = &Q.JyhUserAccountTransaction
	JyhUserBenefit = &Q.JyhUserBenefit
	JyhUserBenefitSnapshot = &Q.JyhUserBenefitSnapshot
	JyhUserCertificate = &Q.JyhUserCertificate
	JyhUserClosure = &Q.JyhUserClosure
	JyhUserExt = &Q.JyhUserExt
	JyhUserLevel = &Q.JyhUserLevel
	JyhUserLiveRoom = &Q.JyhUserLiveRoom
	JyhUserShipLevel = &Q.JyhUserShipLevel
	JyhUserShipLevelBenefit = &Q.JyhUserShipLevelBenefit
	JyhUserTag = &Q.JyhUserTag
	JyhUserTagRelation = &Q.JyhUserTagRelation
}

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:                        db,
		JyhArticle:                newJyhArticle(db, opts...),
		JyhArticleCategory:        newJyhArticleCategory(db, opts...),
		JyhCommission:             newJyhCommission(db, opts...),
		JyhCommissionRate:         newJyhCommissionRate(db, opts...),
		JyhCourse:                 newJyhCourse(db, opts...),
		JyhCourseCategory:         newJyhCourseCategory(db, opts...),
		JyhInviteCode:             newJyhInviteCode(db, opts...),
		JyhInviteRecord:           newJyhInviteRecord(db, opts...),
		JyhMaterial:               newJyhMaterial(db, opts...),
		JyhMaterialCategory:       newJyhMaterialCategory(db, opts...),
		JyhMaterialClaimRecord:    newJyhMaterialClaimRecord(db, opts...),
		JyhMaterialCustom:         newJyhMaterialCustom(db, opts...),
		JyhMaterialFile:           newJyhMaterialFile(db, opts...),
		JyhMaterialRuleConfig:     newJyhMaterialRuleConfig(db, opts...),
		JyhMaterialTag:            newJyhMaterialTag(db, opts...),
		JyhMaterialUpload:         newJyhMaterialUpload(db, opts...),
		JyhMaterialUser:           newJyhMaterialUser(db, opts...),
		JyhMcnOrderDetail:         newJyhMcnOrderDetail(db, opts...),
		JyhOrder:                  newJyhOrder(db, opts...),
		JyhPointsExchange:         newJyhPointsExchange(db, opts...),
		JyhPointsRecord:           newJyhPointsRecord(db, opts...),
		JyhPointsRule:             newJyhPointsRule(db, opts...),
		JyhRecommendDisplayLog:    newJyhRecommendDisplayLog(db, opts...),
		JyhRecommendItem:          newJyhRecommendItem(db, opts...),
		JyhRecommendPosition:      newJyhRecommendPosition(db, opts...),
		JyhTag:                    newJyhTag(db, opts...),
		JyhUser:                   newJyhUser(db, opts...),
		JyhUserAccount:            newJyhUserAccount(db, opts...),
		JyhUserAccountTransaction: newJyhUserAccountTransaction(db, opts...),
		JyhUserBenefit:            newJyhUserBenefit(db, opts...),
		JyhUserBenefitSnapshot:    newJyhUserBenefitSnapshot(db, opts...),
		JyhUserCertificate:        newJyhUserCertificate(db, opts...),
		JyhUserClosure:            newJyhUserClosure(db, opts...),
		JyhUserExt:                newJyhUserExt(db, opts...),
		JyhUserLevel:              newJyhUserLevel(db, opts...),
		JyhUserLiveRoom:           newJyhUserLiveRoom(db, opts...),
		JyhUserShipLevel:          newJyhUserShipLevel(db, opts...),
		JyhUserShipLevelBenefit:   newJyhUserShipLevelBenefit(db, opts...),
		JyhUserTag:                newJyhUserTag(db, opts...),
		JyhUserTagRelation:        newJyhUserTagRelation(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	JyhArticle                jyhArticle
	JyhArticleCategory        jyhArticleCategory
	JyhCommission             jyhCommission
	JyhCommissionRate         jyhCommissionRate
	JyhCourse                 jyhCourse
	JyhCourseCategory         jyhCourseCategory
	JyhInviteCode             jyhInviteCode
	JyhInviteRecord           jyhInviteRecord
	JyhMaterial               jyhMaterial
	JyhMaterialCategory       jyhMaterialCategory
	JyhMaterialClaimRecord    jyhMaterialClaimRecord
	JyhMaterialCustom         jyhMaterialCustom
	JyhMaterialFile           jyhMaterialFile
	JyhMaterialRuleConfig     jyhMaterialRuleConfig
	JyhMaterialTag            jyhMaterialTag
	JyhMaterialUpload         jyhMaterialUpload
	JyhMaterialUser           jyhMaterialUser
	JyhMcnOrderDetail         jyhMcnOrderDetail
	JyhOrder                  jyhOrder
	JyhPointsExchange         jyhPointsExchange
	JyhPointsRecord           jyhPointsRecord
	JyhPointsRule             jyhPointsRule
	JyhRecommendDisplayLog    jyhRecommendDisplayLog
	JyhRecommendItem          jyhRecommendItem
	JyhRecommendPosition      jyhRecommendPosition
	JyhTag                    jyhTag
	JyhUser                   jyhUser
	JyhUserAccount            jyhUserAccount
	JyhUserAccountTransaction jyhUserAccountTransaction
	JyhUserBenefit            jyhUserBenefit
	JyhUserBenefitSnapshot    jyhUserBenefitSnapshot
	JyhUserCertificate        jyhUserCertificate
	JyhUserClosure            jyhUserClosure
	JyhUserExt                jyhUserExt
	JyhUserLevel              jyhUserLevel
	JyhUserLiveRoom           jyhUserLiveRoom
	JyhUserShipLevel          jyhUserShipLevel
	JyhUserShipLevelBenefit   jyhUserShipLevelBenefit
	JyhUserTag                jyhUserTag
	JyhUserTagRelation        jyhUserTagRelation
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:                        db,
		JyhArticle:                q.JyhArticle.clone(db),
		JyhArticleCategory:        q.JyhArticleCategory.clone(db),
		JyhCommission:             q.JyhCommission.clone(db),
		JyhCommissionRate:         q.JyhCommissionRate.clone(db),
		JyhCourse:                 q.JyhCourse.clone(db),
		JyhCourseCategory:         q.JyhCourseCategory.clone(db),
		JyhInviteCode:             q.JyhInviteCode.clone(db),
		JyhInviteRecord:           q.JyhInviteRecord.clone(db),
		JyhMaterial:               q.JyhMaterial.clone(db),
		JyhMaterialCategory:       q.JyhMaterialCategory.clone(db),
		JyhMaterialClaimRecord:    q.JyhMaterialClaimRecord.clone(db),
		JyhMaterialCustom:         q.JyhMaterialCustom.clone(db),
		JyhMaterialFile:           q.JyhMaterialFile.clone(db),
		JyhMaterialRuleConfig:     q.JyhMaterialRuleConfig.clone(db),
		JyhMaterialTag:            q.JyhMaterialTag.clone(db),
		JyhMaterialUpload:         q.JyhMaterialUpload.clone(db),
		JyhMaterialUser:           q.JyhMaterialUser.clone(db),
		JyhMcnOrderDetail:         q.JyhMcnOrderDetail.clone(db),
		JyhOrder:                  q.JyhOrder.clone(db),
		JyhPointsExchange:         q.JyhPointsExchange.clone(db),
		JyhPointsRecord:           q.JyhPointsRecord.clone(db),
		JyhPointsRule:             q.JyhPointsRule.clone(db),
		JyhRecommendDisplayLog:    q.JyhRecommendDisplayLog.clone(db),
		JyhRecommendItem:          q.JyhRecommendItem.clone(db),
		JyhRecommendPosition:      q.JyhRecommendPosition.clone(db),
		JyhTag:                    q.JyhTag.clone(db),
		JyhUser:                   q.JyhUser.clone(db),
		JyhUserAccount:            q.JyhUserAccount.clone(db),
		JyhUserAccountTransaction: q.JyhUserAccountTransaction.clone(db),
		JyhUserBenefit:            q.JyhUserBenefit.clone(db),
		JyhUserBenefitSnapshot:    q.JyhUserBenefitSnapshot.clone(db),
		JyhUserCertificate:        q.JyhUserCertificate.clone(db),
		JyhUserClosure:            q.JyhUserClosure.clone(db),
		JyhUserExt:                q.JyhUserExt.clone(db),
		JyhUserLevel:              q.JyhUserLevel.clone(db),
		JyhUserLiveRoom:           q.JyhUserLiveRoom.clone(db),
		JyhUserShipLevel:          q.JyhUserShipLevel.clone(db),
		JyhUserShipLevelBenefit:   q.JyhUserShipLevelBenefit.clone(db),
		JyhUserTag:                q.JyhUserTag.clone(db),
		JyhUserTagRelation:        q.JyhUserTagRelation.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:                        db,
		JyhArticle:                q.JyhArticle.replaceDB(db),
		JyhArticleCategory:        q.JyhArticleCategory.replaceDB(db),
		JyhCommission:             q.JyhCommission.replaceDB(db),
		JyhCommissionRate:         q.JyhCommissionRate.replaceDB(db),
		JyhCourse:                 q.JyhCourse.replaceDB(db),
		JyhCourseCategory:         q.JyhCourseCategory.replaceDB(db),
		JyhInviteCode:             q.JyhInviteCode.replaceDB(db),
		JyhInviteRecord:           q.JyhInviteRecord.replaceDB(db),
		JyhMaterial:               q.JyhMaterial.replaceDB(db),
		JyhMaterialCategory:       q.JyhMaterialCategory.replaceDB(db),
		JyhMaterialClaimRecord:    q.JyhMaterialClaimRecord.replaceDB(db),
		JyhMaterialCustom:         q.JyhMaterialCustom.replaceDB(db),
		JyhMaterialFile:           q.JyhMaterialFile.replaceDB(db),
		JyhMaterialRuleConfig:     q.JyhMaterialRuleConfig.replaceDB(db),
		JyhMaterialTag:            q.JyhMaterialTag.replaceDB(db),
		JyhMaterialUpload:         q.JyhMaterialUpload.replaceDB(db),
		JyhMaterialUser:           q.JyhMaterialUser.replaceDB(db),
		JyhMcnOrderDetail:         q.JyhMcnOrderDetail.replaceDB(db),
		JyhOrder:                  q.JyhOrder.replaceDB(db),
		JyhPointsExchange:         q.JyhPointsExchange.replaceDB(db),
		JyhPointsRecord:           q.JyhPointsRecord.replaceDB(db),
		JyhPointsRule:             q.JyhPointsRule.replaceDB(db),
		JyhRecommendDisplayLog:    q.JyhRecommendDisplayLog.replaceDB(db),
		JyhRecommendItem:          q.JyhRecommendItem.replaceDB(db),
		JyhRecommendPosition:      q.JyhRecommendPosition.replaceDB(db),
		JyhTag:                    q.JyhTag.replaceDB(db),
		JyhUser:                   q.JyhUser.replaceDB(db),
		JyhUserAccount:            q.JyhUserAccount.replaceDB(db),
		JyhUserAccountTransaction: q.JyhUserAccountTransaction.replaceDB(db),
		JyhUserBenefit:            q.JyhUserBenefit.replaceDB(db),
		JyhUserBenefitSnapshot:    q.JyhUserBenefitSnapshot.replaceDB(db),
		JyhUserCertificate:        q.JyhUserCertificate.replaceDB(db),
		JyhUserClosure:            q.JyhUserClosure.replaceDB(db),
		JyhUserExt:                q.JyhUserExt.replaceDB(db),
		JyhUserLevel:              q.JyhUserLevel.replaceDB(db),
		JyhUserLiveRoom:           q.JyhUserLiveRoom.replaceDB(db),
		JyhUserShipLevel:          q.JyhUserShipLevel.replaceDB(db),
		JyhUserShipLevelBenefit:   q.JyhUserShipLevelBenefit.replaceDB(db),
		JyhUserTag:                q.JyhUserTag.replaceDB(db),
		JyhUserTagRelation:        q.JyhUserTagRelation.replaceDB(db),
	}
}

type queryCtx struct {
	JyhArticle                *jyhArticleDo
	JyhArticleCategory        *jyhArticleCategoryDo
	JyhCommission             *jyhCommissionDo
	JyhCommissionRate         *jyhCommissionRateDo
	JyhCourse                 *jyhCourseDo
	JyhCourseCategory         *jyhCourseCategoryDo
	JyhInviteCode             *jyhInviteCodeDo
	JyhInviteRecord           *jyhInviteRecordDo
	JyhMaterial               *jyhMaterialDo
	JyhMaterialCategory       *jyhMaterialCategoryDo
	JyhMaterialClaimRecord    *jyhMaterialClaimRecordDo
	JyhMaterialCustom         *jyhMaterialCustomDo
	JyhMaterialFile           *jyhMaterialFileDo
	JyhMaterialRuleConfig     *jyhMaterialRuleConfigDo
	JyhMaterialTag            *jyhMaterialTagDo
	JyhMaterialUpload         *jyhMaterialUploadDo
	JyhMaterialUser           *jyhMaterialUserDo
	JyhMcnOrderDetail         *jyhMcnOrderDetailDo
	JyhOrder                  *jyhOrderDo
	JyhPointsExchange         *jyhPointsExchangeDo
	JyhPointsRecord           *jyhPointsRecordDo
	JyhPointsRule             *jyhPointsRuleDo
	JyhRecommendDisplayLog    *jyhRecommendDisplayLogDo
	JyhRecommendItem          *jyhRecommendItemDo
	JyhRecommendPosition      *jyhRecommendPositionDo
	JyhTag                    *jyhTagDo
	JyhUser                   *jyhUserDo
	JyhUserAccount            *jyhUserAccountDo
	JyhUserAccountTransaction *jyhUserAccountTransactionDo
	JyhUserBenefit            *jyhUserBenefitDo
	JyhUserBenefitSnapshot    *jyhUserBenefitSnapshotDo
	JyhUserCertificate        *jyhUserCertificateDo
	JyhUserClosure            *jyhUserClosureDo
	JyhUserExt                *jyhUserExtDo
	JyhUserLevel              *jyhUserLevelDo
	JyhUserLiveRoom           *jyhUserLiveRoomDo
	JyhUserShipLevel          *jyhUserShipLevelDo
	JyhUserShipLevelBenefit   *jyhUserShipLevelBenefitDo
	JyhUserTag                *jyhUserTagDo
	JyhUserTagRelation        *jyhUserTagRelationDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		JyhArticle:                q.JyhArticle.WithContext(ctx),
		JyhArticleCategory:        q.JyhArticleCategory.WithContext(ctx),
		JyhCommission:             q.JyhCommission.WithContext(ctx),
		JyhCommissionRate:         q.JyhCommissionRate.WithContext(ctx),
		JyhCourse:                 q.JyhCourse.WithContext(ctx),
		JyhCourseCategory:         q.JyhCourseCategory.WithContext(ctx),
		JyhInviteCode:             q.JyhInviteCode.WithContext(ctx),
		JyhInviteRecord:           q.JyhInviteRecord.WithContext(ctx),
		JyhMaterial:               q.JyhMaterial.WithContext(ctx),
		JyhMaterialCategory:       q.JyhMaterialCategory.WithContext(ctx),
		JyhMaterialClaimRecord:    q.JyhMaterialClaimRecord.WithContext(ctx),
		JyhMaterialCustom:         q.JyhMaterialCustom.WithContext(ctx),
		JyhMaterialFile:           q.JyhMaterialFile.WithContext(ctx),
		JyhMaterialRuleConfig:     q.JyhMaterialRuleConfig.WithContext(ctx),
		JyhMaterialTag:            q.JyhMaterialTag.WithContext(ctx),
		JyhMaterialUpload:         q.JyhMaterialUpload.WithContext(ctx),
		JyhMaterialUser:           q.JyhMaterialUser.WithContext(ctx),
		JyhMcnOrderDetail:         q.JyhMcnOrderDetail.WithContext(ctx),
		JyhOrder:                  q.JyhOrder.WithContext(ctx),
		JyhPointsExchange:         q.JyhPointsExchange.WithContext(ctx),
		JyhPointsRecord:           q.JyhPointsRecord.WithContext(ctx),
		JyhPointsRule:             q.JyhPointsRule.WithContext(ctx),
		JyhRecommendDisplayLog:    q.JyhRecommendDisplayLog.WithContext(ctx),
		JyhRecommendItem:          q.JyhRecommendItem.WithContext(ctx),
		JyhRecommendPosition:      q.JyhRecommendPosition.WithContext(ctx),
		JyhTag:                    q.JyhTag.WithContext(ctx),
		JyhUser:                   q.JyhUser.WithContext(ctx),
		JyhUserAccount:            q.JyhUserAccount.WithContext(ctx),
		JyhUserAccountTransaction: q.JyhUserAccountTransaction.WithContext(ctx),
		JyhUserBenefit:            q.JyhUserBenefit.WithContext(ctx),
		JyhUserBenefitSnapshot:    q.JyhUserBenefitSnapshot.WithContext(ctx),
		JyhUserCertificate:        q.JyhUserCertificate.WithContext(ctx),
		JyhUserClosure:            q.JyhUserClosure.WithContext(ctx),
		JyhUserExt:                q.JyhUserExt.WithContext(ctx),
		JyhUserLevel:              q.JyhUserLevel.WithContext(ctx),
		JyhUserLiveRoom:           q.JyhUserLiveRoom.WithContext(ctx),
		JyhUserShipLevel:          q.JyhUserShipLevel.WithContext(ctx),
		JyhUserShipLevelBenefit:   q.JyhUserShipLevelBenefit.WithContext(ctx),
		JyhUserTag:                q.JyhUserTag.WithContext(ctx),
		JyhUserTagRelation:        q.JyhUserTagRelation.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
