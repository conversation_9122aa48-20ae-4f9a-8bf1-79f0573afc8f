package response

// UserTagItem 用户标签列表项
type UserTagItem struct {
	ID          uint   `json:"id"`          // 标签ID
	Name        string `json:"name"`        // 标签名称
	Description string `json:"description"` // 标签描述
	Color       string `json:"color"`       // 标签颜色
	Status      uint   `json:"status"`      // 标签状态：1-启用, 0-禁用
	Sort        uint   `json:"sort"`        // 排序权重
	CreatedAt   string `json:"created_at"`  // 创建时间
}

// UserTagDetail 用户标签详情
type UserTagDetail struct {
	ID          uint   `json:"id"`          // 标签ID
	Name        string `json:"name"`        // 标签名称
	Description string `json:"description"` // 标签描述
	Color       string `json:"color"`       // 标签颜色
	Status      uint   `json:"status"`      // 标签状态：1-启用, 0-禁用
	Sort        uint   `json:"sort"`        // 排序权重
	CreatedAt   string `json:"created_at"`  // 创建时间
	UpdatedAt   string `json:"updated_at"`  // 更新时间
}

// UserWithTags 带标签的用户信息
type UserWithTags struct {
	UserID uint          `json:"user_id"` // 用户ID
	Tags   []UserTagItem `json:"tags"`    // 用户拥有的标签列表
}
