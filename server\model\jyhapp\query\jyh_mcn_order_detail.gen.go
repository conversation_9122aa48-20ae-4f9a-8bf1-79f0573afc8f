// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
)

func newJyhMcnOrderDetail(db *gorm.DB, opts ...gen.DOOption) jyhMcnOrderDetail {
	_jyhMcnOrderDetail := jyhMcnOrderDetail{}

	_jyhMcnOrderDetail.jyhMcnOrderDetailDo.UseDB(db, opts...)
	_jyhMcnOrderDetail.jyhMcnOrderDetailDo.UseModel(&jyhapp.JyhMcnOrderDetail{})

	tableName := _jyhMcnOrderDetail.jyhMcnOrderDetailDo.TableName()
	_jyhMcnOrderDetail.ALL = field.NewAsterisk(tableName)
	_jyhMcnOrderDetail.ID = field.NewUint(tableName, "id")
	_jyhMcnOrderDetail.Date = field.NewString(tableName, "date")
	_jyhMcnOrderDetail.TotalCount = field.NewInt(tableName, "total_count")
	_jyhMcnOrderDetail.StartTime = field.NewString(tableName, "start_time")
	_jyhMcnOrderDetail.EndTime = field.NewString(tableName, "end_time")
	_jyhMcnOrderDetail.BillId = field.NewString(tableName, "bill_id")
	_jyhMcnOrderDetail.TotalAmount = field.NewInt(tableName, "total_amount")
	_jyhMcnOrderDetail.TotalServiceFee = field.NewInt(tableName, "total_service_fee")
	_jyhMcnOrderDetail.TotalSettleAmount = field.NewInt(tableName, "total_settle_amount")
	_jyhMcnOrderDetail.AuthorFee = field.NewInt(tableName, "author_fee")
	_jyhMcnOrderDetail.OrganizationFee = field.NewInt(tableName, "organization_fee")
	_jyhMcnOrderDetail.AuthorUid = field.NewString(tableName, "author_uid")
	_jyhMcnOrderDetail.PayType = field.NewString(tableName, "pay_type")
	_jyhMcnOrderDetail.TotalServiceFeeReturn = field.NewInt(tableName, "total_service_fee_return")
	_jyhMcnOrderDetail.AuthorFeeReturn = field.NewInt(tableName, "author_fee_return")
	_jyhMcnOrderDetail.OrganizationFeeReturn = field.NewInt(tableName, "organization_fee_return")
	_jyhMcnOrderDetail.AuthorName = field.NewString(tableName, "author_name")
	_jyhMcnOrderDetail.UidType = field.NewString(tableName, "uid_type")
	_jyhMcnOrderDetail.AppId = field.NewInt(tableName, "app_id")
	_jyhMcnOrderDetail.CommissionAmount = field.NewInt(tableName, "commission_amount")
	_jyhMcnOrderDetail.SumServiceFee = field.NewInt(tableName, "sum_service_fee")
	_jyhMcnOrderDetail.SettleAmount = field.NewInt(tableName, "settle_amount")
	_jyhMcnOrderDetail.SettleAmountReturn = field.NewInt(tableName, "settle_amount_return")
	_jyhMcnOrderDetail.CommissionAmountReturn = field.NewInt(tableName, "commission_amount_return")
	_jyhMcnOrderDetail.SumOrganizationFee = field.NewInt(tableName, "sum_organization_fee")
	_jyhMcnOrderDetail.SumAuthorFee = field.NewInt(tableName, "sum_author_fee")
	_jyhMcnOrderDetail.ColonelFee = field.NewInt(tableName, "colonel_fee")
	_jyhMcnOrderDetail.AuthorModeCount = field.NewInt(tableName, "author_mode_count")
	_jyhMcnOrderDetail.ShopModeCount = field.NewInt(tableName, "shop_mode_count")
	_jyhMcnOrderDetail.DouCustomerFee = field.NewInt(tableName, "dou_customer_fee")
	_jyhMcnOrderDetail.DouCustomerFeeReturn = field.NewInt(tableName, "dou_customer_fee_return")

	_jyhMcnOrderDetail.fillFieldMap()

	return _jyhMcnOrderDetail
}

type jyhMcnOrderDetail struct {
	jyhMcnOrderDetailDo

	ALL                    field.Asterisk
	ID                     field.Uint
	Date                   field.String
	TotalCount             field.Int
	StartTime              field.String
	EndTime                field.String
	BillId                 field.String
	TotalAmount            field.Int
	TotalServiceFee        field.Int
	TotalSettleAmount      field.Int
	AuthorFee              field.Int
	OrganizationFee        field.Int
	AuthorUid              field.String
	PayType                field.String
	TotalServiceFeeReturn  field.Int
	AuthorFeeReturn        field.Int
	OrganizationFeeReturn  field.Int
	AuthorName             field.String
	UidType                field.String
	AppId                  field.Int
	CommissionAmount       field.Int
	SumServiceFee          field.Int
	SettleAmount           field.Int
	SettleAmountReturn     field.Int
	CommissionAmountReturn field.Int
	SumOrganizationFee     field.Int
	SumAuthorFee           field.Int
	ColonelFee             field.Int
	AuthorModeCount        field.Int
	ShopModeCount          field.Int
	DouCustomerFee         field.Int
	DouCustomerFeeReturn   field.Int

	fieldMap map[string]field.Expr
}

func (j jyhMcnOrderDetail) Table(newTableName string) *jyhMcnOrderDetail {
	j.jyhMcnOrderDetailDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j jyhMcnOrderDetail) As(alias string) *jyhMcnOrderDetail {
	j.jyhMcnOrderDetailDo.DO = *(j.jyhMcnOrderDetailDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *jyhMcnOrderDetail) updateTableName(table string) *jyhMcnOrderDetail {
	j.ALL = field.NewAsterisk(table)
	j.ID = field.NewUint(table, "id")
	j.Date = field.NewString(table, "date")
	j.TotalCount = field.NewInt(table, "total_count")
	j.StartTime = field.NewString(table, "start_time")
	j.EndTime = field.NewString(table, "end_time")
	j.BillId = field.NewString(table, "bill_id")
	j.TotalAmount = field.NewInt(table, "total_amount")
	j.TotalServiceFee = field.NewInt(table, "total_service_fee")
	j.TotalSettleAmount = field.NewInt(table, "total_settle_amount")
	j.AuthorFee = field.NewInt(table, "author_fee")
	j.OrganizationFee = field.NewInt(table, "organization_fee")
	j.AuthorUid = field.NewString(table, "author_uid")
	j.PayType = field.NewString(table, "pay_type")
	j.TotalServiceFeeReturn = field.NewInt(table, "total_service_fee_return")
	j.AuthorFeeReturn = field.NewInt(table, "author_fee_return")
	j.OrganizationFeeReturn = field.NewInt(table, "organization_fee_return")
	j.AuthorName = field.NewString(table, "author_name")
	j.UidType = field.NewString(table, "uid_type")
	j.AppId = field.NewInt(table, "app_id")
	j.CommissionAmount = field.NewInt(table, "commission_amount")
	j.SumServiceFee = field.NewInt(table, "sum_service_fee")
	j.SettleAmount = field.NewInt(table, "settle_amount")
	j.SettleAmountReturn = field.NewInt(table, "settle_amount_return")
	j.CommissionAmountReturn = field.NewInt(table, "commission_amount_return")
	j.SumOrganizationFee = field.NewInt(table, "sum_organization_fee")
	j.SumAuthorFee = field.NewInt(table, "sum_author_fee")
	j.ColonelFee = field.NewInt(table, "colonel_fee")
	j.AuthorModeCount = field.NewInt(table, "author_mode_count")
	j.ShopModeCount = field.NewInt(table, "shop_mode_count")
	j.DouCustomerFee = field.NewInt(table, "dou_customer_fee")
	j.DouCustomerFeeReturn = field.NewInt(table, "dou_customer_fee_return")

	j.fillFieldMap()

	return j
}

func (j *jyhMcnOrderDetail) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *jyhMcnOrderDetail) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 31)
	j.fieldMap["id"] = j.ID
	j.fieldMap["date"] = j.Date
	j.fieldMap["total_count"] = j.TotalCount
	j.fieldMap["start_time"] = j.StartTime
	j.fieldMap["end_time"] = j.EndTime
	j.fieldMap["bill_id"] = j.BillId
	j.fieldMap["total_amount"] = j.TotalAmount
	j.fieldMap["total_service_fee"] = j.TotalServiceFee
	j.fieldMap["total_settle_amount"] = j.TotalSettleAmount
	j.fieldMap["author_fee"] = j.AuthorFee
	j.fieldMap["organization_fee"] = j.OrganizationFee
	j.fieldMap["author_uid"] = j.AuthorUid
	j.fieldMap["pay_type"] = j.PayType
	j.fieldMap["total_service_fee_return"] = j.TotalServiceFeeReturn
	j.fieldMap["author_fee_return"] = j.AuthorFeeReturn
	j.fieldMap["organization_fee_return"] = j.OrganizationFeeReturn
	j.fieldMap["author_name"] = j.AuthorName
	j.fieldMap["uid_type"] = j.UidType
	j.fieldMap["app_id"] = j.AppId
	j.fieldMap["commission_amount"] = j.CommissionAmount
	j.fieldMap["sum_service_fee"] = j.SumServiceFee
	j.fieldMap["settle_amount"] = j.SettleAmount
	j.fieldMap["settle_amount_return"] = j.SettleAmountReturn
	j.fieldMap["commission_amount_return"] = j.CommissionAmountReturn
	j.fieldMap["sum_organization_fee"] = j.SumOrganizationFee
	j.fieldMap["sum_author_fee"] = j.SumAuthorFee
	j.fieldMap["colonel_fee"] = j.ColonelFee
	j.fieldMap["author_mode_count"] = j.AuthorModeCount
	j.fieldMap["shop_mode_count"] = j.ShopModeCount
	j.fieldMap["dou_customer_fee"] = j.DouCustomerFee
	j.fieldMap["dou_customer_fee_return"] = j.DouCustomerFeeReturn
}

func (j jyhMcnOrderDetail) clone(db *gorm.DB) jyhMcnOrderDetail {
	j.jyhMcnOrderDetailDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j jyhMcnOrderDetail) replaceDB(db *gorm.DB) jyhMcnOrderDetail {
	j.jyhMcnOrderDetailDo.ReplaceDB(db)
	return j
}

type jyhMcnOrderDetailDo struct{ gen.DO }

func (j jyhMcnOrderDetailDo) Debug() *jyhMcnOrderDetailDo {
	return j.withDO(j.DO.Debug())
}

func (j jyhMcnOrderDetailDo) WithContext(ctx context.Context) *jyhMcnOrderDetailDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jyhMcnOrderDetailDo) ReadDB() *jyhMcnOrderDetailDo {
	return j.Clauses(dbresolver.Read)
}

func (j jyhMcnOrderDetailDo) WriteDB() *jyhMcnOrderDetailDo {
	return j.Clauses(dbresolver.Write)
}

func (j jyhMcnOrderDetailDo) Session(config *gorm.Session) *jyhMcnOrderDetailDo {
	return j.withDO(j.DO.Session(config))
}

func (j jyhMcnOrderDetailDo) Clauses(conds ...clause.Expression) *jyhMcnOrderDetailDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jyhMcnOrderDetailDo) Returning(value interface{}, columns ...string) *jyhMcnOrderDetailDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jyhMcnOrderDetailDo) Not(conds ...gen.Condition) *jyhMcnOrderDetailDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jyhMcnOrderDetailDo) Or(conds ...gen.Condition) *jyhMcnOrderDetailDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jyhMcnOrderDetailDo) Select(conds ...field.Expr) *jyhMcnOrderDetailDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jyhMcnOrderDetailDo) Where(conds ...gen.Condition) *jyhMcnOrderDetailDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jyhMcnOrderDetailDo) Order(conds ...field.Expr) *jyhMcnOrderDetailDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jyhMcnOrderDetailDo) Distinct(cols ...field.Expr) *jyhMcnOrderDetailDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jyhMcnOrderDetailDo) Omit(cols ...field.Expr) *jyhMcnOrderDetailDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jyhMcnOrderDetailDo) Join(table schema.Tabler, on ...field.Expr) *jyhMcnOrderDetailDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jyhMcnOrderDetailDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jyhMcnOrderDetailDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jyhMcnOrderDetailDo) RightJoin(table schema.Tabler, on ...field.Expr) *jyhMcnOrderDetailDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jyhMcnOrderDetailDo) Group(cols ...field.Expr) *jyhMcnOrderDetailDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jyhMcnOrderDetailDo) Having(conds ...gen.Condition) *jyhMcnOrderDetailDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jyhMcnOrderDetailDo) Limit(limit int) *jyhMcnOrderDetailDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jyhMcnOrderDetailDo) Offset(offset int) *jyhMcnOrderDetailDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jyhMcnOrderDetailDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jyhMcnOrderDetailDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jyhMcnOrderDetailDo) Unscoped() *jyhMcnOrderDetailDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jyhMcnOrderDetailDo) Create(values ...*jyhapp.JyhMcnOrderDetail) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jyhMcnOrderDetailDo) CreateInBatches(values []*jyhapp.JyhMcnOrderDetail, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jyhMcnOrderDetailDo) Save(values ...*jyhapp.JyhMcnOrderDetail) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jyhMcnOrderDetailDo) First() (*jyhapp.JyhMcnOrderDetail, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMcnOrderDetail), nil
	}
}

func (j jyhMcnOrderDetailDo) Take() (*jyhapp.JyhMcnOrderDetail, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMcnOrderDetail), nil
	}
}

func (j jyhMcnOrderDetailDo) Last() (*jyhapp.JyhMcnOrderDetail, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMcnOrderDetail), nil
	}
}

func (j jyhMcnOrderDetailDo) Find() ([]*jyhapp.JyhMcnOrderDetail, error) {
	result, err := j.DO.Find()
	return result.([]*jyhapp.JyhMcnOrderDetail), err
}

func (j jyhMcnOrderDetailDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*jyhapp.JyhMcnOrderDetail, err error) {
	buf := make([]*jyhapp.JyhMcnOrderDetail, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jyhMcnOrderDetailDo) FindInBatches(result *[]*jyhapp.JyhMcnOrderDetail, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jyhMcnOrderDetailDo) Attrs(attrs ...field.AssignExpr) *jyhMcnOrderDetailDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jyhMcnOrderDetailDo) Assign(attrs ...field.AssignExpr) *jyhMcnOrderDetailDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jyhMcnOrderDetailDo) Joins(fields ...field.RelationField) *jyhMcnOrderDetailDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jyhMcnOrderDetailDo) Preload(fields ...field.RelationField) *jyhMcnOrderDetailDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jyhMcnOrderDetailDo) FirstOrInit() (*jyhapp.JyhMcnOrderDetail, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMcnOrderDetail), nil
	}
}

func (j jyhMcnOrderDetailDo) FirstOrCreate() (*jyhapp.JyhMcnOrderDetail, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMcnOrderDetail), nil
	}
}

func (j jyhMcnOrderDetailDo) FindByPage(offset int, limit int) (result []*jyhapp.JyhMcnOrderDetail, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jyhMcnOrderDetailDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jyhMcnOrderDetailDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jyhMcnOrderDetailDo) Delete(models ...*jyhapp.JyhMcnOrderDetail) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jyhMcnOrderDetailDo) withDO(do gen.Dao) *jyhMcnOrderDetailDo {
	j.DO = *do.(*gen.DO)
	return j
}
