package jyhapp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/service"
)

type ApiGroup struct {
	MaterialCategoryApi
	MaterialApi
	CourseApi
	CourseCategoryApi
	UserApi
	AdminUserApi
	TagsApi
	RecommendPositionApi
	JyhUserBenefitApi
	LiveApi
	UserContractApi
	JyhUserTagApi
	ArticleApi
	JyhUserLiveRoomApi
	OrderApi
	JyhInviteCodeApi
	JyhUserAccountApi

	ExternalApi
	PointsApi
}

var (
	// 素材管理
	materialCategoryService = service.ServiceGroupApp.JyhServiceGroup.MaterialCategoryService
	materialService         = service.ServiceGroupApp.JyhServiceGroup.MaterialService
	materialSyncService     = service.ServiceGroupApp.JyhServiceGroup.MaterialSyncService
	// 课程管理
	courseService         = service.ServiceGroupApp.JyhServiceGroup.JyhCourseService
	courseCategoryService = service.ServiceGroupApp.JyhServiceGroup.CourseCategoryService
	// 用户管理
	userService = service.ServiceGroupApp.JyhServiceGroup.JyhUserService
	// 标签管理
	tagsService = service.ServiceGroupApp.JyhServiceGroup.TagsService
	// 推荐位管理
	recommendPositionService = service.ServiceGroupApp.JyhServiceGroup.RecommendPositionService
	// 用户权益管理
	userBenefitService = service.ServiceGroupApp.JyhServiceGroup.JyhUserBenefitService
	// 用户签约管理
	userContractService = service.ServiceGroupApp.JyhServiceGroup.JyhUserContractService
	//用户标签管理
	userTagService = service.ServiceGroupApp.JyhServiceGroup.JyhUserTagService
	//文章管理
	articleService = service.ServiceGroupApp.JyhServiceGroup.JyhArticleService
	// 用户直播房间管理
	jyhUserLiveRoomService = service.ServiceGroupApp.JyhServiceGroup.JyhUserLiveRoomService
	// OrderService 用户订单
	OrderService = service.ServiceGroupApp.JyhServiceGroup.JyhOrderService
	// 邀请码管理
	jyhInviteCodeService = service.ServiceGroupApp.JyhServiceGroup.JyhInviteCodeService
	// 用户账户管理
	jyhUserAccountService = service.ServiceGroupApp.JyhServiceGroup.JyhUserAccountService
	// 用户积分管理（今音荟）
	jyhPointsService = service.ServiceGroupApp.JyhServiceGroup.JyhPointsService
)
