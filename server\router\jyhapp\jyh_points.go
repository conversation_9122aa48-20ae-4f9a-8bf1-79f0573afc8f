package jyhapp

import (
	v1 "github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type JyhPointsRouter struct {
}

// InitPointsRouter 初始化 PointsRouter 路由信息
func (s *JyhPointsRouter) InitPointsRouter(Router *gin.RouterGroup) {
	pointsRouter := Router.Group("points").Use(middleware.OperationRecord())
	pointsRouterWithoutRecord := Router.Group("points")
	pointsApi := v1.ApiGroupApp.JyhApiGroup.PointsApi
	{
		pointsRouterWithoutRecord.GET("records", pointsApi.GetPointsRecords)       // 获取积分明细
		pointsRouterWithoutRecord.GET("rules", pointsApi.GetPointsRules)           // 获取积分规则
		pointsRouterWithoutRecord.GET("rules/detail", pointsApi.GetPointsRuleByID) // 获取积分规则详情
	}
	{
		pointsRouter.PUT("rule", pointsApi.UpdatePointsRule)                            // 更新积分规则
		pointsRouterWithoutRecord.GET("user/records", pointsApi.GetPointsRecordsByUser) // 获取指定用户的积分记录
	}
	{
		// 兑换相关
		pointsRouter.POST("exchange", pointsApi.ExchangeMaterial)             // 兑换物品
		pointsRouter.GET("exchange/records", pointsApi.GetExchangeRecord)     // 获取所有兑换记录
		pointsRouter.GET("exchange/user/records", pointsApi.GetUserExchanges) // 获取用户兑换记录
		pointsRouter.POST("exchange/cancel", pointsApi.CancelExchange)        // 取消兑换

		pointsRouter.POST("exchange/complete", pointsApi.CompleteExchange) // 完成兑换
	}
}
