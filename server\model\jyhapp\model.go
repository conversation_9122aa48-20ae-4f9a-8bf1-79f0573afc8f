package jyhapp

import (
	"gorm.io/gorm"
	"time"
)

// 公共类型定义
//
// 说明:
//  1. 自增长主键用 uint 类型；如果量大，可用 uint64

// Model0 见 gorm.Model
type Model0 struct {
	ID        uint `gorm:"primarykey"`
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt gorm.DeletedAt `gorm:"index"`
}

type Model0CamelCase struct {
	ID        uint           `gorm:"primarykey" json:"id"`
	CreatedAt time.Time      `json:"createdAt"`
	UpdatedAt time.Time      `json:"updatedAt"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deletedAt"`
}

type Model0WithBy struct {
	ID        uint `gorm:"primarykey"`
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt gorm.DeletedAt `gorm:"index"`

	CreatedBy uint `gorm:"type:int;comment:创建人"`
	UpdatedBy uint `gorm:"type:int;comment:更新人"`
	DeletedBy uint `gorm:"type:int;comment:删除人"`
}

// Model1 见 gorm.Model，不含逻辑删除的 DeletedAt
type Model1 struct {
	ID        uint `gorm:"primarykey"`
	CreatedAt time.Time
	UpdatedAt time.Time
}

type Model1CamelCase struct {
	ID        uint      `gorm:"primarykey" json:"id"`
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}

type Model1WithBy struct {
	ID        uint `gorm:"primarykey"`
	CreatedAt time.Time
	UpdatedAt time.Time

	CreatedBy uint `gorm:"type:int;comment:创建人"`
	UpdatedBy uint `gorm:"type:int;comment:更新人"`
}

// ModelLocation 位置信息，包括地图坐标
type ModelLocation struct {
	Address   string  `gorm:"type:varchar(512);comment:地址"`
	Latitude  float64 `gorm:"type:decimal(10,6);comment:纬度"`
	Longitude float64 `gorm:"type:decimal(10,6);comment:经度"` // 经度
}

// ModelStore 公共信息：门店
type ModelStore struct {
	StoreId uint `gorm:"index;comment:门店ID"` // 门店ID
}

// Model0WithTime 见 gorm.Model，含创建时间和更新时间默认时间,不含逻辑删除的 DeletedAt
type Model0WithTime struct {
	ID        uint      `gorm:"primarykey"`
	CreatedAt time.Time `gorm:"column:created_at;type:TIMESTAMP;default:CURRENT_TIMESTAMP;<-:create"`
	UpdatedAt time.Time `gorm:"column:updated_at;type:TIMESTAMP;default:CURRENT_TIMESTAMP  on update current_timestamp"`
}

// Model0WithTimeBy 见 gorm.Model，含创建时间和更新时间默认时间
type Model0WithTimeBy struct {
	ID        uint           `gorm:"primarykey"`
	CreatedAt time.Time      `gorm:"column:created_at;type:TIMESTAMP;default:CURRENT_TIMESTAMP;<-:create"`
	UpdatedAt time.Time      `gorm:"column:updated_at;type:TIMESTAMP;default:CURRENT_TIMESTAMP  on update current_timestamp"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"` // 删除时间
}
