package request

import "github.com/flipped-aurora/gin-vue-admin/server/model/common/request"

// UserRegister 用户注册请求
type UserRegister struct {
	Phone      string `json:"phone" binding:"required"`       // 手机号
	InviteCode string `json:"invite_code" binding:"required"` // 邀请码
	SmsCode    string `json:"sms_code" binding:"required"`    // 验证码
}

type SendSmsCode struct {
	Phone string `json:"phone" binding:"required"`
	Scene string `json:"scene"` //场景 register-注册 login-登录 reset-重置
}

// UserLogin 用户登录请求
type UserLogin struct {
	Phone   string `json:"phone" binding:"required"`    // 手机号
	SmsCode string `json:"sms_code" binding:"required"` // 验证码
}

// AdminUserListReq 管理员获取用户列表请求
type AdminUserListReq struct {
	request.PageInfo
	Keyword   string  `json:"keyword" form:"keyword"`       // 搜索关键词（用户名/手机号）
	Status    *int    `json:"status" form:"status"`         // 用户状态 (1-启用 0-禁用)
	UserType  *string `json:"user_type" form:"user_type"`   // 用户类型
	IsAgent   *bool   `json:"is_agent" form:"is_agent"`     // 是否代理
	StartTime string  `json:"start_time" form:"start_time"` // 注册开始时间
	EndTime   string  `json:"end_time" form:"end_time"`     // 注册结束时间
	TagIDs    []uint  `json:"tag_ids" form:"tag_ids"`       // 标签ID列表（多选）
	TagName   string  `json:"tag_name" form:"tag_name"`     // 标签名称（模糊搜索）
}

// UserExtUpdateInfo 用户扩展信息更新
type UserExtUpdateInfo struct {
	IdCard         *string `json:"id_card"`          // 身份证号
	Age            *int    `json:"age"`              // 年龄
	Sex            *string `json:"sex"`              // 性别
	City           *string `json:"city"`             // 城市
	Occupation     *string `json:"occupation"`       // 职业
	Address        *string `json:"address"`          // 收货地址
	WeChat         *string `json:"wechat"`           // 微信号
	WeChatNickname *string `json:"wechat_nickname"`  // 微信昵称
	DouYin         *string `json:"douyin"`           // 抖音号
	DouYinNickname *string `json:"douyin_nickname"`  // 抖音昵称
	DouYinPhone    *string `json:"douyin_phone"`     // 抖音绑定手机号
	QQ             *string `json:"qq"`               // QQ号
	TrackType      *int    `json:"track_type"`       // 测赛道（1 拉流，2 挂车）
	IsCarOwner     *bool   `json:"is_car_owner"`     // 是否挂车
	CarOwnerTime   *string `json:"car_owner_time"`   // 挂车时间
	FansCount      *int    `json:"fans_count"`       // 粉丝数
	ValidFansCount *int    `json:"valid_fans_count"` // 有效粉丝数
}

// AdminUserReq 管理员更新用户请求
type AdminUserReq struct {
	ID                uint   `json:"id"`                       // 用户ID
	Phone             string `json:"phone" binding:"required"` // 手机号
	InviteCode        string `json:"invite_code"`              // 邀请码(可选，代表邀请人邀请码)
	Username          string `json:"user_name"`                // 用户名
	IsAgent           *bool  `json:"is_agent"`                 // 是否代理
	Status            *int   `json:"status"`                   // 状态
	UserType          string `json:"user_type"`                // 用户类型
	CanLiveStream     *bool  `json:"can_live_stream"`          // 是否可以直播
	ContractStatus    *int   `json:"contract_status"`          // 签约状态 0-未签约 1-已签约
	FansCount         int    `json:"fans_count"`               // 粉丝数
	ValidFansCount    int    `json:"valid_fans_count"`         // 有效粉丝数
	LevelID           *uint  `json:"level_id"`                 // 会员等级ID（可选）
	UserExtUpdateInfo        // 用户扩展信息
}

// AdminUserStatusReq 管理员切换用户状态请求
type AdminUserStatusReq struct {
	ID     uint `json:"id" binding:"required"` // 用户ID
	Status int  `json:"status"`                // 状态
}

// UpdateUserInfoReq 仅更新社交媒体信息
type UpdateUserInfoReq struct {
	WeChatNickname *string `json:"wechat_nickname"` // 微信昵称
	WeChat         *string `json:"wechat"`          // 微信号
	DouYinNickname *string `json:"douyin_nickname"` // 抖音昵称
	DouYin         *string `json:"douyin"`          // 抖音号
	DouYinPhone    *string `json:"douyin_phone"`    // 抖音绑定电话
	QQ             *string `json:"qq"`              // QQ号
	IdCard         *string `json:"id_card"`         // 身份证号
	Age            *int    `json:"age"`             // 年龄
	Sex            *string `json:"sex"`             // 性别
	City           *string `json:"city"`            // 城市
	Occupation     *string `json:"occupation"`      // 职业
	Address        *string `json:"address"`         // 收货地址
	Proof          *string `json:"proof"`           // 证明材料（如有）
}

// CertificateReview 凭证审核请求
type CertificateReview struct {
	CertificateID uint   `json:"certificateId" binding:"required"`
	Status        string `json:"status" binding:"required,oneof=approved rejected"`
	Remark        string `json:"remark"` // 审核备注
}

// CertificateSearch 凭证搜索请求
type CertificateSearch struct {
	request.PageInfo
	UserID     *uint  `form:"userId"`     // 用户ID
	Type       string `form:"type"`       // 凭证类型
	Status     string `form:"status"`     // 审核状态
	ReviewerID *uint  `form:"reviewerId"` // 审核人ID
}

// UploadCertificateReq 上传凭证请求
type UploadCertificateReq struct {
	FansCount       int    `json:"fans_count" binding:"required"`       // 粉丝数
	ValidFansCount  int    `json:"valid_fans_count" binding:"required"` // 有效粉丝数
	CertificateFile string `json:"certificate_file" binding:"required"` // 凭证文件路径
}
