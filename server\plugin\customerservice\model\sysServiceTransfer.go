package model

import "github.com/flipped-aurora/gin-vue-admin/server/global"

// SysServiceTransfer 客服转接记录表
type SysServiceTransfer struct {
	global.Model1
	SessionID       uint   `json:"session_id" gorm:"comment:会话记录ID(关联SysServiceRecord.ID);not null"`
	FromServiceID   int64  `json:"from_service_id" gorm:"comment:转出客服ID;not null"`
	ToServiceID     int64  `json:"to_service_id" gorm:"comment:转入客服ID;not null"`
	UserID          uint   `json:"user_id" gorm:"comment:用户ID;not null"`
	Reason          string `json:"reason" gorm:"comment:转接原因;size:500"`
	Status          uint   `json:"status" gorm:"comment:状态(1:待接收 2:已接收 3:已拒绝 4:已取消 5:已超时);default:1"`
	TransferTime    int64  `json:"transfer_time" gorm:"comment:转接时间;not null"`
	AcceptTime      int64  `json:"accept_time" gorm:"comment:接收时间"`
	RejectReason    string `json:"reject_reason" gorm:"comment:拒绝原因;size:500"`
	CancelReason    string `json:"cancel_reason" gorm:"comment:取消原因;size:500"`
	Priority        uint   `json:"priority" gorm:"comment:转接优先级(1:低 2:中 3:高);default:2"`
	TransferType    uint   `json:"transfer_type" gorm:"comment:转接类型(1:主动转接 2:智能转接 3:负载均衡);default:1"`
	ExpireTime      int64  `json:"expire_time" gorm:"comment:过期时间"`
	UserTags        string `json:"user_tags" gorm:"comment:用户标签JSON;type:text"`
	FromServiceInfo string `json:"from_service_info" gorm:"comment:转出客服信息JSON;type:text"`
	ToServiceInfo   string `json:"to_service_info" gorm:"comment:转入客服信息JSON;type:text"`
}

func (SysServiceTransfer) TableName() string {
	return "sys_service_transfer"
}

// 转接记录状态常量
const (
	TransferRecordStatusPending  = 1 // 待接收
	TransferRecordStatusAccepted = 2 // 已接收
	TransferRecordStatusRejected = 3 // 已拒绝
	TransferRecordStatusCanceled = 4 // 已取消
	TransferRecordStatusExpired  = 5 // 已超时
)

// 转接优先级常量
const (
	TransferPriorityLow    = 1 // 低优先级
	TransferPriorityMedium = 2 // 中优先级
	TransferPriorityHigh   = 3 // 高优先级
)

// 转接类型常量
const (
	TransferTypeManual      = 1 // 主动转接
	TransferTypeIntelligent = 2 // 智能转接
	TransferTypeLoadBalance = 3 // 负载均衡
)

// 转接过期时间（秒）
const (
	TransferExpireTime = 300 // 5分钟
)
