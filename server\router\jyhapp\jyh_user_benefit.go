package jyhapp

import (
	v1 "github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type JyhUserBenefitRouter struct{}

// InitJyhUserBenefitRouter 初始化会员权益管理路由
func (r *JyhUserBenefitRouter) InitJyhUserBenefitRouter(Router *gin.RouterGroup) {
	userBenefitRouter := Router.Group("admin/user_benefit")
	userBenefitRouterWithoutMiddleware := Router.Group("admin/user_benefit")
	userBenefitApi := v1.ApiGroupApp.JyhApiGroup.JyhUserBenefitApi

	{
		// ===== 操作记录路由（需要记录操作日志的接口） =====
		userBenefitRouter.Use(middleware.OperationRecord())

		// 会员等级管理（主要管理界面，包含权益配置）
		userBenefitRouter.POST("levels", userBenefitApi.CreateUserShipLevel)   // 创建会员等级（包含权益配置）
		userBenefitRouter.PUT("levels", userBenefitApi.UpdateUserShipLevel)    // 更新会员等级（包含权益配置）
		userBenefitRouter.DELETE("levels", userBenefitApi.DeleteUserShipLevel) // 删除会员等级

		// 用户等级记录管理（用户开通等级）
		userBenefitRouter.PUT("user_levels/:id", userBenefitApi.UpdateUserLevel)    // 更新用户等级记录
		userBenefitRouter.DELETE("user_levels/:id", userBenefitApi.DeleteUserLevel) // 删除用户等级记录
		userBenefitRouter.POST("audit", userBenefitApi.AuditUserLevel)              // 审核用户等级记录
	}

	{
		// ===== 查询路由（只读操作，不需要操作记录） =====

		// 权益类型查询（预设数据，主要用于等级配置时选择）
		userBenefitRouterWithoutMiddleware.GET("benefits", userBenefitApi.GetBenefitList)

		// 会员等级查询
		userBenefitRouterWithoutMiddleware.GET("levels", userBenefitApi.GetAdminUserShipLevelList)  // 获取会员等级列表（管理端）
		userBenefitRouterWithoutMiddleware.GET("levels/:id", userBenefitApi.GetUserShipLevelDetail) // 获取会员等级详情（包含权益配置）

		// 用户等级记录查询
		userBenefitRouterWithoutMiddleware.GET("user_levels", userBenefitApi.GetUserLevelList) // 获取用户等级记录列表（支持状态筛选，包含审核功能）

		// 用户权益快照查询（主要用于查询用户的权益配置）
		userBenefitRouterWithoutMiddleware.GET("user_benefit_snapshots", userBenefitApi.GetUserBenefitSnapshotList)       // 获取权益快照列表
		userBenefitRouterWithoutMiddleware.GET("users/:userId/benefit_snapshots", userBenefitApi.GetUserBenefitSnapshots) // 查询指定用户的权益快照
	}
}
