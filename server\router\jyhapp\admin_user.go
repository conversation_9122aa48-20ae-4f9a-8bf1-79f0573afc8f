package jyhapp

import (
	v1 "github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type AdminUserRouter struct{}

func (r *AdminUserRouter) InitAdminUserRouter(PrivateGroup *gin.RouterGroup) {
	adminUserRecord := PrivateGroup.Group("admin").Use(middleware.OperationRecord())
	adminUserRouter := PrivateGroup.Group("admin")
	adminUserApi := v1.ApiGroupApp.JyhApiGroup.AdminUserApi
	{
		adminUserRecord.POST("/user/create", adminUserApi.CreateUser)               // 创建用户
		adminUserRecord.PUT("/user/update", adminUserApi.UpdateUser)                // 更新用户
		adminUserRecord.POST("/user/toggle_status", adminUserApi.ToggleUserStatus)  // 切换用户状态
		adminUserRecord.POST("/certificate/review", adminUserApi.ReviewCertificate) // 审核用户粉丝凭证
	}
	{
		adminUserRouter.GET("/user/detail", adminUserApi.GetUserDetail)               // 获取用户详情
		adminUserRouter.GET("/user/list", adminUserApi.GetUserList)                   // 获取用户列表
		adminUserRouter.GET("/certificate/detail", adminUserApi.GetCertificateDetail) // 获取粉丝凭证详情
		adminUserRouter.GET("/certificate/list", adminUserApi.GetAllCertificates)     // 获取所有粉丝凭证列表
	}
}
