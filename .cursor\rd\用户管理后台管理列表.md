### 用户管理功能-后台

> 对客户端用户管理的功能，后台管理员可以对用户进行查询、新增、编辑和删除操作。
> 管理员可以查看用户基本信息，设置用户状态，管理代理用户。

1. 用户模型：server\model\jyhapp\jyh_user.go JyhUser模型

#### 用户管理列表

1. 功能描述
   - 分页展示所有用户信息
   - 支持多条件筛选和排序
   - 显示用户基本信息和状态

2. 接口设计
   - 请求方式：GET
   - 路径：/admin/user/list
   - 权限：需要管理员权限

3. 请求参数
   ```json
   {
     "page": 1,                   // 页码
     "pageSize": 10,              // 每页条数
     "keyword": "",               // 搜索关键词（用户名/手机号）
     "status": null,              // 用户状态 (1-启用 0-禁用)
     "userType": null,            // 用户类型
     "isAgent": null,             // 是否代理
     "startTime": "",             // 注册开始时间
     "endTime": ""                // 注册结束时间
   }
   ```

4. 响应数据
   ```json
   {
     "code": 0,
     "data": {
       "total": 100,              // 总记录数
       "list": [
         {
           "id": 1,               // 用户ID
           "username": "138****8888", // 用户名
           "phone": "138****8888", // 手机号(脱敏)
           "inviteCode": "123456", // 邀请码
           "invitedBy": 0,        // 邀请人ID
           "inviterName": "",     // 邀请人用户名
           "isAgent": false,      // 是否代理
           "status": 1,           // 状态
           "userType": 1,         // 用户类型
           "createdAt": "2023-01-01 12:00:00" // 创建时间
         }
       ],
       "page": 1,
       "pageSize": 10
     },
     "msg": "获取成功"
   }
   ```

#### 用户管理新增-代理用户-代注册

1. 功能描述
   - 管理员可以创建新用户
   - 可以直接设置为代理用户
   - 无需验证码验证
   - 系统自动生成邀请码

2. 接口设计
   - 请求方式：POST
   - 路径：/admin/user/create
   - 权限：需要管理员权限

3. 请求参数
   ```json
   {
     "phone": "13800138000",     // 手机号
     "username": "测试用户",      // 用户名(可选，默认使用脱敏手机号)
     "inviteCode": "123456",     // 邀请码(可选，代表邀请人邀请码)
     "isAgent": true,            // 是否设为代理
     "status": 1,                // 状态(1-启用 0-禁用)
     "userType": 1               // 用户类型
   }
   ```

4. 响应数据
   ```json
   {
     "code": 0,
     "data": {
       "id": 1,                  // 新创建的用户ID
       "inviteCode": "789012"    // 系统生成的邀请码
     },
     "msg": "创建成功"
   }
   ```

#### 用户管理编辑

#### 用户管理查询