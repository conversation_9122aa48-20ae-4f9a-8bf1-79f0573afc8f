// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
)

func newJyhUserShipLevel(db *gorm.DB, opts ...gen.DOOption) jyhUserShipLevel {
	_jyhUserShipLevel := jyhUserShipLevel{}

	_jyhUserShipLevel.jyhUserShipLevelDo.UseDB(db, opts...)
	_jyhUserShipLevel.jyhUserShipLevelDo.UseModel(&jyhapp.JyhUserShipLevel{})

	tableName := _jyhUserShipLevel.jyhUserShipLevelDo.TableName()
	_jyhUserShipLevel.ALL = field.NewAsterisk(tableName)
	_jyhUserShipLevel.ID = field.NewUint(tableName, "id")
	_jyhUserShipLevel.CreatedAt = field.NewTime(tableName, "created_at")
	_jyhUserShipLevel.UpdatedAt = field.NewTime(tableName, "updated_at")
	_jyhUserShipLevel.DeletedAt = field.NewField(tableName, "deleted_at")
	_jyhUserShipLevel.Name = field.NewString(tableName, "name")
	_jyhUserShipLevel.Code = field.NewString(tableName, "code")
	_jyhUserShipLevel.Icon = field.NewString(tableName, "icon")
	_jyhUserShipLevel.PriceCents = field.NewUint64(tableName, "price_cents")
	_jyhUserShipLevel.DurationDays = field.NewUint(tableName, "duration_days")
	_jyhUserShipLevel.Sort = field.NewInt(tableName, "sort")
	_jyhUserShipLevel.Description = field.NewString(tableName, "description")
	_jyhUserShipLevel.Hide = field.NewBool(tableName, "hide")
	_jyhUserShipLevel.CommissionRate = field.NewField(tableName, "commission_rate")
	_jyhUserShipLevel.PlatformCommissionRate = field.NewField(tableName, "platform_commission_rate")
	_jyhUserShipLevel.Benefits = jyhUserShipLevelHasManyBenefits{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Benefits", "jyhapp.JyhUserShipLevelBenefit"),
	}

	_jyhUserShipLevel.fillFieldMap()

	return _jyhUserShipLevel
}

type jyhUserShipLevel struct {
	jyhUserShipLevelDo

	ALL                    field.Asterisk
	ID                     field.Uint
	CreatedAt              field.Time
	UpdatedAt              field.Time
	DeletedAt              field.Field
	Name                   field.String
	Code                   field.String
	Icon                   field.String
	PriceCents             field.Uint64
	DurationDays           field.Uint
	Sort                   field.Int
	Description            field.String
	Hide                   field.Bool
	CommissionRate         field.Field
	PlatformCommissionRate field.Field
	Benefits               jyhUserShipLevelHasManyBenefits

	fieldMap map[string]field.Expr
}

func (j jyhUserShipLevel) Table(newTableName string) *jyhUserShipLevel {
	j.jyhUserShipLevelDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j jyhUserShipLevel) As(alias string) *jyhUserShipLevel {
	j.jyhUserShipLevelDo.DO = *(j.jyhUserShipLevelDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *jyhUserShipLevel) updateTableName(table string) *jyhUserShipLevel {
	j.ALL = field.NewAsterisk(table)
	j.ID = field.NewUint(table, "id")
	j.CreatedAt = field.NewTime(table, "created_at")
	j.UpdatedAt = field.NewTime(table, "updated_at")
	j.DeletedAt = field.NewField(table, "deleted_at")
	j.Name = field.NewString(table, "name")
	j.Code = field.NewString(table, "code")
	j.Icon = field.NewString(table, "icon")
	j.PriceCents = field.NewUint64(table, "price_cents")
	j.DurationDays = field.NewUint(table, "duration_days")
	j.Sort = field.NewInt(table, "sort")
	j.Description = field.NewString(table, "description")
	j.Hide = field.NewBool(table, "hide")
	j.CommissionRate = field.NewField(table, "commission_rate")
	j.PlatformCommissionRate = field.NewField(table, "platform_commission_rate")

	j.fillFieldMap()

	return j
}

func (j *jyhUserShipLevel) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *jyhUserShipLevel) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 15)
	j.fieldMap["id"] = j.ID
	j.fieldMap["created_at"] = j.CreatedAt
	j.fieldMap["updated_at"] = j.UpdatedAt
	j.fieldMap["deleted_at"] = j.DeletedAt
	j.fieldMap["name"] = j.Name
	j.fieldMap["code"] = j.Code
	j.fieldMap["icon"] = j.Icon
	j.fieldMap["price_cents"] = j.PriceCents
	j.fieldMap["duration_days"] = j.DurationDays
	j.fieldMap["sort"] = j.Sort
	j.fieldMap["description"] = j.Description
	j.fieldMap["hide"] = j.Hide
	j.fieldMap["commission_rate"] = j.CommissionRate
	j.fieldMap["platform_commission_rate"] = j.PlatformCommissionRate

}

func (j jyhUserShipLevel) clone(db *gorm.DB) jyhUserShipLevel {
	j.jyhUserShipLevelDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j jyhUserShipLevel) replaceDB(db *gorm.DB) jyhUserShipLevel {
	j.jyhUserShipLevelDo.ReplaceDB(db)
	return j
}

type jyhUserShipLevelHasManyBenefits struct {
	db *gorm.DB

	field.RelationField
}

func (a jyhUserShipLevelHasManyBenefits) Where(conds ...field.Expr) *jyhUserShipLevelHasManyBenefits {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a jyhUserShipLevelHasManyBenefits) WithContext(ctx context.Context) *jyhUserShipLevelHasManyBenefits {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a jyhUserShipLevelHasManyBenefits) Session(session *gorm.Session) *jyhUserShipLevelHasManyBenefits {
	a.db = a.db.Session(session)
	return &a
}

func (a jyhUserShipLevelHasManyBenefits) Model(m *jyhapp.JyhUserShipLevel) *jyhUserShipLevelHasManyBenefitsTx {
	return &jyhUserShipLevelHasManyBenefitsTx{a.db.Model(m).Association(a.Name())}
}

type jyhUserShipLevelHasManyBenefitsTx struct{ tx *gorm.Association }

func (a jyhUserShipLevelHasManyBenefitsTx) Find() (result []*jyhapp.JyhUserShipLevelBenefit, err error) {
	return result, a.tx.Find(&result)
}

func (a jyhUserShipLevelHasManyBenefitsTx) Append(values ...*jyhapp.JyhUserShipLevelBenefit) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a jyhUserShipLevelHasManyBenefitsTx) Replace(values ...*jyhapp.JyhUserShipLevelBenefit) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a jyhUserShipLevelHasManyBenefitsTx) Delete(values ...*jyhapp.JyhUserShipLevelBenefit) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a jyhUserShipLevelHasManyBenefitsTx) Clear() error {
	return a.tx.Clear()
}

func (a jyhUserShipLevelHasManyBenefitsTx) Count() int64 {
	return a.tx.Count()
}

type jyhUserShipLevelDo struct{ gen.DO }

func (j jyhUserShipLevelDo) Debug() *jyhUserShipLevelDo {
	return j.withDO(j.DO.Debug())
}

func (j jyhUserShipLevelDo) WithContext(ctx context.Context) *jyhUserShipLevelDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jyhUserShipLevelDo) ReadDB() *jyhUserShipLevelDo {
	return j.Clauses(dbresolver.Read)
}

func (j jyhUserShipLevelDo) WriteDB() *jyhUserShipLevelDo {
	return j.Clauses(dbresolver.Write)
}

func (j jyhUserShipLevelDo) Session(config *gorm.Session) *jyhUserShipLevelDo {
	return j.withDO(j.DO.Session(config))
}

func (j jyhUserShipLevelDo) Clauses(conds ...clause.Expression) *jyhUserShipLevelDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jyhUserShipLevelDo) Returning(value interface{}, columns ...string) *jyhUserShipLevelDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jyhUserShipLevelDo) Not(conds ...gen.Condition) *jyhUserShipLevelDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jyhUserShipLevelDo) Or(conds ...gen.Condition) *jyhUserShipLevelDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jyhUserShipLevelDo) Select(conds ...field.Expr) *jyhUserShipLevelDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jyhUserShipLevelDo) Where(conds ...gen.Condition) *jyhUserShipLevelDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jyhUserShipLevelDo) Order(conds ...field.Expr) *jyhUserShipLevelDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jyhUserShipLevelDo) Distinct(cols ...field.Expr) *jyhUserShipLevelDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jyhUserShipLevelDo) Omit(cols ...field.Expr) *jyhUserShipLevelDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jyhUserShipLevelDo) Join(table schema.Tabler, on ...field.Expr) *jyhUserShipLevelDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jyhUserShipLevelDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jyhUserShipLevelDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jyhUserShipLevelDo) RightJoin(table schema.Tabler, on ...field.Expr) *jyhUserShipLevelDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jyhUserShipLevelDo) Group(cols ...field.Expr) *jyhUserShipLevelDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jyhUserShipLevelDo) Having(conds ...gen.Condition) *jyhUserShipLevelDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jyhUserShipLevelDo) Limit(limit int) *jyhUserShipLevelDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jyhUserShipLevelDo) Offset(offset int) *jyhUserShipLevelDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jyhUserShipLevelDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jyhUserShipLevelDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jyhUserShipLevelDo) Unscoped() *jyhUserShipLevelDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jyhUserShipLevelDo) Create(values ...*jyhapp.JyhUserShipLevel) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jyhUserShipLevelDo) CreateInBatches(values []*jyhapp.JyhUserShipLevel, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jyhUserShipLevelDo) Save(values ...*jyhapp.JyhUserShipLevel) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jyhUserShipLevelDo) First() (*jyhapp.JyhUserShipLevel, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserShipLevel), nil
	}
}

func (j jyhUserShipLevelDo) Take() (*jyhapp.JyhUserShipLevel, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserShipLevel), nil
	}
}

func (j jyhUserShipLevelDo) Last() (*jyhapp.JyhUserShipLevel, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserShipLevel), nil
	}
}

func (j jyhUserShipLevelDo) Find() ([]*jyhapp.JyhUserShipLevel, error) {
	result, err := j.DO.Find()
	return result.([]*jyhapp.JyhUserShipLevel), err
}

func (j jyhUserShipLevelDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*jyhapp.JyhUserShipLevel, err error) {
	buf := make([]*jyhapp.JyhUserShipLevel, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jyhUserShipLevelDo) FindInBatches(result *[]*jyhapp.JyhUserShipLevel, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jyhUserShipLevelDo) Attrs(attrs ...field.AssignExpr) *jyhUserShipLevelDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jyhUserShipLevelDo) Assign(attrs ...field.AssignExpr) *jyhUserShipLevelDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jyhUserShipLevelDo) Joins(fields ...field.RelationField) *jyhUserShipLevelDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jyhUserShipLevelDo) Preload(fields ...field.RelationField) *jyhUserShipLevelDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jyhUserShipLevelDo) FirstOrInit() (*jyhapp.JyhUserShipLevel, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserShipLevel), nil
	}
}

func (j jyhUserShipLevelDo) FirstOrCreate() (*jyhapp.JyhUserShipLevel, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserShipLevel), nil
	}
}

func (j jyhUserShipLevelDo) FindByPage(offset int, limit int) (result []*jyhapp.JyhUserShipLevel, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jyhUserShipLevelDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jyhUserShipLevelDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jyhUserShipLevelDo) Delete(models ...*jyhapp.JyhUserShipLevel) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jyhUserShipLevelDo) withDO(do gen.Dao) *jyhUserShipLevelDo {
	j.DO = *do.(*gen.DO)
	return j
}
