package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
)

// MaterialSearch 素材分类查询
type MaterialSearch struct {
	request.PageInfo
	Name       string `json:"name" form:"name"`             // 素材名称
	CategoryID uint   `json:"categoryId" form:"categoryId"` // 分类ID
	Status     *uint  `json:"status" form:"status"`         // 状态
	IsSend     *bool  `json:"isSend" form:"isSend"`         // 是否已下发
	Type       string `json:"type" form:"type"`             // 素材类型 // 'stream','video','image'
}

// MaterialCreate 素材创建
type MaterialCreate struct {
	Name             string         `json:"name" binding:"required"`        // 素材名称
	CategoryID       uint           `json:"categoryId" binding:"required"`  // 分类ID
	Copywriting      string         `json:"copywriting" binding:"required"` // 文案
	DouyinProductUrl string         `json:"douyinProductUrl"`               // 抖音商品链接
	MusicUrl         string         `json:"musicUrl"`                       // 背景音乐链接
	Description      string         `json:"description"`                    // 描述
	FileUrl          string         `json:"fileUrl" binding:"required"`     // 素材首图URL
	Status           uint           `json:"status"`                         // 状态
	TagIds           []uint         `json:"tagIds"`                         // 标签ID列表
	Files            []MaterialFile `json:"files"`                          // 素材文件列表
}

// MaterialUpdate 素材修改
type MaterialUpdate struct {
	ID               uint           `json:"id" binding:"required"`         // ID
	Name             string         `json:"name" binding:"required"`       // 素材名称
	CategoryID       uint           `json:"categoryId" binding:"required"` // 分类ID
	Copywriting      string         `json:"copywriting"`                   // 文案
	DouyinProductUrl string         `json:"douyinProductUrl"`              // 抖音商品链接
	MusicUrl         string         `json:"musicUrl"`                      // 背景音乐链接
	Description      string         `json:"description"`                   // 描述
	FileUrl          string         `json:"fileUrl" binding:"required"`    // 素材首图URL
	Status           uint           `json:"status"`                        // 状态
	TagIds           []uint         `json:"tagIds"`                        // 标签ID列表
	Files            []MaterialFile `json:"files"`                         // 素材文件列表
}

type MaterialFilterReq struct {
	request.PageInfo
	UserID     uint   `json:"userID" form:"userID"` // 用户ID
	CategoryID uint   `json:"categoryID" form:"categoryID"`
	TagIDs     []uint `json:"tagIDs" form:"tagIDs"`     // 标签ID列表
	Page       int    `json:"page" form:"page"`         // 页码
	PageSize   int    `json:"pageSize" form:"pageSize"` // 每页数量
}

type MaterialFile struct {
	FileUrl  string `json:"fileUrl" binding:"required"`  // 素材文件URL
	FileType string `json:"fileType" binding:"required"` // 文件类型（'image','video','music','document'）
	FileName string `json:"fileName" binding:"required"` // 文件名称
}

// MaterialClaim 素材领取
type MaterialClaim struct {
	UserID     uint `json:"userId" binding:"required"`     // 用户ID
	MaterialID uint `json:"materialId" binding:"required"` // 素材ID
}

// MaterialSendToUser 发送素材给用户
type MaterialSendToUser struct {
	UserIDs     []uint `json:"userIds" binding:"required"`     // 用户ID
	MaterialIDs []uint `json:"materialIds" binding:"required"` // 素材ID
	Type        int    `json:"type"`                           // 发送类型 0-普通素材， 1-自定义素材
}

// UserMaterialSearch 获取用户素材
type UserMaterialSearch struct {
	request.PageInfo
	UserID     uint   `json:"userId" form:"userId"`         // 用户ID
	CategoryID uint   `json:"categoryId" form:"categoryId"` // 分类ID
	TagIDs     []uint `json:"tagIds" from:"tagIds"`         // 标签ID列表
}

// CustomMaterialReq 自定义素材请求
type CustomMaterialReq struct {
	UserID      uint   `json:"userId" binding:"required"`      // 用户ID
	MaterialUrl string `json:"materialUrl" binding:"required"` // 素材URL
}

// CustomMaterialSearch 自定义素材查询
type CustomMaterialSearch struct {
	request.PageInfo
	UserID uint `json:"userId" form:"userId"` // 用户ID
}

// UserMaterialSendRecordSearch 获取发放记录
type UserMaterialSendRecordSearch struct {
	request.PageInfo
	UserID       uint   `json:"userId" form:"userId"`             // 用户ID
	CategoryID   uint   `json:"categoryId" form:"categoryId"`     // 分类ID
	MaterialName string `json:"materialName" form:"materialName"` // 素材名称
	TagIDs       []uint `json:"tagIds" from:"tagIds"`             // 标签ID列表
}

// MaterialUploadAndRecognize 素材上传和识别
type MaterialUploadAndRecognize struct {
	MaterialUserID uint   `json:"materialUserId" binding:"required"` // 素材用户ID
	FileUrl        string `json:"fileUrl" binding:"required"`        // 上传的抖音URL
	UserID         uint   `json:"userId"`                            // 用户ID
}

// MaterialRuleConfigSearch 素材规则配置
type MaterialRuleConfigSearch struct {
	request.PageInfo
}

// MaterialRuleConfigCreate 素材规则配置
type MaterialRuleConfigCreate struct {
	MinFans       int    `json:"minFans" binding:"required"`       // 最小粉丝数
	MaxFans       int    `json:"maxFans" binding:"required"`       // 最大粉丝数
	MinValidFans  int    `json:"minValidFans" binding:"required"`  // 最小有效粉丝数
	MaxValidFans  int    `json:"maxValidFans" binding:"required"`  // 最大有效粉丝数
	FrequencyType string `json:"frequencyType" binding:"required"` // 领取频率类型 'daily','weekly'
	SelectionType string `json:"selectionType" binding:"required"` // 选择类型 'fixed','flexible', // 固定或组合
	MaterialCount int    `json:"materialCount"`                    // 素材总数量（每天/每周）
	StreamCount   int    `json:"streamCount"`                      // 拉流素材数量
	VideoCount    int    `json:"videoCount" `                      // 视频素材数量
	ImageCount    int    `json:"imageCount"`                       // 图文素材数量
	Remark        string `json:"remark"`                           // 备注说明
}

// MaterialRuleConfigUpdate 素材规则配置
type MaterialRuleConfigUpdate struct {
	ID            uint   `json:"id" binding:"required"`            // 主键ID
	MinFans       int    `json:"minFans" binding:"required"`       // 最小粉丝数
	MaxFans       int    `json:"maxFans" binding:"required"`       // 最大粉丝数
	MinValidFans  int    `json:"minValidFans" binding:"required"`  // 最小有效粉丝数
	MaxValidFans  int    `json:"maxValidFans" binding:"required"`  // 最大有效粉丝数
	FrequencyType string `json:"frequencyType" binding:"required"` // 领取频率类型 'daily','weekly'
	SelectionType string `json:"selectionType" binding:"required"` // 选择类型 'fixed','flexible', // 固定或组合
	MaterialCount int    `json:"materialCount"`                    // 素材总数量（每天/每周）
	StreamCount   int    `json:"streamCount"`                      // 拉流素材数量
	VideoCount    int    `json:"videoCount" `                      // 视频素材数量
	ImageCount    int    `json:"imageCount"`                       // 图文素材数量
	Remark        string `json:"remark"`                           // 备注说明
}
