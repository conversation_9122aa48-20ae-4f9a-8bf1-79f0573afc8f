package jyhapp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type UserContractApi struct{}

// SendContractVerificationCode 发送签约验证码
// @Tags     UserContract
// @Summary  发送签约验证码
// @Security ApiKeyAuth
// @accept   application/json
// @Produce  application/json
// @Success  200  {object}  response.Response{msg=string}            "发送签约验证码"
// @Router   /jyh/contract/send_code [post]
func (m *UserContractApi) SendContractVerificationCode(c *gin.Context) {
	uid := utils.GetUserID(c)
	err := userContractService.SendContractVerificationCode(uid)
	if err != nil {
		global.GVA_LOG.Error("发送签约验证码失败!", zap.Error(err))
		response.FailWithMessage("发送签约验证码失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("发送签约验证码成功", c)
}

// VerifyContractCode 验证签约验证码
// @Tags     UserContract
// @Summary  验证签约验证码
// @Security ApiKeyAuth
// @accept   application/json
// @Produce  application/json
// @Param    data body      request.VerifyContractCodeReq            true  "验证签约验证码请求"
// @Success  200  {object}  response.Response{data=response.VerifyContractCodeResp,msg=string}  "验证签约验证码"
// @Router   /jyh/contract/verify_code [post]
func (m *UserContractApi) VerifyContractCode(c *gin.Context) {
	var req request.VerifyContractCodeReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	uid := utils.GetUserID(c)
	result, err := userContractService.VerifyContractCode(uid, req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithData(result, c)
}

// GetUserInfoByTicket 根据ticket获取用户信息
// @Tags     UserContract
// @Summary  根据ticket获取用户信息
// @Security ApiKeyAuth
// @accept   application/json
// @Produce  application/json
// @Param    data body      request.GetUserInfoByTicketReq            true  "根据ticket获取用户信息请求"
// @Success  200  {object}  response.Response{data=response.MCNUserInfo,msg=string}  "根据ticket获取用户信息"
// @Router   /jyh/contract/user_info [post]
func (m *UserContractApi) GetUserInfoByTicket(c *gin.Context) {
	var req request.GetUserInfoByTicketReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	uid := utils.GetUserID(c)
	result, err := userContractService.GetUserInfoByTicket(uid, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithData(result, c)
}

// AddAccountBind 绑定用户账号到机构
// @Tags     UserContract
// @Summary  绑定用户账号到机构
// @Security ApiKeyAuth
// @accept   application/json
// @Produce  application/json
// @Param    data body      request.AddAccountBindReq            true  "绑定用户账号到机构请求"
// @Success  200  {object}  response.Response{data=response.AddAccountBindResp,msg=string}  "绑定用户账号到机构"
// @Router   /jyh/contract/account_bind [post]
func (m *UserContractApi) AddAccountBind(c *gin.Context) {
	var req request.AddAccountBindReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	uid := utils.GetUserID(c)
	result, err := userContractService.AddAccountBind(uid, &req)
	if err != nil {
		global.GVA_LOG.Error("绑定用户账号到机构失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithData(result, c)
}

// AddUserBindPid 绑定结算账号
// @Tags     UserContract
// @Summary  绑定结算账号
// @Security ApiKeyAuth
// @accept   application/json
// @Produce  application/json
// @Param    data body      request.AddUserBindPidReq            true  "绑定结算账号请求"
// @Success  200  {object}  response.Response{data=response.AddUserBindPidResp,msg=string}  "绑定结算账号"
// @Router   /jyh/contract/bind_pid [post]
func (m *UserContractApi) AddUserBindPid(c *gin.Context) {
	var req request.AddUserBindPidReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	result, err := userContractService.AddUserBindPid(&req)
	if err != nil {
		global.GVA_LOG.Error("绑定结算账号失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithData(result, c)
}
