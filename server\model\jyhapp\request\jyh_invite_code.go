package request

import "github.com/flipped-aurora/gin-vue-admin/server/model/common/request"

// CreateInviteCodeReq 创建邀请码请求
type CreateInviteCodeReq struct {
	Type         string `json:"type" binding:"required"`     // 邀请码类型：user-用户生成 platform-平台生成
	UserID       *uint  `json:"user_id"`                     // 生成邀请码的用户ID（平台生成时为空）
	LevelID      uint   `json:"level_id" binding:"required"` // 会员等级ID
	SalePrice    uint64 `json:"sale_price"`                  // 出售价格(单位分)
	ValidityDays uint   `json:"validity_days"`               // 有效期（天）
}

// BatchCreateInviteCodeReq 批量创建邀请码请求
type BatchCreateInviteCodeReq struct {
	Type         string `json:"type" binding:"required"`                    // 邀请码类型：user-用户生成 platform-平台生成
	UserID       *uint  `json:"user_id"`                                    // 生成邀请码的用户ID（平台生成时为空）
	LevelID      uint   `json:"level_id" binding:"required"`                // 会员等级ID
	SalePrice    uint64 `json:"sale_price"`                                 // 出售价格(单位分)
	ValidityDays uint   `json:"validity_days"`                              // 有效期（天）
	Quantity     uint   `json:"quantity" binding:"required,min=1,max=1000"` // 创建数量
}

// InviteCodeListReq 邀请码列表请求
type InviteCodeListReq struct {
	request.PageInfo
	Type      string `json:"type" form:"type"`             // 邀请码类型
	UserID    *uint  `json:"user_id" form:"user_id"`       // 生成用户ID
	LevelID   *uint  `json:"level_id" form:"level_id"`     // 等级ID
	Status    *int   `json:"status" form:"status"`         // 状态
	IsUsed    *bool  `json:"is_used" form:"is_used"`       // 是否已使用
	Code      string `json:"code" form:"code"`             // 邀请码（模糊搜索）
	StartTime string `json:"start_time" form:"start_time"` // 开始时间
	EndTime   string `json:"end_time" form:"end_time"`     // 结束时间
}

// UpdateInviteCodeStatusReq 更新邀请码状态请求
type UpdateInviteCodeStatusReq struct {
	ID     uint `json:"id" binding:"required"`     // 邀请码ID
	Status int  `json:"status" binding:"required"` // 状态：0 未使用 1 已使用 2 已过期
}

// TransferInviteCodeReq 转让邀请码请求
type TransferInviteCodeReq struct {
	InviteCodeID uint   `json:"invite_code_id" binding:"required"` // 邀请码ID
	ToUserID     uint   `json:"to_user_id" binding:"required"`     // 新持有用户ID
	Reason       string `json:"reason"`                            // 转让原因（可选）
}
