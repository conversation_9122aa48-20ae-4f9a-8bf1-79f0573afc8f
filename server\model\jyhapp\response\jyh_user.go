package response

import "github.com/shopspring/decimal"

type JyhUserResp struct {
	JyhUserInfo
	Token     string `json:"token"`      //token
	ExpiresAt int64  `json:"expires_at"` //过期时间
}

type JyhUserInfo struct {
	Id                uint            `json:"id"`
	Username          string          `json:"user_name"`           //用户名
	Avatar            string          `json:"avatar"`              //头像
	Status            uint            `json:"status"`              //状态 0  禁用 1启用
	UserType          string          `json:"user_type"`           //用户类型 user admin
	Phone             string          `json:"phone"`               //脱敏
	InvitedBy         uint            `json:"invited_by"`          //邀请人id
	IsAgent           bool            `json:"is_agent"`            //是否是代理
	InviteCode        string          `json:"invite_code"`         //邀请码
	ContractStatus    int             `json:"contract_status"`     //签约状态 0 未签约 1 签约成功
	CanLiveStream     bool            `json:"can_live_stream"`     //是否可以直播
	TotalIncomeAmount decimal.Decimal `json:"total_income_amount"` //总收入金额
	InviteNum         int64           `json:"invite_num"`          //邀请人数
	Ext               *Ext            `json:"ext"`
	// 用户等级信息
	CurrentLevel *UserLevelInfo `json:"current_level"` //当前生效的等级信息

	// 用户扩展信息
	UserExt *UserExtInfo `json:"user_ext,omitempty"` //用户扩展信息
}

// UserLevelInfo 用户等级信息
type UserLevelInfo struct {
	ID           uint   `json:"id"`            // 用户等级记录ID
	LevelID      uint   `json:"level_id"`      // 会员等级ID
	LevelName    string `json:"level_name"`    // 等级名称
	LevelCode    string `json:"level_code"`    // 等级编码
	LevelIcon    string `json:"level_icon"`    // 等级图标
	PriceCents   uint64 `json:"price_cents"`   // 等级价格（分）
	DurationDays uint   `json:"duration_days"` // 有效期天数
	StartAt      string `json:"start_at"`      // 生效时间
	EndAt        string `json:"end_at"`        // 过期时间
	Status       string `json:"status"`        // 状态
}
type Ext struct {
	AppId       int    `json:"app_id"`
	UserId      string `json:"user_id"`
	AwemeId     string `json:"aweme_id"`
	UserName    string `json:"user_name"`
	AvatarImg   string `json:"avatar_img"`
	AuthorLevel string `json:"author_level"`
}

// UserExtInfo 用户扩展信息
type UserExtInfo struct {
	IdCard         string `json:"id_card"`          // 身份证号
	Age            int    `json:"age"`              // 年龄
	Sex            string `json:"sex"`              // 性别
	City           string `json:"city"`             // 城市
	Occupation     string `json:"occupation"`       // 职业
	Address        string `json:"address"`          // 收货地址
	WeChat         string `json:"wechat"`           // 微信号
	WeChatNickname string `json:"wechat_nickname"`  // 微信昵称
	DouYin         string `json:"douyin"`           // 抖音号
	DouYinNickname string `json:"douyin_nickname"`  // 抖音昵称
	DouYinPhone    string `json:"douyin_phone"`     // 抖音绑定手机号
	QQ             string `json:"qq"`               // QQ号
	TrackType      int    `json:"track_type"`       // 测赛道 1 挂车，2 拉流
	IsCarOwner     bool   `json:"is_car_owner"`     // 是否挂车
	CarOwnerTime   string `json:"car_owner_time"`   // 挂车时间
	CarOwnerDay    int    `json:"car_owner_day"`    // 挂车天数
	FansCount      int    `json:"fans_count"`       // 粉丝数
	ValidFansCount int    `json:"valid_fans_count"` // 有效粉丝数（非僵尸粉）
}
