package response

type CourseDetailRes struct {
	ID           int64  `json:"ID"`           // 课程ID
	Title        string `json:"title"`        // 课程标题
	CourseDate   string `json:"courseDate"`   // 课程日期，格式为YYYY-MM-DD
	ViewCount    int    `json:"viewCount"`    // 课程浏览量
	ImageUrl     string `json:"imageUrl"`     // 课程封面图
	VideoUrl     string `json:"videoUrl"`     // 课程视频地址
	Detail       string `json:"detail"`       // 课程详情
	Duration     int    `json:"duration"`     // 课程时长（分钟）
	Teacher      string `json:"teacher"`      // 讲师姓名
	Status       uint   `json:"status"`       // 课程状态
	CategoryId   uint   `json:"categoryId"`   // 课程分类ID
	CategoryName string `json:"categoryName"` // 课程分类名称
	CreatedAt    string `json:"createdAt"`    // 创建时间
}
