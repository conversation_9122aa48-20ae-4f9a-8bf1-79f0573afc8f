package service

import (
	"github.com/flipped-aurora/gin-vue-admin/server/service/example"
	"github.com/flipped-aurora/gin-vue-admin/server/service/jyhapp"
	"github.com/flipped-aurora/gin-vue-admin/server/service/system"
)

type ServiceGroup struct {
	SystemServiceGroup  system.ServiceGroup
	ExampleServiceGroup example.ServiceGroup
	JyhServiceGroup     jyhapp.ServiceGroup
}

var ServiceGroupApp = new(ServiceGroup)

func Init() (err error) {
	if err = ServiceGroupApp.JyhServiceGroup.Init(); err != nil {
		return
	}
	return
}
