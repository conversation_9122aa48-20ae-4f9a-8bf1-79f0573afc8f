# 保利威RSA密钥配置指南

## 概述

本文档介绍如何配置和使用保利威RSA密钥进行安全的认证信息传输。系统支持两种密钥格式：
- **PEM格式**：标准的RSA密钥格式，包含完整的头部和尾部
- **Base64格式**：纯base64编码的密钥内容，更适合配置文件存储

## 配置方式

### 方式一：使用API生成密钥对

#### 1. 生成新的RSA密钥对

```bash
curl -X POST "http://localhost:8888/api/live/generate_rsa_keys" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

响应示例：
```json
{
  "code": 0,
  "data": {
    "privateKeyPEM": "-----BEGIN RSA PRIVATE KEY-----\n...\n-----END RSA PRIVATE KEY-----",
    "publicKeyPEM": "-----BEGIN PUBLIC KEY-----\n...\n-----<PERSON>ND PUBLIC KEY-----",
    "privateKeyBase64": "MIIEpAIBAAKCAQEA...",
    "publicKeyBase64": "MIIBIjANBgkqhkiG9w0BAQEF...",
    "configExample": {
      "private_key": "MIIEpAIBAAKCAQEA...",
      "public_key": "MIIBIjANBgkqhkiG9w0BAQEF..."
    }
  },
  "msg": "操作成功"
}
```

#### 2. 更新配置文件

将返回的`privateKeyBase64`和`publicKeyBase64`复制到配置文件中：

```yaml
# config.yaml
polyv:
   app_id: h7lpbqwt8v
   secret_key: c48fcdbe11ae4da6968258f4e436a240
   private_key: "MIIEpAIBAAKCAQEA..." # 从API响应中复制
   public_key: "MIIBIjANBgkqhkiG9w0BAQEF..." # 从API响应中复制
```

#### 3. 重启服务

配置文件更新后，重启服务使配置生效。

### 方式二：手动生成密钥对

#### 1. 使用OpenSSL生成密钥对

```bash
# 生成私钥
openssl genrsa -out private_key.pem 2048

# 生成公钥
openssl rsa -in private_key.pem -pubout -out public_key.pem

# 转换为base64格式（去掉PEM头尾）
openssl rsa -in private_key.pem -outform DER | base64 -w 0 > private_key.base64
openssl rsa -in private_key.pem -pubout -outform DER | base64 -w 0 > public_key.base64
```

#### 2. 配置到文件

```yaml
# 使用base64格式（推荐）
polyv:
   private_key: "MIIEpAIBAAKCAQEA..." # private_key.base64文件内容
   public_key: "MIIBIjANBgkqhkiG9w0BAQEF..." # public_key.base64文件内容

# 或使用PEM格式（需要保持换行符）
polyv:
   private_key: |
     -----BEGIN RSA PRIVATE KEY-----
     MIIEpAIBAAKCAQEA...
     -----END RSA PRIVATE KEY-----
   public_key: |
     -----BEGIN PUBLIC KEY-----
     MIIBIjANBgkqhkiG9w0BAQEF...
     -----END PUBLIC KEY-----
```

## 验证配置

### 检查当前密钥配置

```bash
curl -X GET "http://localhost:8888/api/live/current_rsa_keys" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

响应示例：
```json
{
  "code": 0,
  "data": {
    "hasKeys": true,
    "privateKeyPEM": "-----BEGIN RSA PRIVATE KEY-----\n...\n-----END RSA PRIVATE KEY-----",
    "publicKeyPEM": "-----BEGIN PUBLIC KEY-----\n...\n-----END PUBLIC KEY-----",
    "privateKeyBase64": "MIIEpAIBAAKCAQEA...",
    "publicKeyBase64": "MIIBIjANBgkqhkiG9w0BAQEF...",
    "configStatus": "已配置"
  },
  "msg": "操作成功"
}
```

### 配置状态说明

- `hasKeys: true` - 密钥配置正确
- `hasKeys: false` - 未配置密钥，系统将自动生成临时密钥对
- `configStatus: "已配置"` - 密钥配置正常
- `configStatus: "密钥格式错误"` - 密钥格式有问题，需要重新配置
- `configStatus: "未配置或自动生成"` - 使用的是系统自动生成的临时密钥

## 使用场景

### 1. 创建直播房间时获取加密认证信息

```bash
curl -X POST "http://localhost:8888/api/live/start_live" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "channelName": "我的直播间",
    "password": "123456"
  }'
```

响应中包含加密的认证信息：
```json
{
  "code": 0,
  "data": {
    "channelId": 123456,
    "channelPasswd": "abcd1234",
    "authInfo": {
      "data": "base64加密的认证信息",
      "publicKey": "RSA公钥PEM格式",
      "timestamp": 1234567890
    }
  }
}
```

### 2. 前端解密认证信息

前端可以使用返回的私钥解密`authInfo.data`，获得保利威的AppId和SecretKey：

```javascript
// 使用node-rsa库解密
const NodeRSA = require('node-rsa');

// 获取私钥（从API获取）
const privateKeyPEM = "-----BEGIN RSA PRIVATE KEY-----\n...\n-----END RSA PRIVATE KEY-----";

// 创建RSA实例
const key = new NodeRSA();
key.importKey(privateKeyPEM, 'pkcs1-private-pem');

// 解密
const decryptedData = key.decrypt(authInfo.data, 'utf8');
const authInfo = JSON.parse(decryptedData);

console.log('AppId:', authInfo.appId);
console.log('SecretKey:', authInfo.secretKey);
```

## 安全建议

### 1. 密钥管理
- 定期更换密钥对（建议每3-6个月）
- 不要在代码库中提交密钥信息
- 使用环境变量或安全的配置管理工具

### 2. 传输安全
- 生产环境必须使用HTTPS
- 密钥传输过程中注意网络安全
- 前端获取的私钥应该安全存储，避免泄露

### 3. 权限控制
- 生成密钥的API应该限制管理员权限
- 定期审计密钥使用情况
- 监控异常的解密请求

## 故障排除

### 1. 密钥格式错误
```
错误信息：解析RSA私钥失败
解决方案：检查base64编码是否正确，确保没有换行符和空格
```

### 2. 解密失败
```
错误信息：RSA解密失败
解决方案：确认使用的是正确的密钥对，检查数据是否被篡改
```

### 3. 配置不生效
```
错误信息：使用自动生成的密钥
解决方案：检查配置文件格式，重启服务，确认配置文件路径正确
```

## 最佳实践

1. **开发环境**：使用API生成临时密钥对，快速测试
2. **测试环境**：使用固定的密钥对，便于测试用例编写
3. **生产环境**：使用安全生成的密钥对，定期轮换
4. **备份**：保存密钥对的副本，避免意外丢失
5. **监控**：记录密钥使用情况，监控异常访问 