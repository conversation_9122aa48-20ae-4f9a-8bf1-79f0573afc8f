package task

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
	jyhappResp "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/mcn"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"strings"
	"time"
)

//@author: <PERSON> z<PERSON>
//@function: MCNLogin
//@description: MCN的登录状态
//@param: redis(数据库对象) redis.UniversalClient
//@return: error

func MCNLogin(redis redis.UniversalClient) error {

	mcnClient := mcn.NewMCNClient()
	if mcnClient == nil {
		return fmt.Errorf("failed to create MCN client")
	}
	// 获取登录状态 @todo
	// 这里假设获取登录状态的函数是 GetLoginStatus
	//loginStatus, err := mcnClient.GetLoginStatus()
	// if err != nil {
	// 	return fmt.Errorf("failed to get login status: %v", err)
	// }
	return nil
}

// MCNSetLogin 设置登录
func MCNSetLogin(redis redis.UniversalClient) error {
	cookies := []string{
		"s_v_web_id=verify_mb68e3vf_lytezbhb_8Ksb_40YP_ANCQ_qGhaq561gsaU",
		"passport_csrf_token=b50b7e218d2caf2739224e3e90cf9030",
		"passport_csrf_token_default=b50b7e218d2caf2739224e3e90cf9030",
		"is_staff_user=false",
		"x-web-secsdk-uid=68f29686-b5e4-4aec-b833-2bb66f30156d",
		"scmVer=1.0.1.8947",
		"csrf_session_id=268139fe15677db578939fa2fd70f52e",
		"store-region=cn-sc",
		"store-region-src=uid",
		"gfkadpd=2631,22740",
		"ttwid=1%7CQfwOv7Z2wb2AMKtAMGz5OXUVmWGqGtmPN4zL_iNWCC4%7C1749004575%7C81a8c03d737985680e60a499e609c0ac17f68ba328f98d470e45db19c0855cc4",
		"odin_tt=153fde3540a1b08f50b90b98ffa0c324f0ce5515f413d2b5b7ce613f58614797c51714b5069ea2da6d567027c280c644dbdc8141bbb1788a784e2356e6967b42",
		"passport_auth_status=c7e48cbd656933d93cf111161683c44f%2C0bad8e685cc49e9b91a768db5eff5d8f",
		"passport_auth_status_ss=c7e48cbd656933d93cf111161683c44f%2C0bad8e685cc49e9b91a768db5eff5d8f",
		"uid_tt=fc7451cb5030a07abb7bc1f6996db898",
		"uid_tt_ss=fc7451cb5030a07abb7bc1f6996db898",
		"sid_tt=46ef08558f2e87964d113a0d2961e59a",
		"sessionid=46ef08558f2e87964d113a0d2961e59a",
		"sessionid_ss=46ef08558f2e87964d113a0d2961e59a",
		"ucas_c0_buyin=CkEKBTEuMC4wELSIh_bqq-yfaBi9LyDMi5CSs826BiiPETCthZCc5MzUBkDh4v7BBkjhlrvEBlC2vMz6gLPXmmhYfRIULI-jtTvNgd6np5uIfwdPA5bD30Y",
		"ucas_c0_ss_buyin=CkEKBTEuMC4wELSIh_bqq-yfaBi9LyDMi5CSs826BiiPETCthZCc5MzUBkDh4v7BBkjhlrvEBlC2vMz6gLPXmmhYfRIULI-jtTvNgd6np5uIfwdPA5bD30Y",
		"sid_guard=46ef08558f2e87964d113a0d2961e59a%7C1749004642%7C5183999%7CSun%2C+03-Aug-2025+02%3A37%3A21+GMT",
		"sid_ucp_v1=1.0.0-KDk1ZjE3MTAzZDA3NGMyMmQ4YWI3NjZiYmNiNzNjNjA3MTAwNzFiYzAKGQithZCc5MzUBhDi4v7BBhiPESAMOAJA8QcaAmxxIiA0NmVmMDg1NThmMmU4Nzk2NGQxMTNhMGQyOTYxZTU5YQ",
		"ssid_ucp_v1=1.0.0-KDk1ZjE3MTAzZDA3NGMyMmQ4YWI3NjZiYmNiNzNjNjA3MTAwNzFiYzAKGQithZCc5MzUBhDi4v7BBhiPESAMOAJA8QcaAmxxIiA0NmVmMDg1NThmMmU4Nzk2NGQxMTNhMGQyOTYxZTU5YQ",
		"SASID=SID2_7511916321426112809",
		"BUYIN_SASID=SID2_7511916321426112809",
		"buyin_shop_type=23",
		"buyin_account_child_type=1",
		"buyin_app_id=26311",
		"buyin_shop_type_v2=23",
		"buyin_account_child_type_v2=1",
		"buyin_app_id_v2=26311",
		"_tea_utm_cache_3813=undefined",
		"passport_mfa_token=CjTI9VpDc7u3VBLRgdDOxZIJnQtMJYExYdaFziW0QO5PqYJdVE0tzVhCAmFciFnP0DhLoAeBGkoKPAAAAAAAAAAAAABPE8TKRmCR9T43btN62oalzs5MGYtaDcUc2yjXcrnqbyqMHS7lh9I7LBCcRosjcXgzuRDQlvMNGPax0WwgAiIBA%2B5und4%3D",
		"d_ticket=33065379de897552254bdbc5a3ea477766c6e",
	}

	csrfToken := "0001000000013da2bd81a32c1852fc24bc00d5022edba49d5668f36b0b0d2381b455401876e01845b91c7f269fe3"

	if redis == nil {
		return fmt.Errorf("redis client is nil")
	}
	// 将 cookies 和 csrfToken 保存到 Redis
	// 保存 cookies
	err := redis.Set(context.Background(), mcn.MCNKEYPREFIX+mcn.MCNCookiesKey, strings.Join(cookies, "; "), 0).Err()
	if err != nil {
		fmt.Println("Error saving cookies to Redis:", err)
	}

	// 保存 x-secsdk-csrf-token
	err = redis.Set(context.Background(), mcn.MCNKEYPREFIX+mcn.MCNXCsrfTokenKey, csrfToken, 0).Err()
	if err != nil {
		fmt.Println("Error saving x-secsdk-csrf-token to Redis:", err)
	}

	fmt.Println("McnLogin: Cookies and csrfToken saved to Redis successfully.")
	return nil
}

// MCNFetchCommission 抓取佣金
func MCNFetchCommission(db *gorm.DB, redis redis.UniversalClient) error {
	mcnClient := mcn.NewMCNClient()
	if mcnClient == nil {
		return fmt.Errorf("failed to create MCN client")
	}

	// 获取当前日期的零点,作为开始时间； 23:59:59作为结束时间
	// 这里需要使用 time 包获取昨天的日期
	// 昨天的日期
	now := time.Now().Local().AddDate(0, 0, -1).Format("2006-01-02")
	startTime := now + "00:00:00" // 将时间格式化为 "YYYY-MM-DD 00:00:00"
	endTime := now + "23:59:59"   // 将时间格式化为 "YYYY-MM-DD 23:59:59"

	page := 1
	pageSize := 30

	for {
		// 获取佣金账单
		commissionBill, err := mcnClient.FetchDailyCommissionBill(startTime, endTime, page, pageSize)
		if err != nil {
			return fmt.Errorf("failed to get commission bill: %v", err)
		}

		fmt.Printf("抓取佣金账单: %s\n", commissionBill)

		// 解析响应
		var result jyhappResp.MCNBillResult
		if err := json.Unmarshal([]byte(commissionBill), &result); err != nil {
			global.GVA_LOG.Error("抓取佣金失败", zap.Error(err))
			return fmt.Errorf("failed to parse commission bill response: %v", err)
		}
		total := result.Data.Total
		totalPage := (total + pageSize - 1) / pageSize
		if result.Code != 0 {
			global.GVA_LOG.Error("抓取佣金失败", zap.String("response", result.Msg))
			return fmt.Errorf("failed to fetch commission bill: %s", result.Msg)
		}
		// 保存数据到佣金表
		if len(result.Data.OrganizationBillList) > 0 {
			var dbCommissionBills []jyhapp.JyhMcnOrderDetail
			for _, bill := range result.Data.OrganizationBillList {
				dbCommissionBills = append(dbCommissionBills, jyhapp.JyhMcnOrderDetail{
					Date:                   bill.Date,
					TotalCount:             bill.TotalCount,
					StartTime:              bill.StartTime,
					EndTime:                bill.EndTime,
					BillId:                 bill.BillId,
					TotalAmount:            bill.TotalAmount,
					TotalServiceFee:        bill.TotalServiceFee,
					TotalSettleAmount:      bill.TotalSettleAmount,
					AuthorFee:              bill.AuthorFee,
					OrganizationFee:        bill.OrganizationFee,
					AuthorUid:              bill.AuthorUid,
					PayType:                bill.PayType,
					TotalServiceFeeReturn:  bill.TotalServiceFeeReturn,
					AuthorFeeReturn:        bill.AuthorFeeReturn,
					OrganizationFeeReturn:  bill.OrganizationFeeReturn,
					AuthorName:             bill.AuthorName,
					UidType:                bill.UidType,
					AppId:                  bill.AppId,
					CommissionAmount:       bill.CommissionAmount,
					SumServiceFee:          bill.SumServiceFee,
					SettleAmount:           bill.SettleAmount,
					SettleAmountReturn:     bill.SettleAmountReturn,
					CommissionAmountReturn: bill.CommissionAmountReturn,
					SumOrganizationFee:     bill.SumOrganizationFee,
					SumAuthorFee:           bill.SumAuthorFee,
					ColonelFee:             bill.ColonelFee,
					AuthorModeCount:        bill.AuthorModeCount,
					ShopModeCount:          bill.ShopModeCount,
					DouCustomerFee:         bill.DouCustomerFee,
					DouCustomerFeeReturn:   bill.DouCustomerFeeReturn,
				})
			}

			// 批量插入佣金账单
			if err := db.CreateInBatches(dbCommissionBills, len(dbCommissionBills)).Error; err != nil {
				global.GVA_LOG.Error("批量插入佣金账单失败", zap.Error(err))
				return fmt.Errorf("failed to save commission bill to database: %v", err)
			}
		}

		if total == 0 {
			break
		}
		if page >= totalPage {
			break
		}
		page++

		global.GVA_LOG.Info("MCN佣金抓取完成", zap.Int("total", len(result.Data.OrganizationBillList)), zap.Int("page", page), zap.Int("pageSize", pageSize))
	}
	return nil
}
