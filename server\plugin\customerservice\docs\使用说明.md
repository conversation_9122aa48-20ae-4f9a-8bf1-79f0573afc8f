# 客服智能分配使用说明

## 快速开始

### 1. 数据库迁移
执行以下SQL脚本创建规则-客服关联表：
```sql
-- 运行 server/plugin/customerservice/migration/create_rule_service_relation.sql
```

### 2. 创建分配规则
通过现有的分发规则管理接口，现在支持同时绑定标签和客服：

```json
POST /api/customerservice/services/distribute_rules
{
  "rule_name": "VIP客户专属服务",
  "priority": 1,
  "max_users": 3,
  "work_time_start": "09:00",
  "work_time_end": "22:00",
  "is_default": false,
  "tag_ids": [1, 2],        // 关联的标签ID
  "service_ids": [10, 11]   // 关联的客服ID（按优先级排序）
}
```

### 3. 更新分配规则
```json
PUT /api/customerservice/services/distribute_rules
{
  "id": 1,
  "rule_name": "VIP客户专属服务",
  "service_ids": [10, 11, 12]  // 更新关联的客服
}
```

### 4. 查看规则详情
```json
GET /api/customerservice/distribute-rules/1

// 返回结果包含：
{
  "rule": { ... },
  "tag_ids": [1, 2],
  "service_ids": [10, 11],
  "service_details": [
    {
      "id": 10,
      "nickname": "高级客服A",
      "priority": 1
    },
    {
      "id": 11,
      "nickname": "高级客服B", 
      "priority": 2
    }
  ]
}
```

### 5. 获取规则列表
规则列表接口现在自动返回关联的标签和客服信息：
```json
GET /api/customerservice/services/distribute_rules

// 返回结果中每个规则包含：
{
  "id": 1,
  "rule_name": "VIP客户专属服务",
  "tags": [
    {"id": 1, "name": "VIP客户"}
  ],
  "services": [
    {"id": 10, "nickname": "高级客服A", "priority": 1},
    {"id": 11, "nickname": "高级客服B", "priority": 2}
  ]
}
```

## 分配逻辑

### 有标签用户
1. 查找匹配标签的规则（按优先级）
2. 优先分配规则关联的客服（按优先级）
3. 如果规则客服不可用，回退到标签匹配
4. 最后使用默认规则

### 无标签用户
1. 使用默认规则关联的客服
2. 如果默认规则无关联客服，使用通用分配

## 辅助接口

### 获取可选客服列表
用于前端配置规则时选择客服：
```json
GET /api/customerservice/services/available
```

## 注意事项

1. **客服优先级**：在 `service_ids` 数组中的顺序决定优先级，第一个为最高优先级
2. **向后兼容**：保持原有标签匹配逻辑作为回退方案
3. **实时生效**：规则修改后立即生效，无需重启服务
4. **负载均衡**：在同优先级客服中，选择当前负载最低的客服

## 示例场景

### VIP客户服务
```json
{
  "rule_name": "VIP客户专属",
  "priority": 1,
  "tag_ids": [1],           // VIP标签
  "service_ids": [10, 11],  // 专属高级客服
  "max_users": 3
}
```

### 技术支持
```json
{
  "rule_name": "技术问题处理",
  "priority": 2,
  "tag_ids": [2, 3],        // 技术问题、产品咨询标签
  "service_ids": [20, 21],  // 技术专员
  "max_users": 5
}
```

### 默认规则
```json
{
  "rule_name": "默认分配",
  "priority": 999,
  "is_default": true,
  "service_ids": [30, 31, 32],  // 通用客服
  "max_users": 10
}
```
