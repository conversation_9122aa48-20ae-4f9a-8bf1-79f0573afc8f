package request

import "time"

// CategoryCreate 文章分类创建请求
type CategoryCreate struct {
	Name        string `json:"name" binding:"required"` // 分类名称
	ParentID    uint   `json:"parentId"`                // 父分类ID
	Sort        int    `json:"sort"`                    // 排序
	IsActive    bool   `json:"isActive"`                // 是否启用
	Description string `json:"description"`             // 描述
}

// CategoryUpdate 文章分类更新请求
type CategoryUpdate struct {
	ID          uint   `json:"id" binding:"required"`   // 分类ID
	Name        string `json:"name" binding:"required"` // 分类名称
	ParentID    uint   `json:"parentId"`                // 父分类ID
	Sort        int    `json:"sort"`                    // 排序
	IsActive    bool   `json:"isActive"`                // 是否启用
	Description string `json:"description"`             // 描述
}

// CategorySearch 文章分类搜索请求
type CategorySearch struct {
	Page     int    `form:"page"`     // 页码
	PageSize int    `form:"pageSize"` // 每页数量
	Name     string `form:"name"`     // 分类名称
	ParentID *uint  `form:"parentId"` // 父分类ID
	IsActive *bool  `form:"isActive"` // 是否启用
}

// ArticleCreate 文章创建请求
type ArticleCreate struct {
	Title       string     `json:"title" binding:"required"`      // 标题
	CategoryID  uint       `json:"categoryId" binding:"required"` // 分类ID
	Author      string     `json:"author"`                        // 作者
	Summary     string     `json:"summary"`                       // 摘要
	Content     string     `json:"content" binding:"required"`    // 内容
	CoverImage  string     `json:"coverImage"`                    // 封面图
	Status      string     `json:"status"`                        // 状态
	IsTop       bool       `json:"isTop"`                         // 是否置顶
	PublishedAt *time.Time `json:"publishedAt"`                   // 发布时间
}

// ArticleUpdate 文章更新请求
type ArticleUpdate struct {
	ID          uint       `json:"id" binding:"required"`         // 文章ID
	Title       string     `json:"title" binding:"required"`      // 标题
	CategoryID  uint       `json:"categoryId" binding:"required"` // 分类ID
	Author      string     `json:"author"`                        // 作者
	Summary     string     `json:"summary"`                       // 摘要
	Content     string     `json:"content" binding:"required"`    // 内容
	CoverImage  string     `json:"coverImage"`                    // 封面图
	Status      string     `json:"status"`                        // 状态
	IsTop       bool       `json:"isTop"`                         // 是否置顶
	PublishedAt *time.Time `json:"publishedAt"`                   // 发布时间
}

// ArticleSearch 文章搜索请求
type ArticleSearch struct {
	Page       int    `form:"page"`       // 页码
	PageSize   int    `form:"pageSize"`   // 每页数量
	CategoryID *uint  `form:"categoryId"` // 分类ID
	Title      string `form:"title"`      // 标题
	Status     string `form:"status"`     // 状态
	IsTop      *bool  `form:"isTop"`      // 是否置顶
	Author     string `form:"author"`     // 作者
}

// SetTopRequest 设置置顶请求
type SetTopRequest struct {
	ID    uint `json:"id" binding:"required"` // 文章ID
	IsTop bool `json:"isTop"`                 // 是否置顶
}
