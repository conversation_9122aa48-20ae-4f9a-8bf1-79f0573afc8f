package response

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
)

// JyhUserLiveRoomResponse 用户直播房间响应
type JyhUserLiveRoomResponse struct {
	ID                      uint       `json:"id"`
	UserID                  uint       `json:"user_id"`
	ChannelId               int64      `json:"channel_id"`
	ChannelName             string     `json:"channel_name"`
	PolyvUserId             string     `json:"polyv_user_id"`
	Scene                   *string    `json:"scene"`
	ChannelPasswd           string     `json:"channel_passwd"`
	SeminarHostPassword     *string    `json:"seminar_host_password"`
	SeminarAttendeePassword *string    `json:"seminar_attendee_password"`
	Status                  string     `json:"status"`
	LastUsedAt              *time.Time `json:"last_used_at"`
	CreatedAt               time.Time  `json:"created_at"`
	UpdatedAt               time.Time  `json:"updated_at"`

	// 关联用户信息
	User *jyhapp.JyhUser `json:"user,omitempty"`
}

// CreateJyhUserLiveRoomResponse 创建用户直播房间响应
type CreateJyhUserLiveRoomResponse struct {
	ID                      uint    `json:"id"`
	ChannelId               int64   `json:"channel_id"`
	ChannelName             string  `json:"channel_name"`
	PolyvUserId             string  `json:"polyv_user_id"`
	Scene                   *string `json:"scene"`
	ChannelPasswd           string  `json:"channel_passwd"`
	SeminarHostPassword     *string `json:"seminar_host_password"`
	SeminarAttendeePassword *string `json:"seminar_attendee_password"`
	Status                  string  `json:"status"`

	AuthInfo *EncryptedAuthInfo `json:"authInfo,omitempty"` // 加密后的保利威认证信息
}

// JyhUserLiveRoomListItem 用户直播房间列表项
type JyhUserLiveRoomListItem struct {
	ID                      uint    `json:"id"`
	UserID                  uint    `json:"user_id"`
	Username                string  `json:"username"`   // 用户名
	UserPhone               string  `json:"user_phone"` // 用户手机号（脱敏）
	ChannelId               int64   `json:"channel_id"`
	ChannelName             string  `json:"channel_name"`
	PolyvUserId             string  `json:"polyv_user_id"`
	Scene                   *string `json:"scene"`
	ChannelPasswd           string  `json:"channel_passwd"`
	SeminarHostPassword     *string `json:"seminar_host_password"`
	SeminarAttendeePassword *string `json:"seminar_attendee_password"`
	Status                  string  `json:"status"`
	LastUsedAt              *string `json:"last_used_at"` // 格式化后的时间字符串
	CreatedAt               string  `json:"created_at"`   // 格式化后的时间字符串
	UpdatedAt               string  `json:"updated_at"`   // 格式化后的时间字符串
}

type EncryptedAuthInfo struct {
	Data       string `json:"data"`        // 加密后的数据（Base64编码）
	PrivateKey string `json:"private_key"` // 公钥（PEM格式）
	Timestamp  int64  `json:"timestamp"`   // 加密时间戳
}
