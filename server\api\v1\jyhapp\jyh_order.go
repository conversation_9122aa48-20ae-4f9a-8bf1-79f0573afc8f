package jyhapp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	jyhReq "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type OrderApi struct{}

// GetOrderStatistics 按天, 按月获取用户的订单
// @Tags Order
// @Summary 按天， 按月获取用户的订单
// @Produce  json
// @Param data body request.OrderStatistics true "按天获取用户的订单"
// @Success   200   {object}  response.Response{data=response.PageResult,msg=string}  "按天获取用户的订单"
// @Router    /order/statistics [get]
func (o *OrderApi) GetOrderStatistics(c *gin.Context) {
	var req jyhReq.OrderStatistics

	req.UserID = utils.GetUserID(c)
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	res, err := OrderService.GetOrderStatistics(req)
	if err != nil {
		global.GVA_LOG.Error("统计用户订单收益失败", zap.Error(err))
		response.FailWithMessage("统计用户订单收益失败: "+err.Error(), c)
		return
	}

	response.OkWithData(res, c)
}

// GetOrderDetails 获取订单佣金明细
// @Tags Order
// @Summary 获取订单佣金明细
// @Produce  json
// @Param data body request.OrderDetail true "获取订单佣金明细"
// @Success   200   {object}  response.Response{data=[]response.JyhOrderDetail,msg=string}  "获取抖音订单明细"
// @Router    /order/fee [get]
func (o *OrderApi) GetOrderDetails(c *gin.Context) {
	var req jyhReq.OrderDetail

	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	list, total, err := OrderService.GetOrderDetails(req)
	if err != nil {
		global.GVA_LOG.Error("获取订单佣金明细失败", zap.Error(err))
		response.FailWithMessage("获取订单佣金明细失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取订单佣金明细成功", c)
}
