kind: pipeline
type: docker
name: build

volumes:
  - name: cachebuild
    host:
      path: /data/drone/cache/douyindev-go-build
  - name: cachemod
    host:
      path: /data/drone/cache/douyindev-go-mod
steps:
  - name: build-dev
    image: hero-registry.cn-beijing.cr.aliyuncs.com/base/douyin-server:1.23
    pull: if-not-exists
    volumes:
      - name: cachebuild
        path: /root/.cache
      - name: cachemod
        path: /go/pkg/mod
    environment:
      GOPROXY: https://proxy.golang.com.cn,direct
      GOPRIVATE: git-ins.dot5.cn
      GO111MODULE: on
      CGO_ENABLED: 1
    when:
      event: [push]
      ref:
        - refs/heads/feature/*
    commands:
      - echo "编译开始"
      - cd server
      #- go env
      - make all_dev
  - name: build-prod
    image: hero-registry.cn-beijing.cr.aliyuncs.com/base/douyin-server:1.23
    pull: if-not-exists
    volumes:
      - name: cachebuild
        path: /root/.cache
      - name: cachemod
        path: /go/pkg/mod
    environment:
      GOPROXY: https://proxy.golang.com.cn,direct
      GOPRIVATE: git-ins.dot5.cn
      GO111MODULE: on
      CGO_ENABLED: 1
    when:
      ref:
        - refs/tags/*
    commands:
      - echo "编译开始"
      - cd server
      - go mod tidy
      - make clean linux update_srv
