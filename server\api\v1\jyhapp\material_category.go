package jyhapp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	commonReq "github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type MaterialCategoryApi struct{}

// Create 创建素材分类
// @Tags      MaterialCategory
// @Summary   创建素材分类
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.MaterialCategoryCreate  true  "创建素材分类"
// @Success   200   {object}  response.Response{msg=string}  "创建素材分类"
// @Router    /materialCategory/create [post]
func (m *MaterialCategoryApi) Create(c *gin.Context) {
	var req request.MaterialCategoryCreate
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = materialCategoryService.Create(req)
	if err != nil {
		global.GVA_LOG.Error("创建素材分类失败!", zap.Error(err))
		response.FailWithMessage("创建素材分类失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建素材分类成功", c)
}

// Update 更新素材分类
// @Tags      MaterialCategory
// @Summary   更新素材分类
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.MaterialCategoryUpdate  true  "更新素材分类"
// @Success   200   {object}  response.Response{msg=string}  "更新素材分类"
// @Router    /materialCategory/update [put]
func (m *MaterialCategoryApi) Update(c *gin.Context) {
	var req request.MaterialCategoryUpdate
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = materialCategoryService.Update(&req)
	if err != nil {
		global.GVA_LOG.Error("更新素材分类失败!", zap.Error(err))
		response.FailWithMessage("更新素材分类失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新素材分类成功", c)
}

// Delete 删除素材分类
// @Tags      MaterialCategory
// @Summary   删除素材分类
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      commonReq.GetById  true  "删除素材分类"
// @Success   200   {object}  response.Response{msg=string}  "删除素材分类"
// @Router    /materialCategory/delete [delete]
func (m *MaterialCategoryApi) Delete(c *gin.Context) {
	var req commonReq.GetById
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = materialCategoryService.Delete(req.Uint())
	if err != nil {
		global.GVA_LOG.Error("删除素材分类失败!", zap.Error(err))
		response.FailWithMessage("删除素材分类失败: "+err.Error(), c)
		return
	}
	response.OkWithMessage("删除素材分类成功", c)
}

// GetDetail 获取素材分类详情
// @Tags      MaterialCategory
// @Summary   获取素材分类详情
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     commonReq.GetById  true  "获取素材分类详情"
// @Success   200   {object}  response.Response{data=jyhapp.JyhMaterialCategory,msg=string}  "获取素材分类详情"
// @Router    /materialCategory/detail [get]
func (m *MaterialCategoryApi) GetDetail(c *gin.Context) {
	var req commonReq.GetById
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	category, err := materialCategoryService.GetDetail(req.Uint())
	if err != nil {
		global.GVA_LOG.Error("获取素材分类失败!", zap.Error(err))
		response.FailWithMessage("获取素材分类失败", c)
		return
	}
	response.OkWithData(category, c)
}

// GetList 获取素材分类列表
// @Tags      MaterialCategory
// @Summary   获取素材分类列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     request.MaterialCategorySearch  true  "获取素材分类列表"
// @Success   200   {object}  response.Response{data=response.PageResult,msg=string}  "获取素材分类列表"
// @Router    /materialCategory/list [get]
func (m *MaterialCategoryApi) GetList(c *gin.Context) {
	var req request.MaterialCategorySearch
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	list, total, err := materialCategoryService.GetList(&req, &req.PageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取素材分类失败!", zap.Error(err))
		response.FailWithMessage("获取素材分类失败", c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取素材分类成功", c)
}

// GetTreeList 获取素材分类树形列表
// @Tags      MaterialCategory
// @Summary   获取素材分类树形列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Success   200   {object}  response.Response{data=response.Response,msg=string}  "获取素材分类树形列表"
// @Router    /materialCategory/treeList [get]
func (m *MaterialCategoryApi) GetTreeList(c *gin.Context) {
	list, err := materialCategoryService.GetTreeList()
	if err != nil {
		global.GVA_LOG.Error("获取素材分类列表失败!", zap.Error(err))
		response.FailWithMessage("获取素材分类列表失败:"+err.Error(), c)
		return
	}
	response.OkWithData(list, c)
}

// GetListByParentId 生成父级分类的获取素材列表
// @Tags      MaterialCategory
// @Summary   生成父级分类的获取素材列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Success   200   {object}  response.Response{data=response.Response,msg=string}  "生成父级分类的获取素材列表"
// @Router    /materialCategory/getListByParentId [get]
func (m *MaterialCategoryApi) GetListByParentId(c *gin.Context) {
	var req request.MaterialCategoryParentId
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, err := materialCategoryService.GetListByParentId(req)
	if err != nil {
		global.GVA_LOG.Error("获取素材分类列表失败!", zap.Error(err))
		response.FailWithMessage("获取素材分类列表失败:"+err.Error(), c)
		return
	}
	response.OkWithData(list, c)
}
