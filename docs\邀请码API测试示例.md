# 邀请码API测试示例

## 测试环境准备

1. 确保数据库已创建相关表
2. 确保会员等级数据已存在
3. 准备管理员账号进行测试

## 测试流程

### 1. 创建平台邀请码

**请求示例**:
```bash
curl -X POST "http://localhost:8080/api/v1/jyh/invite-code/admin" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "platform",
    "level_id": 1,
    "sale_price": 0,
    "validity_days": 30
  }'
```

**预期响应**:
```json
{
  "code": 0,
  "message": "创建邀请码成功"
}
```

### 2. 批量创建邀请码

**请求示例**:
```bash
curl -X POST "http://localhost:8080/api/v1/jyh/invite-code/admin/batch" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "platform",
    "level_id": 1,
    "quantity": 10,
    "validity_days": 30
  }'
```

**预期响应**:
```json
{
  "code": 0,
  "message": "批量创建邀请码成功"
}
```

### 3. 查询邀请码列表

**请求示例**:
```bash
curl -X GET "http://localhost:8080/api/v1/jyh/invite-code/admin/list?page=1&page_size=10&type=platform" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

**预期响应**:
```json
{
  "code": 0,
  "message": "获取邀请码列表成功",
  "data": {
    "list": [
      {
        "id": 1,
        "type": "platform",
        "user_id": null,
        "code": "ABC12345",
        "level_id": 1,
        "level_name": "炬火达人",
        "sale_price": 0,
        "used_at": null,
        "used_by_uid": null,
        "status": 0,
        "is_used": false,
        "expired_at": "2024-02-01T00:00:00Z",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 11,
    "page": 1,
    "page_size": 10
  }
}
```

### 4. 验证邀请码

**请求示例**:
```bash
curl -X GET "http://localhost:8080/api/v1/jyh/invite-code/validate?code=ABC12345" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**预期响应**:
```json
{
  "code": 0,
  "message": "邀请码验证成功",
  "data": {
    "id": 1,
    "type": "platform",
    "code": "ABC12345",
    "level_id": 1,
    "status": 0,
    "is_used": false,
    "expired_at": "2024-02-01T00:00:00Z"
  }
}
```

### 5. 用户注册使用邀请码

**请求示例**:
```bash
curl -X POST "http://localhost:8080/api/v1/jyh/user/register" \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "13800138001",
    "invite_code": "ABC12345",
    "sms_code": "123456"
  }'
```

**预期响应**:
```json
{
  "code": 0,
  "message": "注册成功"
}
```

### 6. 查看邀请码统计

**请求示例**:
```bash
curl -X GET "http://localhost:8080/api/v1/jyh/invite-code/admin/statistics" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

**预期响应**:
```json
{
  "code": 0,
  "message": "获取邀请码统计信息成功",
  "data": {
    "total": 11,
    "used": 1,
    "unused": 10,
    "expired": 0,
    "platform": 11,
    "user": 0
  }
}
```

### 7. 更新邀请码状态

**请求示例**:
```bash
curl -X PUT "http://localhost:8080/api/v1/jyh/invite-code/admin/status" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "id": 2,
    "status": 2
  }'
```

**预期响应**:
```json
{
  "code": 0,
  "message": "更新邀请码状态成功"
}
```

### 8. 删除未使用的邀请码

**请求示例**:
```bash
curl -X DELETE "http://localhost:8080/api/v1/jyh/invite-code/admin/3" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

**预期响应**:
```json
{
  "code": 0,
  "message": "删除邀请码成功"
}
```

## 错误场景测试

### 1. 使用已使用的邀请码注册

**请求示例**:
```bash
curl -X POST "http://localhost:8080/api/v1/jyh/user/register" \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "13800138002",
    "invite_code": "ABC12345",
    "sms_code": "123456"
  }'
```

**预期响应**:
```json
{
  "code": 400,
  "message": "验证邀请码失败: 邀请码已被使用"
}
```

### 2. 使用不存在的邀请码

**请求示例**:
```bash
curl -X POST "http://localhost:8080/api/v1/jyh/user/register" \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "13800138003",
    "invite_code": "INVALID1",
    "sms_code": "123456"
  }'
```

**预期响应**:
```json
{
  "code": 400,
  "message": "验证邀请码失败: 邀请码不存在"
}
```

### 3. 删除已使用的邀请码

**请求示例**:
```bash
curl -X DELETE "http://localhost:8080/api/v1/jyh/invite-code/admin/1" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

**预期响应**:
```json
{
  "code": 400,
  "message": "删除邀请码失败: 已使用的邀请码不能删除"
}
```

## 数据验证

### 1. 验证用户等级分配

注册成功后，查询用户信息验证是否正确分配了等级：

```bash
curl -X GET "http://localhost:8080/api/v1/jyh/user/info" \
  -H "Authorization: Bearer USER_TOKEN"
```

检查响应中的 `current_level` 字段是否正确。

### 2. 验证邀请记录

查询数据库中的 `jyh_invite_record` 表，确认邀请关系记录正确：

```sql
SELECT * FROM jyh_invite_record WHERE code_used = 'ABC12345';
```

### 3. 验证邀请码状态

确认使用后的邀请码状态已更新：

```sql
SELECT * FROM jyh_invite_code WHERE code = 'ABC12345';
-- 应该显示 is_used = true, status = 1, used_at 有值, used_by_uid 有值
```

## 性能测试

### 批量创建性能测试

```bash
# 创建1000个邀请码
curl -X POST "http://localhost:8080/api/v1/jyh/invite-code/admin/batch" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "platform",
    "level_id": 1,
    "quantity": 1000,
    "validity_days": 30
  }'
```

### 列表查询性能测试

```bash
# 分页查询大量数据
curl -X GET "http://localhost:8080/api/v1/jyh/invite-code/admin/list?page=50&page_size=20" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

## 注意事项

1. **权限验证**：确保测试时使用了正确的管理员Token
2. **数据清理**：测试完成后清理测试数据
3. **环境隔离**：在测试环境进行测试，避免影响生产数据
4. **并发测试**：可以使用工具进行并发注册测试，验证邀请码的唯一性约束
5. **日志监控**：观察服务器日志，确认业务逻辑执行正常 