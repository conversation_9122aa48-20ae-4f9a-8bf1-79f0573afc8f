// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
)

func newJyhArticleCategory(db *gorm.DB, opts ...gen.DOOption) jyhArticleCategory {
	_jyhArticleCategory := jyhArticleCategory{}

	_jyhArticleCategory.jyhArticleCategoryDo.UseDB(db, opts...)
	_jyhArticleCategory.jyhArticleCategoryDo.UseModel(&jyhapp.JyhArticleCategory{})

	tableName := _jyhArticleCategory.jyhArticleCategoryDo.TableName()
	_jyhArticleCategory.ALL = field.NewAsterisk(tableName)
	_jyhArticleCategory.ID = field.NewUint(tableName, "id")
	_jyhArticleCategory.CreatedAt = field.NewTime(tableName, "created_at")
	_jyhArticleCategory.UpdatedAt = field.NewTime(tableName, "updated_at")
	_jyhArticleCategory.DeletedAt = field.NewField(tableName, "deleted_at")
	_jyhArticleCategory.Name = field.NewString(tableName, "name")
	_jyhArticleCategory.ParentID = field.NewUint(tableName, "parent_id")
	_jyhArticleCategory.Sort = field.NewInt(tableName, "sort")
	_jyhArticleCategory.IsActive = field.NewBool(tableName, "is_active")
	_jyhArticleCategory.Description = field.NewString(tableName, "description")
	_jyhArticleCategory.Children = jyhArticleCategoryHasManyChildren{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Children", "jyhapp.JyhArticleCategory"),
		Parent: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Children.Parent", "jyhapp.JyhArticleCategory"),
		},
		Children: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Children.Children", "jyhapp.JyhArticleCategory"),
		},
		Articles: struct {
			field.RelationField
			Category struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Children.Articles", "jyhapp.JyhArticle"),
			Category: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Children.Articles.Category", "jyhapp.JyhArticleCategory"),
			},
		},
	}

	_jyhArticleCategory.Articles = jyhArticleCategoryHasManyArticles{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Articles", "jyhapp.JyhArticle"),
	}

	_jyhArticleCategory.Parent = jyhArticleCategoryBelongsToParent{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Parent", "jyhapp.JyhArticleCategory"),
	}

	_jyhArticleCategory.fillFieldMap()

	return _jyhArticleCategory
}

type jyhArticleCategory struct {
	jyhArticleCategoryDo

	ALL         field.Asterisk
	ID          field.Uint
	CreatedAt   field.Time
	UpdatedAt   field.Time
	DeletedAt   field.Field
	Name        field.String
	ParentID    field.Uint
	Sort        field.Int
	IsActive    field.Bool
	Description field.String
	Children    jyhArticleCategoryHasManyChildren

	Articles jyhArticleCategoryHasManyArticles

	Parent jyhArticleCategoryBelongsToParent

	fieldMap map[string]field.Expr
}

func (j jyhArticleCategory) Table(newTableName string) *jyhArticleCategory {
	j.jyhArticleCategoryDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j jyhArticleCategory) As(alias string) *jyhArticleCategory {
	j.jyhArticleCategoryDo.DO = *(j.jyhArticleCategoryDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *jyhArticleCategory) updateTableName(table string) *jyhArticleCategory {
	j.ALL = field.NewAsterisk(table)
	j.ID = field.NewUint(table, "id")
	j.CreatedAt = field.NewTime(table, "created_at")
	j.UpdatedAt = field.NewTime(table, "updated_at")
	j.DeletedAt = field.NewField(table, "deleted_at")
	j.Name = field.NewString(table, "name")
	j.ParentID = field.NewUint(table, "parent_id")
	j.Sort = field.NewInt(table, "sort")
	j.IsActive = field.NewBool(table, "is_active")
	j.Description = field.NewString(table, "description")

	j.fillFieldMap()

	return j
}

func (j *jyhArticleCategory) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *jyhArticleCategory) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 12)
	j.fieldMap["id"] = j.ID
	j.fieldMap["created_at"] = j.CreatedAt
	j.fieldMap["updated_at"] = j.UpdatedAt
	j.fieldMap["deleted_at"] = j.DeletedAt
	j.fieldMap["name"] = j.Name
	j.fieldMap["parent_id"] = j.ParentID
	j.fieldMap["sort"] = j.Sort
	j.fieldMap["is_active"] = j.IsActive
	j.fieldMap["description"] = j.Description

}

func (j jyhArticleCategory) clone(db *gorm.DB) jyhArticleCategory {
	j.jyhArticleCategoryDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j jyhArticleCategory) replaceDB(db *gorm.DB) jyhArticleCategory {
	j.jyhArticleCategoryDo.ReplaceDB(db)
	return j
}

type jyhArticleCategoryHasManyChildren struct {
	db *gorm.DB

	field.RelationField

	Parent struct {
		field.RelationField
	}
	Children struct {
		field.RelationField
	}
	Articles struct {
		field.RelationField
		Category struct {
			field.RelationField
		}
	}
}

func (a jyhArticleCategoryHasManyChildren) Where(conds ...field.Expr) *jyhArticleCategoryHasManyChildren {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a jyhArticleCategoryHasManyChildren) WithContext(ctx context.Context) *jyhArticleCategoryHasManyChildren {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a jyhArticleCategoryHasManyChildren) Session(session *gorm.Session) *jyhArticleCategoryHasManyChildren {
	a.db = a.db.Session(session)
	return &a
}

func (a jyhArticleCategoryHasManyChildren) Model(m *jyhapp.JyhArticleCategory) *jyhArticleCategoryHasManyChildrenTx {
	return &jyhArticleCategoryHasManyChildrenTx{a.db.Model(m).Association(a.Name())}
}

type jyhArticleCategoryHasManyChildrenTx struct{ tx *gorm.Association }

func (a jyhArticleCategoryHasManyChildrenTx) Find() (result []*jyhapp.JyhArticleCategory, err error) {
	return result, a.tx.Find(&result)
}

func (a jyhArticleCategoryHasManyChildrenTx) Append(values ...*jyhapp.JyhArticleCategory) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a jyhArticleCategoryHasManyChildrenTx) Replace(values ...*jyhapp.JyhArticleCategory) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a jyhArticleCategoryHasManyChildrenTx) Delete(values ...*jyhapp.JyhArticleCategory) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a jyhArticleCategoryHasManyChildrenTx) Clear() error {
	return a.tx.Clear()
}

func (a jyhArticleCategoryHasManyChildrenTx) Count() int64 {
	return a.tx.Count()
}

type jyhArticleCategoryHasManyArticles struct {
	db *gorm.DB

	field.RelationField
}

func (a jyhArticleCategoryHasManyArticles) Where(conds ...field.Expr) *jyhArticleCategoryHasManyArticles {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a jyhArticleCategoryHasManyArticles) WithContext(ctx context.Context) *jyhArticleCategoryHasManyArticles {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a jyhArticleCategoryHasManyArticles) Session(session *gorm.Session) *jyhArticleCategoryHasManyArticles {
	a.db = a.db.Session(session)
	return &a
}

func (a jyhArticleCategoryHasManyArticles) Model(m *jyhapp.JyhArticleCategory) *jyhArticleCategoryHasManyArticlesTx {
	return &jyhArticleCategoryHasManyArticlesTx{a.db.Model(m).Association(a.Name())}
}

type jyhArticleCategoryHasManyArticlesTx struct{ tx *gorm.Association }

func (a jyhArticleCategoryHasManyArticlesTx) Find() (result []*jyhapp.JyhArticle, err error) {
	return result, a.tx.Find(&result)
}

func (a jyhArticleCategoryHasManyArticlesTx) Append(values ...*jyhapp.JyhArticle) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a jyhArticleCategoryHasManyArticlesTx) Replace(values ...*jyhapp.JyhArticle) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a jyhArticleCategoryHasManyArticlesTx) Delete(values ...*jyhapp.JyhArticle) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a jyhArticleCategoryHasManyArticlesTx) Clear() error {
	return a.tx.Clear()
}

func (a jyhArticleCategoryHasManyArticlesTx) Count() int64 {
	return a.tx.Count()
}

type jyhArticleCategoryBelongsToParent struct {
	db *gorm.DB

	field.RelationField
}

func (a jyhArticleCategoryBelongsToParent) Where(conds ...field.Expr) *jyhArticleCategoryBelongsToParent {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a jyhArticleCategoryBelongsToParent) WithContext(ctx context.Context) *jyhArticleCategoryBelongsToParent {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a jyhArticleCategoryBelongsToParent) Session(session *gorm.Session) *jyhArticleCategoryBelongsToParent {
	a.db = a.db.Session(session)
	return &a
}

func (a jyhArticleCategoryBelongsToParent) Model(m *jyhapp.JyhArticleCategory) *jyhArticleCategoryBelongsToParentTx {
	return &jyhArticleCategoryBelongsToParentTx{a.db.Model(m).Association(a.Name())}
}

type jyhArticleCategoryBelongsToParentTx struct{ tx *gorm.Association }

func (a jyhArticleCategoryBelongsToParentTx) Find() (result *jyhapp.JyhArticleCategory, err error) {
	return result, a.tx.Find(&result)
}

func (a jyhArticleCategoryBelongsToParentTx) Append(values ...*jyhapp.JyhArticleCategory) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a jyhArticleCategoryBelongsToParentTx) Replace(values ...*jyhapp.JyhArticleCategory) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a jyhArticleCategoryBelongsToParentTx) Delete(values ...*jyhapp.JyhArticleCategory) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a jyhArticleCategoryBelongsToParentTx) Clear() error {
	return a.tx.Clear()
}

func (a jyhArticleCategoryBelongsToParentTx) Count() int64 {
	return a.tx.Count()
}

type jyhArticleCategoryDo struct{ gen.DO }

func (j jyhArticleCategoryDo) Debug() *jyhArticleCategoryDo {
	return j.withDO(j.DO.Debug())
}

func (j jyhArticleCategoryDo) WithContext(ctx context.Context) *jyhArticleCategoryDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jyhArticleCategoryDo) ReadDB() *jyhArticleCategoryDo {
	return j.Clauses(dbresolver.Read)
}

func (j jyhArticleCategoryDo) WriteDB() *jyhArticleCategoryDo {
	return j.Clauses(dbresolver.Write)
}

func (j jyhArticleCategoryDo) Session(config *gorm.Session) *jyhArticleCategoryDo {
	return j.withDO(j.DO.Session(config))
}

func (j jyhArticleCategoryDo) Clauses(conds ...clause.Expression) *jyhArticleCategoryDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jyhArticleCategoryDo) Returning(value interface{}, columns ...string) *jyhArticleCategoryDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jyhArticleCategoryDo) Not(conds ...gen.Condition) *jyhArticleCategoryDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jyhArticleCategoryDo) Or(conds ...gen.Condition) *jyhArticleCategoryDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jyhArticleCategoryDo) Select(conds ...field.Expr) *jyhArticleCategoryDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jyhArticleCategoryDo) Where(conds ...gen.Condition) *jyhArticleCategoryDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jyhArticleCategoryDo) Order(conds ...field.Expr) *jyhArticleCategoryDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jyhArticleCategoryDo) Distinct(cols ...field.Expr) *jyhArticleCategoryDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jyhArticleCategoryDo) Omit(cols ...field.Expr) *jyhArticleCategoryDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jyhArticleCategoryDo) Join(table schema.Tabler, on ...field.Expr) *jyhArticleCategoryDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jyhArticleCategoryDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jyhArticleCategoryDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jyhArticleCategoryDo) RightJoin(table schema.Tabler, on ...field.Expr) *jyhArticleCategoryDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jyhArticleCategoryDo) Group(cols ...field.Expr) *jyhArticleCategoryDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jyhArticleCategoryDo) Having(conds ...gen.Condition) *jyhArticleCategoryDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jyhArticleCategoryDo) Limit(limit int) *jyhArticleCategoryDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jyhArticleCategoryDo) Offset(offset int) *jyhArticleCategoryDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jyhArticleCategoryDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jyhArticleCategoryDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jyhArticleCategoryDo) Unscoped() *jyhArticleCategoryDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jyhArticleCategoryDo) Create(values ...*jyhapp.JyhArticleCategory) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jyhArticleCategoryDo) CreateInBatches(values []*jyhapp.JyhArticleCategory, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jyhArticleCategoryDo) Save(values ...*jyhapp.JyhArticleCategory) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jyhArticleCategoryDo) First() (*jyhapp.JyhArticleCategory, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhArticleCategory), nil
	}
}

func (j jyhArticleCategoryDo) Take() (*jyhapp.JyhArticleCategory, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhArticleCategory), nil
	}
}

func (j jyhArticleCategoryDo) Last() (*jyhapp.JyhArticleCategory, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhArticleCategory), nil
	}
}

func (j jyhArticleCategoryDo) Find() ([]*jyhapp.JyhArticleCategory, error) {
	result, err := j.DO.Find()
	return result.([]*jyhapp.JyhArticleCategory), err
}

func (j jyhArticleCategoryDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*jyhapp.JyhArticleCategory, err error) {
	buf := make([]*jyhapp.JyhArticleCategory, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jyhArticleCategoryDo) FindInBatches(result *[]*jyhapp.JyhArticleCategory, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jyhArticleCategoryDo) Attrs(attrs ...field.AssignExpr) *jyhArticleCategoryDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jyhArticleCategoryDo) Assign(attrs ...field.AssignExpr) *jyhArticleCategoryDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jyhArticleCategoryDo) Joins(fields ...field.RelationField) *jyhArticleCategoryDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jyhArticleCategoryDo) Preload(fields ...field.RelationField) *jyhArticleCategoryDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jyhArticleCategoryDo) FirstOrInit() (*jyhapp.JyhArticleCategory, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhArticleCategory), nil
	}
}

func (j jyhArticleCategoryDo) FirstOrCreate() (*jyhapp.JyhArticleCategory, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhArticleCategory), nil
	}
}

func (j jyhArticleCategoryDo) FindByPage(offset int, limit int) (result []*jyhapp.JyhArticleCategory, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jyhArticleCategoryDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jyhArticleCategoryDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jyhArticleCategoryDo) Delete(models ...*jyhapp.JyhArticleCategory) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jyhArticleCategoryDo) withDO(do gen.Dao) *jyhArticleCategoryDo {
	j.DO = *do.(*gen.DO)
	return j
}
