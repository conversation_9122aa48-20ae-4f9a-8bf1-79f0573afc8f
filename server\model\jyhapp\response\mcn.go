package response

type MCNBillResult struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		BaseResp struct {
			StatusCode    int    `json:"StatusCode"`
			StatusMessage string `json:"StatusMessage"`
		} `json:"BaseResp"`
		RespCode             string                    `json:"RespCode"`
		RespMsg              string                    `json:"RespMsg"`
		OrganizationBillList []McnOrganizationBillList `json:"organizationBillList"`
		Total                int                       `json:"total"`
		TotalAmount          int                       `json:"totalAmount"`
	} `json:"data"`
}

type McnOrganizationBillList struct {
	AppId                  int    `json:"app_id"`
	AuthorFee              int    `json:"author_fee"`
	AuthorFeeReturn        int    `json:"author_fee_return"`
	AuthorModeCount        int    `json:"author_mode_count"`
	AuthorName             string `json:"author_name"`
	AuthorUid              string `json:"author_uid"`
	BillId                 string `json:"bill_id"`
	ColonelFee             int    `json:"colonel_fee"`
	CommissionAmount       int    `json:"commission_amount"`
	CommissionAmountReturn int    `json:"commission_amount_return"`
	Date                   string `json:"date"`
	DouCustomerFee         int    `json:"dou_customer_fee"`
	DouCustomerFeeReturn   int    `json:"dou_customer_fee_return"`
	EndTime                string `json:"end_time"`
	OrganizationFee        int    `json:"organization_fee"`
	OrganizationFeeReturn  int    `json:"organization_fee_return"`
	PayType                string `json:"pay_type"`
	SettleAmount           int    `json:"settle_amount"`
	SettleAmountReturn     int    `json:"settle_amount_return"`
	ShopModeCount          int    `json:"shop_mode_count"`
	StartTime              string `json:"start_time"`
	SumAuthorFee           int    `json:"sum_author_fee"`
	SumOrganizationFee     int    `json:"sum_organization_fee"`
	SumServiceFee          int    `json:"sum_service_fee"`
	TotalAmount            int    `json:"total_amount"`
	TotalCount             int    `json:"total_count"`
	TotalServiceFee        int    `json:"total_service_fee"`
	TotalServiceFeeReturn  int    `json:"total_service_fee_return"`
	TotalSettleAmount      int    `json:"total_settle_amount"`
	UidType                string `json:"uid_type"`
}
