package jyhapp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	commonReq "github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"strconv"
)

type ArticleApi struct{}

// CreateCategory 创建文章分类
// @Tags 文章分类管理
// @Summary 创建文章分类
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.CategoryCreate true "创建文章分类"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /category/create [post]
func (api *ArticleApi) CreateCategory(c *gin.Context) {
	var req request.CategoryCreate
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := articleService.CategoryCreate(req); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败: "+err.Error(), c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// UpdateCategory 更新文章分类
// @Tags 文章分类管理
// @Summary 更新文章分类
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.CategoryUpdate true "更新文章分类"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /category/update [put]
func (api *ArticleApi) UpdateCategory(c *gin.Context) {
	var req request.CategoryUpdate
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := articleService.CategoryUpdate(&req); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败: "+err.Error(), c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// DeleteCategory 删除文章分类
// @Tags 文章分类管理
// @Summary 删除文章分类
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body commonReq.GetById true "删除文章分类"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /category/delete [delete]
func (api *ArticleApi) DeleteCategory(c *gin.Context) {
	var req commonReq.GetById
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := articleService.CategoryDelete(req.Uint()); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败: "+err.Error(), c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// GetCategoryDetail 获取分类详情
// @Tags 文章分类管理
// @Summary 获取分类详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id query uint true "分类ID"
// @Success 200 {object} response.Response{data=jyhapp.JyhArticleCategory} "获取成功"
// @Router /category/detail [get]
func (api *ArticleApi) GetCategoryDetail(c *gin.Context) {
	var req commonReq.GetById
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if category, err := articleService.CategoryGetDetail(req.Uint()); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(gin.H{"category": category}, c)
	}
}

// GetCategoryList 获取分类列表
// @Tags 文章分类管理
// @Summary 获取分类列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.CategorySearch true "获取分类列表"
// @Success 200 {object} response.Response{data=response.PageResult{list=[]jyhapp.JyhArticleCategory}} "获取成功"
// @Router /category/list [get]
func (api *ArticleApi) GetCategoryList(c *gin.Context) {
	var req request.CategorySearch
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	list, total, err := articleService.CategoryGetList(&req)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}

// GetCategoryTree 获取分类树
// @Tags 文章分类管理
// @Summary 获取分类树
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=[]jyhapp.JyhArticleCategory} "获取成功"
// @Router /category/tree [get]
func (api *ArticleApi) GetCategoryTree(c *gin.Context) {
	if tree, err := articleService.CategoryGetTree(); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(gin.H{"tree": tree}, c)
	}
}

// CreateArticle 创建文章
// @Tags 文章管理
// @Summary 创建文章
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.ArticleCreate true "创建文章"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /article/create [post]
func (api *ArticleApi) CreateArticle(c *gin.Context) {
	var req request.ArticleCreate
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := articleService.Create(req); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败: "+err.Error(), c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// UpdateArticle 更新文章
// @Tags 文章管理
// @Summary 更新文章
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.ArticleUpdate true "更新文章"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /article/update [put]
func (api *ArticleApi) UpdateArticle(c *gin.Context) {
	var req request.ArticleUpdate
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := articleService.Update(&req); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败: "+err.Error(), c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// DeleteArticle 删除文章
// @Tags 文章管理
// @Summary 删除文章
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body commonReq.GetById true "删除文章"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /article/delete [delete]
func (api *ArticleApi) DeleteArticle(c *gin.Context) {
	var req commonReq.GetById
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := articleService.Delete(req.Uint()); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败: "+err.Error(), c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// GetArticleDetail 获取文章详情
// @Tags 文章管理
// @Summary 获取文章详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id query uint true "文章ID"
// @Success 200 {object} response.Response{data=jyhapp.JyhArticle} "获取成功"
// @Router /article/detail [get]
func (api *ArticleApi) GetArticleDetail(c *gin.Context) {
	var req commonReq.GetById
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if article, err := articleService.GetDetail(req.Uint()); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(gin.H{"article": article}, c)
	}
}

// GetArticleList 获取文章列表
// @Tags 文章管理
// @Summary 获取文章列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.ArticleSearch true "获取文章列表"
// @Success 200 {object} response.Response{data=response.PageResult{list=[]jyhapp.JyhArticle}} "获取成功"
// @Router /article/list [get]
func (api *ArticleApi) GetArticleList(c *gin.Context) {
	var req request.ArticleSearch
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	list, total, err := articleService.GetList(&req)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}

// PublishArticle 发布文章
// @Tags 文章管理
// @Summary 发布文章
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body commonReq.GetById true "发布文章"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"发布成功"}"
// @Router /article/publish [post]
func (api *ArticleApi) PublishArticle(c *gin.Context) {
	var req commonReq.GetById
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := articleService.Publish(req.Uint()); err != nil {
		global.GVA_LOG.Error("发布失败!", zap.Error(err))
		response.FailWithMessage("发布失败: "+err.Error(), c)
	} else {
		response.OkWithMessage("发布成功", c)
	}
}

// ArchiveArticle 归档文章
// @Tags 文章管理
// @Summary 归档文章
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body commonReq.GetById true "归档文章"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"归档成功"}"
// @Router /article/archive [post]
func (api *ArticleApi) ArchiveArticle(c *gin.Context) {
	var req commonReq.GetById
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := articleService.Archive(req.Uint()); err != nil {
		global.GVA_LOG.Error("归档失败!", zap.Error(err))
		response.FailWithMessage("归档失败: "+err.Error(), c)
	} else {
		response.OkWithMessage("归档成功", c)
	}
}

// SetArticleTop 设置文章置顶
// @Tags 文章管理
// @Summary 设置文章置顶
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.SetTopRequest true "设置文章置顶"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"操作成功"}"
// @Router /article/setTop [post]
func (api *ArticleApi) SetArticleTop(c *gin.Context) {
	var req request.SetTopRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := articleService.SetTop(req.ID, req.IsTop); err != nil {
		global.GVA_LOG.Error("操作失败!", zap.Error(err))
		response.FailWithMessage("操作失败: "+err.Error(), c)
	} else {
		response.OkWithMessage("操作成功", c)
	}
}

// GetPublicArticleDetail 获取文章详情
// @Tags 文章管理
// @Summary 获取文章详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id query uint true "文章ID"
// @Success 200 {object} response.Response{data=jyhapp.JyhArticle} "获取成功"
// @Router /article/detail/{id} [get]
func (api *ArticleApi) GetPublicArticleDetail(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.FailWithMessage("无效的文章ID参数", c)
		return
	}

	if article, err := articleService.GetDetail(uint(id)); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(gin.H{"article": article}, c)
	}
}
