package config

type Polyv struct {
	AppId      string `mapstructure:"app_id" json:"app_id" yaml:"app_id"`
	SecretKey  string `mapstructure:"secret_key" json:"secret_key" yaml:"secret_key"`
	UserId     string `mapstructure:"user_id" json:"user_id" yaml:"user_id"`
	PrivateKey string `mapstructure:"private_key" json:"private_key" yaml:"private_key"` // RSA私钥（PEM格式）
	PublicKey  string `mapstructure:"public_key" json:"public_key" yaml:"public_key"`    // RSA公钥（PEM格式）
}
