package request

import "time"

// RecommendPositionCreate 创建推荐位请求
type RecommendPositionCreate struct {
	Name        string `json:"name" binding:"required"`        // 推荐位名称
	PositionKey string `json:"positionKey" binding:"required"` // 推荐位标识
	Description string `json:"description"`                    // 描述
	Width       int    `json:"width"`                          // 宽度
	Height      int    `json:"height"`                         // 高度
	MaxItems    int    `json:"maxItems"`                       // 最大展示数
	Sort        int    `json:"sort"`                           // 排序
	IsEnabled   bool   `json:"isEnabled"`                      // 是否启用
}

// RecommendPositionUpdate 更新推荐位请求
type RecommendPositionUpdate struct {
	ID          uint   `json:"id" binding:"required"`          // 推荐位ID
	Name        string `json:"name" binding:"required"`        // 推荐位名称
	PositionKey string `json:"positionKey" binding:"required"` // 推荐位标识
	Description string `json:"description"`                    // 描述
	Width       int    `json:"width"`                          // 宽度
	Height      int    `json:"height"`                         // 高度
	MaxItems    int    `json:"maxItems"`                       // 最大展示数
	Sort        int    `json:"sort"`                           // 排序
	IsEnabled   bool   `json:"isEnabled"`                      // 是否启用
}

// RecommendPositionSearch 搜索推荐位请求
type RecommendPositionSearch struct {
	Page        int    `form:"page"`        // 页码
	PageSize    int    `form:"pageSize"`    // 每页数量
	Name        string `form:"name"`        // 名称搜索
	PositionKey string `form:"positionKey"` // 标识搜索
	IsEnabled   *bool  `form:"isEnabled"`   // 启用状态筛选
}

// RecommendItemCreate 创建推荐内容项请求
type RecommendItemCreate struct {
	PositionID   uint      `json:"positionId" binding:"required"`                         // 关联推荐位ID
	Title        string    `json:"title" binding:"required"`                              // 标题
	ContentType  string    `json:"contentType" binding:"required,oneof=image text video"` // 内容类型
	ContentValue string    `json:"contentValue" binding:"required"`                       // 内容值
	LinkUrl      string    `json:"linkUrl"`                                               // 跳转链接
	BgColor      string    `json:"bgColor"`                                               // 背景颜色
	TextColor    string    `json:"textColor"`                                             // 文字颜色
	StartTime    time.Time `json:"startTime" binding:"required"`                          // 生效时间
	EndTime      time.Time `json:"endTime" binding:"required"`                            // 结束时间
	Sort         int       `json:"sort"`                                                  // 排序值
	Status       int       `json:"status" binding:"required"`                             // 状态
}

// RecommendItemUpdate 更新推荐内容项请求
type RecommendItemUpdate struct {
	ID           uint      `json:"id" binding:"required"`                                 // 内容项ID
	PositionID   uint      `json:"positionId" binding:"required"`                         // 关联推荐位ID
	Title        string    `json:"title" binding:"required"`                              // 标题
	ContentType  string    `json:"contentType" binding:"required,oneof=image text video"` // 内容类型
	ContentValue string    `json:"contentValue" binding:"required"`                       // 内容值
	LinkUrl      string    `json:"linkUrl"`                                               // 跳转链接
	BgColor      string    `json:"bgColor"`                                               // 背景颜色
	TextColor    string    `json:"textColor"`                                             // 文字颜色
	StartTime    time.Time `json:"startTime" binding:"required"`                          // 生效时间
	EndTime      time.Time `json:"endTime" binding:"required"`                            // 结束时间
	Sort         int       `json:"sort"`                                                  // 排序值
	Status       int       `json:"status" binding:"required"`                             // 状态
}

// RecommendItemSearch 搜索推荐内容项请求
type RecommendItemSearch struct {
	Page        int        `form:"page"`        // 页码
	PageSize    int        `form:"pageSize"`    // 每页数量
	PositionID  uint       `form:"positionId"`  // 推荐位ID
	Title       string     `form:"title"`       // 标题搜索
	ContentType string     `form:"contentType"` // 内容类型筛选
	Status      int        `form:"status"`      // 状态筛选
	StartTime   *time.Time `form:"startTime"`   // 开始时间筛选
	EndTime     *time.Time `form:"endTime"`     // 结束时间筛选
	PositionKey string     `form:"positionKey"` // 推荐位标识
}
