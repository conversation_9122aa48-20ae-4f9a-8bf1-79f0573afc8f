// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
)

func newJyhMaterial(db *gorm.DB, opts ...gen.DOOption) jyhMaterial {
	_jyhMaterial := jyhMaterial{}

	_jyhMaterial.jyhMaterialDo.UseDB(db, opts...)
	_jyhMaterial.jyhMaterialDo.UseModel(&jyhapp.JyhMaterial{})

	tableName := _jyhMaterial.jyhMaterialDo.TableName()
	_jyhMaterial.ALL = field.NewAsterisk(tableName)
	_jyhMaterial.ID = field.NewUint(tableName, "id")
	_jyhMaterial.CreatedAt = field.NewTime(tableName, "created_at")
	_jyhMaterial.UpdatedAt = field.NewTime(tableName, "updated_at")
	_jyhMaterial.DeletedAt = field.NewField(tableName, "deleted_at")
	_jyhMaterial.Name = field.NewString(tableName, "name")
	_jyhMaterial.Description = field.NewString(tableName, "description")
	_jyhMaterial.FileUrl = field.NewString(tableName, "file_url")
	_jyhMaterial.CategoryID = field.NewUint(tableName, "category_id")
	_jyhMaterial.Copywriting = field.NewString(tableName, "copywriting")
	_jyhMaterial.DouyinProductUrl = field.NewString(tableName, "douyin_product_url")
	_jyhMaterial.MusicUrl = field.NewString(tableName, "music_url")
	_jyhMaterial.ViewCount = field.NewInt(tableName, "view_count")
	_jyhMaterial.DownloadCount = field.NewInt(tableName, "download_count")
	_jyhMaterial.Price = field.NewField(tableName, "price")
	_jyhMaterial.IsFree = field.NewBool(tableName, "is_free")
	_jyhMaterial.Status = field.NewUint(tableName, "status")
	_jyhMaterial.IsSend = field.NewBool(tableName, "is_send")
	_jyhMaterial.Type = field.NewString(tableName, "type")
	_jyhMaterial.PointsRequired = field.NewInt(tableName, "points_required")
	_jyhMaterial.Files = jyhMaterialHasManyFiles{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Files", "jyhapp.JyhMaterialFile"),
	}

	_jyhMaterial.Category = jyhMaterialBelongsToCategory{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Category", "jyhapp.JyhMaterialCategory"),
	}

	_jyhMaterial.fillFieldMap()

	return _jyhMaterial
}

type jyhMaterial struct {
	jyhMaterialDo

	ALL              field.Asterisk
	ID               field.Uint
	CreatedAt        field.Time
	UpdatedAt        field.Time
	DeletedAt        field.Field
	Name             field.String
	Description      field.String
	FileUrl          field.String
	CategoryID       field.Uint
	Copywriting      field.String
	DouyinProductUrl field.String
	MusicUrl         field.String
	ViewCount        field.Int
	DownloadCount    field.Int
	Price            field.Field
	IsFree           field.Bool
	Status           field.Uint
	IsSend           field.Bool
	Type             field.String
	PointsRequired   field.Int
	Files            jyhMaterialHasManyFiles

	Category jyhMaterialBelongsToCategory

	fieldMap map[string]field.Expr
}

func (j jyhMaterial) Table(newTableName string) *jyhMaterial {
	j.jyhMaterialDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j jyhMaterial) As(alias string) *jyhMaterial {
	j.jyhMaterialDo.DO = *(j.jyhMaterialDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *jyhMaterial) updateTableName(table string) *jyhMaterial {
	j.ALL = field.NewAsterisk(table)
	j.ID = field.NewUint(table, "id")
	j.CreatedAt = field.NewTime(table, "created_at")
	j.UpdatedAt = field.NewTime(table, "updated_at")
	j.DeletedAt = field.NewField(table, "deleted_at")
	j.Name = field.NewString(table, "name")
	j.Description = field.NewString(table, "description")
	j.FileUrl = field.NewString(table, "file_url")
	j.CategoryID = field.NewUint(table, "category_id")
	j.Copywriting = field.NewString(table, "copywriting")
	j.DouyinProductUrl = field.NewString(table, "douyin_product_url")
	j.MusicUrl = field.NewString(table, "music_url")
	j.ViewCount = field.NewInt(table, "view_count")
	j.DownloadCount = field.NewInt(table, "download_count")
	j.Price = field.NewField(table, "price")
	j.IsFree = field.NewBool(table, "is_free")
	j.Status = field.NewUint(table, "status")
	j.IsSend = field.NewBool(table, "is_send")
	j.Type = field.NewString(table, "type")
	j.PointsRequired = field.NewInt(table, "points_required")

	j.fillFieldMap()

	return j
}

func (j *jyhMaterial) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *jyhMaterial) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 21)
	j.fieldMap["id"] = j.ID
	j.fieldMap["created_at"] = j.CreatedAt
	j.fieldMap["updated_at"] = j.UpdatedAt
	j.fieldMap["deleted_at"] = j.DeletedAt
	j.fieldMap["name"] = j.Name
	j.fieldMap["description"] = j.Description
	j.fieldMap["file_url"] = j.FileUrl
	j.fieldMap["category_id"] = j.CategoryID
	j.fieldMap["copywriting"] = j.Copywriting
	j.fieldMap["douyin_product_url"] = j.DouyinProductUrl
	j.fieldMap["music_url"] = j.MusicUrl
	j.fieldMap["view_count"] = j.ViewCount
	j.fieldMap["download_count"] = j.DownloadCount
	j.fieldMap["price"] = j.Price
	j.fieldMap["is_free"] = j.IsFree
	j.fieldMap["status"] = j.Status
	j.fieldMap["is_send"] = j.IsSend
	j.fieldMap["type"] = j.Type
	j.fieldMap["points_required"] = j.PointsRequired

}

func (j jyhMaterial) clone(db *gorm.DB) jyhMaterial {
	j.jyhMaterialDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j jyhMaterial) replaceDB(db *gorm.DB) jyhMaterial {
	j.jyhMaterialDo.ReplaceDB(db)
	return j
}

type jyhMaterialHasManyFiles struct {
	db *gorm.DB

	field.RelationField
}

func (a jyhMaterialHasManyFiles) Where(conds ...field.Expr) *jyhMaterialHasManyFiles {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a jyhMaterialHasManyFiles) WithContext(ctx context.Context) *jyhMaterialHasManyFiles {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a jyhMaterialHasManyFiles) Session(session *gorm.Session) *jyhMaterialHasManyFiles {
	a.db = a.db.Session(session)
	return &a
}

func (a jyhMaterialHasManyFiles) Model(m *jyhapp.JyhMaterial) *jyhMaterialHasManyFilesTx {
	return &jyhMaterialHasManyFilesTx{a.db.Model(m).Association(a.Name())}
}

type jyhMaterialHasManyFilesTx struct{ tx *gorm.Association }

func (a jyhMaterialHasManyFilesTx) Find() (result []*jyhapp.JyhMaterialFile, err error) {
	return result, a.tx.Find(&result)
}

func (a jyhMaterialHasManyFilesTx) Append(values ...*jyhapp.JyhMaterialFile) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a jyhMaterialHasManyFilesTx) Replace(values ...*jyhapp.JyhMaterialFile) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a jyhMaterialHasManyFilesTx) Delete(values ...*jyhapp.JyhMaterialFile) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a jyhMaterialHasManyFilesTx) Clear() error {
	return a.tx.Clear()
}

func (a jyhMaterialHasManyFilesTx) Count() int64 {
	return a.tx.Count()
}

type jyhMaterialBelongsToCategory struct {
	db *gorm.DB

	field.RelationField
}

func (a jyhMaterialBelongsToCategory) Where(conds ...field.Expr) *jyhMaterialBelongsToCategory {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a jyhMaterialBelongsToCategory) WithContext(ctx context.Context) *jyhMaterialBelongsToCategory {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a jyhMaterialBelongsToCategory) Session(session *gorm.Session) *jyhMaterialBelongsToCategory {
	a.db = a.db.Session(session)
	return &a
}

func (a jyhMaterialBelongsToCategory) Model(m *jyhapp.JyhMaterial) *jyhMaterialBelongsToCategoryTx {
	return &jyhMaterialBelongsToCategoryTx{a.db.Model(m).Association(a.Name())}
}

type jyhMaterialBelongsToCategoryTx struct{ tx *gorm.Association }

func (a jyhMaterialBelongsToCategoryTx) Find() (result *jyhapp.JyhMaterialCategory, err error) {
	return result, a.tx.Find(&result)
}

func (a jyhMaterialBelongsToCategoryTx) Append(values ...*jyhapp.JyhMaterialCategory) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a jyhMaterialBelongsToCategoryTx) Replace(values ...*jyhapp.JyhMaterialCategory) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a jyhMaterialBelongsToCategoryTx) Delete(values ...*jyhapp.JyhMaterialCategory) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a jyhMaterialBelongsToCategoryTx) Clear() error {
	return a.tx.Clear()
}

func (a jyhMaterialBelongsToCategoryTx) Count() int64 {
	return a.tx.Count()
}

type jyhMaterialDo struct{ gen.DO }

func (j jyhMaterialDo) Debug() *jyhMaterialDo {
	return j.withDO(j.DO.Debug())
}

func (j jyhMaterialDo) WithContext(ctx context.Context) *jyhMaterialDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jyhMaterialDo) ReadDB() *jyhMaterialDo {
	return j.Clauses(dbresolver.Read)
}

func (j jyhMaterialDo) WriteDB() *jyhMaterialDo {
	return j.Clauses(dbresolver.Write)
}

func (j jyhMaterialDo) Session(config *gorm.Session) *jyhMaterialDo {
	return j.withDO(j.DO.Session(config))
}

func (j jyhMaterialDo) Clauses(conds ...clause.Expression) *jyhMaterialDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jyhMaterialDo) Returning(value interface{}, columns ...string) *jyhMaterialDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jyhMaterialDo) Not(conds ...gen.Condition) *jyhMaterialDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jyhMaterialDo) Or(conds ...gen.Condition) *jyhMaterialDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jyhMaterialDo) Select(conds ...field.Expr) *jyhMaterialDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jyhMaterialDo) Where(conds ...gen.Condition) *jyhMaterialDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jyhMaterialDo) Order(conds ...field.Expr) *jyhMaterialDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jyhMaterialDo) Distinct(cols ...field.Expr) *jyhMaterialDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jyhMaterialDo) Omit(cols ...field.Expr) *jyhMaterialDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jyhMaterialDo) Join(table schema.Tabler, on ...field.Expr) *jyhMaterialDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jyhMaterialDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jyhMaterialDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jyhMaterialDo) RightJoin(table schema.Tabler, on ...field.Expr) *jyhMaterialDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jyhMaterialDo) Group(cols ...field.Expr) *jyhMaterialDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jyhMaterialDo) Having(conds ...gen.Condition) *jyhMaterialDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jyhMaterialDo) Limit(limit int) *jyhMaterialDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jyhMaterialDo) Offset(offset int) *jyhMaterialDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jyhMaterialDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jyhMaterialDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jyhMaterialDo) Unscoped() *jyhMaterialDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jyhMaterialDo) Create(values ...*jyhapp.JyhMaterial) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jyhMaterialDo) CreateInBatches(values []*jyhapp.JyhMaterial, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jyhMaterialDo) Save(values ...*jyhapp.JyhMaterial) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jyhMaterialDo) First() (*jyhapp.JyhMaterial, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterial), nil
	}
}

func (j jyhMaterialDo) Take() (*jyhapp.JyhMaterial, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterial), nil
	}
}

func (j jyhMaterialDo) Last() (*jyhapp.JyhMaterial, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterial), nil
	}
}

func (j jyhMaterialDo) Find() ([]*jyhapp.JyhMaterial, error) {
	result, err := j.DO.Find()
	return result.([]*jyhapp.JyhMaterial), err
}

func (j jyhMaterialDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*jyhapp.JyhMaterial, err error) {
	buf := make([]*jyhapp.JyhMaterial, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jyhMaterialDo) FindInBatches(result *[]*jyhapp.JyhMaterial, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jyhMaterialDo) Attrs(attrs ...field.AssignExpr) *jyhMaterialDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jyhMaterialDo) Assign(attrs ...field.AssignExpr) *jyhMaterialDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jyhMaterialDo) Joins(fields ...field.RelationField) *jyhMaterialDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jyhMaterialDo) Preload(fields ...field.RelationField) *jyhMaterialDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jyhMaterialDo) FirstOrInit() (*jyhapp.JyhMaterial, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterial), nil
	}
}

func (j jyhMaterialDo) FirstOrCreate() (*jyhapp.JyhMaterial, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterial), nil
	}
}

func (j jyhMaterialDo) FindByPage(offset int, limit int) (result []*jyhapp.JyhMaterial, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jyhMaterialDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jyhMaterialDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jyhMaterialDo) Delete(models ...*jyhapp.JyhMaterial) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jyhMaterialDo) withDO(do gen.Dao) *jyhMaterialDo {
	j.DO = *do.(*gen.DO)
	return j
}
