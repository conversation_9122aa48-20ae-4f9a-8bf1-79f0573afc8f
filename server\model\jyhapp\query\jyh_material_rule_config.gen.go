// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
)

func newJyhMaterialRuleConfig(db *gorm.DB, opts ...gen.DOOption) jyhMaterialRuleConfig {
	_jyhMaterialRuleConfig := jyhMaterialRuleConfig{}

	_jyhMaterialRuleConfig.jyhMaterialRuleConfigDo.UseDB(db, opts...)
	_jyhMaterialRuleConfig.jyhMaterialRuleConfigDo.UseModel(&jyhapp.JyhMaterialRuleConfig{})

	tableName := _jyhMaterialRuleConfig.jyhMaterialRuleConfigDo.TableName()
	_jyhMaterialRuleConfig.ALL = field.NewAsterisk(tableName)
	_jyhMaterialRuleConfig.ID = field.NewUint(tableName, "id")
	_jyhMaterialRuleConfig.MinFans = field.NewInt(tableName, "min_fans")
	_jyhMaterialRuleConfig.MaxFans = field.NewInt(tableName, "max_fans")
	_jyhMaterialRuleConfig.MinValidFans = field.NewInt(tableName, "min_valid_fans")
	_jyhMaterialRuleConfig.MaxValidFans = field.NewInt(tableName, "max_valid_fans")
	_jyhMaterialRuleConfig.FrequencyType = field.NewString(tableName, "frequency_type")
	_jyhMaterialRuleConfig.SelectionType = field.NewString(tableName, "selection_type")
	_jyhMaterialRuleConfig.MaterialCount = field.NewInt(tableName, "material_count")
	_jyhMaterialRuleConfig.StreamCount = field.NewInt(tableName, "stream_count")
	_jyhMaterialRuleConfig.VideoCount = field.NewInt(tableName, "video_count")
	_jyhMaterialRuleConfig.ImageCount = field.NewInt(tableName, "image_count")
	_jyhMaterialRuleConfig.Remark = field.NewString(tableName, "remark")

	_jyhMaterialRuleConfig.fillFieldMap()

	return _jyhMaterialRuleConfig
}

type jyhMaterialRuleConfig struct {
	jyhMaterialRuleConfigDo

	ALL           field.Asterisk
	ID            field.Uint
	MinFans       field.Int
	MaxFans       field.Int
	MinValidFans  field.Int
	MaxValidFans  field.Int
	FrequencyType field.String
	SelectionType field.String
	MaterialCount field.Int
	StreamCount   field.Int
	VideoCount    field.Int
	ImageCount    field.Int
	Remark        field.String

	fieldMap map[string]field.Expr
}

func (j jyhMaterialRuleConfig) Table(newTableName string) *jyhMaterialRuleConfig {
	j.jyhMaterialRuleConfigDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j jyhMaterialRuleConfig) As(alias string) *jyhMaterialRuleConfig {
	j.jyhMaterialRuleConfigDo.DO = *(j.jyhMaterialRuleConfigDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *jyhMaterialRuleConfig) updateTableName(table string) *jyhMaterialRuleConfig {
	j.ALL = field.NewAsterisk(table)
	j.ID = field.NewUint(table, "id")
	j.MinFans = field.NewInt(table, "min_fans")
	j.MaxFans = field.NewInt(table, "max_fans")
	j.MinValidFans = field.NewInt(table, "min_valid_fans")
	j.MaxValidFans = field.NewInt(table, "max_valid_fans")
	j.FrequencyType = field.NewString(table, "frequency_type")
	j.SelectionType = field.NewString(table, "selection_type")
	j.MaterialCount = field.NewInt(table, "material_count")
	j.StreamCount = field.NewInt(table, "stream_count")
	j.VideoCount = field.NewInt(table, "video_count")
	j.ImageCount = field.NewInt(table, "image_count")
	j.Remark = field.NewString(table, "remark")

	j.fillFieldMap()

	return j
}

func (j *jyhMaterialRuleConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *jyhMaterialRuleConfig) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 12)
	j.fieldMap["id"] = j.ID
	j.fieldMap["min_fans"] = j.MinFans
	j.fieldMap["max_fans"] = j.MaxFans
	j.fieldMap["min_valid_fans"] = j.MinValidFans
	j.fieldMap["max_valid_fans"] = j.MaxValidFans
	j.fieldMap["frequency_type"] = j.FrequencyType
	j.fieldMap["selection_type"] = j.SelectionType
	j.fieldMap["material_count"] = j.MaterialCount
	j.fieldMap["stream_count"] = j.StreamCount
	j.fieldMap["video_count"] = j.VideoCount
	j.fieldMap["image_count"] = j.ImageCount
	j.fieldMap["remark"] = j.Remark
}

func (j jyhMaterialRuleConfig) clone(db *gorm.DB) jyhMaterialRuleConfig {
	j.jyhMaterialRuleConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j jyhMaterialRuleConfig) replaceDB(db *gorm.DB) jyhMaterialRuleConfig {
	j.jyhMaterialRuleConfigDo.ReplaceDB(db)
	return j
}

type jyhMaterialRuleConfigDo struct{ gen.DO }

func (j jyhMaterialRuleConfigDo) Debug() *jyhMaterialRuleConfigDo {
	return j.withDO(j.DO.Debug())
}

func (j jyhMaterialRuleConfigDo) WithContext(ctx context.Context) *jyhMaterialRuleConfigDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jyhMaterialRuleConfigDo) ReadDB() *jyhMaterialRuleConfigDo {
	return j.Clauses(dbresolver.Read)
}

func (j jyhMaterialRuleConfigDo) WriteDB() *jyhMaterialRuleConfigDo {
	return j.Clauses(dbresolver.Write)
}

func (j jyhMaterialRuleConfigDo) Session(config *gorm.Session) *jyhMaterialRuleConfigDo {
	return j.withDO(j.DO.Session(config))
}

func (j jyhMaterialRuleConfigDo) Clauses(conds ...clause.Expression) *jyhMaterialRuleConfigDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jyhMaterialRuleConfigDo) Returning(value interface{}, columns ...string) *jyhMaterialRuleConfigDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jyhMaterialRuleConfigDo) Not(conds ...gen.Condition) *jyhMaterialRuleConfigDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jyhMaterialRuleConfigDo) Or(conds ...gen.Condition) *jyhMaterialRuleConfigDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jyhMaterialRuleConfigDo) Select(conds ...field.Expr) *jyhMaterialRuleConfigDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jyhMaterialRuleConfigDo) Where(conds ...gen.Condition) *jyhMaterialRuleConfigDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jyhMaterialRuleConfigDo) Order(conds ...field.Expr) *jyhMaterialRuleConfigDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jyhMaterialRuleConfigDo) Distinct(cols ...field.Expr) *jyhMaterialRuleConfigDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jyhMaterialRuleConfigDo) Omit(cols ...field.Expr) *jyhMaterialRuleConfigDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jyhMaterialRuleConfigDo) Join(table schema.Tabler, on ...field.Expr) *jyhMaterialRuleConfigDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jyhMaterialRuleConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jyhMaterialRuleConfigDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jyhMaterialRuleConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) *jyhMaterialRuleConfigDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jyhMaterialRuleConfigDo) Group(cols ...field.Expr) *jyhMaterialRuleConfigDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jyhMaterialRuleConfigDo) Having(conds ...gen.Condition) *jyhMaterialRuleConfigDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jyhMaterialRuleConfigDo) Limit(limit int) *jyhMaterialRuleConfigDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jyhMaterialRuleConfigDo) Offset(offset int) *jyhMaterialRuleConfigDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jyhMaterialRuleConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jyhMaterialRuleConfigDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jyhMaterialRuleConfigDo) Unscoped() *jyhMaterialRuleConfigDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jyhMaterialRuleConfigDo) Create(values ...*jyhapp.JyhMaterialRuleConfig) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jyhMaterialRuleConfigDo) CreateInBatches(values []*jyhapp.JyhMaterialRuleConfig, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jyhMaterialRuleConfigDo) Save(values ...*jyhapp.JyhMaterialRuleConfig) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jyhMaterialRuleConfigDo) First() (*jyhapp.JyhMaterialRuleConfig, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterialRuleConfig), nil
	}
}

func (j jyhMaterialRuleConfigDo) Take() (*jyhapp.JyhMaterialRuleConfig, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterialRuleConfig), nil
	}
}

func (j jyhMaterialRuleConfigDo) Last() (*jyhapp.JyhMaterialRuleConfig, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterialRuleConfig), nil
	}
}

func (j jyhMaterialRuleConfigDo) Find() ([]*jyhapp.JyhMaterialRuleConfig, error) {
	result, err := j.DO.Find()
	return result.([]*jyhapp.JyhMaterialRuleConfig), err
}

func (j jyhMaterialRuleConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*jyhapp.JyhMaterialRuleConfig, err error) {
	buf := make([]*jyhapp.JyhMaterialRuleConfig, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jyhMaterialRuleConfigDo) FindInBatches(result *[]*jyhapp.JyhMaterialRuleConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jyhMaterialRuleConfigDo) Attrs(attrs ...field.AssignExpr) *jyhMaterialRuleConfigDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jyhMaterialRuleConfigDo) Assign(attrs ...field.AssignExpr) *jyhMaterialRuleConfigDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jyhMaterialRuleConfigDo) Joins(fields ...field.RelationField) *jyhMaterialRuleConfigDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jyhMaterialRuleConfigDo) Preload(fields ...field.RelationField) *jyhMaterialRuleConfigDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jyhMaterialRuleConfigDo) FirstOrInit() (*jyhapp.JyhMaterialRuleConfig, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterialRuleConfig), nil
	}
}

func (j jyhMaterialRuleConfigDo) FirstOrCreate() (*jyhapp.JyhMaterialRuleConfig, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterialRuleConfig), nil
	}
}

func (j jyhMaterialRuleConfigDo) FindByPage(offset int, limit int) (result []*jyhapp.JyhMaterialRuleConfig, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jyhMaterialRuleConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jyhMaterialRuleConfigDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jyhMaterialRuleConfigDo) Delete(models ...*jyhapp.JyhMaterialRuleConfig) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jyhMaterialRuleConfigDo) withDO(do gen.Dao) *jyhMaterialRuleConfigDo {
	j.DO = *do.(*gen.DO)
	return j
}
