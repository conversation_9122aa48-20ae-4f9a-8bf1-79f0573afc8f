package jyhapp

type ServiceGroup struct {
	// 素材管理
	MaterialCategoryService
	MaterialService
	MaterialSyncService
	// 课程管理
	JyhCourseService
	CourseCategoryService
	JyhUserService //用户服务
	// 标签管理
	TagsService
	// 推荐位管理
	RecommendPositionService
	// 用户权益管理
	JyhUserBenefitService
	// 用户签约管理
	JyhUserContractService
	//用户标签管理
	JyhUserTagService
	//用户层级管理
	JyhUserClosureService
	// 文章相关
	JyhArticleService
	// 用户直播房间管理
	JyhUserLiveRoomService
	// 用户订单
	JyhOrderService
	// 邀请码管理
	JyhInviteCodeService
	// 用户账户管理
	JyhUserAccountService
	// 用户积分管理
	JyhPointsService
}

var gSrv *ServiceGroup

func (S *ServiceGroup) Init() (err error) {

	gSrv = S

	return err
}
