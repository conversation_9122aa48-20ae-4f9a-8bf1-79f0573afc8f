package jyhapp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	commonReq "github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type CourseApi struct{}

// Create 创建课程管理
// @Tags      Course
// @Summary   创建课程管理
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.CourseCreateReq  true  "创建课程管理"
// @Success   200   {object}  response.Response{msg=string}  "创建课程管理"
// @Router    /course/create [post]
func (m *CourseApi) Create(c *gin.Context) {
	var req request.CourseCreateReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = courseService.Create(req)
	if err != nil {
		global.GVA_LOG.Error("创建课程管理失败!", zap.Error(err))
		response.FailWithMessage("创建课程管理失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建课程管理成功", c)
}

// Update 更新课程管理
// @Tags      Course
// @Summary   更新课程管理
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.CourseUpdateReq  true  "更新课程管理"
// @Success   200   {object}  response.Response{msg=string}  "更新课程管理"
// @Router    /course/update [put]
func (m *CourseApi) Update(c *gin.Context) {
	var req request.CourseUpdateReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = courseService.Update(&req)
	if err != nil {
		global.GVA_LOG.Error("更新课程管理失败!", zap.Error(err))
		response.FailWithMessage("更新课程管理失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新课程管理成功", c)
}

// Delete 删除课程管理
// @Tags      Course
// @Summary   删除课程管理
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      commonReq.GetById  true  "删除课程管理"
// @Success   200   {object}  response.Response{msg=string}  "删除课程管理"
// @Router    /course/delete [delete]
func (m *CourseApi) Delete(c *gin.Context) {
	var req commonReq.GetById
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = courseService.Delete(req.Uint())
	if err != nil {
		global.GVA_LOG.Error("删除课程管理失败!", zap.Error(err))
		response.FailWithMessage("删除课程管理失败: "+err.Error(), c)
		return
	}
	response.OkWithMessage("删除课程管理成功", c)
}

// GetDetail 获取课程管理详情
// @Tags      Course
// @Summary   获取课程管理详情
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     commonReq.GetById  true  "获取课程管理详情"
// @Success   200   {object}  response.Response{data=jyhapp.JyhCourse,msg=string}  "获取课程管理详情"
// @Router    /course/detail [get]
func (m *CourseApi) GetDetail(c *gin.Context) {
	var req commonReq.GetById
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	course, err := courseService.GetDetail(req.Uint())
	if err != nil {
		global.GVA_LOG.Error("获取课程管理失败!", zap.Error(err))
		response.FailWithMessage("获取课程管理失败", c)
		return
	}
	response.OkWithData(course, c)
}

// GetList 获取课程管理列表
// @Tags      Course
// @Summary   获取课程管理列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     request.CourseSearchReq  true  "获取课程管理列表"
// @Success   200   {object}  response.Response{data=response.PageResult,msg=string}  "获取课程管理列表"
// @Router    /course/list [get]
func (m *CourseApi) GetList(c *gin.Context) {
	var req request.CourseSearchReq
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	list, total, err := courseService.GetList(&req)
	if err != nil {
		global.GVA_LOG.Error("获取课程管理失败!", zap.Error(err))
		response.FailWithMessage("获取课程管理失败", c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取课程管理成功", c)
}

// IncrementViewCount 增加课程观看人数
// @Tags      Course
// @Summary   增加课程观看人数
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      commonReq.GetById  true  "课程ID"
// @Success   200   {object}  response.Response{msg=string}  "增加课程观看人数"
// @Router    /course/incrementView [post]
func (m *CourseApi) IncrementViewCount(c *gin.Context) {
	var req commonReq.GetById
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = courseService.IncrementViewCount(req.Uint())
	if err != nil {
		global.GVA_LOG.Error("增加课程观看人数失败!", zap.Error(err))
		response.FailWithMessage("增加课程观看人数失败: "+err.Error(), c)
		return
	}
	response.OkWithMessage("增加课程观看人数成功", c)
}
