### 用户管理功能

#### 用户注册
用户模型：server\model\jyhapp\jyh_user.go  JyhUser模型

1. 用户注册需要填写手机号和邀请码，邀请码是必填项
   - 邀请码验证规则：
     - 必须是已存在用户的邀请码
     - 从用户表 jyh_user 中的 invite_code 字段进行验证
     - 邀请码不能为空
     - 验证失败时返回对应的错误信息
   - 注册成功后：
     - 自动生成新的6位数字邀请码（确保唯一）
     - 记录邀请人ID (inviter_id)
     - 用户名使用脱敏后的手机号
     - 设置默认值：
       - is_agent: false (非代理)
       - status: 1 (启用)
       - user_type: user (普通用户)

2. 验证电话号合法性
   - 必须是中国大陆手机号（1开头的11位数字）
   - 支持的号段：13-19
   - 注册和展示时进行脱敏处理（例：138****8888）
   - 同一手机号不能重复注册

3. 填写之后发送验证码进行注册
   - 验证码为6位数字
   - 有效期5分钟
   - 同一手机号1分钟内只能发送一次

#### 用户登录
1. 使用手机号+验证码方式登录
2. 验证规则同注册流程
3. 登录成功后返回用户信息，包括：
   - 用户ID
   - 脱敏后的手机号
   - 用户名
   - 邀请码
   - 用户类型
   - 状态

#### 获取用户基础信息
1. 根据用户ID获取用户信息
2. 返回用户基本资料：
   - 用户ID
   - 手机号（脱敏处理）
   - 邀请码
   - 注册时间
   - 用户类型
   - 代理状态
   - 账户状态

#### 验证码功能
1. 验证码场景
   - 注册验证码 (register)
   - 登录验证码 (login)
   - 重置密码验证码 (reset)
   - 绑定手机号验证码 (bind)

2. 验证码配置
   - 验证码长度：6位数字
   - 有效期：5分钟（300秒）
   - 发送间隔：60秒
   - 每日最大发送次数：10次
   - 测试环境固定验证码：123456

3. 发送验证码流程
   - 验证手机号格式
   - 验证短信场景是否有效
   - 检查发送频率限制
   - 生成随机验证码
   - 保存到Redis并设置过期时间
   - 发送短信（根据环境判断）

4. 验证码存储（Redis）
   - 验证码键：sms:code:{phone}:{scene}
   - 发送间隔键：sms:interval:{phone}:{scene}
   - 每日计数键：sms:count:{phone}:{date}

5. 验证码校验
   - 测试环境支持固定验证码
   - 检查验证码是否存在
   - 检查验证码是否过期
   - 验证码匹配校验
   - 使用后立即删除验证码

6. 错误处理
   - 手机号格式错误
   - 发送过于频繁
   - 超出每日发送限制
   - 验证码过期
   - 验证码错误
   - 无效的短信场景

#### 数据库字段说明
```sql
CREATE TABLE jyh_user (
  id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  username VARCHAR(50) NOT NULL COMMENT '用户名(脱敏手机号)',
  phone VARCHAR(11) NOT NULL COMMENT '手机号',
  invite_code VARCHAR(6) NOT NULL COMMENT '邀请码',
  inviter_id BIGINT UNSIGNED DEFAULT NULL COMMENT '邀请人ID',
  is_agent TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否代理(0-否 1-是)',
  status TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态(0-禁用 1-启用)',
  user_type TINYINT(1) NOT NULL DEFAULT 1 COMMENT '用户类型(1-普通用户)',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (id),
  UNIQUE KEY uk_phone (phone),
  UNIQUE KEY uk_invite_code (invite_code),
  KEY idx_inviter_id (inviter_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
```