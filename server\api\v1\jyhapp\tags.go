package jyhapp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	commonReq "github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type TagsApi struct{}

// Create 创建标签
// @Tags      Tags
// @Summary   创建标签
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.TagsCreate  true  "创建标签"
// @Success   200   {object}  response.Response{msg=string}  "创建标签"
// @Router    /tags/create [post]
func (m *TagsApi) Create(c *gin.Context) {
	var req request.TagsCreate
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = tagsService.Create(req)
	if err != nil {
		global.GVA_LOG.Error("创建标签失败!", zap.Error(err))
		response.FailWithMessage("创建标签失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建标签成功", c)
}

// Update 更新标签
// @Tags      Tags
// @Summary   更新标签
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.TagsUpdate  true  "更新标签"
// @Success   200   {object}  response.Response{msg=string}  "更新标签"
// @Router    /tags/update [put]
func (m *TagsApi) Update(c *gin.Context) {
	var req request.TagsUpdate
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = tagsService.Update(&req)
	if err != nil {
		global.GVA_LOG.Error("更新标签失败!", zap.Error(err))
		response.FailWithMessage("更新标签失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新标签成功", c)
}

// Delete 删除标签
// @Tags      Tags
// @Summary   删除标签
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      commonReq.GetById  true  "删除标签"
// @Success   200   {object}  response.Response{msg=string}  "删除标签"
// @Router    /tags/delete [delete]
func (m *TagsApi) Delete(c *gin.Context) {
	var req commonReq.GetById
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = tagsService.Delete(req.Uint())
	if err != nil {
		global.GVA_LOG.Error("删除标签失败!", zap.Error(err))
		response.FailWithMessage("删除标签失败: "+err.Error(), c)
		return
	}
	response.OkWithMessage("删除标签成功", c)
}

// GetDetail 获取标签详情
// @Tags      Tags
// @Summary   获取标签详情
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     commonReq.GetById  true  "获取标签详情"
// @Success   200   {object}  response.Response{data=interface{},msg=string}  "获取标签详情"
// @Router    /tags/detail [get]
func (m *TagsApi) GetDetail(c *gin.Context) {
	var req commonReq.GetById
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	category, err := tagsService.GetDetail(req.Uint())
	if err != nil {
		global.GVA_LOG.Error("获取标签失败!", zap.Error(err))
		response.FailWithMessage("获取标签失败", c)
		return
	}
	response.OkWithData(category, c)
}

// GetList 获取标签列表
// @Tags      Tags
// @Summary   获取标签列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     request.TagsSearch  true  "获取标签列表"
// @Success   200   {object}  response.Response{data=response.PageResult,msg=string}  "获取标签列表"
// @Router    /tags/list [get]
func (m *TagsApi) GetList(c *gin.Context) {
	var req request.TagsSearch
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	list, total, err := tagsService.GetList(&req)
	if err != nil {
		global.GVA_LOG.Error("获取标签失败!", zap.Error(err))
		response.FailWithMessage("获取标签失败", c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取标签成功", c)
}

// GetTags 获取所有标签
// @Tags      Tags
// @Summary   获取所有标签
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     request.TagsFilterReq  true  "获取所有标签"
// @Success   200   {object}  response.Response{data=interface{},msg=string}  "获取所有标签"
// @Router    /tags/getTags [get]
func (m *TagsApi) GetTags(c *gin.Context) {
	var req request.TagsFilterReq

	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	tags, err := tagsService.GetTags(req)
	if err != nil {
		global.GVA_LOG.Error("获取素材标签失败", zap.Error(err))
		response.FailWithMessage("获取素材标签失败: "+err.Error(), c)
		return
	}

	response.OkWithData(tags, c)
}
