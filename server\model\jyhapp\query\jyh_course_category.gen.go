// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
)

func newJyhCourseCategory(db *gorm.DB, opts ...gen.DOOption) jyhCourseCategory {
	_jyhCourseCategory := jyhCourseCategory{}

	_jyhCourseCategory.jyhCourseCategoryDo.UseDB(db, opts...)
	_jyhCourseCategory.jyhCourseCategoryDo.UseModel(&jyhapp.JyhCourseCategory{})

	tableName := _jyhCourseCategory.jyhCourseCategoryDo.TableName()
	_jyhCourseCategory.ALL = field.NewAsterisk(tableName)
	_jyhCourseCategory.ID = field.NewUint(tableName, "id")
	_jyhCourseCategory.CreatedAt = field.NewTime(tableName, "created_at")
	_jyhCourseCategory.UpdatedAt = field.NewTime(tableName, "updated_at")
	_jyhCourseCategory.DeletedAt = field.NewField(tableName, "deleted_at")
	_jyhCourseCategory.Name = field.NewString(tableName, "name")
	_jyhCourseCategory.Sort = field.NewInt(tableName, "sort")
	_jyhCourseCategory.IsActive = field.NewBool(tableName, "is_active")
	_jyhCourseCategory.CatDesc = field.NewString(tableName, "cat_desc")

	_jyhCourseCategory.fillFieldMap()

	return _jyhCourseCategory
}

type jyhCourseCategory struct {
	jyhCourseCategoryDo

	ALL       field.Asterisk
	ID        field.Uint
	CreatedAt field.Time
	UpdatedAt field.Time
	DeletedAt field.Field
	Name      field.String
	Sort      field.Int
	IsActive  field.Bool
	CatDesc   field.String

	fieldMap map[string]field.Expr
}

func (j jyhCourseCategory) Table(newTableName string) *jyhCourseCategory {
	j.jyhCourseCategoryDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j jyhCourseCategory) As(alias string) *jyhCourseCategory {
	j.jyhCourseCategoryDo.DO = *(j.jyhCourseCategoryDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *jyhCourseCategory) updateTableName(table string) *jyhCourseCategory {
	j.ALL = field.NewAsterisk(table)
	j.ID = field.NewUint(table, "id")
	j.CreatedAt = field.NewTime(table, "created_at")
	j.UpdatedAt = field.NewTime(table, "updated_at")
	j.DeletedAt = field.NewField(table, "deleted_at")
	j.Name = field.NewString(table, "name")
	j.Sort = field.NewInt(table, "sort")
	j.IsActive = field.NewBool(table, "is_active")
	j.CatDesc = field.NewString(table, "cat_desc")

	j.fillFieldMap()

	return j
}

func (j *jyhCourseCategory) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *jyhCourseCategory) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 8)
	j.fieldMap["id"] = j.ID
	j.fieldMap["created_at"] = j.CreatedAt
	j.fieldMap["updated_at"] = j.UpdatedAt
	j.fieldMap["deleted_at"] = j.DeletedAt
	j.fieldMap["name"] = j.Name
	j.fieldMap["sort"] = j.Sort
	j.fieldMap["is_active"] = j.IsActive
	j.fieldMap["cat_desc"] = j.CatDesc
}

func (j jyhCourseCategory) clone(db *gorm.DB) jyhCourseCategory {
	j.jyhCourseCategoryDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j jyhCourseCategory) replaceDB(db *gorm.DB) jyhCourseCategory {
	j.jyhCourseCategoryDo.ReplaceDB(db)
	return j
}

type jyhCourseCategoryDo struct{ gen.DO }

func (j jyhCourseCategoryDo) Debug() *jyhCourseCategoryDo {
	return j.withDO(j.DO.Debug())
}

func (j jyhCourseCategoryDo) WithContext(ctx context.Context) *jyhCourseCategoryDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jyhCourseCategoryDo) ReadDB() *jyhCourseCategoryDo {
	return j.Clauses(dbresolver.Read)
}

func (j jyhCourseCategoryDo) WriteDB() *jyhCourseCategoryDo {
	return j.Clauses(dbresolver.Write)
}

func (j jyhCourseCategoryDo) Session(config *gorm.Session) *jyhCourseCategoryDo {
	return j.withDO(j.DO.Session(config))
}

func (j jyhCourseCategoryDo) Clauses(conds ...clause.Expression) *jyhCourseCategoryDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jyhCourseCategoryDo) Returning(value interface{}, columns ...string) *jyhCourseCategoryDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jyhCourseCategoryDo) Not(conds ...gen.Condition) *jyhCourseCategoryDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jyhCourseCategoryDo) Or(conds ...gen.Condition) *jyhCourseCategoryDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jyhCourseCategoryDo) Select(conds ...field.Expr) *jyhCourseCategoryDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jyhCourseCategoryDo) Where(conds ...gen.Condition) *jyhCourseCategoryDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jyhCourseCategoryDo) Order(conds ...field.Expr) *jyhCourseCategoryDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jyhCourseCategoryDo) Distinct(cols ...field.Expr) *jyhCourseCategoryDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jyhCourseCategoryDo) Omit(cols ...field.Expr) *jyhCourseCategoryDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jyhCourseCategoryDo) Join(table schema.Tabler, on ...field.Expr) *jyhCourseCategoryDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jyhCourseCategoryDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jyhCourseCategoryDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jyhCourseCategoryDo) RightJoin(table schema.Tabler, on ...field.Expr) *jyhCourseCategoryDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jyhCourseCategoryDo) Group(cols ...field.Expr) *jyhCourseCategoryDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jyhCourseCategoryDo) Having(conds ...gen.Condition) *jyhCourseCategoryDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jyhCourseCategoryDo) Limit(limit int) *jyhCourseCategoryDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jyhCourseCategoryDo) Offset(offset int) *jyhCourseCategoryDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jyhCourseCategoryDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jyhCourseCategoryDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jyhCourseCategoryDo) Unscoped() *jyhCourseCategoryDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jyhCourseCategoryDo) Create(values ...*jyhapp.JyhCourseCategory) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jyhCourseCategoryDo) CreateInBatches(values []*jyhapp.JyhCourseCategory, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jyhCourseCategoryDo) Save(values ...*jyhapp.JyhCourseCategory) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jyhCourseCategoryDo) First() (*jyhapp.JyhCourseCategory, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhCourseCategory), nil
	}
}

func (j jyhCourseCategoryDo) Take() (*jyhapp.JyhCourseCategory, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhCourseCategory), nil
	}
}

func (j jyhCourseCategoryDo) Last() (*jyhapp.JyhCourseCategory, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhCourseCategory), nil
	}
}

func (j jyhCourseCategoryDo) Find() ([]*jyhapp.JyhCourseCategory, error) {
	result, err := j.DO.Find()
	return result.([]*jyhapp.JyhCourseCategory), err
}

func (j jyhCourseCategoryDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*jyhapp.JyhCourseCategory, err error) {
	buf := make([]*jyhapp.JyhCourseCategory, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jyhCourseCategoryDo) FindInBatches(result *[]*jyhapp.JyhCourseCategory, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jyhCourseCategoryDo) Attrs(attrs ...field.AssignExpr) *jyhCourseCategoryDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jyhCourseCategoryDo) Assign(attrs ...field.AssignExpr) *jyhCourseCategoryDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jyhCourseCategoryDo) Joins(fields ...field.RelationField) *jyhCourseCategoryDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jyhCourseCategoryDo) Preload(fields ...field.RelationField) *jyhCourseCategoryDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jyhCourseCategoryDo) FirstOrInit() (*jyhapp.JyhCourseCategory, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhCourseCategory), nil
	}
}

func (j jyhCourseCategoryDo) FirstOrCreate() (*jyhapp.JyhCourseCategory, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhCourseCategory), nil
	}
}

func (j jyhCourseCategoryDo) FindByPage(offset int, limit int) (result []*jyhapp.JyhCourseCategory, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jyhCourseCategoryDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jyhCourseCategoryDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jyhCourseCategoryDo) Delete(models ...*jyhapp.JyhCourseCategory) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jyhCourseCategoryDo) withDO(do gen.Dao) *jyhCourseCategoryDo {
	j.DO = *do.(*gen.DO)
	return j
}
