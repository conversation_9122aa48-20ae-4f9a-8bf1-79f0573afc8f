package jyhapp

import (
	"time"
)

// JyhTag 标签表
type JyhTag struct {
	ID        uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	Name      string    `gorm:"type:varchar(50);unique;not null;comment:标签名称" json:"name"`
	Type      string    `gorm:"type:enum('system','custom');default:'system';comment:标签类型" json:"type"`
	CreatedAt time.Time `gorm:"not null" json:"createdAt"`
}

// TableName JyhTag 标签表名
func (JyhTag) TableName() string {
	return "jyh_tag"
}
