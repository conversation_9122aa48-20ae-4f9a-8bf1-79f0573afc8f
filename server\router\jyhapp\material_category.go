package jyhapp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type MaterialCategoryRouter struct {
}

// InitMaterialCategoryRouter 初始化 materialCategory 路由信息
func (s *MaterialCategoryRouter) InitMaterialCategoryRouter(Router *gin.RouterGroup, JyhRouter *gin.RouterGroup) {
	materialCategoryRouter := Router.Group("materialCategory").Use(middleware.OperationRecord())
	materialCategoryRouterWithoutRecord := Router.Group("materialCategory")
	var materialCategoryApi = v1.ApiGroupApp.JyhApiGroup.MaterialCategoryApi
	{
		// 素材分类相关
		materialCategoryRouter.POST("create", materialCategoryApi.Create)   // 新建素材分类
		materialCategoryRouter.DELETE("delete", materialCategoryApi.Delete) // 删除素材分类
		//materialCategoryRouter.DELETE("deleteByIds", materialCategoryApi.DeleteMaterialCategoryByIds) // 批量删除素材分类
		materialCategoryRouter.PUT("update", materialCategoryApi.Update) // 更新素材分类
	}
	{
		materialCategoryRouterWithoutRecord.GET("detail", materialCategoryApi.GetDetail)     // 根据ID获取素材分类
		materialCategoryRouterWithoutRecord.GET("list", materialCategoryApi.GetList)         // 获取素材分类列表
		materialCategoryRouterWithoutRecord.GET("treeList", materialCategoryApi.GetTreeList) // 获取素材分类列表
	}
	{
		materialCategoryRouterWithoutRecord.GET("getListByParentId", materialCategoryApi.GetListByParentId) // 根据父级ID获取所有素材分类列表
	}
}
