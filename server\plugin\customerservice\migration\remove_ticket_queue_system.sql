-- 客服系统重构：移除工单和排队管理系统
-- 执行时间：2025-01-16

-- ========================================
-- 第一部分：备份现有数据（可选）
-- ========================================

-- 备份工单数据（如果需要保留历史数据）
-- CREATE TABLE `sys_service_ticket_backup` AS SELECT * FROM `sys_service_ticket`;

-- 备份排队数据（如果需要保留历史数据）
-- CREATE TABLE `sys_service_queue_backup` AS SELECT * FROM `sys_service_queue`;

-- 备份转接数据中的工单关联信息
-- CREATE TABLE `sys_service_transfer_backup` AS SELECT * FROM `sys_service_transfer`;

-- ========================================
-- 第二部分：更新转接表结构
-- ========================================

-- 1. 添加新的session_id字段
ALTER TABLE `sys_service_transfer` 
ADD COLUMN `session_id` varchar(100) NOT NULL DEFAULT '' COMMENT '会话ID' AFTER `id`;

-- 2. 为session_id添加索引
CREATE INDEX `idx_transfer_session` ON `sys_service_transfer` (`session_id`);

-- 3. 更新现有转接记录的session_id（基于工单ID生成临时会话ID）
-- 注意：这是一个临时方案，实际部署时需要根据具体情况调整
UPDATE `sys_service_transfer` 
SET `session_id` = CONCAT('session_', `ticket_id`, '_', `user_id`) 
WHERE `session_id` = '' OR `session_id` IS NULL;

-- 4. 删除ticket_id字段（在确认数据迁移正确后执行）
-- ALTER TABLE `sys_service_transfer` DROP COLUMN `ticket_id`;

-- ========================================
-- 第三部分：删除工单和排队相关表
-- ========================================

-- 注意：在删除表之前，请确保已经备份了重要数据

-- 1. 删除工单相关的外键约束（如果存在）
-- ALTER TABLE `sys_service_transfer` DROP FOREIGN KEY IF EXISTS `fk_transfer_ticket`;
-- ALTER TABLE `sys_service_record` DROP FOREIGN KEY IF EXISTS `fk_record_ticket`;

-- 2. 删除工单表
DROP TABLE IF EXISTS `sys_service_ticket`;

-- 3. 删除排队表
DROP TABLE IF EXISTS `sys_service_queue`;

-- ========================================
-- 第四部分：更新相关表结构
-- ========================================

-- 1. 更新客服记录表，确保有session_id字段
ALTER TABLE `sys_service_record` 
ADD COLUMN IF NOT EXISTS `session_id` varchar(100) DEFAULT NULL COMMENT '会话ID' AFTER `id`;

-- 为session_id添加索引
CREATE INDEX IF NOT EXISTS `idx_record_session` ON `sys_service_record` (`session_id`);

-- 2. 更新现有记录的session_id（生成基于用户ID和客服ID的会话ID）
UPDATE `sys_service_record` 
SET `session_id` = CONCAT('chat_', `uid`, '_', `service_id`, '_', UNIX_TIMESTAMP(`add_time`))
WHERE `session_id` IS NULL OR `session_id` = '';

-- 3. 确保transfer_status字段存在
ALTER TABLE `sys_service_record` 
ADD COLUMN IF NOT EXISTS `transfer_status` varchar(50) DEFAULT 'normal' COMMENT '转接状态(normal:正常 transferring:转接中 transferred:已转接)' AFTER `session_id`;

-- ========================================
-- 第五部分：清理和优化
-- ========================================

-- 1. 删除不再需要的索引
DROP INDEX IF EXISTS `idx_transfer_ticket` ON `sys_service_transfer`;

-- 2. 重新创建优化的索引
CREATE INDEX IF NOT EXISTS `idx_transfer_session_status` ON `sys_service_transfer` (`session_id`, `status`);
CREATE INDEX IF NOT EXISTS `idx_transfer_user_status` ON `sys_service_transfer` (`user_id`, `status`);

-- 3. 更新转接统计视图（如果存在）
DROP VIEW IF EXISTS `v_transfer_statistics`;

CREATE VIEW `v_transfer_statistics` AS
SELECT 
    DATE(FROM_UNIXTIME(transfer_time)) as transfer_date,
    from_service_id,
    to_service_id,
    status,
    priority,
    transfer_type,
    COUNT(*) as transfer_count,
    AVG(CASE WHEN accept_time > 0 THEN accept_time - transfer_time ELSE NULL END) as avg_response_time
FROM `sys_service_transfer`
WHERE transfer_time > 0
GROUP BY transfer_date, from_service_id, to_service_id, status, priority, transfer_type;

-- ========================================
-- 第六部分：数据验证
-- ========================================

-- 验证转接表数据完整性
SELECT 
    COUNT(*) as total_transfers,
    COUNT(CASE WHEN session_id IS NOT NULL AND session_id != '' THEN 1 END) as valid_sessions,
    COUNT(CASE WHEN status = 1 THEN 1 END) as pending_transfers,
    COUNT(CASE WHEN status = 2 THEN 1 END) as accepted_transfers
FROM `sys_service_transfer`;

-- 验证客服记录表数据完整性
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN session_id IS NOT NULL AND session_id != '' THEN 1 END) as valid_sessions,
    COUNT(DISTINCT uid) as unique_users,
    COUNT(DISTINCT service_id) as unique_services
FROM `sys_service_record`;

-- ========================================
-- 第七部分：权限和安全
-- ========================================

-- 更新相关用户权限（根据实际需求调整）
-- REVOKE ALL ON `sys_service_ticket` FROM 'customerservice_user'@'%';
-- REVOKE ALL ON `sys_service_queue` FROM 'customerservice_user'@'%';

-- 确保转接表的权限正确
-- GRANT SELECT, INSERT, UPDATE ON `sys_service_transfer` TO 'customerservice_user'@'%';
-- GRANT SELECT, INSERT, UPDATE ON `sys_service_record` TO 'customerservice_user'@'%';

-- ========================================
-- 完成提示
-- ========================================

SELECT 'Customer service system refactoring completed successfully!' as message,
       'Ticket and queue management systems have been removed.' as details,
       'Transfer system has been updated to use session-based approach.' as changes;

-- ========================================
-- 回滚脚本（紧急情况使用）
-- ========================================

/*
-- 如果需要回滚，可以使用以下脚本：

-- 1. 恢复工单表
CREATE TABLE `sys_service_ticket` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `ticket_no` varchar(100) NOT NULL COMMENT '工单号',
  `user_id` int(11) unsigned NOT NULL COMMENT '用户ID',
  `service_id` bigint(20) NOT NULL COMMENT '客服ID',
  `priority` int(11) NOT NULL DEFAULT '3' COMMENT '优先级',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '状态',
  `user_tag_ids` text COMMENT '用户标签',
  `start_time` bigint(20) DEFAULT NULL COMMENT '开始时间',
  `end_time` bigint(20) DEFAULT NULL COMMENT '结束时间',
  `close_reason` varchar(500) DEFAULT NULL COMMENT '关闭原因',
  `summary` text COMMENT '服务总结',
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_ticket_no` (`ticket_no`),
  KEY `idx_ticket_user` (`user_id`),
  KEY `idx_ticket_service` (`service_id`),
  KEY `idx_ticket_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客服工单表';

-- 2. 恢复排队表
CREATE TABLE `sys_service_queue` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned NOT NULL COMMENT '用户ID',
  `service_id` bigint(20) DEFAULT NULL COMMENT '客服ID',
  `ticket_id` bigint(20) unsigned DEFAULT NULL COMMENT '工单ID',
  `user_tag_ids` text COMMENT '用户标签',
  `queue_no` bigint(20) NOT NULL COMMENT '排队号',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '状态',
  `priority` int(11) NOT NULL DEFAULT '3' COMMENT '优先级',
  `join_time` bigint(20) NOT NULL COMMENT '入队时间',
  `start_time` bigint(20) DEFAULT NULL COMMENT '开始服务时间',
  `end_time` bigint(20) DEFAULT NULL COMMENT '结束时间',
  `wait_time` bigint(20) DEFAULT NULL COMMENT '等待时间',
  `service_time` bigint(20) DEFAULT NULL COMMENT '服务时长',
  PRIMARY KEY (`id`),
  KEY `idx_queue_user` (`user_id`),
  KEY `idx_queue_service` (`service_id`),
  KEY `idx_queue_status` (`status`),
  KEY `idx_queue_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客服排队表';

-- 3. 恢复转接表的ticket_id字段
ALTER TABLE `sys_service_transfer` ADD COLUMN `ticket_id` bigint(20) unsigned DEFAULT NULL COMMENT '工单ID' AFTER `session_id`;

-- 4. 从备份表恢复数据
INSERT INTO `sys_service_ticket` SELECT * FROM `sys_service_ticket_backup`;
INSERT INTO `sys_service_queue` SELECT * FROM `sys_service_queue_backup`;
*/
