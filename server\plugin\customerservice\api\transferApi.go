package api

import (
	"fmt"
	"strconv"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/customerservice/service"
	"github.com/gin-gonic/gin"
)

type TransferApi struct{}

// RequestTransfer 发起转接申请
func (t *TransferApi) RequestTransfer(c *gin.Context) {
	var req struct {
		SessionID     uint   `json:"session_id" binding:"required"` // SysServiceRecord表的ID
		FromServiceID int64  `json:"from_service_id" binding:"required"`
		ToServiceID   int64  `json:"to_service_id" binding:"required"`
		UserID        uint   `json:"user_id" binding:"required"`
		Reason        string `json:"reason"`
		Priority      uint   `json:"priority"`      // 转接优先级，可选
		TransferType  uint   `json:"transfer_type"` // 转接类型，可选
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}

	// 设置默认值
	if req.Priority == 0 {
		req.Priority = 2 // 中等优先级
	}
	if req.TransferType == 0 {
		req.TransferType = 1 // 手动转接
	}

	transferService := &service.TransferService{}

	// 构建转接请求
	transferReq := &service.TransferRequest{
		SessionID:     req.SessionID,
		FromServiceID: req.FromServiceID,
		ToServiceID:   req.ToServiceID,
		UserID:        req.UserID,
		Reason:        req.Reason,
		Priority:      req.Priority,
		TransferType:  req.TransferType,
	}

	// 创建转接记录
	transfer, err := transferService.CreateTransferRequest(transferReq)
	if err != nil {
		response.FailWithMessage("创建转接申请失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(transfer, "转接申请已发送", c)
}

// AcceptTransfer 接受转接
func (t *TransferApi) AcceptTransfer(c *gin.Context) {
	var req struct {
		TransferID uint `json:"transfer_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}

	transferService := &service.TransferService{}
	err := transferService.AcceptTransfer(req.TransferID)
	if err != nil {
		response.FailWithMessage("接受转接失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("转接已接受", c)
}

// RejectTransfer 拒绝转接
func (t *TransferApi) RejectTransfer(c *gin.Context) {
	var req struct {
		TransferID uint `json:"transfer_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}

	transferService := &service.TransferService{}
	err := transferService.RejectTransfer(req.TransferID)
	if err != nil {
		response.FailWithMessage("拒绝转接失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("转接已拒绝", c)
}

// GetTransferList 获取转接记录列表
func (t *TransferApi) GetTransferList(c *gin.Context) {
	statusStr := c.Query("status")
	pageStr := c.Query("page")
	pageSizeStr := c.Query("page_size")

	var serviceIDFromContext, _ = c.Get("service_id")
	var status uint
	var page int = 1
	var pageSize int = 10
	var serviceID int64
	serviceIdStr := fmt.Sprintf("%v", serviceIDFromContext)
	if serviceIdStr != "" {
		serviceId, err := strconv.ParseInt(serviceIdStr, 10, 64)
		if err != nil {
			response.FailWithMessage("service_id参数错误", c)
			return
		}
		serviceID = serviceId
	}

	if statusStr != "" {
		statusInt, err := strconv.ParseUint(statusStr, 10, 32)
		if err != nil {
			response.FailWithMessage("status参数错误", c)
			return
		}
		status = uint(statusInt)
	}

	if pageStr != "" {
		pageInt, err := strconv.Atoi(pageStr)
		if err != nil {
			response.FailWithMessage("page参数错误", c)
			return
		}
		page = pageInt
	}

	if pageSizeStr != "" {
		pageSizeInt, err := strconv.Atoi(pageSizeStr)
		if err != nil {
			response.FailWithMessage("page_size参数错误", c)
			return
		}
		pageSize = pageSizeInt
	}

	transferService := &service.TransferService{}
	transfers, total, err := transferService.GetTransferList(serviceID, status, page, pageSize)

	if err != nil {
		response.FailWithMessage("获取转接记录失败: "+err.Error(), c)
		return
	}

	result := map[string]interface{}{
		"list":      transfers,
		"total":     total,
		"page":      page,
		"page_size": pageSize,
	}

	response.OkWithDetailed(result, "获取成功", c)
}

// GetPendingTransfers 获取待处理转接请求
func (t *TransferApi) GetPendingTransfers(c *gin.Context) {
	serviceIdStr := c.Query("service_id")
	if serviceIdStr == "" {
		response.FailWithMessage("service_id参数必填", c)
		return
	}

	serviceId, err := strconv.ParseInt(serviceIdStr, 10, 64)
	if err != nil {
		response.FailWithMessage("service_id参数错误", c)
		return
	}

	transferService := &service.TransferService{}
	transfers, err := transferService.GetPendingTransfers(serviceId)

	if err != nil {
		response.FailWithMessage("获取待处理转接失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(transfers, "获取成功", c)
}

// GetTransferDetail 获取转接详情
func (t *TransferApi) GetTransferDetail(c *gin.Context) {
	transferIdStr := c.Param("transfer_id")
	transferId, err := strconv.ParseUint(transferIdStr, 10, 32)
	if err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}

	transferService := &service.TransferService{}
	transfer, err := transferService.GetTransferDetail(uint(transferId))
	if err != nil {
		response.FailWithMessage("获取转接详情失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(transfer, "获取成功", c)
}

// GetAvailableServices 获取可转接的客服列表
func (t *TransferApi) GetAvailableServices(c *gin.Context) {
	currentServiceIdStr := c.Query("current_service_id")
	if currentServiceIdStr == "" {
		response.FailWithMessage("current_service_id参数必填", c)
		return
	}

	currentServiceId, err := strconv.ParseInt(currentServiceIdStr, 10, 64)
	if err != nil {
		response.FailWithMessage("current_service_id参数错误", c)
		return
	}

	// 获取标签ID列表
	tagIdsStr := c.Query("tag_ids")
	var tagIDs []uint
	if tagIdsStr != "" {
		// 解析标签ID列表，假设格式为 "1,2,3"
		// 这里简化处理，实际可能需要更复杂的解析
	}

	transferService := &service.TransferService{}
	services, err := transferService.GetAvailableServices(currentServiceId, tagIDs)

	if err != nil {
		response.FailWithMessage("获取可转接客服失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(services, "获取成功", c)
}

// GetTransferStatistics 获取转接统计信息
func (t *TransferApi) GetTransferStatistics(c *gin.Context) {
	serviceIdStr := c.Query("service_id")
	if serviceIdStr == "" {
		response.FailWithMessage("service_id参数必填", c)
		return
	}

	serviceId, err := strconv.ParseInt(serviceIdStr, 10, 64)
	if err != nil {
		response.FailWithMessage("service_id参数错误", c)
		return
	}

	transferService := &service.TransferService{}
	stats, err := transferService.GetTransferStatistics(serviceId)

	if err != nil {
		response.FailWithMessage("获取转接统计失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(stats, "获取成功", c)
}

// GetTransferHistory 获取转接历史
func (t *TransferApi) GetTransferHistory(c *gin.Context) {
	userIdStr := c.Query("user_id")
	if userIdStr == "" {
		response.FailWithMessage("user_id参数必填", c)
		return
	}

	userId, err := strconv.ParseUint(userIdStr, 10, 32)
	if err != nil {
		response.FailWithMessage("user_id参数错误", c)
		return
	}

	transferService := &service.TransferService{}
	transfers, err := transferService.GetTransferHistory(uint(userId))
	if err != nil {
		response.FailWithMessage("获取转接历史失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(transfers, "获取成功", c)
}

// CancelTransfer 取消转接
func (t *TransferApi) CancelTransfer(c *gin.Context) {
	var req struct {
		TransferID   uint   `json:"transfer_id" binding:"required"`
		CancelReason string `json:"cancel_reason" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}

	transferService := &service.TransferService{}
	err := transferService.CancelTransfer(req.TransferID, req.CancelReason)
	if err != nil {
		response.FailWithMessage("取消转接失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("转接已取消", c)
}

// GetTransfersByStatus 根据状态获取转接记录
func (t *TransferApi) GetTransfersByStatus(c *gin.Context) {
	statusStr := c.Param("status")
	serviceIdStr := c.Query("service_id")

	status, err := strconv.ParseUint(statusStr, 10, 32)
	if err != nil {
		response.FailWithMessage("状态参数错误", c)
		return
	}

	var serviceID int64
	if serviceIdStr != "" {
		serviceId, err := strconv.ParseInt(serviceIdStr, 10, 64)
		if err != nil {
			response.FailWithMessage("service_id参数错误", c)
			return
		}
		serviceID = serviceId
	}

	transferService := &service.TransferService{}

	// 根据状态和客服ID获取转接记录
	transfers, _, err := transferService.GetTransferList(serviceID, uint(status), 1, 100)
	if err != nil {
		response.FailWithMessage("获取转接记录失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(transfers, "获取成功", c)
}

// BatchTransfer 批量转接
func (t *TransferApi) BatchTransfer(c *gin.Context) {
	var req struct {
		SessionIDs    []uint `json:"session_ids" binding:"required"` // SysServiceRecord表的ID列表
		FromServiceID int64  `json:"from_service_id" binding:"required"`
		ToServiceID   int64  `json:"to_service_id" binding:"required"`
		Reason        string `json:"reason" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}

	transferService := &service.TransferService{}
	err := transferService.BatchTransfer(req.SessionIDs, req.FromServiceID, req.ToServiceID, req.Reason)
	if err != nil {
		response.FailWithMessage("批量转接失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("批量转接申请已发送", c)
}

// CleanupExpiredTransfers 清理过期转接请求（管理员接口）
func (t *TransferApi) CleanupExpiredTransfers(c *gin.Context) {
	transferService := &service.TransferService{}
	err := transferService.CleanupExpiredTransfers()
	if err != nil {
		response.FailWithMessage("清理过期转接失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("过期转接清理完成", c)
}

// GetTransferMetrics 获取转接指标统计
func (t *TransferApi) GetTransferMetrics(c *gin.Context) {
	serviceIdStr := c.Query("service_id")
	startTimeStr := c.Query("start_time")
	endTimeStr := c.Query("end_time")

	var serviceID int64
	if serviceIdStr != "" {
		serviceId, err := strconv.ParseInt(serviceIdStr, 10, 64)
		if err != nil {
			response.FailWithMessage("service_id参数错误", c)
			return
		}
		serviceID = serviceId
	}

	// 这里可以添加时间范围查询逻辑
	_ = startTimeStr
	_ = endTimeStr

	transferService := &service.TransferService{}
	stats, err := transferService.GetTransferStatistics(serviceID)
	if err != nil {
		response.FailWithMessage("获取转接指标失败: "+err.Error(), c)
		return
	}

	// 扩展统计信息
	extendedStats := map[string]interface{}{
		"basic_stats": stats,
		"timestamp":   time.Now().Unix(),
	}

	response.OkWithDetailed(extendedStats, "获取成功", c)
}
