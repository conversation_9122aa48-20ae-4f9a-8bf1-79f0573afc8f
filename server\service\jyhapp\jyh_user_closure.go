package jyhapp

import (
	"errors"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/query"
	"go.uber.org/zap"
)

type JyhUserClosureService struct{}

// JyhUserTree 用户树形结构
type JyhUserTree struct {
	jyhapp.JyhUser
	Children []*JyhUserTree `json:"children"`
}

// MaintainUserClosure 添加用户时维护闭包表
func (s *JyhUserClosureService) MaintainUserClosure(tx *query.Query, userID, parentID uint) error {
	var dbUserClosure = tx.JyhUserClosure
	// 1. 添加自身关系 (userID, userID, 0)
	selfRelation := jyhapp.JyhUserClosure{
		AncestorID:   userID,
		DescendantID: userID,
		Depth:        0,
	}
	if err := dbUserClosure.Create(&selfRelation); err != nil {
		return err
	}

	// 2. 如果用户有上级，添加与上级的层级关系
	if parentID > 0 {
		// 获取上级的所有祖先关系
		var parentRelations []jyhapp.JyhUserClosure
		if err := dbUserClosure.Where(dbUserClosure.DescendantID.Eq(parentID)).Scan(&parentRelations); err != nil {
			return err
		}

		// 为当前用户添加所有上级关系，深度+1
		newRelations := make([]*jyhapp.JyhUserClosure, 0, len(parentRelations))
		for _, rel := range parentRelations {
			newRelations = append(newRelations, &jyhapp.JyhUserClosure{
				AncestorID:   rel.AncestorID,
				DescendantID: userID,
				Depth:        rel.Depth + 1,
			})
		}

		if len(newRelations) > 0 {
			if err := dbUserClosure.CreateInBatches(newRelations, len(newRelations)); err != nil {
				return err
			}
		}
	}
	return nil
}

// GetUserDescendants 获取用户的所有下级用户
func (s *JyhUserClosureService) GetUserDescendants(userID uint) ([]jyhapp.JyhUser, error) {
	if userID == 0 {
		return nil, errors.New("无效的用户ID")
	}

	// 获取所有后代用户ID
	descendantIDs, err := s.getDescendantIDs(userID)
	if err != nil {
		global.GVA_LOG.Error("获取下级用户ID失败",
			zap.Uint("userID", userID),
			zap.Error(err))
		return nil, err
	}

	if len(descendantIDs) == 0 {
		return []jyhapp.JyhUser{}, nil
	}

	// 根据ID列表查询用户详情
	return s.getUsersByIDs(descendantIDs)
}

// 获取用户的所有后代用户ID
func (s *JyhUserClosureService) getDescendantIDs(userID uint) ([]uint, error) {
	var closureList []jyhapp.JyhUserClosure
	err := global.GVA_DB.
		Where("ancestor_id = ? AND depth > 0", userID).
		Find(&closureList).Error

	if err != nil {
		return nil, err
	}

	// 提取所有后代ID（去重）
	idMap := make(map[uint]bool)
	for _, closure := range closureList {
		idMap[closure.DescendantID] = true
	}

	ids := make([]uint, 0, len(idMap))
	for id := range idMap {
		ids = append(ids, id)
	}
	return ids, nil
}

// 根据用户ID列表查询用户详情
func (s *JyhUserClosureService) getUsersByIDs(ids []uint) ([]jyhapp.JyhUser, error) {
	var users []jyhapp.JyhUser
	err := global.GVA_DB.
		Where("id IN ?", ids).
		Find(&users).Error
	return users, err
}

// GetUserDescendantTree 获取用户的所有下级（树形结构）
func (s *JyhUserClosureService) GetUserDescendantTree(userID uint) ([]*JyhUserTree, error) {
	if userID == 0 {
		return nil, errors.New("无效的用户ID")
	}

	// 获取所有直接下级
	directDescendants, err := s.getDirectDescendants(userID)
	if err != nil {
		return nil, err
	}

	// 构建树形结构
	tree := make([]*JyhUserTree, 0, len(directDescendants))
	for _, user := range directDescendants {
		node := &JyhUserTree{
			JyhUser: *user,
		}
		// 递归获取下级
		children, err := s.GetUserDescendantTree(user.ID)
		if err != nil {
			return nil, err
		}
		node.Children = children
		tree = append(tree, node)
	}

	return tree, nil
}

// 获取直接下级
func (s *JyhUserClosureService) getDirectDescendants(userID uint) ([]*jyhapp.JyhUser, error) {
	var closureList []jyhapp.JyhUserClosure
	err := global.GVA_DB.
		Where("ancestor_id = ? AND depth = 1", userID).
		Find(&closureList).Error
	if err != nil {
		return nil, err
	}

	// 提取直接下级ID
	ids := make([]uint, 0, len(closureList))
	for _, closure := range closureList {
		ids = append(ids, closure.DescendantID)
	}

	if len(ids) == 0 {
		return []*jyhapp.JyhUser{}, nil
	}

	var users []*jyhapp.JyhUser
	err = global.GVA_DB.
		Where("id IN ?", ids).
		Find(&users).Error
	return users, err
}
