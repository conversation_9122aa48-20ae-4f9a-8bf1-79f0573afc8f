// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
)

func newJyhUserExt(db *gorm.DB, opts ...gen.DOOption) jyhUserExt {
	_jyhUserExt := jyhUserExt{}

	_jyhUserExt.jyhUserExtDo.UseDB(db, opts...)
	_jyhUserExt.jyhUserExtDo.UseModel(&jyhapp.JyhUserExt{})

	tableName := _jyhUserExt.jyhUserExtDo.TableName()
	_jyhUserExt.ALL = field.NewAsterisk(tableName)
	_jyhUserExt.UserID = field.NewUint(tableName, "user_id")
	_jyhUserExt.IdCard = field.NewString(tableName, "id_card")
	_jyhUserExt.Age = field.NewInt(tableName, "age")
	_jyhUserExt.Sex = field.NewString(tableName, "sex")
	_jyhUserExt.City = field.NewString(tableName, "city")
	_jyhUserExt.Occupation = field.NewString(tableName, "occupation")
	_jyhUserExt.Address = field.NewString(tableName, "address")
	_jyhUserExt.WeChat = field.NewString(tableName, "we_chat")
	_jyhUserExt.WeChatNickname = field.NewString(tableName, "we_chat_nickname")
	_jyhUserExt.DouYin = field.NewString(tableName, "dou_yin")
	_jyhUserExt.DouYinNickname = field.NewString(tableName, "dou_yin_nickname")
	_jyhUserExt.DouYinPhone = field.NewString(tableName, "dou_yin_phone")
	_jyhUserExt.QQ = field.NewString(tableName, "qq")
	_jyhUserExt.TrackType = field.NewInt(tableName, "track_type")
	_jyhUserExt.IsCarOwner = field.NewBool(tableName, "is_car_owner")
	_jyhUserExt.CarOwnerTime = field.NewTime(tableName, "car_owner_time")
	_jyhUserExt.FansCount = field.NewInt(tableName, "fans_count")
	_jyhUserExt.ValidFansCount = field.NewInt(tableName, "valid_fans_count")

	_jyhUserExt.fillFieldMap()

	return _jyhUserExt
}

type jyhUserExt struct {
	jyhUserExtDo

	ALL            field.Asterisk
	UserID         field.Uint
	IdCard         field.String
	Age            field.Int
	Sex            field.String
	City           field.String
	Occupation     field.String
	Address        field.String
	WeChat         field.String
	WeChatNickname field.String
	DouYin         field.String
	DouYinNickname field.String
	DouYinPhone    field.String
	QQ             field.String
	TrackType      field.Int
	IsCarOwner     field.Bool
	CarOwnerTime   field.Time
	FansCount      field.Int
	ValidFansCount field.Int

	fieldMap map[string]field.Expr
}

func (j jyhUserExt) Table(newTableName string) *jyhUserExt {
	j.jyhUserExtDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j jyhUserExt) As(alias string) *jyhUserExt {
	j.jyhUserExtDo.DO = *(j.jyhUserExtDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *jyhUserExt) updateTableName(table string) *jyhUserExt {
	j.ALL = field.NewAsterisk(table)
	j.UserID = field.NewUint(table, "user_id")
	j.IdCard = field.NewString(table, "id_card")
	j.Age = field.NewInt(table, "age")
	j.Sex = field.NewString(table, "sex")
	j.City = field.NewString(table, "city")
	j.Occupation = field.NewString(table, "occupation")
	j.Address = field.NewString(table, "address")
	j.WeChat = field.NewString(table, "we_chat")
	j.WeChatNickname = field.NewString(table, "we_chat_nickname")
	j.DouYin = field.NewString(table, "dou_yin")
	j.DouYinNickname = field.NewString(table, "dou_yin_nickname")
	j.DouYinPhone = field.NewString(table, "dou_yin_phone")
	j.QQ = field.NewString(table, "qq")
	j.TrackType = field.NewInt(table, "track_type")
	j.IsCarOwner = field.NewBool(table, "is_car_owner")
	j.CarOwnerTime = field.NewTime(table, "car_owner_time")
	j.FansCount = field.NewInt(table, "fans_count")
	j.ValidFansCount = field.NewInt(table, "valid_fans_count")

	j.fillFieldMap()

	return j
}

func (j *jyhUserExt) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *jyhUserExt) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 18)
	j.fieldMap["user_id"] = j.UserID
	j.fieldMap["id_card"] = j.IdCard
	j.fieldMap["age"] = j.Age
	j.fieldMap["sex"] = j.Sex
	j.fieldMap["city"] = j.City
	j.fieldMap["occupation"] = j.Occupation
	j.fieldMap["address"] = j.Address
	j.fieldMap["we_chat"] = j.WeChat
	j.fieldMap["we_chat_nickname"] = j.WeChatNickname
	j.fieldMap["dou_yin"] = j.DouYin
	j.fieldMap["dou_yin_nickname"] = j.DouYinNickname
	j.fieldMap["dou_yin_phone"] = j.DouYinPhone
	j.fieldMap["qq"] = j.QQ
	j.fieldMap["track_type"] = j.TrackType
	j.fieldMap["is_car_owner"] = j.IsCarOwner
	j.fieldMap["car_owner_time"] = j.CarOwnerTime
	j.fieldMap["fans_count"] = j.FansCount
	j.fieldMap["valid_fans_count"] = j.ValidFansCount
}

func (j jyhUserExt) clone(db *gorm.DB) jyhUserExt {
	j.jyhUserExtDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j jyhUserExt) replaceDB(db *gorm.DB) jyhUserExt {
	j.jyhUserExtDo.ReplaceDB(db)
	return j
}

type jyhUserExtDo struct{ gen.DO }

func (j jyhUserExtDo) Debug() *jyhUserExtDo {
	return j.withDO(j.DO.Debug())
}

func (j jyhUserExtDo) WithContext(ctx context.Context) *jyhUserExtDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jyhUserExtDo) ReadDB() *jyhUserExtDo {
	return j.Clauses(dbresolver.Read)
}

func (j jyhUserExtDo) WriteDB() *jyhUserExtDo {
	return j.Clauses(dbresolver.Write)
}

func (j jyhUserExtDo) Session(config *gorm.Session) *jyhUserExtDo {
	return j.withDO(j.DO.Session(config))
}

func (j jyhUserExtDo) Clauses(conds ...clause.Expression) *jyhUserExtDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jyhUserExtDo) Returning(value interface{}, columns ...string) *jyhUserExtDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jyhUserExtDo) Not(conds ...gen.Condition) *jyhUserExtDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jyhUserExtDo) Or(conds ...gen.Condition) *jyhUserExtDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jyhUserExtDo) Select(conds ...field.Expr) *jyhUserExtDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jyhUserExtDo) Where(conds ...gen.Condition) *jyhUserExtDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jyhUserExtDo) Order(conds ...field.Expr) *jyhUserExtDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jyhUserExtDo) Distinct(cols ...field.Expr) *jyhUserExtDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jyhUserExtDo) Omit(cols ...field.Expr) *jyhUserExtDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jyhUserExtDo) Join(table schema.Tabler, on ...field.Expr) *jyhUserExtDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jyhUserExtDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jyhUserExtDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jyhUserExtDo) RightJoin(table schema.Tabler, on ...field.Expr) *jyhUserExtDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jyhUserExtDo) Group(cols ...field.Expr) *jyhUserExtDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jyhUserExtDo) Having(conds ...gen.Condition) *jyhUserExtDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jyhUserExtDo) Limit(limit int) *jyhUserExtDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jyhUserExtDo) Offset(offset int) *jyhUserExtDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jyhUserExtDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jyhUserExtDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jyhUserExtDo) Unscoped() *jyhUserExtDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jyhUserExtDo) Create(values ...*jyhapp.JyhUserExt) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jyhUserExtDo) CreateInBatches(values []*jyhapp.JyhUserExt, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jyhUserExtDo) Save(values ...*jyhapp.JyhUserExt) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jyhUserExtDo) First() (*jyhapp.JyhUserExt, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserExt), nil
	}
}

func (j jyhUserExtDo) Take() (*jyhapp.JyhUserExt, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserExt), nil
	}
}

func (j jyhUserExtDo) Last() (*jyhapp.JyhUserExt, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserExt), nil
	}
}

func (j jyhUserExtDo) Find() ([]*jyhapp.JyhUserExt, error) {
	result, err := j.DO.Find()
	return result.([]*jyhapp.JyhUserExt), err
}

func (j jyhUserExtDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*jyhapp.JyhUserExt, err error) {
	buf := make([]*jyhapp.JyhUserExt, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jyhUserExtDo) FindInBatches(result *[]*jyhapp.JyhUserExt, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jyhUserExtDo) Attrs(attrs ...field.AssignExpr) *jyhUserExtDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jyhUserExtDo) Assign(attrs ...field.AssignExpr) *jyhUserExtDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jyhUserExtDo) Joins(fields ...field.RelationField) *jyhUserExtDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jyhUserExtDo) Preload(fields ...field.RelationField) *jyhUserExtDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jyhUserExtDo) FirstOrInit() (*jyhapp.JyhUserExt, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserExt), nil
	}
}

func (j jyhUserExtDo) FirstOrCreate() (*jyhapp.JyhUserExt, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserExt), nil
	}
}

func (j jyhUserExtDo) FindByPage(offset int, limit int) (result []*jyhapp.JyhUserExt, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jyhUserExtDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jyhUserExtDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jyhUserExtDo) Delete(models ...*jyhapp.JyhUserExt) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jyhUserExtDo) withDO(do gen.Dao) *jyhUserExtDo {
	j.DO = *do.(*gen.DO)
	return j
}
