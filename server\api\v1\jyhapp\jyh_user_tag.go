package jyhapp

import (
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	jyhReq "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/request"
	jyhResp "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type JyhUserTagApi struct{}

// CreateUserTag
// @Tags      JyhUserTag
// @Summary   创建用户标签
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      jyhReq.UserTagCreateReq  true  "用户标签信息"
// @Success   200   {object}  response.Response{msg=string}  "创建成功"
// @Router    /admin/user_tag/create [post]
func (a *JyhUserTagApi) CreateUserTag(c *gin.Context) {
	var req jyhReq.UserTagCreateReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := userTagService.CreateUserTag(&req); err != nil {
		global.GVA_LOG.Error("创建用户标签失败!", zap.Error(err))
		response.FailWithMessage("创建用户标签失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("创建成功", c)
}

// UpdateUserTag
// @Tags      JyhUserTag
// @Summary   更新用户标签
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      jyhReq.UserTagUpdateReq  true  "用户标签信息"
// @Success   200   {object}  response.Response{msg=string}  "更新成功"
// @Router    /admin/user_tag/update [post]
func (a *JyhUserTagApi) UpdateUserTag(c *gin.Context) {
	var req jyhReq.UserTagUpdateReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := userTagService.UpdateUserTag(&req); err != nil {
		global.GVA_LOG.Error("更新用户标签失败!", zap.Error(err))
		response.FailWithMessage("更新用户标签失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("更新成功", c)
}

// DeleteUserTag
// @Tags      JyhUserTag
// @Summary   删除用户标签
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     id   path      int  true  "标签ID"
// @Success   200  {object}  response.Response{msg=string}  "删除成功"
// @Router    /admin/user_tag/delete/{id} [delete]
func (a *JyhUserTagApi) DeleteUserTag(c *gin.Context) {
	id, _ := strconv.Atoi(c.Param("id"))

	if err := userTagService.DeleteUserTag(uint(id)); err != nil {
		global.GVA_LOG.Error("删除用户标签失败!", zap.Error(err))
		response.FailWithMessage("删除用户标签失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("删除成功", c)
}

// GetUserTagList
// @Tags      JyhUserTag
// @Summary   获取用户标签列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     jyhReq.UserTagListReq                                    true  "查询参数"
// @Success   200   {object}  response.Response{data=response.PageResult,msg=string}  "获取成功"
// @Router    /admin/user_tag/list [get]
func (a *JyhUserTagApi) GetUserTagList(c *gin.Context) {
	var req jyhReq.UserTagListReq
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	// 设置默认值
	if req.Page < 1 {
		req.Page = 1
	}
	if req.PageSize < 1 {
		req.PageSize = 10
	}

	list, total, err := userTagService.GetUserTagList(&req)
	if err != nil {
		global.GVA_LOG.Error("获取用户标签列表失败!", zap.Error(err))
		response.FailWithMessage("获取用户标签列表失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取成功", c)
}

// GetUserTagDetail
// @Tags      JyhUserTag
// @Summary   获取用户标签详情
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     id   path      int                                          true  "标签ID"
// @Success   200  {object}  response.Response{data=jyhResp.UserTagDetail,msg=string}  "获取成功"
// @Router    /admin/user_tag/detail/{id} [get]
func (a *JyhUserTagApi) GetUserTagDetail(c *gin.Context) {
	id, _ := strconv.Atoi(c.Param("id"))
	detail := &jyhResp.UserTagDetail{}
	detail, err := userTagService.GetUserTagDetail(uint(id))
	if err != nil {
		global.GVA_LOG.Error("获取用户标签详情失败!", zap.Error(err))
		response.FailWithMessage("获取用户标签详情失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(detail, "获取成功", c)
}

// BindUserTag
// @Tags      JyhUserTag
// @Summary   绑定用户标签
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      jyhReq.UserTagBindReq  true  "绑定信息"
// @Success   200   {object}  response.Response{msg=string}  "绑定成功"
// @Router    /admin/user_tag/bind [post]
func (a *JyhUserTagApi) BindUserTag(c *gin.Context) {
	var req jyhReq.UserTagBindReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := userTagService.BindUserTag(&req); err != nil {
		global.GVA_LOG.Error("绑定用户标签失败!", zap.Error(err))
		response.FailWithMessage("绑定用户标签失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("绑定成功", c)
}

// GetUserTags
// @Tags      JyhUserTag
// @Summary   获取用户的标签
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     userId  path      int                                           true  "用户ID"
// @Success   200     {object}  response.Response{data=jyhResp.UserWithTags,msg=string}  "获取成功"
// @Router    /admin/user_tag/userTags/{userId} [get]
func (a *JyhUserTagApi) GetUserTags(c *gin.Context) {
	id, _ := strconv.Atoi(c.Param("userId"))

	tags, err := userTagService.GetUserTags(uint(id))
	if err != nil {
		global.GVA_LOG.Error("获取用户标签失败!", zap.Error(err))
		response.FailWithMessage("获取用户标签失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(tags, "获取成功", c)
}
