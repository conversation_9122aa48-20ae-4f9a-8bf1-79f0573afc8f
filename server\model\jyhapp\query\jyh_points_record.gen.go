// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
)

func newJyhPointsRecord(db *gorm.DB, opts ...gen.DOOption) jyhPointsRecord {
	_jyhPointsRecord := jyhPointsRecord{}

	_jyhPointsRecord.jyhPointsRecordDo.UseDB(db, opts...)
	_jyhPointsRecord.jyhPointsRecordDo.UseModel(&jyhapp.JyhPointsRecord{})

	tableName := _jyhPointsRecord.jyhPointsRecordDo.TableName()
	_jyhPointsRecord.ALL = field.NewAsterisk(tableName)
	_jyhPointsRecord.ID = field.NewUint(tableName, "id")
	_jyhPointsRecord.UserID = field.NewUint(tableName, "user_id")
	_jyhPointsRecord.RuleID = field.NewUint(tableName, "rule_id")
	_jyhPointsRecord.Points = field.NewInt(tableName, "points")
	_jyhPointsRecord.Source = field.NewString(tableName, "source")
	_jyhPointsRecord.Remark = field.NewString(tableName, "remark")
	_jyhPointsRecord.CreatedAt = field.NewTime(tableName, "created_at")
	_jyhPointsRecord.User = jyhPointsRecordBelongsToUser{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("User", "jyhapp.JyhUser"),
		Inviter: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("User.Inviter", "jyhapp.JyhUser"),
		},
		JyhUserExt: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("User.JyhUserExt", "jyhapp.JyhUserExt"),
		},
		Invitees: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("User.Invitees", "jyhapp.JyhUser"),
		},
		Tags: struct {
			field.RelationField
			Users struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("User.Tags", "jyhapp.JyhUserTag"),
			Users: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("User.Tags.Users", "jyhapp.JyhUser"),
			},
		},
	}

	_jyhPointsRecord.Rule = jyhPointsRecordBelongsToRule{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Rule", "jyhapp.JyhPointsRule"),
	}

	_jyhPointsRecord.fillFieldMap()

	return _jyhPointsRecord
}

type jyhPointsRecord struct {
	jyhPointsRecordDo

	ALL       field.Asterisk
	ID        field.Uint
	UserID    field.Uint
	RuleID    field.Uint
	Points    field.Int
	Source    field.String
	Remark    field.String
	CreatedAt field.Time
	User      jyhPointsRecordBelongsToUser

	Rule jyhPointsRecordBelongsToRule

	fieldMap map[string]field.Expr
}

func (j jyhPointsRecord) Table(newTableName string) *jyhPointsRecord {
	j.jyhPointsRecordDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j jyhPointsRecord) As(alias string) *jyhPointsRecord {
	j.jyhPointsRecordDo.DO = *(j.jyhPointsRecordDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *jyhPointsRecord) updateTableName(table string) *jyhPointsRecord {
	j.ALL = field.NewAsterisk(table)
	j.ID = field.NewUint(table, "id")
	j.UserID = field.NewUint(table, "user_id")
	j.RuleID = field.NewUint(table, "rule_id")
	j.Points = field.NewInt(table, "points")
	j.Source = field.NewString(table, "source")
	j.Remark = field.NewString(table, "remark")
	j.CreatedAt = field.NewTime(table, "created_at")

	j.fillFieldMap()

	return j
}

func (j *jyhPointsRecord) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *jyhPointsRecord) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 9)
	j.fieldMap["id"] = j.ID
	j.fieldMap["user_id"] = j.UserID
	j.fieldMap["rule_id"] = j.RuleID
	j.fieldMap["points"] = j.Points
	j.fieldMap["source"] = j.Source
	j.fieldMap["remark"] = j.Remark
	j.fieldMap["created_at"] = j.CreatedAt

}

func (j jyhPointsRecord) clone(db *gorm.DB) jyhPointsRecord {
	j.jyhPointsRecordDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j jyhPointsRecord) replaceDB(db *gorm.DB) jyhPointsRecord {
	j.jyhPointsRecordDo.ReplaceDB(db)
	return j
}

type jyhPointsRecordBelongsToUser struct {
	db *gorm.DB

	field.RelationField

	Inviter struct {
		field.RelationField
	}
	JyhUserExt struct {
		field.RelationField
	}
	Invitees struct {
		field.RelationField
	}
	Tags struct {
		field.RelationField
		Users struct {
			field.RelationField
		}
	}
}

func (a jyhPointsRecordBelongsToUser) Where(conds ...field.Expr) *jyhPointsRecordBelongsToUser {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a jyhPointsRecordBelongsToUser) WithContext(ctx context.Context) *jyhPointsRecordBelongsToUser {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a jyhPointsRecordBelongsToUser) Session(session *gorm.Session) *jyhPointsRecordBelongsToUser {
	a.db = a.db.Session(session)
	return &a
}

func (a jyhPointsRecordBelongsToUser) Model(m *jyhapp.JyhPointsRecord) *jyhPointsRecordBelongsToUserTx {
	return &jyhPointsRecordBelongsToUserTx{a.db.Model(m).Association(a.Name())}
}

type jyhPointsRecordBelongsToUserTx struct{ tx *gorm.Association }

func (a jyhPointsRecordBelongsToUserTx) Find() (result *jyhapp.JyhUser, err error) {
	return result, a.tx.Find(&result)
}

func (a jyhPointsRecordBelongsToUserTx) Append(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a jyhPointsRecordBelongsToUserTx) Replace(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a jyhPointsRecordBelongsToUserTx) Delete(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a jyhPointsRecordBelongsToUserTx) Clear() error {
	return a.tx.Clear()
}

func (a jyhPointsRecordBelongsToUserTx) Count() int64 {
	return a.tx.Count()
}

type jyhPointsRecordBelongsToRule struct {
	db *gorm.DB

	field.RelationField
}

func (a jyhPointsRecordBelongsToRule) Where(conds ...field.Expr) *jyhPointsRecordBelongsToRule {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a jyhPointsRecordBelongsToRule) WithContext(ctx context.Context) *jyhPointsRecordBelongsToRule {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a jyhPointsRecordBelongsToRule) Session(session *gorm.Session) *jyhPointsRecordBelongsToRule {
	a.db = a.db.Session(session)
	return &a
}

func (a jyhPointsRecordBelongsToRule) Model(m *jyhapp.JyhPointsRecord) *jyhPointsRecordBelongsToRuleTx {
	return &jyhPointsRecordBelongsToRuleTx{a.db.Model(m).Association(a.Name())}
}

type jyhPointsRecordBelongsToRuleTx struct{ tx *gorm.Association }

func (a jyhPointsRecordBelongsToRuleTx) Find() (result *jyhapp.JyhPointsRule, err error) {
	return result, a.tx.Find(&result)
}

func (a jyhPointsRecordBelongsToRuleTx) Append(values ...*jyhapp.JyhPointsRule) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a jyhPointsRecordBelongsToRuleTx) Replace(values ...*jyhapp.JyhPointsRule) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a jyhPointsRecordBelongsToRuleTx) Delete(values ...*jyhapp.JyhPointsRule) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a jyhPointsRecordBelongsToRuleTx) Clear() error {
	return a.tx.Clear()
}

func (a jyhPointsRecordBelongsToRuleTx) Count() int64 {
	return a.tx.Count()
}

type jyhPointsRecordDo struct{ gen.DO }

func (j jyhPointsRecordDo) Debug() *jyhPointsRecordDo {
	return j.withDO(j.DO.Debug())
}

func (j jyhPointsRecordDo) WithContext(ctx context.Context) *jyhPointsRecordDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jyhPointsRecordDo) ReadDB() *jyhPointsRecordDo {
	return j.Clauses(dbresolver.Read)
}

func (j jyhPointsRecordDo) WriteDB() *jyhPointsRecordDo {
	return j.Clauses(dbresolver.Write)
}

func (j jyhPointsRecordDo) Session(config *gorm.Session) *jyhPointsRecordDo {
	return j.withDO(j.DO.Session(config))
}

func (j jyhPointsRecordDo) Clauses(conds ...clause.Expression) *jyhPointsRecordDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jyhPointsRecordDo) Returning(value interface{}, columns ...string) *jyhPointsRecordDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jyhPointsRecordDo) Not(conds ...gen.Condition) *jyhPointsRecordDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jyhPointsRecordDo) Or(conds ...gen.Condition) *jyhPointsRecordDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jyhPointsRecordDo) Select(conds ...field.Expr) *jyhPointsRecordDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jyhPointsRecordDo) Where(conds ...gen.Condition) *jyhPointsRecordDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jyhPointsRecordDo) Order(conds ...field.Expr) *jyhPointsRecordDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jyhPointsRecordDo) Distinct(cols ...field.Expr) *jyhPointsRecordDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jyhPointsRecordDo) Omit(cols ...field.Expr) *jyhPointsRecordDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jyhPointsRecordDo) Join(table schema.Tabler, on ...field.Expr) *jyhPointsRecordDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jyhPointsRecordDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jyhPointsRecordDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jyhPointsRecordDo) RightJoin(table schema.Tabler, on ...field.Expr) *jyhPointsRecordDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jyhPointsRecordDo) Group(cols ...field.Expr) *jyhPointsRecordDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jyhPointsRecordDo) Having(conds ...gen.Condition) *jyhPointsRecordDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jyhPointsRecordDo) Limit(limit int) *jyhPointsRecordDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jyhPointsRecordDo) Offset(offset int) *jyhPointsRecordDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jyhPointsRecordDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jyhPointsRecordDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jyhPointsRecordDo) Unscoped() *jyhPointsRecordDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jyhPointsRecordDo) Create(values ...*jyhapp.JyhPointsRecord) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jyhPointsRecordDo) CreateInBatches(values []*jyhapp.JyhPointsRecord, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jyhPointsRecordDo) Save(values ...*jyhapp.JyhPointsRecord) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jyhPointsRecordDo) First() (*jyhapp.JyhPointsRecord, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhPointsRecord), nil
	}
}

func (j jyhPointsRecordDo) Take() (*jyhapp.JyhPointsRecord, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhPointsRecord), nil
	}
}

func (j jyhPointsRecordDo) Last() (*jyhapp.JyhPointsRecord, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhPointsRecord), nil
	}
}

func (j jyhPointsRecordDo) Find() ([]*jyhapp.JyhPointsRecord, error) {
	result, err := j.DO.Find()
	return result.([]*jyhapp.JyhPointsRecord), err
}

func (j jyhPointsRecordDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*jyhapp.JyhPointsRecord, err error) {
	buf := make([]*jyhapp.JyhPointsRecord, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jyhPointsRecordDo) FindInBatches(result *[]*jyhapp.JyhPointsRecord, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jyhPointsRecordDo) Attrs(attrs ...field.AssignExpr) *jyhPointsRecordDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jyhPointsRecordDo) Assign(attrs ...field.AssignExpr) *jyhPointsRecordDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jyhPointsRecordDo) Joins(fields ...field.RelationField) *jyhPointsRecordDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jyhPointsRecordDo) Preload(fields ...field.RelationField) *jyhPointsRecordDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jyhPointsRecordDo) FirstOrInit() (*jyhapp.JyhPointsRecord, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhPointsRecord), nil
	}
}

func (j jyhPointsRecordDo) FirstOrCreate() (*jyhapp.JyhPointsRecord, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhPointsRecord), nil
	}
}

func (j jyhPointsRecordDo) FindByPage(offset int, limit int) (result []*jyhapp.JyhPointsRecord, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jyhPointsRecordDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jyhPointsRecordDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jyhPointsRecordDo) Delete(models ...*jyhapp.JyhPointsRecord) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jyhPointsRecordDo) withDO(do gen.Dao) *jyhPointsRecordDo {
	j.DO = *do.(*gen.DO)
	return j
}
