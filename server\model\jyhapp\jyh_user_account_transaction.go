package jyhapp

import (
	"time"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// 交易类型常量
const (
	TransactionTypeCommissionIncome = "COMMISSION_INCOME" // 佣金收入
	TransactionTypeWithdrawal       = "WITHDRAWAL"        // 提现
	TransactionTypeRefund           = "REFUND"            // 退款
	TransactionTypeAdjustment       = "ADJUSTMENT"        // 调整
)

// 资金来源/影响项常量
const (
	SourceTypeDy            = "dy"  // 抖音佣金
	SourceTypeJyh           = "jyh" // jyh佣金
	SourceTypeWithdrawalOut = "out" // 提现支出
)

// 交易状态常量
const (
	TransactionStatusPending  = "PENDING"  // 待处理
	TransactionStatusSuccess  = "SUCCESS"  // 成功
	TransactionStatusFailed   = "FAILED"   // 失败
	TransactionStatusRollback = "ROLLBACK" // 已回滚
)

// 变动类型常量
const (
	ChangeTypeIncrease = 1 // 增加
	ChangeTypeDecrease = 2 // 减少
)

// TransactionTypeName 获取交易类型名称
func TransactionTypeName(transactionType string) string {
	switch transactionType {
	case TransactionTypeCommissionIncome:
		return "佣金收入"
	case TransactionTypeWithdrawal:
		return "提现"
	case TransactionTypeRefund:
		return "退款"
	case TransactionTypeAdjustment:
		return "调整"
	default:
		return transactionType
	}
}

// SourceTypeName 获取资金来源名称
func SourceTypeName(sourceType string) string {
	switch sourceType {
	case SourceTypeDy:
		return "抖音佣金"
	case SourceTypeJyh:
		return "JYH佣金"
	case SourceTypeWithdrawalOut:
		return "提现支出"
	default:
		return sourceType
	}
}

// TransactionStatusName 获取交易状态名称
func TransactionStatusName(status string) string {
	switch status {
	case TransactionStatusPending:
		return "待处理"
	case TransactionStatusSuccess:
		return "成功"
	case TransactionStatusFailed:
		return "失败"
	case TransactionStatusRollback:
		return "已回滚"
	default:
		return status
	}
}

// ChangeTypeName 获取变动类型名称
func ChangeTypeName(changeType int) string {
	switch changeType {
	case ChangeTypeIncrease:
		return "增加"
	case ChangeTypeDecrease:
		return "减少"
	default:
		return ""
	}
}

// GetChangeTypeBySource 根据资金来源获取变动类型
func GetChangeTypeBySource(sourceType string) int {
	if sourceType == SourceTypeWithdrawalOut {
		return ChangeTypeDecrease // 提现支出是减少
	}
	return ChangeTypeIncrease // 其他都是增加
}

// JyhUserAccountTransaction 用户账户交易流水表 - 记录所有账户余额变动
type JyhUserAccountTransaction struct {
	TransactionID          uint            `json:"transaction_id" gorm:"primaryKey;autoIncrement;comment:交易唯一ID"`
	AccountID              uint            `json:"account_id" gorm:"not null;index;comment:关联的用户账户ID"`
	Amount                 decimal.Decimal `json:"amount" gorm:"type:decimal(18,2);not null;comment:交易金额(正数表示变动金额)"`
	TransactionType        string          `json:"transaction_type" gorm:"type:varchar(50);not null;index;comment:交易类型"`
	SourceType             string          `json:"source_type" gorm:"type:varchar(50);not null;index;comment:资金来源/影响项"`
	ChangeType             int             `json:"change_type" gorm:"type:int(4);not null;index;comment:变动类型(1增加/2减少)"`
	RelatedBusinessID      *uint           `json:"related_business_id" gorm:"index;comment:相关业务ID"`
	TransactionDescription string          `json:"transaction_description" gorm:"type:varchar(255);comment:交易描述"`
	TransactionStatus      string          `json:"transaction_status" gorm:"type:varchar(20);not null;default:'PENDING';index;comment:交易状态"`
	CreatedAt              time.Time       `json:"created_at" gorm:"index;comment:交易创建时间"`
	CompletedAt            *time.Time      `json:"completed_at" gorm:"comment:交易完成/更新时间"`
	BalanceBefore          decimal.Decimal `json:"balance_before" gorm:"type:decimal(18,2);not null;comment:交易前账户总余额"`
	BalanceAfter           decimal.Decimal `json:"balance_after" gorm:"type:decimal(18,2);not null;comment:交易后账户总余额"`

	// 关联关系
	Account *JyhUserAccount `json:"account,omitempty" gorm:"foreignKey:AccountID;references:AccountID"`
}

// TableName 指定表名
func (JyhUserAccountTransaction) TableName() string {
	return "jyh_user_account_transactions"
}

// BeforeCreate GORM钩子 - 创建前设置默认值
func (uat *JyhUserAccountTransaction) BeforeCreate(tx *gorm.DB) error {
	if uat.TransactionStatus == "" {
		uat.TransactionStatus = TransactionStatusPending
	}

	// 自动设置变动类型
	if uat.ChangeType == 0 {
		uat.ChangeType = GetChangeTypeBySource(uat.SourceType)
	}

	return nil
}

// MarkAsSuccess 标记交易为成功
func (uat *JyhUserAccountTransaction) MarkAsSuccess() {
	uat.TransactionStatus = TransactionStatusSuccess
	now := time.Now()
	uat.CompletedAt = &now
}

// MarkAsFailed 标记交易为失败
func (uat *JyhUserAccountTransaction) MarkAsFailed() {
	uat.TransactionStatus = TransactionStatusFailed
	now := time.Now()
	uat.CompletedAt = &now
}

// MarkAsRollback 标记交易为已回滚
func (uat *JyhUserAccountTransaction) MarkAsRollback() {
	uat.TransactionStatus = TransactionStatusRollback
	now := time.Now()
	uat.CompletedAt = &now
}

// IsSuccessful 检查交易是否成功
func (uat *JyhUserAccountTransaction) IsSuccessful() bool {
	return uat.TransactionStatus == TransactionStatusSuccess
}

// IsPending 检查交易是否待处理
func (uat *JyhUserAccountTransaction) IsPending() bool {
	return uat.TransactionStatus == TransactionStatusPending
}

// IsFailed 检查交易是否失败
func (uat *JyhUserAccountTransaction) IsFailed() bool {
	return uat.TransactionStatus == TransactionStatusFailed
}

// IsRollback 检查交易是否已回滚
func (uat *JyhUserAccountTransaction) IsRollback() bool {
	return uat.TransactionStatus == TransactionStatusRollback
}

// IsIncrease 检查交易是否为增加类型
func (uat *JyhUserAccountTransaction) IsIncrease() bool {
	return uat.ChangeType == ChangeTypeIncrease
}

// IsDecrease 检查交易是否为减少类型
func (uat *JyhUserAccountTransaction) IsDecrease() bool {
	return uat.ChangeType == ChangeTypeDecrease
}

// GetSignedAmount 获取有符号的交易金额（增加为正数，减少为负数）
func (uat *JyhUserAccountTransaction) GetSignedAmount() decimal.Decimal {
	if uat.IsDecrease() {
		return uat.Amount.Neg() // 减少类型返回负数
	}
	return uat.Amount // 增加类型返回正数
}

// ValidateBalanceChange 验证余额变动是否正确
func (uat *JyhUserAccountTransaction) ValidateBalanceChange() bool {
	expected := uat.BalanceBefore

	// 根据变动类型计算预期的余额变化
	if uat.IsIncrease() {
		expected = expected.Add(uat.Amount)
	} else {
		expected = expected.Sub(uat.Amount)
	}

	return uat.BalanceAfter.Equal(expected)
}
