package jyhapp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/errno"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
	systemReq "github.com/flipped-aurora/gin-vue-admin/server/model/system/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"go.uber.org/zap"
)

func authGenJwtToken(user *jyhapp.JyhUser) (token string, expiresAt int64, err error) {
	j := &utils.JWT{SigningKey: []byte(global.GVA_CONFIG.JWT.SigningKey)} // 唯一签名
	claims := j.CreateClaims(systemReq.BaseClaims{
		ID:       user.ID,
		NickName: user.Username,
		Username: user.Username,
		//AuthorityId: user.AuthorityId,
	})
	token, err = j.CreateToken(claims)
	if err != nil {
		global.GVA_LOG.Error("获取token失败!", zap.Error(err))
		err = errno.InternalServerError.CombineErrorMsg("获取token失败!", err)
		return
	}
	expiresAt = claims.RegisteredClaims.ExpiresAt.Unix() * 1000
	/*if global.GVA_CONFIG.System.UseMultipoint {
		if jwtStr, e1 := jwtService.GetRedisJWT(user.Username); e1 == redis.Nil {
			if e2 := jwtService.SetRedisJWT(token, user.Username); e2 != nil {
				global.GVA_LOG.Error("设置登录状态失败!", zap.Error(e2))
				return "", 0, errno.InternalServerError.CombineErrorMsg("设置登录状态失败!", e2)
			}
		} else if e1 != nil {
			global.GVA_LOG.Error("设置登录状态失败!", zap.Error(e1))
			return "", 0, errno.InternalServerError.CombineErrorMsg("设置登录状态失败", e1)
		} else {
			var blackJWT system.JwtBlacklist
			blackJWT.Jwt = jwtStr
			if e2 := jwtService.JsonInBlacklist(blackJWT); e2 != nil {
				return "", 0, errno.InternalServerError.CombineErrorMsg("jwt作废失败", e2)
			}
			if e2 := jwtService.SetRedisJWT(token, user.Username); e2 != nil {
				return "", 0, errno.InternalServerError.CombineErrorMsg("设置登录状态失败", e2)
			}
		}
	}*/
	return
}
