// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
)

func newJyhUserLevel(db *gorm.DB, opts ...gen.DOOption) jyhUserLevel {
	_jyhUserLevel := jyhUserLevel{}

	_jyhUserLevel.jyhUserLevelDo.UseDB(db, opts...)
	_jyhUserLevel.jyhUserLevelDo.UseModel(&jyhapp.JyhUserLevel{})

	tableName := _jyhUserLevel.jyhUserLevelDo.TableName()
	_jyhUserLevel.ALL = field.NewAsterisk(tableName)
	_jyhUserLevel.ID = field.NewUint(tableName, "id")
	_jyhUserLevel.CreatedAt = field.NewTime(tableName, "created_at")
	_jyhUserLevel.UpdatedAt = field.NewTime(tableName, "updated_at")
	_jyhUserLevel.UserID = field.NewUint(tableName, "user_id")
	_jyhUserLevel.LevelID = field.NewUint(tableName, "level_id")
	_jyhUserLevel.OrderID = field.NewUint(tableName, "order_id")
	_jyhUserLevel.StartAt = field.NewTime(tableName, "start_at")
	_jyhUserLevel.EndAt = field.NewTime(tableName, "end_at")
	_jyhUserLevel.Status = field.NewString(tableName, "status")

	_jyhUserLevel.fillFieldMap()

	return _jyhUserLevel
}

type jyhUserLevel struct {
	jyhUserLevelDo

	ALL       field.Asterisk
	ID        field.Uint
	CreatedAt field.Time
	UpdatedAt field.Time
	UserID    field.Uint
	LevelID   field.Uint
	OrderID   field.Uint
	StartAt   field.Time
	EndAt     field.Time
	Status    field.String

	fieldMap map[string]field.Expr
}

func (j jyhUserLevel) Table(newTableName string) *jyhUserLevel {
	j.jyhUserLevelDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j jyhUserLevel) As(alias string) *jyhUserLevel {
	j.jyhUserLevelDo.DO = *(j.jyhUserLevelDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *jyhUserLevel) updateTableName(table string) *jyhUserLevel {
	j.ALL = field.NewAsterisk(table)
	j.ID = field.NewUint(table, "id")
	j.CreatedAt = field.NewTime(table, "created_at")
	j.UpdatedAt = field.NewTime(table, "updated_at")
	j.UserID = field.NewUint(table, "user_id")
	j.LevelID = field.NewUint(table, "level_id")
	j.OrderID = field.NewUint(table, "order_id")
	j.StartAt = field.NewTime(table, "start_at")
	j.EndAt = field.NewTime(table, "end_at")
	j.Status = field.NewString(table, "status")

	j.fillFieldMap()

	return j
}

func (j *jyhUserLevel) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *jyhUserLevel) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 9)
	j.fieldMap["id"] = j.ID
	j.fieldMap["created_at"] = j.CreatedAt
	j.fieldMap["updated_at"] = j.UpdatedAt
	j.fieldMap["user_id"] = j.UserID
	j.fieldMap["level_id"] = j.LevelID
	j.fieldMap["order_id"] = j.OrderID
	j.fieldMap["start_at"] = j.StartAt
	j.fieldMap["end_at"] = j.EndAt
	j.fieldMap["status"] = j.Status
}

func (j jyhUserLevel) clone(db *gorm.DB) jyhUserLevel {
	j.jyhUserLevelDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j jyhUserLevel) replaceDB(db *gorm.DB) jyhUserLevel {
	j.jyhUserLevelDo.ReplaceDB(db)
	return j
}

type jyhUserLevelDo struct{ gen.DO }

func (j jyhUserLevelDo) Debug() *jyhUserLevelDo {
	return j.withDO(j.DO.Debug())
}

func (j jyhUserLevelDo) WithContext(ctx context.Context) *jyhUserLevelDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jyhUserLevelDo) ReadDB() *jyhUserLevelDo {
	return j.Clauses(dbresolver.Read)
}

func (j jyhUserLevelDo) WriteDB() *jyhUserLevelDo {
	return j.Clauses(dbresolver.Write)
}

func (j jyhUserLevelDo) Session(config *gorm.Session) *jyhUserLevelDo {
	return j.withDO(j.DO.Session(config))
}

func (j jyhUserLevelDo) Clauses(conds ...clause.Expression) *jyhUserLevelDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jyhUserLevelDo) Returning(value interface{}, columns ...string) *jyhUserLevelDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jyhUserLevelDo) Not(conds ...gen.Condition) *jyhUserLevelDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jyhUserLevelDo) Or(conds ...gen.Condition) *jyhUserLevelDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jyhUserLevelDo) Select(conds ...field.Expr) *jyhUserLevelDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jyhUserLevelDo) Where(conds ...gen.Condition) *jyhUserLevelDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jyhUserLevelDo) Order(conds ...field.Expr) *jyhUserLevelDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jyhUserLevelDo) Distinct(cols ...field.Expr) *jyhUserLevelDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jyhUserLevelDo) Omit(cols ...field.Expr) *jyhUserLevelDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jyhUserLevelDo) Join(table schema.Tabler, on ...field.Expr) *jyhUserLevelDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jyhUserLevelDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jyhUserLevelDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jyhUserLevelDo) RightJoin(table schema.Tabler, on ...field.Expr) *jyhUserLevelDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jyhUserLevelDo) Group(cols ...field.Expr) *jyhUserLevelDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jyhUserLevelDo) Having(conds ...gen.Condition) *jyhUserLevelDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jyhUserLevelDo) Limit(limit int) *jyhUserLevelDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jyhUserLevelDo) Offset(offset int) *jyhUserLevelDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jyhUserLevelDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jyhUserLevelDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jyhUserLevelDo) Unscoped() *jyhUserLevelDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jyhUserLevelDo) Create(values ...*jyhapp.JyhUserLevel) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jyhUserLevelDo) CreateInBatches(values []*jyhapp.JyhUserLevel, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jyhUserLevelDo) Save(values ...*jyhapp.JyhUserLevel) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jyhUserLevelDo) First() (*jyhapp.JyhUserLevel, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserLevel), nil
	}
}

func (j jyhUserLevelDo) Take() (*jyhapp.JyhUserLevel, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserLevel), nil
	}
}

func (j jyhUserLevelDo) Last() (*jyhapp.JyhUserLevel, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserLevel), nil
	}
}

func (j jyhUserLevelDo) Find() ([]*jyhapp.JyhUserLevel, error) {
	result, err := j.DO.Find()
	return result.([]*jyhapp.JyhUserLevel), err
}

func (j jyhUserLevelDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*jyhapp.JyhUserLevel, err error) {
	buf := make([]*jyhapp.JyhUserLevel, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jyhUserLevelDo) FindInBatches(result *[]*jyhapp.JyhUserLevel, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jyhUserLevelDo) Attrs(attrs ...field.AssignExpr) *jyhUserLevelDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jyhUserLevelDo) Assign(attrs ...field.AssignExpr) *jyhUserLevelDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jyhUserLevelDo) Joins(fields ...field.RelationField) *jyhUserLevelDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jyhUserLevelDo) Preload(fields ...field.RelationField) *jyhUserLevelDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jyhUserLevelDo) FirstOrInit() (*jyhapp.JyhUserLevel, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserLevel), nil
	}
}

func (j jyhUserLevelDo) FirstOrCreate() (*jyhapp.JyhUserLevel, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserLevel), nil
	}
}

func (j jyhUserLevelDo) FindByPage(offset int, limit int) (result []*jyhapp.JyhUserLevel, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jyhUserLevelDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jyhUserLevelDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jyhUserLevelDo) Delete(models ...*jyhapp.JyhUserLevel) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jyhUserLevelDo) withDO(do gen.Dao) *jyhUserLevelDo {
	j.DO = *do.(*gen.DO)
	return j
}
