package response

type CourseCategoryTree struct {
	ID       uint                  `json:"id"`       // 分类ID
	Name     string                `json:"name"`     // 分类名称
	ParentID uint                  `json:"parentId"` // 父级ID
	Sort     int                   `json:"sort"`     // 排序
	IsActive bool                  `json:"isActive"` // 状态
	CatDesc  string                `json:"catDesc"`  // 描述
	Children []*CourseCategoryTree `json:"children"` // 子级
}
