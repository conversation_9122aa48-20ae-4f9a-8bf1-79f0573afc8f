package service

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/customerservice/model"
	"gorm.io/gorm"
)

type TransferService struct{}

// CreateTransferRequest 创建转接请求
func (s *TransferService) CreateTransferRequest(req *TransferRequest) (*model.SysServiceTransfer, error) {
	// 验证转接请求
	if err := s.validateTransferRequest(req); err != nil {
		return nil, err
	}

	// 获取用户标签
	userTags, _ := s.getUserTags(req.UserID)
	userTagsJSON, _ := json.Marshal(userTags)

	// 获取客服信息
	fromServiceInfo, _ := s.getServiceInfo(req.FromServiceID)
	toServiceInfo, _ := s.getServiceInfo(req.ToServiceID)
	fromServiceJSON, _ := json.Marshal(fromServiceInfo)
	toServiceJSON, _ := json.Marshal(toServiceInfo)

	// 计算过期时间
	expireTime := time.Now().Add(time.Duration(model.TransferExpireTime) * time.Second).Unix()

	transfer := &model.SysServiceTransfer{
		SessionID:       req.SessionID,
		FromServiceID:   req.FromServiceID,
		ToServiceID:     req.ToServiceID,
		UserID:          req.UserID,
		Reason:          req.Reason,
		Status:          model.TransferRecordStatusPending,
		TransferTime:    time.Now().Unix(),
		Priority:        req.Priority,
		TransferType:    req.TransferType,
		ExpireTime:      expireTime,
		UserTags:        string(userTagsJSON),
		FromServiceInfo: string(fromServiceJSON),
		ToServiceInfo:   string(toServiceJSON),
	}

	err := global.GVA_DB.Create(transfer).Error
	if err != nil {
		return nil, err
	}

	// 更新会话记录中的客服分配状态
	s.updateSessionServiceStatus(req.SessionID, req.FromServiceID, "transferring")

	// 发送WebSocket通知
	s.notifyTransferRequest(transfer)

	return transfer, nil
}

// TransferRequest 转接请求结构
type TransferRequest struct {
	SessionID     uint   `json:"session_id" binding:"required"` // SysServiceRecord表的ID
	FromServiceID int64  `json:"from_service_id" binding:"required"`
	ToServiceID   int64  `json:"to_service_id" binding:"required"`
	UserID        uint   `json:"user_id" binding:"required"`
	Reason        string `json:"reason" binding:"required"`
	Priority      uint   `json:"priority"`
	TransferType  uint   `json:"transfer_type"`
}

// AcceptTransfer 接受转接
func (s *TransferService) AcceptTransfer(transferID uint) error {
	var transfer model.SysServiceTransfer
	err := global.GVA_DB.Where("id = ?", transferID).First(&transfer).Error
	if err != nil {
		return err
	}

	// 检查转接状态
	if transfer.Status != model.TransferRecordStatusPending {
		return fmt.Errorf("转接请求状态不正确")
	}

	// 检查是否已过期
	if time.Now().Unix() > transfer.ExpireTime {
		// 自动设置为过期状态
		s.expireTransfer(transferID)
		return fmt.Errorf("转接请求已过期")
	}

	// 再次检查目标客服是否可以接收
	canTransfer, msg := s.CanTransfer(transfer.FromServiceID, transfer.ToServiceID)
	if !canTransfer {
		return fmt.Errorf("无法接受转接: %s", msg)
	}

	// 使用事务处理转接
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 更新转接状态
		updates := map[string]interface{}{
			"status":      model.TransferRecordStatusAccepted,
			"accept_time": time.Now().Unix(),
		}

		err = tx.Model(&model.SysServiceTransfer{}).Where("id = ?", transferID).Updates(updates).Error
		if err != nil {
			return err
		}

		// 更新会话记录
		err = tx.Model(&model.SysServiceRecord{}).
			Where("uid = ? AND service_id = ?", transfer.UserID, transfer.FromServiceID).
			Updates(map[string]interface{}{
				"service_id":          transfer.ToServiceID,
				"transfer_status":     model.TransferStatusTransferred,
				"original_service_id": transfer.FromServiceID,
				"transfer_count":      global.GVA_DB.Raw("transfer_count + 1"),
			}).Error
		if err != nil {
			return err
		}

		// 更新客服负载
		s.updateServiceLoad(transfer.FromServiceID, "remove")
		s.updateServiceLoad(transfer.ToServiceID, "add")

		// 发送WebSocket通知
		s.notifyTransferAccepted(&transfer)

		return nil
	})
}

// RejectTransfer 拒绝转接
func (s *TransferService) RejectTransfer(transferID uint) error {
	updates := map[string]interface{}{
		"status": model.TransferRecordStatusRejected,
	}

	return global.GVA_DB.Model(&model.SysServiceTransfer{}).Where("id = ?", transferID).Updates(updates).Error
}

// GetTransferList 获取转接记录列表
func (s *TransferService) GetTransferList(serviceID int64, status uint, page int, pageSize int) ([]model.SysServiceTransfer, int64, error) {
	var transfers []model.SysServiceTransfer
	var total int64

	query := global.GVA_DB.Model(&model.SysServiceTransfer{})
	if serviceID > 0 {
		query = query.Where("from_service_id = ? OR to_service_id = ?", serviceID, serviceID)
	}
	if status > 0 {
		query = query.Where("status = ?", status)
	}

	// 获取总数
	query.Count(&total)

	// 分页查询
	offset := (page - 1) * pageSize
	err := query.Order("transfer_time DESC").Offset(offset).Limit(pageSize).Find(&transfers).Error

	return transfers, total, err
}

// GetPendingTransfers 获取待处理的转接请求
func (s *TransferService) GetPendingTransfers(serviceID int64) ([]model.SysServiceTransfer, error) {
	var transfers []model.SysServiceTransfer
	err := global.GVA_DB.Where("to_service_id = ? AND status = ?", serviceID, model.TransferRecordStatusPending).
		Order("transfer_time ASC").Find(&transfers).Error
	return transfers, err
}

// GetTransferDetail 获取转接详情
func (s *TransferService) GetTransferDetail(transferID uint) (*model.SysServiceTransfer, error) {
	var transfer model.SysServiceTransfer
	err := global.GVA_DB.Where("id = ?", transferID).First(&transfer).Error
	return &transfer, err
}

// GetTransfersByTicket 获取工单的转接记录
func (s *TransferService) GetTransfersByTicket(ticketID uint) ([]model.SysServiceTransfer, error) {
	var transfers []model.SysServiceTransfer
	err := global.GVA_DB.Where("ticket_id = ?", ticketID).Order("transfer_time ASC").Find(&transfers).Error
	return transfers, err
}

// GetTransferStatistics 获取转接统计信息
func (s *TransferService) GetTransferStatistics(serviceID int64) (map[string]interface{}, error) {
	var stats map[string]interface{} = make(map[string]interface{})

	// 总转接次数
	var totalCount int64
	query := global.GVA_DB.Model(&model.SysServiceTransfer{})
	if serviceID > 0 {
		query = query.Where("from_service_id = ? OR to_service_id = ?", serviceID, serviceID)
	}
	query.Count(&totalCount)
	stats["total_count"] = totalCount

	// 发起的转接次数
	var outCount int64
	query = global.GVA_DB.Model(&model.SysServiceTransfer{})
	if serviceID > 0 {
		query = query.Where("from_service_id = ?", serviceID)
	}
	query.Count(&outCount)
	stats["out_count"] = outCount

	// 接收的转接次数
	var inCount int64
	query = global.GVA_DB.Model(&model.SysServiceTransfer{})
	if serviceID > 0 {
		query = query.Where("to_service_id = ?", serviceID)
	}
	query.Count(&inCount)
	stats["in_count"] = inCount

	// 待处理转接数
	var pendingCount int64
	query = global.GVA_DB.Model(&model.SysServiceTransfer{}).Where("status = ?", model.TransferRecordStatusPending)
	if serviceID > 0 {
		query = query.Where("to_service_id = ?", serviceID)
	}
	query.Count(&pendingCount)
	stats["pending_count"] = pendingCount

	// 接受的转接数
	var acceptedCount int64
	query = global.GVA_DB.Model(&model.SysServiceTransfer{}).Where("status = ?", model.TransferRecordStatusAccepted)
	if serviceID > 0 {
		query = query.Where("to_service_id = ?", serviceID)
	}
	query.Count(&acceptedCount)
	stats["accepted_count"] = acceptedCount

	// 拒绝的转接数
	var rejectedCount int64
	query = global.GVA_DB.Model(&model.SysServiceTransfer{}).Where("status = ?", model.TransferRecordStatusRejected)
	if serviceID > 0 {
		query = query.Where("to_service_id = ?", serviceID)
	}
	query.Count(&rejectedCount)
	stats["rejected_count"] = rejectedCount

	return stats, nil
}

// GetAvailableServices 获取可转接的客服列表
func (s *TransferService) GetAvailableServices(currentServiceID int64, tagIDs []uint) ([]model.SysService, error) {
	var services []model.SysService
	query := global.GVA_DB.Where("online = 1 AND work_status = ? AND id != ?", model.WorkStatusAvailable, currentServiceID)

	// 如果有标签要求，优先匹配相同标签的客服
	if len(tagIDs) > 0 {
		query = query.Joins("LEFT JOIN sys_service_tag_relation ON sys_service.id = sys_service_tag_relation.service_id").
			Where("sys_service_tag_relation.tag_id IN ?", tagIDs)
	}

	err := query.Order("current_user_count ASC").Find(&services).Error
	return services, err
}

// CanTransfer 检查是否可以转接
func (s *TransferService) CanTransfer(fromServiceID int64, toServiceID int64) (bool, string) {
	// 检查目标客服是否在线
	var targetService model.SysService
	err := global.GVA_DB.Where("id = ? AND online = 1", toServiceID).First(&targetService).Error
	if err != nil {
		return false, "目标客服不在线"
	}

	// 检查目标客服是否可接单
	if targetService.WorkStatus != model.WorkStatusAvailable {
		return false, "目标客服当前不可接单"
	}

	// 检查目标客服负载
	if targetService.CurrentUserCount >= targetService.MaxUserCount {
		return false, "目标客服负载已满"
	}

	// 检查是否是自己转给自己
	if fromServiceID == toServiceID {
		return false, "不能转接给自己"
	}

	return true, ""
}

// updateServiceLoad 更新客服负载
func (s *TransferService) updateServiceLoad(serviceID int64, operation string) {
	if operation == "add" {
		global.GVA_DB.Model(&model.SysService{}).Where("id = ?", serviceID).
			Update("current_user_count", global.GVA_DB.Raw("current_user_count + 1"))
	} else if operation == "remove" {
		global.GVA_DB.Model(&model.SysService{}).Where("id = ?", serviceID).
			Update("current_user_count", global.GVA_DB.Raw("current_user_count - 1"))
	}
}

// GetTransferHistory 获取用户的转接历史
func (s *TransferService) GetTransferHistory(userID uint) ([]model.SysServiceTransfer, error) {
	var transfers []model.SysServiceTransfer
	err := global.GVA_DB.Where("user_id = ?", userID).Order("transfer_time DESC").Find(&transfers).Error
	return transfers, err
}

// BatchTransfer 批量转接
func (s *TransferService) BatchTransfer(sessionIDs []uint, fromServiceID int64, toServiceID int64, reason string) error {
	// 检查是否可以转接
	canTransfer, msg := s.CanTransfer(fromServiceID, toServiceID)
	if !canTransfer {
		return fmt.Errorf("批量转接失败: %s", msg)
	}

	// 检查目标客服是否有足够容量
	var targetService model.SysService
	global.GVA_DB.Where("id = ?", toServiceID).First(&targetService)
	if targetService.CurrentUserCount+len(sessionIDs) > targetService.MaxUserCount {
		return fmt.Errorf("目标客服容量不足，无法接收 %d 个会话", len(sessionIDs))
	}

	// 批量创建转接记录
	for _, sessionID := range sessionIDs {
		// 从会话记录中获取用户ID
		var record model.SysServiceRecord
		global.GVA_DB.Where("id = ? AND service_id = ?", sessionID, fromServiceID).First(&record)

		transfer := &model.SysServiceTransfer{
			SessionID:     sessionID,
			FromServiceID: fromServiceID,
			ToServiceID:   toServiceID,
			UserID:        uint(record.Uid),
			Reason:        reason,
			Status:        model.TransferRecordStatusPending,
			TransferTime:  time.Now().Unix(),
		}

		global.GVA_DB.Create(transfer)
	}

	return nil
}

// ==================== 新增的辅助方法 ====================

// validateTransferRequest 验证转接请求
func (s *TransferService) validateTransferRequest(req *TransferRequest) error {
	// 检查会话记录是否存在
	var record model.SysServiceRecord
	if err := global.GVA_DB.Where("id = ?", req.SessionID).First(&record).Error; err != nil {
		return fmt.Errorf("会话记录不存在")
	}

	// 检查会话是否属于转出客服
	if record.ServiceId != req.FromServiceID {
		return fmt.Errorf("会话不属于当前客服")
	}

	// 检查用户ID是否匹配
	if uint(record.Uid) != req.UserID {
		return fmt.Errorf("用户ID不匹配")
	}

	// 检查是否可以转接
	canTransfer, msg := s.CanTransfer(req.FromServiceID, req.ToServiceID)
	if !canTransfer {
		return fmt.Errorf(msg)
	}

	// 检查是否有未完成的转接请求
	var existingTransfer model.SysServiceTransfer
	err := global.GVA_DB.Where("id = ? AND status = ?", req.SessionID, model.TransferRecordStatusPending).
		First(&existingTransfer).Error
	if err == nil {
		return fmt.Errorf("该会话已有待处理的转接请求")
	}

	return nil
}

// getUserTags 获取用户标签
func (s *TransferService) getUserTags(userID uint) ([]uint, error) {
	var tags []uint
	var relations []struct {
		TagID uint `json:"tag_id"`
	}

	// 查询用户标签关系
	err := global.GVA_DB.Table("jyh_user_tag_relation").
		Select("tag_id").
		Where("user_id = ?", userID).
		Find(&relations).Error

	if err != nil {
		return tags, err
	}

	for _, rel := range relations {
		tags = append(tags, rel.TagID)
	}

	return tags, nil
}

// getServiceInfo 获取客服信息
func (s *TransferService) getServiceInfo(serviceID int64) (map[string]interface{}, error) {
	var service model.SysService
	err := global.GVA_DB.Where("id = ?", serviceID).First(&service).Error
	if err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"id":       service.Id,
		"nickname": service.Nickname,
		"avatar":   service.Avatar,
		"online":   service.Online,
		"status":   service.WorkStatus,
	}, nil
}

// updateSessionServiceStatus 更新会话服务状态
func (s *TransferService) updateSessionServiceStatus(sessionID uint, serviceID int64, status string) error {
	return global.GVA_DB.Model(&model.SysServiceRecord{}).
		Where("id = ? AND service_id = ?", sessionID, serviceID).
		Update("transfer_status", status).Error
}

// notifyTransferRequest 通知转接请求
func (s *TransferService) notifyTransferRequest(transfer *model.SysServiceTransfer) {
	// 构建通知消息
	notification := map[string]interface{}{
		"type":            "transfer_request",
		"transfer_id":     transfer.ID,
		"from_service_id": transfer.FromServiceID,
		"to_service_id":   transfer.ToServiceID,
		"user_id":         transfer.UserID,
		"reason":          transfer.Reason,
		"priority":        transfer.Priority,
		"expire_time":     transfer.ExpireTime,
		"timestamp":       time.Now().Unix(),
	}

	// 发送给目标客服
	s.sendWebSocketNotification(fmt.Sprintf("kf%d", transfer.ToServiceID), notification)

	// 发送给转出客服（确认消息）
	confirmMsg := map[string]interface{}{
		"type":          "transfer_sent",
		"transfer_id":   transfer.ID,
		"to_service_id": transfer.ToServiceID,
		"timestamp":     time.Now().Unix(),
	}
	s.sendWebSocketNotification(fmt.Sprintf("kf%d", transfer.FromServiceID), confirmMsg)
}

// sendWebSocketNotification 发送WebSocket通知
func (s *TransferService) sendWebSocketNotification(clientKey string, data interface{}) {
	// WebSocket通知实现
	// 这里暂时用日志记录，实际使用时需要集成WebSocket
	fmt.Printf("WebSocket通知 [%s]: %+v\n", clientKey, data)
}

// expireTransfer 设置转接为过期状态
func (s *TransferService) expireTransfer(transferID uint) error {
	updates := map[string]interface{}{
		"status": model.TransferRecordStatusExpired,
	}
	return global.GVA_DB.Model(&model.SysServiceTransfer{}).
		Where("id = ?", transferID).
		Updates(updates).Error
}

// notifyTransferAccepted 通知转接已接受
func (s *TransferService) notifyTransferAccepted(transfer *model.SysServiceTransfer) {
	// 通知转出客服
	acceptedMsg := map[string]interface{}{
		"type":          "transfer_accepted",
		"transfer_id":   transfer.ID,
		"to_service_id": transfer.ToServiceID,
		"timestamp":     time.Now().Unix(),
	}
	s.sendWebSocketNotification(fmt.Sprintf("kf%d", transfer.FromServiceID), acceptedMsg)

	// 通知用户（可选）
	userMsg := map[string]interface{}{
		"type":        "service_changed",
		"new_service": transfer.ToServiceID,
		"timestamp":   time.Now().Unix(),
	}
	s.sendWebSocketNotification(fmt.Sprintf("user%d", transfer.UserID), userMsg)
}

// CancelTransfer 取消转接
func (s *TransferService) CancelTransfer(transferID uint, reason string) error {
	var transfer model.SysServiceTransfer
	err := global.GVA_DB.Where("id = ?", transferID).First(&transfer).Error
	if err != nil {
		return err
	}

	// 检查转接状态
	if transfer.Status != model.TransferRecordStatusPending {
		return fmt.Errorf("只能取消待处理的转接请求")
	}

	// 更新转接状态
	updates := map[string]interface{}{
		"status":        model.TransferRecordStatusCanceled,
		"cancel_reason": reason,
	}

	err = global.GVA_DB.Model(&model.SysServiceTransfer{}).
		Where("id = ?", transferID).
		Updates(updates).Error
	if err != nil {
		return err
	}

	// 发送取消通知
	s.notifyTransferCanceled(&transfer, reason)

	return nil
}

// notifyTransferCanceled 通知转接已取消
func (s *TransferService) notifyTransferCanceled(transfer *model.SysServiceTransfer, reason string) {
	cancelMsg := map[string]interface{}{
		"type":          "transfer_canceled",
		"transfer_id":   transfer.ID,
		"cancel_reason": reason,
		"timestamp":     time.Now().Unix(),
	}
	s.sendWebSocketNotification(fmt.Sprintf("kf%d", transfer.ToServiceID), cancelMsg)
}

// CleanupExpiredTransfers 清理过期的转接请求
func (s *TransferService) CleanupExpiredTransfers() error {
	now := time.Now().Unix()

	// 查找过期的转接请求
	var expiredTransfers []model.SysServiceTransfer
	err := global.GVA_DB.Where("status = ? AND expire_time < ?",
		model.TransferRecordStatusPending, now).Find(&expiredTransfers).Error
	if err != nil {
		return err
	}

	// 批量更新为过期状态
	if len(expiredTransfers) > 0 {
		var transferIDs []uint
		for _, transfer := range expiredTransfers {
			transferIDs = append(transferIDs, transfer.ID)
		}

		err = global.GVA_DB.Model(&model.SysServiceTransfer{}).
			Where("id IN ?", transferIDs).
			Update("status", model.TransferRecordStatusExpired).Error
		if err != nil {
			return err
		}

		// 发送过期通知
		for _, transfer := range expiredTransfers {
			s.notifyTransferExpired(&transfer)
		}
	}

	return nil
}

// notifyTransferExpired 通知转接已过期
func (s *TransferService) notifyTransferExpired(transfer *model.SysServiceTransfer) {
	expiredMsg := map[string]interface{}{
		"type":        "transfer_expired",
		"transfer_id": transfer.ID,
		"timestamp":   time.Now().Unix(),
	}
	s.sendWebSocketNotification(fmt.Sprintf("kf%d", transfer.FromServiceID), expiredMsg)
	s.sendWebSocketNotification(fmt.Sprintf("kf%d", transfer.ToServiceID), expiredMsg)
}
