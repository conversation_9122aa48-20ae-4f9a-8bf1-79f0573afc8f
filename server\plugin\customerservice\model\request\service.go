package request

import "github.com/flipped-aurora/gin-vue-admin/server/plugin/customerservice/model/common"

type ServiceReq struct {
	Id         int64  `json:"id" form:"id"`
	MerchantId uint   `json:"merchant_id" form:"merchant_id"`
	Uid        uint   `json:"uid" form:"uid"`
	Online     uint   `json:"online" form:"online"`
	Account    string `json:"account" form:"account"`
	Password   string `json:"password" form:"password"`
	Avatar     string `json:"avatar" form:"avatar"`
	Nickname   string `json:"nickname" form:"nickname"`
	AddTime    int64  `json:"add_time" form:"add_time"`
	Status     *uint  `json:"status" form:"status"`
	TagIDs     []uint `json:"tag_ids" form:"tag_ids"`
}

type GetDistributeRulesReq struct {
	common.PageInfo
	RuleName string `json:"rule_name" form:"rule_name"`
	Status   uint   `json:"status" form:"status"`
}
