// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
)

func newJyhUserCertificate(db *gorm.DB, opts ...gen.DOOption) jyhUserCertificate {
	_jyhUserCertificate := jyhUserCertificate{}

	_jyhUserCertificate.jyhUserCertificateDo.UseDB(db, opts...)
	_jyhUserCertificate.jyhUserCertificateDo.UseModel(&jyhapp.JyhUserCertificate{})

	tableName := _jyhUserCertificate.jyhUserCertificateDo.TableName()
	_jyhUserCertificate.ALL = field.NewAsterisk(tableName)
	_jyhUserCertificate.ID = field.NewUint(tableName, "id")
	_jyhUserCertificate.UserID = field.NewUint(tableName, "user_id")
	_jyhUserCertificate.CertificateFile = field.NewString(tableName, "certificate_file")
	_jyhUserCertificate.FansCount = field.NewInt(tableName, "fans_count")
	_jyhUserCertificate.ValidFansCount = field.NewInt(tableName, "valid_fans_count")
	_jyhUserCertificate.Description = field.NewString(tableName, "description")
	_jyhUserCertificate.Status = field.NewString(tableName, "status")
	_jyhUserCertificate.Remark = field.NewString(tableName, "remark")
	_jyhUserCertificate.CreatedAt = field.NewTime(tableName, "created_at")
	_jyhUserCertificate.UpdatedAt = field.NewTime(tableName, "updated_at")
	_jyhUserCertificate.ReviewerID = field.NewUint(tableName, "reviewer_id")
	_jyhUserCertificate.ReviewedAt = field.NewTime(tableName, "reviewed_at")
	_jyhUserCertificate.User = jyhUserCertificateBelongsToUser{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("User", "jyhapp.JyhUser"),
		Inviter: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("User.Inviter", "jyhapp.JyhUser"),
		},
		JyhUserExt: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("User.JyhUserExt", "jyhapp.JyhUserExt"),
		},
		Invitees: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("User.Invitees", "jyhapp.JyhUser"),
		},
		Tags: struct {
			field.RelationField
			Users struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("User.Tags", "jyhapp.JyhUserTag"),
			Users: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("User.Tags.Users", "jyhapp.JyhUser"),
			},
		},
	}

	_jyhUserCertificate.Reviewer = jyhUserCertificateBelongsToReviewer{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Reviewer", "system.SysUser"),
		Authority: struct {
			field.RelationField
			DataAuthorityId struct {
				field.RelationField
			}
			SysBaseMenus struct {
				field.RelationField
				Parameters struct {
					field.RelationField
				}
				MenuBtn struct {
					field.RelationField
				}
				SysAuthoritys struct {
					field.RelationField
				}
			}
			Users struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Reviewer.Authority", "system.SysAuthority"),
			DataAuthorityId: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Reviewer.Authority.DataAuthorityId", "system.SysAuthority"),
			},
			SysBaseMenus: struct {
				field.RelationField
				Parameters struct {
					field.RelationField
				}
				MenuBtn struct {
					field.RelationField
				}
				SysAuthoritys struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Reviewer.Authority.SysBaseMenus", "system.SysBaseMenu"),
				Parameters: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Reviewer.Authority.SysBaseMenus.Parameters", "system.SysBaseMenuParameter"),
				},
				MenuBtn: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Reviewer.Authority.SysBaseMenus.MenuBtn", "system.SysBaseMenuBtn"),
				},
				SysAuthoritys: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Reviewer.Authority.SysBaseMenus.SysAuthoritys", "system.SysAuthority"),
				},
			},
			Users: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Reviewer.Authority.Users", "system.SysUser"),
			},
		},
		Authorities: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Reviewer.Authorities", "system.SysAuthority"),
		},
	}

	_jyhUserCertificate.fillFieldMap()

	return _jyhUserCertificate
}

type jyhUserCertificate struct {
	jyhUserCertificateDo

	ALL             field.Asterisk
	ID              field.Uint
	UserID          field.Uint
	CertificateFile field.String
	FansCount       field.Int
	ValidFansCount  field.Int
	Description     field.String
	Status          field.String
	Remark          field.String
	CreatedAt       field.Time
	UpdatedAt       field.Time
	ReviewerID      field.Uint
	ReviewedAt      field.Time
	User            jyhUserCertificateBelongsToUser

	Reviewer jyhUserCertificateBelongsToReviewer

	fieldMap map[string]field.Expr
}

func (j jyhUserCertificate) Table(newTableName string) *jyhUserCertificate {
	j.jyhUserCertificateDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j jyhUserCertificate) As(alias string) *jyhUserCertificate {
	j.jyhUserCertificateDo.DO = *(j.jyhUserCertificateDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *jyhUserCertificate) updateTableName(table string) *jyhUserCertificate {
	j.ALL = field.NewAsterisk(table)
	j.ID = field.NewUint(table, "id")
	j.UserID = field.NewUint(table, "user_id")
	j.CertificateFile = field.NewString(table, "certificate_file")
	j.FansCount = field.NewInt(table, "fans_count")
	j.ValidFansCount = field.NewInt(table, "valid_fans_count")
	j.Description = field.NewString(table, "description")
	j.Status = field.NewString(table, "status")
	j.Remark = field.NewString(table, "remark")
	j.CreatedAt = field.NewTime(table, "created_at")
	j.UpdatedAt = field.NewTime(table, "updated_at")
	j.ReviewerID = field.NewUint(table, "reviewer_id")
	j.ReviewedAt = field.NewTime(table, "reviewed_at")

	j.fillFieldMap()

	return j
}

func (j *jyhUserCertificate) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *jyhUserCertificate) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 14)
	j.fieldMap["id"] = j.ID
	j.fieldMap["user_id"] = j.UserID
	j.fieldMap["certificate_file"] = j.CertificateFile
	j.fieldMap["fans_count"] = j.FansCount
	j.fieldMap["valid_fans_count"] = j.ValidFansCount
	j.fieldMap["description"] = j.Description
	j.fieldMap["status"] = j.Status
	j.fieldMap["remark"] = j.Remark
	j.fieldMap["created_at"] = j.CreatedAt
	j.fieldMap["updated_at"] = j.UpdatedAt
	j.fieldMap["reviewer_id"] = j.ReviewerID
	j.fieldMap["reviewed_at"] = j.ReviewedAt

}

func (j jyhUserCertificate) clone(db *gorm.DB) jyhUserCertificate {
	j.jyhUserCertificateDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j jyhUserCertificate) replaceDB(db *gorm.DB) jyhUserCertificate {
	j.jyhUserCertificateDo.ReplaceDB(db)
	return j
}

type jyhUserCertificateBelongsToUser struct {
	db *gorm.DB

	field.RelationField

	Inviter struct {
		field.RelationField
	}
	JyhUserExt struct {
		field.RelationField
	}
	Invitees struct {
		field.RelationField
	}
	Tags struct {
		field.RelationField
		Users struct {
			field.RelationField
		}
	}
}

func (a jyhUserCertificateBelongsToUser) Where(conds ...field.Expr) *jyhUserCertificateBelongsToUser {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a jyhUserCertificateBelongsToUser) WithContext(ctx context.Context) *jyhUserCertificateBelongsToUser {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a jyhUserCertificateBelongsToUser) Session(session *gorm.Session) *jyhUserCertificateBelongsToUser {
	a.db = a.db.Session(session)
	return &a
}

func (a jyhUserCertificateBelongsToUser) Model(m *jyhapp.JyhUserCertificate) *jyhUserCertificateBelongsToUserTx {
	return &jyhUserCertificateBelongsToUserTx{a.db.Model(m).Association(a.Name())}
}

type jyhUserCertificateBelongsToUserTx struct{ tx *gorm.Association }

func (a jyhUserCertificateBelongsToUserTx) Find() (result *jyhapp.JyhUser, err error) {
	return result, a.tx.Find(&result)
}

func (a jyhUserCertificateBelongsToUserTx) Append(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a jyhUserCertificateBelongsToUserTx) Replace(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a jyhUserCertificateBelongsToUserTx) Delete(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a jyhUserCertificateBelongsToUserTx) Clear() error {
	return a.tx.Clear()
}

func (a jyhUserCertificateBelongsToUserTx) Count() int64 {
	return a.tx.Count()
}

type jyhUserCertificateBelongsToReviewer struct {
	db *gorm.DB

	field.RelationField

	Authority struct {
		field.RelationField
		DataAuthorityId struct {
			field.RelationField
		}
		SysBaseMenus struct {
			field.RelationField
			Parameters struct {
				field.RelationField
			}
			MenuBtn struct {
				field.RelationField
			}
			SysAuthoritys struct {
				field.RelationField
			}
		}
		Users struct {
			field.RelationField
		}
	}
	Authorities struct {
		field.RelationField
	}
}

func (a jyhUserCertificateBelongsToReviewer) Where(conds ...field.Expr) *jyhUserCertificateBelongsToReviewer {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a jyhUserCertificateBelongsToReviewer) WithContext(ctx context.Context) *jyhUserCertificateBelongsToReviewer {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a jyhUserCertificateBelongsToReviewer) Session(session *gorm.Session) *jyhUserCertificateBelongsToReviewer {
	a.db = a.db.Session(session)
	return &a
}

func (a jyhUserCertificateBelongsToReviewer) Model(m *jyhapp.JyhUserCertificate) *jyhUserCertificateBelongsToReviewerTx {
	return &jyhUserCertificateBelongsToReviewerTx{a.db.Model(m).Association(a.Name())}
}

type jyhUserCertificateBelongsToReviewerTx struct{ tx *gorm.Association }

func (a jyhUserCertificateBelongsToReviewerTx) Find() (result *system.SysUser, err error) {
	return result, a.tx.Find(&result)
}

func (a jyhUserCertificateBelongsToReviewerTx) Append(values ...*system.SysUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a jyhUserCertificateBelongsToReviewerTx) Replace(values ...*system.SysUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a jyhUserCertificateBelongsToReviewerTx) Delete(values ...*system.SysUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a jyhUserCertificateBelongsToReviewerTx) Clear() error {
	return a.tx.Clear()
}

func (a jyhUserCertificateBelongsToReviewerTx) Count() int64 {
	return a.tx.Count()
}

type jyhUserCertificateDo struct{ gen.DO }

func (j jyhUserCertificateDo) Debug() *jyhUserCertificateDo {
	return j.withDO(j.DO.Debug())
}

func (j jyhUserCertificateDo) WithContext(ctx context.Context) *jyhUserCertificateDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jyhUserCertificateDo) ReadDB() *jyhUserCertificateDo {
	return j.Clauses(dbresolver.Read)
}

func (j jyhUserCertificateDo) WriteDB() *jyhUserCertificateDo {
	return j.Clauses(dbresolver.Write)
}

func (j jyhUserCertificateDo) Session(config *gorm.Session) *jyhUserCertificateDo {
	return j.withDO(j.DO.Session(config))
}

func (j jyhUserCertificateDo) Clauses(conds ...clause.Expression) *jyhUserCertificateDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jyhUserCertificateDo) Returning(value interface{}, columns ...string) *jyhUserCertificateDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jyhUserCertificateDo) Not(conds ...gen.Condition) *jyhUserCertificateDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jyhUserCertificateDo) Or(conds ...gen.Condition) *jyhUserCertificateDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jyhUserCertificateDo) Select(conds ...field.Expr) *jyhUserCertificateDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jyhUserCertificateDo) Where(conds ...gen.Condition) *jyhUserCertificateDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jyhUserCertificateDo) Order(conds ...field.Expr) *jyhUserCertificateDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jyhUserCertificateDo) Distinct(cols ...field.Expr) *jyhUserCertificateDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jyhUserCertificateDo) Omit(cols ...field.Expr) *jyhUserCertificateDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jyhUserCertificateDo) Join(table schema.Tabler, on ...field.Expr) *jyhUserCertificateDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jyhUserCertificateDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jyhUserCertificateDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jyhUserCertificateDo) RightJoin(table schema.Tabler, on ...field.Expr) *jyhUserCertificateDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jyhUserCertificateDo) Group(cols ...field.Expr) *jyhUserCertificateDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jyhUserCertificateDo) Having(conds ...gen.Condition) *jyhUserCertificateDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jyhUserCertificateDo) Limit(limit int) *jyhUserCertificateDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jyhUserCertificateDo) Offset(offset int) *jyhUserCertificateDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jyhUserCertificateDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jyhUserCertificateDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jyhUserCertificateDo) Unscoped() *jyhUserCertificateDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jyhUserCertificateDo) Create(values ...*jyhapp.JyhUserCertificate) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jyhUserCertificateDo) CreateInBatches(values []*jyhapp.JyhUserCertificate, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jyhUserCertificateDo) Save(values ...*jyhapp.JyhUserCertificate) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jyhUserCertificateDo) First() (*jyhapp.JyhUserCertificate, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserCertificate), nil
	}
}

func (j jyhUserCertificateDo) Take() (*jyhapp.JyhUserCertificate, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserCertificate), nil
	}
}

func (j jyhUserCertificateDo) Last() (*jyhapp.JyhUserCertificate, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserCertificate), nil
	}
}

func (j jyhUserCertificateDo) Find() ([]*jyhapp.JyhUserCertificate, error) {
	result, err := j.DO.Find()
	return result.([]*jyhapp.JyhUserCertificate), err
}

func (j jyhUserCertificateDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*jyhapp.JyhUserCertificate, err error) {
	buf := make([]*jyhapp.JyhUserCertificate, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jyhUserCertificateDo) FindInBatches(result *[]*jyhapp.JyhUserCertificate, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jyhUserCertificateDo) Attrs(attrs ...field.AssignExpr) *jyhUserCertificateDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jyhUserCertificateDo) Assign(attrs ...field.AssignExpr) *jyhUserCertificateDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jyhUserCertificateDo) Joins(fields ...field.RelationField) *jyhUserCertificateDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jyhUserCertificateDo) Preload(fields ...field.RelationField) *jyhUserCertificateDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jyhUserCertificateDo) FirstOrInit() (*jyhapp.JyhUserCertificate, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserCertificate), nil
	}
}

func (j jyhUserCertificateDo) FirstOrCreate() (*jyhapp.JyhUserCertificate, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserCertificate), nil
	}
}

func (j jyhUserCertificateDo) FindByPage(offset int, limit int) (result []*jyhapp.JyhUserCertificate, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jyhUserCertificateDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jyhUserCertificateDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jyhUserCertificateDo) Delete(models ...*jyhapp.JyhUserCertificate) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jyhUserCertificateDo) withDO(do gen.Dao) *jyhUserCertificateDo {
	j.DO = *do.(*gen.DO)
	return j
}
