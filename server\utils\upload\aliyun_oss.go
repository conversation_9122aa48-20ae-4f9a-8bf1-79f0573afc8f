package upload

import (
	"encoding/base64"
	"errors"
	"fmt"
	"mime/multipart"
	"strings"
	"time"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"go.uber.org/zap"
)

type AliyunOSS struct{}

func (*AliyunOSS) UploadFile(file *multipart.FileHeader) (string, string, error) {
	bucket, err := NewBucket()
	if err != nil {
		global.GVA_LOG.Error("function AliyunOSS.NewBucket() Failed", zap.Any("err", err.Error()))
		return "", "", errors.New("function AliyunOSS.NewBucket() Failed, err:" + err.Error())
	}

	// 读取本地文件。
	f, openError := file.Open()
	if openError != nil {
		global.GVA_LOG.Error("function file.Open() Failed", zap.Any("err", openError.Error()))
		return "", "", errors.New("function file.Open() Failed, err:" + openError.Error())
	}
	defer f.Close() // 创建文件 defer 关闭
	// 上传阿里云路径 文件名格式 自己可以改 建议保证唯一性
	// yunFileTmpPath := filepath.Join("uploads", time.Now().Format("2006-01-02")) + "/" + file.Filename
	yunFileTmpPath := global.GVA_CONFIG.AliyunOSS.BasePath + "/" + "uploads" + "/" + time.Now().Format("2006-01-02") + "/" + file.Filename

	// 上传文件流。
	err = bucket.PutObject(yunFileTmpPath, f)
	if err != nil {
		global.GVA_LOG.Error("function formUploader.Put() Failed", zap.Any("err", err.Error()))
		return "", "", errors.New("function formUploader.Put() Failed, err:" + err.Error())
	}

	return global.GVA_CONFIG.AliyunOSS.BucketUrl + "/" + yunFileTmpPath, yunFileTmpPath, nil
}

func (*AliyunOSS) DeleteFile(key string) error {
	bucket, err := NewBucket()
	if err != nil {
		global.GVA_LOG.Error("function AliyunOSS.NewBucket() Failed", zap.Any("err", err.Error()))
		return errors.New("function AliyunOSS.NewBucket() Failed, err:" + err.Error())
	}

	// 删除单个文件。objectName表示删除OSS文件时需要指定包含文件后缀在内的完整路径，例如abc/efg/123.jpg。
	// 如需删除文件夹，请将objectName设置为对应的文件夹名称。如果文件夹非空，则需要将文件夹下的所有object删除后才能删除该文件夹。
	err = bucket.DeleteObject(key)
	if err != nil {
		global.GVA_LOG.Error("function bucketManager.Delete() failed", zap.Any("err", err.Error()))
		return errors.New("function bucketManager.Delete() failed, err:" + err.Error())
	}

	return nil
}

// ImageBlindWatermark 生成图片盲水印
//
//	 https://help.aliyun.com/zh/oss/user-guide/add-blindwatermarks#3b9085669fr7b
//
//		ossSourceImageName: 指定原图名称，如果图片不在Bucket根目录，需携带文件完整访问路径，例如 "sourceDir/source.jpg"
//		targetBucketName: 指定用于存放处理后图片的Bucket名称，该Bucket需与原图所在Bucket在同一地域。如果不指定，则默认为源图所在的Bucket
//		targetImageName: 指定处理后图片名称，如果图片不在Bucket根目录，需携带文件完整访问路径，例如 "targetDir/target.jpg"
//		content: 指定水印内容，如 "阿里云版权所有"
func (*AliyunOSS) ImageBlindWatermark(ossSourceImageName, targetBucketName, targetImageName, content string) (ok bool, err error) {
	bucket, err := NewBucket()
	if err != nil {
		global.GVA_LOG.Error("function AliyunOSS.NewBucket() Failed", zap.Any("err", err.Error()))
		return false, errors.New("function AliyunOSS.NewBucket() Failed, err:" + err.Error())
	}
	encodedContent := base64.RawURLEncoding.EncodeToString([]byte(content))

	sb := strings.Builder{}
	sb.WriteString("image/blindwatermark")
	sb.WriteString(",content_")
	sb.WriteString(encodedContent)

	sb.WriteString("|")

	sb.WriteString("sys/saveas")
	sb.WriteString(",o_")
	sb.WriteString(base64.RawURLEncoding.EncodeToString([]byte(targetImageName)))
	if targetBucketName != "" {
		sb.WriteString(",b_")
		sb.WriteString(base64.RawURLEncoding.EncodeToString([]byte(targetBucketName)))
	}

	process := sb.String()

	global.GVA_LOG.Info("ImageBlindWatermark process", zap.String("process", process), zap.String("source", ossSourceImageName))
	result, err := bucket.ProcessObject(ossSourceImageName, process)
	if err != nil {
		return false, errors.New("function bucket.ProcessObject() Failed, err:" + err.Error())
	}
	fmt.Println(result)
	return true, nil
}

// ImageDeBlindWatermark 提取图片中的盲水印
//
//	 https://help.aliyun.com/zh/oss/user-guide/add-blindwatermarks#0425a520cagzy
//
//		sourceKey: 指定水印图文件名称，例如 "targetDir/target.jpg"
//		notifyTopic: 指定MNS消息的topic，例如 "imm-blindwatermark-test"
func (*AliyunOSS) ImageDeBlindWatermark(sourceKey, notifyTopic string) (requestId string, err error) {
	bucket, err := NewBucket()
	if err != nil {
		global.GVA_LOG.Error("function AliyunOSS.NewBucket() Failed", zap.Any("err", err.Error()))
		return "", errors.New("function AliyunOSS.NewBucket() Failed, err:" + err.Error())
	}

	// 提取指定图片中的水印内容。
	style := "image/deblindwatermark,s_low,t_text"
	encodedTopic := strings.TrimRight(base64.URLEncoding.EncodeToString([]byte(notifyTopic)), "=")
	process := fmt.Sprintf("%s|sys/notify,topic_%s", style, encodedTopic)

	// 调用异步流媒体处理接口。
	result, err := bucket.AsyncProcessObject(sourceKey, process)
	if err != nil {
		return "", errors.New("function bucket.AsyncProcessObject() Failed, err:" + err.Error())
	}
	fmt.Println(result.RequestId)
	return result.RequestId, nil
}

func NewBucket() (*oss.Bucket, error) {
	// 创建OSSClient实例。
	client, err := oss.New(global.GVA_CONFIG.AliyunOSS.Endpoint, global.GVA_CONFIG.AliyunOSS.AccessKeyId, global.GVA_CONFIG.AliyunOSS.AccessKeySecret)
	if err != nil {
		return nil, err
	}

	// 获取存储空间。
	bucket, err := client.Bucket(global.GVA_CONFIG.AliyunOSS.BucketName)
	if err != nil {
		return nil, err
	}

	return bucket, nil
}
