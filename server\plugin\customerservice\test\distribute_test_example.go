package test

import (
	"encoding/json"
	"fmt"
	"log"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/customerservice/model"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/customerservice/service"
)

// TestDistributeLogic 测试分配逻辑的示例函数
func TestDistributeLogic() {
	distributeService := &service.DistributeService{}

	// 测试场景1：VIP用户分配
	fmt.Println("=== 测试场景1：VIP用户分配 ===")
	vipUserTags := []uint{1} // 假设1是VIP标签ID
	vipUser, err := distributeService.SmartAssign(1001, vipUserTags)
	if err != nil {
		log.Printf("VIP用户分配失败: %v", err)
	} else {
		fmt.Printf("VIP用户分配到客服: ID=%d, 昵称=%s, 当前负载=%d\n",
			vipUser.Id, vipUser.Nickname, vipUser.CurrentUserCount)
	}

	// 测试场景2：普通用户分配
	fmt.Println("\n=== 测试场景2：普通用户分配 ===")
	normalUserTags := []uint{2} // 假设2是普通用户标签ID
	normalUser, err := distributeService.SmartAssign(1002, normalUserTags)
	if err != nil {
		log.Printf("普通用户分配失败: %v", err)
	} else {
		fmt.Printf("普通用户分配到客服: ID=%d, 昵称=%s, 当前负载=%d\n",
			normalUser.Id, normalUser.Nickname, normalUser.CurrentUserCount)
	}

	// 测试场景3：无标签用户分配
	fmt.Println("\n=== 测试场景3：无标签用户分配 ===")
	noTagUser, err := distributeService.SmartAssign(1003, []uint{})
	if err != nil {
		log.Printf("无标签用户分配失败: %v", err)
	} else {
		fmt.Printf("无标签用户分配到客服: ID=%d, 昵称=%s, 当前负载=%d\n",
			noTagUser.Id, noTagUser.Nickname, noTagUser.CurrentUserCount)
	}
}

// SetupTestData 设置测试数据的示例函数
func SetupTestData() {
	fmt.Println("=== 设置测试数据 ===")

	// 1. 创建测试规则
	rule1 := &model.SysServiceDistributeRule{
		RuleName:      "VIP客户专属服务",
		Priority:      1,
		MaxUsers:      3,
		WorkTimeStart: "09:00",
		WorkTimeEnd:   "22:00",
		Status:        model.RuleStatusEnabled,
		IsDefault:     false,
	}
	global.GVA_DB.Create(rule1)
	fmt.Printf("创建规则: %s (ID: %d)\n", rule1.RuleName, rule1.ID)

	rule2 := &model.SysServiceDistributeRule{
		RuleName:      "普通客户服务",
		Priority:      2,
		MaxUsers:      5,
		WorkTimeStart: "08:00",
		WorkTimeEnd:   "20:00",
		Status:        model.RuleStatusEnabled,
		IsDefault:     false,
	}
	global.GVA_DB.Create(rule2)
	fmt.Printf("创建规则: %s (ID: %d)\n", rule2.RuleName, rule2.ID)

	defaultRule := &model.SysServiceDistributeRule{
		RuleName:      "默认分配规则",
		Priority:      999,
		MaxUsers:      10,
		WorkTimeStart: "00:00",
		WorkTimeEnd:   "23:59",
		Status:        model.RuleStatusEnabled,
		IsDefault:     true,
	}
	global.GVA_DB.Create(defaultRule)
	fmt.Printf("创建默认规则: %s (ID: %d)\n", defaultRule.RuleName, defaultRule.ID)

	// 2. 创建规则-标签关联
	tagRelations := []model.SysServiceRuleTagRelation{
		{RuleID: rule1.ID, TagID: 1}, // VIP标签关联到VIP规则
		{RuleID: rule2.ID, TagID: 2}, // 普通标签关联到普通规则
	}
	for _, relation := range tagRelations {
		global.GVA_DB.Create(&relation)
		fmt.Printf("创建规则-标签关联: 规则%d -> 标签%d\n", relation.RuleID, relation.TagID)
	}

	// 3. 创建规则-客服关联
	serviceRelations := []model.SysServiceRuleServiceRelation{
		{RuleID: rule1.ID, ServiceID: 10, Priority: 1}, // VIP规则关联高级客服
		{RuleID: rule1.ID, ServiceID: 11, Priority: 2},
		{RuleID: rule2.ID, ServiceID: 12, Priority: 1}, // 普通规则关联普通客服
		{RuleID: rule2.ID, ServiceID: 13, Priority: 2},
		{RuleID: defaultRule.ID, ServiceID: 14, Priority: 1}, // 默认规则关联默认客服
	}
	for _, relation := range serviceRelations {
		global.GVA_DB.Create(&relation)
		fmt.Printf("创建规则-客服关联: 规则%d -> 客服%d (优先级%d)\n",
			relation.RuleID, relation.ServiceID, relation.Priority)
	}

	fmt.Println("测试数据设置完成！")
}

// PrintDistributeStatistics 打印分配统计信息
func PrintDistributeStatistics() {
	distributeService := &service.DistributeService{}
	stats, err := distributeService.GetDistributeStatistics()
	if err != nil {
		log.Printf("获取统计信息失败: %v", err)
		return
	}

	fmt.Println("\n=== 分配统计信息 ===")
	statsJSON, _ := json.MarshalIndent(stats, "", "  ")
	fmt.Println(string(statsJSON))
}

// TestRuleServiceBinding 测试规则-客服绑定功能
func TestRuleServiceBinding() {
	distributeService := &service.DistributeService{}

	fmt.Println("\n=== 测试规则-客服绑定 ===")

	// 测试绑定客服到规则（通过创建/更新规则API实现）
	ruleID := uint(1)
	serviceIDs := []int64{10, 11, 12}

	err := distributeService.BindRuleToServices(ruleID, serviceIDs)
	if err != nil {
		log.Printf("绑定客服失败: %v", err)
		return
	}
	fmt.Printf("成功绑定客服 %v 到规则 %d\n", serviceIDs, ruleID)

	// 测试获取规则关联的客服
	services, err := distributeService.GetServicesByRule(ruleID)
	if err != nil {
		log.Printf("获取规则客服失败: %v", err)
		return
	}

	fmt.Printf("规则 %d 关联的客服:\n", ruleID)
	for _, service := range services {
		fmt.Printf("  - 客服ID: %d, 昵称: %s, 在线: %d, 工作状态: %d\n",
			service.Id, service.Nickname, service.Online, service.WorkStatus)
	}
}
