# 客服转接功能使用指南

## 功能概述

客服转接功能允许当前客服将正在服务的用户转接给其他更合适的客服，支持实时通知、状态管理和完整的转接流程控制。

## 核心特性

### 1. 转接流程管理
- **发起转接**：当前客服可以选择目标客服发起转接请求
- **接受/拒绝**：目标客服可以接受或拒绝转接请求
- **取消转接**：发起方可以在未被处理前取消转接
- **自动过期**：转接请求5分钟后自动过期

### 2. 智能客服推荐
- **标签匹配**：根据用户标签推荐合适的客服
- **负载均衡**：优先推荐负载较低的客服
- **技能匹配**：根据问题类型推荐专业客服

### 3. 实时通知系统
- **WebSocket通知**：实时通知相关客服和用户
- **状态同步**：转接状态实时同步
- **消息推送**：支持多种通知类型

## API接口说明

### 1. 发起转接申请

```http
POST /api/customerservice/transfers/request
Content-Type: application/json

{
  "ticket_id": 123,
  "from_service_id": 1,
  "to_service_id": 2,
  "user_id": 1001,
  "reason": "技术问题需要专业客服处理",
  "priority": 3,
  "transfer_type": 1
}
```

**参数说明：**
- `ticket_id`: 工单ID（必填）
- `from_service_id`: 转出客服ID（必填）
- `to_service_id`: 转入客服ID（必填）
- `user_id`: 用户ID（必填）
- `reason`: 转接原因（必填）
- `priority`: 优先级（1:低 2:中 3:高，默认2）
- `transfer_type`: 转接类型（1:手动 2:智能 3:负载均衡，默认1）

### 2. 接受转接

```http
POST /api/customerservice/transfers/accept
Content-Type: application/json

{
  "transfer_id": 123
}
```

### 3. 拒绝转接

```http
POST /api/customerservice/transfers/reject
Content-Type: application/json

{
  "transfer_id": 123,
  "reject_reason": "当前负载过高，无法接收"
}
```

### 4. 取消转接

```http
POST /api/customerservice/transfers/cancel
Content-Type: application/json

{
  "transfer_id": 123,
  "cancel_reason": "用户问题已解决"
}
```

### 5. 获取可转接客服列表

```http
GET /api/customerservice/transfers/services/available?current_service_id=1&tag_ids=1,2,3
```

### 6. 获取待处理转接

```http
GET /api/customerservice/transfers/pending?service_id=2
```

### 7. 获取转接历史

```http
GET /api/customerservice/transfers/history?user_id=1001
```

### 8. 批量转接

```http
POST /api/customerservice/transfers/batch
Content-Type: application/json

{
  "ticket_ids": [123, 124, 125],
  "from_service_id": 1,
  "to_service_id": 2,
  "reason": "负载均衡转接"
}
```

## 数据库结构

### 转接记录表 (sys_service_transfer)

| 字段 | 类型 | 说明 |
|------|------|------|
| id | bigint | 主键ID |
| ticket_id | int | 工单ID |
| from_service_id | bigint | 转出客服ID |
| to_service_id | bigint | 转入客服ID |
| user_id | int | 用户ID |
| reason | varchar(500) | 转接原因 |
| status | tinyint | 状态(1:待接收 2:已接收 3:已拒绝 4:已取消 5:已超时) |
| priority | tinyint | 优先级(1:低 2:中 3:高) |
| transfer_type | tinyint | 转接类型(1:主动 2:智能 3:负载均衡) |
| transfer_time | bigint | 转接时间 |
| accept_time | bigint | 接收时间 |
| expire_time | bigint | 过期时间 |
| reject_reason | varchar(500) | 拒绝原因 |
| cancel_reason | varchar(500) | 取消原因 |
| user_tags | text | 用户标签JSON |
| from_service_info | text | 转出客服信息JSON |
| to_service_info | text | 转入客服信息JSON |

## WebSocket通知

### 通知类型

1. **transfer_request** - 转接请求
2. **transfer_accepted** - 转接已接受
3. **transfer_rejected** - 转接已拒绝
4. **transfer_canceled** - 转接已取消
5. **transfer_expired** - 转接已过期
6. **service_changed** - 客服变更（通知用户）

### 通知格式

```json
{
  "type": "transfer_request",
  "data": {
    "transfer_id": 123,
    "from_service_id": 1,
    "user_id": 1001,
    "reason": "技术问题需要专业客服处理",
    "priority": 3,
    "expire_time": 1640995200
  },
  "timestamp": 1640995200
}
```

## 使用场景

### 1. 技术问题转接
```javascript
// 前端发起技术问题转接
const transferRequest = {
  ticket_id: currentTicket.id,
  from_service_id: currentService.id,
  to_service_id: techSupportService.id,
  user_id: currentUser.id,
  reason: "用户遇到技术问题，需要技术支持处理",
  priority: 3
};

fetch('/api/customerservice/transfers/request', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(transferRequest)
});
```

### 2. VIP客户转接
```javascript
// 自动检测VIP客户并推荐高级客服
const vipTransfer = {
  ticket_id: currentTicket.id,
  from_service_id: currentService.id,
  to_service_id: vipService.id,
  user_id: vipUser.id,
  reason: "VIP客户需要高级客服专属服务",
  priority: 3,
  transfer_type: 2 // 智能转接
};
```

### 3. 负载均衡转接
```javascript
// 系统自动进行负载均衡
const balanceTransfer = {
  ticket_ids: overloadedTickets,
  from_service_id: busyService.id,
  to_service_id: availableService.id,
  reason: "负载均衡，优化服务质量",
  transfer_type: 3 // 负载均衡转接
};
```

## 最佳实践

### 1. 转接时机
- 遇到专业问题时及时转接
- 客服负载过高时主动转接
- VIP客户优先转接给高级客服

### 2. 转接原因
- 详细说明转接原因
- 包含用户问题的关键信息
- 便于接收客服快速了解情况

### 3. 响应时效
- 及时处理转接请求
- 合理设置转接优先级
- 避免转接请求过期

### 4. 用户体验
- 转接过程中保持与用户沟通
- 告知用户转接原因和预期等待时间
- 确保转接后的服务连续性

## 监控和统计

### 转接指标
- 转接成功率
- 平均响应时间
- 转接原因分析
- 客服转接负载

### 性能优化
- 定期清理过期转接
- 优化转接推荐算法
- 监控WebSocket连接状态
- 分析转接模式和趋势

## 故障排查

### 常见问题
1. **转接请求失败** - 检查工单状态和客服可用性
2. **通知未收到** - 检查WebSocket连接状态
3. **转接超时** - 检查过期时间设置和清理机制
4. **权限问题** - 验证客服转接权限配置

### 日志监控
- 转接请求日志
- WebSocket通知日志
- 数据库操作日志
- 错误异常日志
