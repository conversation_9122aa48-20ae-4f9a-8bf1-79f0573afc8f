# 邀请码系统使用指南

## 系统概述

新的邀请码系统支持1对多的消耗型邀请码，管理员可以给用户分配邀请码，用户在注册时使用邀请码会根据绑定的等级直接获得对应的会员等级。

## 主要特性

1. **消耗型邀请码**：每个邀请码只能使用一次
2. **等级绑定**：邀请码绑定特定的会员等级
3. **类型支持**：支持平台生成和用户生成两种类型
4. **有效期管理**：支持设置邀请码过期时间
5. **状态跟踪**：完整的邀请码状态管理

## API 接口

### 后台管理接口

#### 1. 创建邀请码
```
POST /api/v1/jyh/invite-code/admin
{
  "type": "platform",        // 邀请码类型：platform-平台生成 user-用户生成
  "user_id": null,           // 生成用户ID（平台生成时为null）
  "level_id": 1,             // 会员等级ID
  "sale_price": 0,           // 出售价格（单位分）
  "validity_days": 30        // 有效期（天）
}
```

#### 2. 批量创建邀请码
```
POST /api/v1/jyh/invite-code/admin/batch
{
  "type": "platform",
  "level_id": 1,
  "quantity": 10,            // 创建数量
  "validity_days": 30
}
```

#### 3. 获取邀请码列表
```
GET /api/v1/jyh/invite-code/admin/list?page=1&page_size=20&type=platform&status=0
```

#### 4. 删除邀请码
```
DELETE /api/v1/jyh/invite-code/admin/{id}
```

#### 5. 更新邀请码状态
```
PUT /api/v1/jyh/invite-code/admin/status
{
  "id": 1,
  "status": 2  // 0-未使用 1-已使用 2-已过期
}
```

#### 6. 获取统计信息
```
GET /api/v1/jyh/invite-code/admin/statistics
```

### 用户端接口

#### 验证邀请码
```
GET /api/v1/jyh/invite-code/validate?code=ABC12345
```

## 使用流程

### 1. 管理员创建邀请码

1. 登录后台管理系统
2. 访问邀请码管理页面
3. 选择创建单个邀请码或批量创建
4. 选择要绑定的会员等级
5. 设置有效期
6. 提交创建

### 2. 用户使用邀请码注册

1. 用户在注册页面输入邀请码
2. 系统验证邀请码有效性
3. 注册成功后自动获得邀请码绑定的会员等级
4. 邀请码状态变为已使用

### 3. 邀请码状态管理

- **未使用(0)**：邀请码创建后的初始状态
- **已使用(1)**：用户注册时使用后的状态
- **已过期(2)**：超过有效期或管理员手动设置

## 数据库结构

### 邀请码表 (jyh_invite_code)
```sql
- id: 主键
- type: 邀请码类型（user/platform）
- user_id: 生成用户ID（可为空）
- code: 唯一邀请码
- level_id: 绑定的会员等级ID
- sale_price: 出售价格（分）
- used_at: 使用时间
- used_by_uid: 使用者用户ID
- status: 状态（0/1/2）
- is_used: 是否已使用
- expired_at: 过期时间
```

### 邀请记录表 (jyh_invite_record)
```sql
- id: 主键
- inviter_id: 邀请人ID
- invitee_id: 被邀请人ID
- code_used: 使用的邀请码
```

## 业务逻辑

### 邀请码验证流程
1. 检查邀请码是否存在
2. 检查是否已被使用
3. 检查是否已过期
4. 检查状态是否有效

### 用户注册流程
1. 验证手机号和验证码
2. 验证邀请码有效性
3. 创建用户账号
4. 标记邀请码为已使用
5. 根据邀请码等级分配用户等级
6. 创建邀请记录

## 注意事项

1. **邀请码唯一性**：系统自动生成8位字母数字组合的唯一邀请码
2. **等级分配**：注册成功后会自动为用户分配邀请码绑定的会员等级
3. **事务安全**：整个注册流程在事务中执行，确保数据一致性
4. **兼容性**：保留了原有的用户表邀请码字段，确保系统兼容性
5. **权限控制**：后台管理接口需要相应的权限验证

## 示例场景

### 场景1：平台推广活动
1. 管理员批量创建100个"炬火达人"等级的邀请码
2. 设置30天有效期
3. 通过各种渠道分发邀请码
4. 用户使用邀请码注册后自动获得"炬火达人"等级

### 场景2：用户推荐奖励
1. 系统为达到条件的用户生成推荐邀请码
2. 用户分享邀请码给朋友
3. 朋友注册后获得指定等级
4. 系统记录推荐关系用于后续奖励计算

## 错误处理

常见错误及处理方式：
- 邀请码不存在：返回"邀请码不存在"
- 邀请码已使用：返回"邀请码已被使用"
- 邀请码已过期：返回"邀请码已过期"
- 等级不存在：注册成功但不分配等级，记录错误日志 