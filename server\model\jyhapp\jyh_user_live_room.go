package jyhapp

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// JyhUserLiveRoom 用户直播房间绑定表
type JyhUserLiveRoom struct {
	global.GVA_MODEL
	UserID                  uint       `gorm:"not null;index;comment:用户ID" json:"userId"`
	ChannelId               int64      `gorm:"not null;comment:保利威频道ID" json:"channelId"`
	ChannelName             string     `gorm:"type:varchar(255);not null;comment:直播间名称" json:"channelName"`
	PolyvUserId             string     `gorm:"type:varchar(100);comment:保利威用户ID" json:"polyvUserId"`
	Scene                   *string    `gorm:"type:varchar(50);comment:直播场景" json:"scene"`
	ChannelPasswd           string     `gorm:"type:varchar(100);comment:频道密码" json:"channelPasswd"`
	SeminarHostPassword     *string    `gorm:"type:varchar(100);comment:研讨会主持人密码" json:"seminarHostPassword"`
	SeminarAttendeePassword *string    `gorm:"type:varchar(100);comment:研讨会参会人密码" json:"seminarAttendeePassword"`
	Status                  string     `gorm:"type:enum('active','inactive','disabled');default:'active';comment:状态：active-可用，inactive-不可用，disabled-禁用" json:"status"`
	LastUsedAt              *time.Time `gorm:"comment:最后使用时间" json:"lastUsedAt"`

	// 关联关系
	User JyhUser `gorm:"foreignKey:UserID" json:"user"`
}

// TableName JyhUserLiveRoom 表名
func (JyhUserLiveRoom) TableName() string {
	return "jyh_user_live_room"
}
