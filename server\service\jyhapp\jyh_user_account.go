package jyhapp

import (
	"errors"
	"fmt"
	"time"

	"github.com/xtulnx/jkit-go/jtime"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/query"
	jyhReq "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/response"
	jyhResp "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/response"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type JyhUserAccountService struct{}

// GetOrCreateUserAccount 获取或创建用户账户
func (service *JyhUserAccountService) GetOrCreateUserAccount(userID uint) (*jyhapp.JyhUserAccount, error) {
	return service.TyrQueryCreateUserAccount(query.Q, userID)
}

// TyrQueryCreateUserAccount 获取或创建用户账户（支持 gen query 事务）
func (service *JyhUserAccountService) TyrQueryCreateUserAccount(q *query.Query, userID uint) (*jyhapp.JyhUserAccount, error) {
	dbUserAccount := q.JyhUserAccount
	account, err := dbUserAccount.Where(dbUserAccount.UserID.Eq(userID)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 创建新账户
			newAccount := &jyhapp.JyhUserAccount{
				UserID:           userID,
				DyBalance:        decimal.Zero,
				JyhBalance:       decimal.Zero,
				WithdrawnBalance: decimal.Zero,
				CurrentBalance:   decimal.Zero,
				Version:          0,
			}

			if err := dbUserAccount.Create(newAccount); err != nil {
				return nil, fmt.Errorf("创建用户账户失败: %w", err)
			}
			return newAccount, nil
		}
		return nil, fmt.Errorf("查询用户账户失败: %w", err)
	}

	return account, nil
}

// GetUserAccountSummary 获取用户余额汇总 - 用户端接口1
func (service *JyhUserAccountService) GetUserAccountSummary(userID uint) (resp *jyhResp.UserAccountDetail, err error) {
	err = TryTransaction(func(tx *query.Query) (err error) {
		account, err := service.TyrQueryCreateUserAccount(tx, userID)
		if err != nil {
			return
		}
		// 统计交易信息
		var totalIncomeAmount, totalWithdrawals decimal.Decimal
		var transactionCount int64

		dbTransaction := query.JyhUserAccountTransaction

		// 计算总收入（佣金收入 + 退款）
		err = dbTransaction.Where(
			dbTransaction.AccountID.Eq(account.AccountID),
			dbTransaction.TransactionType.In(jyhapp.TransactionTypeCommissionIncome, jyhapp.TransactionTypeRefund),
			dbTransaction.TransactionStatus.Eq(jyhapp.TransactionStatusSuccess),
		).Select(dbTransaction.Amount.Sum().As("total_income")).Scan(&totalIncomeAmount)
		if err != nil {
			totalIncomeAmount = decimal.Zero
		}

		// 计算总提现
		err = dbTransaction.Where(
			dbTransaction.AccountID.Eq(account.AccountID),
			dbTransaction.TransactionType.Eq(jyhapp.TransactionTypeWithdrawal),
			dbTransaction.TransactionStatus.Eq(jyhapp.TransactionStatusSuccess),
		).Select(dbTransaction.Amount.Sum().As("total_withdrawals")).Scan(&totalWithdrawals)
		if err != nil {
			totalWithdrawals = decimal.Zero
		}

		// 计算交易次数
		transactionCount, err = dbTransaction.Where(
			dbTransaction.AccountID.Eq(account.AccountID),
			dbTransaction.TransactionStatus.Eq(jyhapp.TransactionStatusSuccess),
		).Count()
		if err != nil {
			transactionCount = 0
		}

		var totalPoints uint
		dbUser := tx.JyhUser
		err = dbUser.Select(dbUser.Points).Where(dbUser.ID.Eq(userID)).Scan(&totalPoints)
		if err != nil {
			totalPoints = 0
		}

		resp = &jyhResp.UserAccountDetail{
			AccountID:         account.AccountID,
			UserID:            account.UserID,
			DyBalance:         account.DyBalance,
			JyhBalance:        account.JyhBalance,
			WithdrawnBalance:  account.WithdrawnBalance,
			CurrentBalance:    account.CurrentBalance,
			LastUpdatedAt:     formatTimePtr(account.LastUpdatedAt),
			TotalIncomeAmount: totalIncomeAmount,
			TotalWithdrawals:  totalWithdrawals,
			TransactionCount:  transactionCount,
			TotalPoints:       totalPoints, // 假设积分统计逻辑在其他地方处理
		}
		return
	})
	if err != nil {
		return
	}
	return
}

// GetUserTransactionList 获取用户交易明细列表 - 用户端接口2
func (service *JyhUserAccountService) GetUserTransactionList(userID uint, req *jyhReq.TransactionListReq) (*jyhResp.TransactionListResp, error) {
	// 获取用户账户
	account, err := service.GetOrCreateUserAccount(userID)
	if err != nil {
		return nil, err
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	dbTransaction := query.JyhUserAccountTransaction
	queryBuilder := dbTransaction.Where(dbTransaction.AccountID.Eq(account.AccountID))

	// 添加查询条件
	if req.TransactionType != "" {
		queryBuilder = queryBuilder.Where(dbTransaction.TransactionType.Eq(req.TransactionType))
	}
	if req.SourceType != "" {
		queryBuilder = queryBuilder.Where(dbTransaction.SourceType.Eq(req.SourceType))
	}
	if req.TransactionStatus != "" {
		queryBuilder = queryBuilder.Where(dbTransaction.TransactionStatus.Eq(req.TransactionStatus))
	}
	if date := jtime.Str2Date(req.StartTime); !date.IsZero() {
		queryBuilder = queryBuilder.Where(dbTransaction.CreatedAt.Date().Gte(date))
	}
	if date := jtime.Str2Date(req.EndTime); !date.IsZero() {
		queryBuilder = queryBuilder.Where(dbTransaction.CreatedAt.Date().Lte(date))
	}
	if req.MinAmount != nil {
		queryBuilder = queryBuilder.Where(dbTransaction.Amount.Gte(*req.MinAmount))
	}
	if req.MaxAmount != nil {
		queryBuilder = queryBuilder.Where(dbTransaction.Amount.Lte(*req.MaxAmount))
	}

	// 查询总数
	total, err := queryBuilder.Count()
	if err != nil {
		return nil, fmt.Errorf("查询交易记录总数失败: %w", err)
	}

	// 查询数据
	offset := (req.Page - 1) * req.PageSize
	transactions, err := queryBuilder.Order(dbTransaction.CreatedAt.Desc()).
		Limit(req.PageSize).
		Offset(offset).
		Find()
	if err != nil {
		return nil, fmt.Errorf("查询交易记录失败: %w", err)
	}
	// 转换为响应格式
	var items []*jyhResp.TransactionItem
	for _, transaction := range transactions {
		item := &jyhResp.TransactionItem{
			TransactionID:          transaction.TransactionID,
			AccountID:              transaction.AccountID,
			ChangeType:             transaction.ChangeType,
			UserID:                 userID,
			Amount:                 transaction.Amount,
			TransactionType:        transaction.TransactionType,
			TransactionTypeName:    jyhapp.TransactionTypeName(transaction.TransactionType),
			SourceType:             transaction.SourceType,
			SourceTypeName:         jyhapp.SourceTypeName(transaction.SourceType),
			RelatedBusinessID:      transaction.RelatedBusinessID,
			TransactionDescription: transaction.TransactionDescription,
			TransactionStatus:      transaction.TransactionStatus,
			TransactionStatusName:  jyhapp.TransactionStatusName(transaction.TransactionStatus),
			BalanceBefore:          transaction.BalanceBefore,
			BalanceAfter:           transaction.BalanceAfter,
			CreatedAt:              transaction.CreatedAt.Format("2006-01-02 15:04:05"),
			CompletedAt:            formatTimePtrToPtr(transaction.CompletedAt),
		}
		items = append(items, item)
	}

	return &jyhResp.TransactionListResp{
		List:     items,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, nil
}

// GetUserAccountDetail 用户端查看余额明细 - 用户端接口4
func (service *JyhUserAccountService) GetUserAccountDetail(userID uint) (*jyhResp.UserAccountDetail, error) {
	// 复用余额汇总接口的逻辑
	return service.GetUserAccountSummary(userID)
}

// AddCommissionIncome 添加佣金收入（带乐观锁重试机制）
func (service *JyhUserAccountService) AddCommissionIncome(userID uint, amount decimal.Decimal, sourceType string, relatedBusinessID *uint, description string) error {
	if amount.LessThanOrEqual(decimal.Zero) {
		return errors.New("佣金金额必须大于0")
	}

	err := TryTransaction(func(tx *query.Query) error {
		return service.TryAddAccount(tx, TryAddIncomeParams{
			UserID:            userID,
			Amount:            amount,
			SourceType:        sourceType,
			RelatedBusinessID: relatedBusinessID,
			Description:       description,
		})
	})
	if err != nil {
		err = errors.New("操作失败，请重试")
		return err
	}
	return nil
}

type TryAddIncomeParams struct {
	UserID            uint            //用户id
	Amount            decimal.Decimal //变动金额
	SourceType        string          //数据变动类型
	RelatedBusinessID *uint           //业务关联id 可选
	Description       string          //变动信息
}

// TryAddAccount 添加佣金收入的单次尝试
func (service *JyhUserAccountService) TryAddAccount(tx *query.Query, params TryAddIncomeParams) error {
	var (
		userID            = params.UserID
		amount            = params.Amount
		sourceType        = params.SourceType
		relatedBusinessID = params.RelatedBusinessID
		description       = params.Description
	)

	account, err := service.TyrQueryCreateUserAccount(tx, userID)
	if err != nil {
		return fmt.Errorf("查询用户账户失败: %w", err)
	}
	balanceBefore := account.CurrentBalance
	switch sourceType {
	case jyhapp.SourceTypeDy:
		account.DyBalance = account.DyBalance.Add(amount)
	case jyhapp.SourceTypeJyh:
		account.JyhBalance = account.JyhBalance.Add(amount)
	default:
		return errors.New("无效的资金来源类型")
	}
	account.UpdateCurrentBalance()
	originalVersion := account.Version
	dbUserAccount := tx.JyhUserAccount
	result, err := dbUserAccount.Where(
		dbUserAccount.AccountID.Eq(account.AccountID),
		dbUserAccount.Version.Eq(originalVersion),
	).Updates(map[string]interface{}{
		"dy_balance":        account.DyBalance,
		"jyh_balance":       account.JyhBalance,
		"withdrawn_balance": account.WithdrawnBalance,
		"current_balance":   account.CurrentBalance,
		"version":           originalVersion + 1,
		"last_updated_at":   time.Now(),
	})
	if err != nil {
		return fmt.Errorf("更新账户余额失败: %w", err)
	}
	if result.RowsAffected == 0 {
		return errors.New("乐观锁冲突，账户已被其他事务修改")
	}
	transaction := &jyhapp.JyhUserAccountTransaction{
		AccountID:              account.AccountID,
		Amount:                 amount,
		TransactionType:        jyhapp.TransactionTypeCommissionIncome,
		SourceType:             sourceType,
		RelatedBusinessID:      relatedBusinessID,
		TransactionDescription: description,
		TransactionStatus:      jyhapp.TransactionStatusSuccess,
		BalanceBefore:          balanceBefore,
		BalanceAfter:           account.CurrentBalance,
	}
	transaction.MarkAsSuccess()
	dbTransaction := tx.JyhUserAccountTransaction
	if err := dbTransaction.Create(transaction); err != nil {
		return fmt.Errorf("创建交易记录失败: %w", err)
	}
	return nil
}

// ProcessWithdrawal 处理提现申请（带乐观锁重试机制）
func (service *JyhUserAccountService) ProcessWithdrawal(userID uint, amount decimal.Decimal, relatedBusinessID *uint, description string) error {
	if amount.LessThanOrEqual(decimal.Zero) && amount.LessThan(decimal.Zero) {
		return errors.New("提现金额必须大于0")
	}
	return TryTransaction(func(tx *query.Query) error {
		err := service.TryReduceAccount(tx, userID, amount, relatedBusinessID, description)
		if err != nil {
			return err
		}
		return nil
	})
}

// TryReduceAccount 处理提现
func (service *JyhUserAccountService) TryReduceAccount(tx *query.Query, userID uint, amount decimal.Decimal, relatedBusinessID *uint, description string) error {
	account, err := service.TyrQueryCreateUserAccount(tx, userID)
	if err != nil {
		return fmt.Errorf("查询用户账户失败: %w", err)
	}
	if !account.CanWithdraw(amount) {
		return errors.New("余额不足，无法提现")
	}
	balanceBefore := account.CurrentBalance
	account.WithdrawnBalance = account.WithdrawnBalance.Add(amount)
	account.UpdateCurrentBalance()
	originalVersion := account.Version
	dbUserAccount := tx.JyhUserAccount
	result, err := dbUserAccount.Where(
		dbUserAccount.AccountID.Eq(account.AccountID),
		dbUserAccount.Version.Eq(originalVersion),
	).Updates(map[string]interface{}{
		"withdrawn_balance": account.WithdrawnBalance,
		"current_balance":   account.CurrentBalance,
		"version":           originalVersion + 1,
		"last_updated_at":   time.Now(),
	})
	if err != nil {
		return fmt.Errorf("更新账户余额失败: %w", err)
	}
	if result.RowsAffected == 0 {
		return errors.New("乐观锁冲突，账户已被其他事务修改")
	}
	// 6. 创建交易记录
	transaction := &jyhapp.JyhUserAccountTransaction{
		AccountID:              account.AccountID,
		Amount:                 amount,
		TransactionType:        jyhapp.TransactionTypeWithdrawal,
		SourceType:             jyhapp.SourceTypeWithdrawalOut,
		RelatedBusinessID:      relatedBusinessID,
		TransactionDescription: description,
		TransactionStatus:      jyhapp.TransactionStatusSuccess,
		BalanceBefore:          balanceBefore,
		BalanceAfter:           account.CurrentBalance,
	}
	transaction.MarkAsSuccess()
	dbTransaction := tx.JyhUserAccountTransaction
	if err := dbTransaction.Create(transaction); err != nil {
		return fmt.Errorf("创建交易记录失败: %w", err)
	}
	return nil
}

// GetUserAccountWithTransactions 获取用户账户及交易记录
func (service *JyhUserAccountService) GetUserAccountWithTransactions(userID uint, limit int, offset int) (*jyhapp.JyhUserAccount, []*jyhapp.JyhUserAccountTransaction, error) {
	account, err := service.GetOrCreateUserAccount(userID)
	if err != nil {
		return nil, nil, err
	}

	dbTransaction := query.JyhUserAccountTransaction
	transactions, err := dbTransaction.Where(dbTransaction.AccountID.Eq(account.AccountID)).
		Order(dbTransaction.CreatedAt.Desc()).
		Limit(limit).
		Offset(offset).
		Find()

	if err != nil {
		return nil, nil, fmt.Errorf("查询交易记录失败: %w", err)
	}

	return account, transactions, nil
}

// ValidateAccountBalance 验证账户余额一致性
func (service *JyhUserAccountService) ValidateAccountBalance(accountID uint) (bool, error) {
	dbUserAccount := query.JyhUserAccount
	account, err := dbUserAccount.Where(dbUserAccount.AccountID.Eq(accountID)).First()
	if err != nil {
		return false, fmt.Errorf("查询账户失败: %w", err)
	}

	// 验证计算的余额是否与存储的余额一致
	return account.ValidateBalance(), nil
}

// formatTimePtr 格式化时间指针
func formatTimePtr(t *time.Time) string {
	if t == nil {
		return ""
	}
	return t.Format("2006-01-02 15:04:05")
}

// formatTimePtrToPtr 格式化时间指针为字符串指针
func formatTimePtrToPtr(t *time.Time) *string {
	if t == nil {
		return nil
	}
	formatted := t.Format("2006-01-02 15:04:05")
	return &formatted
}

// GetAccountStatistics 获取账户统计信息
func (s *JyhUserAccountService) GetAccountStatistics() (*response.UserAccountStatistics, error) {
	var statistics response.UserAccountStatistics

	dbUserAccount := query.JyhUserAccount
	dbTransaction := query.JyhUserAccountTransaction

	// 获取账户总数
	accountCount, err := dbUserAccount.Count()
	if err != nil {
		return nil, fmt.Errorf("获取账户总数失败: %v", err)
	}
	statistics.TotalAccounts = accountCount

	// 获取余额统计
	var balanceStats struct {
		TotalCurrentBalance decimal.Decimal `gorm:"column:total_current_balance"`
		TotalDyBalance      decimal.Decimal `gorm:"column:total_dy_balance"`
		TotalJyhBalance     decimal.Decimal `gorm:"column:total_jyh_balance"`
		TotalWithdrawn      decimal.Decimal `gorm:"column:total_withdrawn"`
	}

	err = dbUserAccount.Select(
		dbUserAccount.CurrentBalance.Sum().As("total_current_balance"),
		dbUserAccount.DyBalance.Sum().As("total_dy_balance"),
		dbUserAccount.JyhBalance.Sum().As("total_jyh_balance"),
		dbUserAccount.WithdrawnBalance.Sum().As("total_withdrawn"),
	).Scan(&balanceStats)

	if err != nil {
		return nil, fmt.Errorf("获取余额统计失败: %v", err)
	}

	statistics.TotalCurrentBalance = balanceStats.TotalCurrentBalance
	statistics.TotalDyBalance = balanceStats.TotalDyBalance
	statistics.TotalJyhBalance = balanceStats.TotalJyhBalance
	statistics.TotalWithdrawn = balanceStats.TotalWithdrawn

	// 获取交易总数
	transactionCount, err := dbTransaction.Count()
	if err != nil {
		return nil, fmt.Errorf("获取交易总数失败: %v", err)
	}
	statistics.TotalTransactions = transactionCount

	// 获取今日交易统计
	today := time.Now().Format("2006-01-02")
	todayStart := today + " 00:00:00"
	todayEnd := today + " 23:59:59"

	// 今日交易数
	todayTransactionCount, err := dbTransaction.Where(
		dbTransaction.CreatedAt.Between(jtime.Str2Date(todayStart), jtime.Str2Date(todayEnd)),
	).Count()
	if err != nil {
		return nil, fmt.Errorf("获取今日交易数失败: %v", err)
	}
	statistics.TodayTransactions = todayTransactionCount

	// 今日收入和提现统计
	var todayStats struct {
		TodayIncomeAmount   decimal.Decimal `gorm:"column:today_income_amount"`
		TodayWithdrawAmount decimal.Decimal `gorm:"column:today_withdraw_amount"`
	}

	// 今日收入统计
	err = dbTransaction.Where(
		dbTransaction.CreatedAt.Between(jtime.Str2Date(todayStart), jtime.Str2Date(todayEnd)),
		dbTransaction.TransactionType.Eq(jyhapp.TransactionTypeCommissionIncome),
	).Select(dbTransaction.Amount.Sum().As("today_income_amount")).Scan(&todayStats.TodayIncomeAmount)
	if err != nil {
		todayStats.TodayIncomeAmount = decimal.Zero
	}

	// 今日提现统计
	err = dbTransaction.Where(
		dbTransaction.CreatedAt.Between(jtime.Str2Date(todayStart), jtime.Str2Date(todayEnd)),
		dbTransaction.TransactionType.Eq(jyhapp.TransactionTypeWithdrawal),
	).Select(dbTransaction.Amount.Sum().As("today_withdraw_amount")).Scan(&todayStats.TodayWithdrawAmount)
	if err != nil {
		todayStats.TodayWithdrawAmount = decimal.Zero
	}

	statistics.TodayIncomeAmount = todayStats.TodayIncomeAmount
	statistics.TodayWithdrawAmount = todayStats.TodayWithdrawAmount

	return &statistics, nil
}
