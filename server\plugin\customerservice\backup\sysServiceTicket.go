package model

import "github.com/flipped-aurora/gin-vue-admin/server/global"

// SysServiceTicket 客服工单表（系统自动创建）
type SysServiceTicket struct {
	global.Model1
	TicketNo    string `json:"ticket_no" gorm:"comment:工单号，自动生成"`
	UserID      uint   `json:"user_id" gorm:"comment:用户ID"`
	ServiceID   int64  `json:"service_id" gorm:"comment:分配的客服ID"`
	Priority    int    `json:"priority" gorm:"comment:优先级(1:高 2:中 3:低);default:3"`
	Status      uint   `json:"status" gorm:"comment:状态(1:待接单 2:服务中 3:已完成 4:已关闭);default:1"`
	UserTagIDs  string `json:"user_tag_ids" gorm:"comment:用户标签ID，JSON格式"`
	StartTime   int64  `json:"start_time" gorm:"comment:开始服务时间"`
	EndTime     int64  `json:"end_time" gorm:"comment:结束时间"`
	CloseReason string `json:"close_reason" gorm:"comment:关闭原因"`
	Summary     string `json:"summary" gorm:"comment:服务总结"`
}

func (SysServiceTicket) TableName() string {
	return "sys_service_ticket"
}

// 工单状态常量
const (
	TicketStatusPending   = 1 // 待接单
	TicketStatusServing   = 2 // 服务中
	TicketStatusCompleted = 3 // 已完成
	TicketStatusClosed    = 4 // 已关闭
)

// 工单优先级常量
const (
	TicketPriorityHigh   = 1 // 高优先级
	TicketPriorityMedium = 2 // 中优先级
	TicketPriorityLow    = 3 // 低优先级
)
