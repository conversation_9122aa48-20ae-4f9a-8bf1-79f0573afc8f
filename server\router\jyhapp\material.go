package jyhapp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type MaterialRouter struct {
}

// InitMaterialRouter 初始化 MaterialRouter 路由信息
func (m *MaterialRouter) InitMaterialRouter(Router *gin.RouterGroup, JyhRouter *gin.RouterGroup) {
	materialRouter := Router.Group("material").Use(middleware.OperationRecord())
	materialRouterWithoutRecord := Router.Group("material")

	//materialJyhRouter := Router.Group("material").Use(middleware.OperationRecord())
	//materialJyhRouterWithoutRecord := JyhRouter.Group("material")
	var materialApi = v1.ApiGroupApp.JyhApiGroup.MaterialApi
	{
		// 素材相关
		materialRouter.POST("create", materialApi.Create)   // 新建素材
		materialRouter.DELETE("delete", materialApi.Delete) // 删除素材
		//materialRouter.DELETE("deleteByIds", materialApi.DeleteMaterialByIds) // 批量删除素材
		materialRouter.PUT("update", materialApi.Update)                                // 更新素材
		materialRouter.POST("sendToUser", materialApi.SendToUser)                       // 下发素材给用户
		materialRouter.POST("createRuleConfig", materialApi.CreateMaterialRuleConfig)   // 创建素材配置规则
		materialRouter.PUT("updateRuleConfig", materialApi.UpdateMaterialRuleConfig)    // 更新素材配置规则
		materialRouter.DELETE("deleteRuleConfig", materialApi.DeleteMaterialRuleConfig) // 删除素材配置规则
	}
	{
		materialRouterWithoutRecord.GET("detail", materialApi.GetDetail) // 根据ID获取素材
		materialRouterWithoutRecord.GET("list", materialApi.GetList)     // 获取素材分类列表
		//materialRouterWithoutRecord.GET("getTags", materialApi.GetTags)  // 获取所有标签
		materialRouterWithoutRecord.GET("sendRecords", materialApi.GetSendRecords)                  // 获取素材下发记录
		materialRouterWithoutRecord.GET("getRuleConfig", materialApi.GetMaterialRuleConfig)         // 获取素材配置规则
		materialRouterWithoutRecord.GET("getRuleConfigByID", materialApi.GetMaterialRuleConfigByID) // 根据ID获取素材配置规则详情
	}
	{
		materialRouter.POST("claim", materialApi.ClaimMaterial)       // 领取素材
		materialRouter.POST("custom", materialApi.CustomMaterial)     // 自定义素材
		materialRouter.POST("upload", materialApi.UploadAndRecognize) // 上传素材识图
	}
	{
		materialRouterWithoutRecord.GET("getMaterials", materialApi.GetMaterials)          // 获取素材分类列表
		materialRouterWithoutRecord.GET("getUserMaterials", materialApi.GetUserMaterials)  // 获取用户素材列表
		materialRouterWithoutRecord.GET("customMaterials", materialApi.GetCustomMaterials) // 获取自定义素材列表

	}
}
