package jyhapp

import (
	v1 "github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type UserContractRouter struct{}

func (r *UserContractRouter) InitUserContractRouter(publicGroup, JyhappGroup *gin.RouterGroup) {
	contractApi := v1.ApiGroupApp.JyhApiGroup.UserContractApi
	// 需要记录操作日志的接口
	contractRecord := JyhappGroup.Group("jyh/contract").Use(middleware.OperationRecord())
	{
		contractRecord.POST("send_code", contractApi.SendContractVerificationCode) // 发送签约验证码
		contractRecord.POST("verify_code", contractApi.VerifyContractCode)         // 验证签约验证码
		contractRecord.POST("user_info", contractApi.GetUserInfoByTicket)          // 根据ticket获取用户信息
		contractRecord.POST("account_bind", contractApi.AddAccountBind)            // 绑定用户账号到机构
		contractRecord.POST("bind_pid", contractApi.AddUserBindPid)                // 绑定结算账号
	}
}
