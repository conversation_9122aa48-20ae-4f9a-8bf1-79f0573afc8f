package jyhapp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	commonReq "github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type MaterialApi struct{}

// Create 创建素材
// @Tags      Material
// @Summary   创建素材分类
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.MaterialCreate  true  "创建素材"
// @Success   200   {object}  response.Response{msg=string}  "创建素材"
// @Router    /material/create [post]
func (m *MaterialApi) Create(c *gin.Context) {
	var req request.MaterialCreate
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = materialService.Create(&req)
	if err != nil {
		global.GVA_LOG.Error("创建素材失败!", zap.Error(err))
		response.FailWithMessage("创建素材失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建素材成功", c)
}

// Delete 删除素材
// @Tags      Material
// @Summary   删除素材
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      commonReq.GetById  true  "删除素材"
// @Success   200   {object}  response.Response{msg=string}  "删除素材"
// @Router    /material/delete [delete]
func (m *MaterialApi) Delete(c *gin.Context) {
	var id commonReq.GetById

	err := c.ShouldBindJSON(&id)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = materialService.Delete(id.Uint())
	if err != nil {
		global.GVA_LOG.Error("删除素材失败！", zap.Error(err))
		response.FailWithMessage("删除素材失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("删除素材成功", c)
}

// Update 更新素材
// @Tags      Material
// @Summary   更新素材
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.MaterialUpdate  true  "更新素材"
// @Success   200   {object}  response.Response{msg=string}  "更新素材"
// @Router    /material/update [put]
func (m *MaterialApi) Update(c *gin.Context) {
	var req request.MaterialUpdate

	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = materialService.Update(&req)
	if err != nil {
		global.GVA_LOG.Error("更新素材失败！", zap.Error(err))
		response.FailWithMessage("更新素材失败:"+err.Error(), c)
		return
	}

	response.OkWithMessage("更新素材成功", c)
}

// GetList 获取素材列表
// @Tags      Material
// @Summary   获取素材列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     request.MaterialSearch  true  "获取素材列表"
// @Success   200   {object}  response.Response{data=response.PageResult,msg=string}  "获取素材列表"
// @Router    /material/list [get]
func (m *MaterialApi) GetList(c *gin.Context) {
	var req request.MaterialSearch

	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	list, total, err := materialService.GetList(&req)
	if err != nil {
		global.GVA_LOG.Error("获取素材失败", zap.Error(err))
		response.FailWithMessage("获取素材失败"+err.Error(), c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取素材成功", c)
}

// GetDetail 获取素材详情
// @Tags      Material
// @Summary   获取素材详情
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     commonReq.GetById  true  "获取素材详情"
// @Success   200   {object}  response.Response{data=response.PageResult,msg=string}  "获取素材详情"
// @Router    /material/detail [get]
func (m *MaterialApi) GetDetail(c *gin.Context) {
	var req commonReq.GetById

	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	res, err := materialService.GetDetail(req.Uint())
	if err != nil {
		global.GVA_LOG.Error("查询素材失败", zap.Error(err))
		response.FailWithMessage("查询素材失败: "+err.Error(), c)
		return
	}

	response.OkWithData(res, c)
}

// GetMaterials 获取素材列表
// @Tags      Material
// @Summary   获取素材列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     request.MaterialFilterReq  true  "通过分类或者标签获取素材列表"
// @Success   200   {object}  response.Response{data=response.PageResult,msg=string}  "通过分类或者标签获取素材列表"
// @Router    /material/getMaterials [get]
func (m *MaterialApi) GetMaterials(c *gin.Context) {
	var req request.MaterialFilterReq
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}

	req.UserID = utils.GetUserID(c)
	materials, total, err := materialService.GetMaterialsByCategoryAndTags(req)
	if err != nil {
		response.FailWithMessage("查询失败", c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     materials,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "查询成功", c)
}

// ClaimMaterial 领取素材
// @Tags      Material
// @Summary   领取素材
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.MaterialClaim  true  "领取素材"
// @Success   200   {object}  response.Response{msg=string}  "领取素材"
// @Router    /material/claim [post]
func (m *MaterialApi) ClaimMaterial(c *gin.Context) {
	var req request.MaterialClaim
	req.UserID = utils.GetUserID(c)
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = materialService.MaterialClaim(req)
	if err != nil {
		global.GVA_LOG.Error("领取素材失败", zap.Error(err))
		response.FailWithMessage("领取素材失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("领取素材成功", c)
}

// GetUserMaterials 获取用户素材列表
// @Tags      Material
// @Summary   获取用户素材列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     request.UserMaterialSearch  true  "获取用户素材列表"
// @Success   200   {object}  response.Response{data=response.PageResult,msg=string}  "获取用户素材列表"
// @Router    /material/getUserMaterials [get]
func (m *MaterialApi) GetUserMaterials(c *gin.Context) {
	var req request.UserMaterialSearch

	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	req.UserID = utils.GetUserID(c)
	list, total, err := materialService.GetUserMaterials(&req)
	if err != nil {
		global.GVA_LOG.Error("获取用户已领取的素材列表失败", zap.Error(err))
		response.FailWithMessage("获取用户已领取的素材列表失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取用户已领取的素材列表成功", c)
}

// SendToUser 发送素材到用户
// @Tags      Material
// @Summary   发送素材到用户
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.MaterialSendToUser  true  "发送素材到用户"
// @Success   200   {object}  response.Response{msg=string}  "发送素材到用户"
// @Router    /material/sendToUser [post]
func (m *MaterialApi) SendToUser(c *gin.Context) {
	var req request.MaterialSendToUser

	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = materialService.SendToUser(req)
	if err != nil {
		global.GVA_LOG.Error("发送素材到用户失败", zap.Error(err))
		response.FailWithMessage("发送素材到用户失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("发送素材到用户成功", c)
}

// CustomMaterial 自定义素材
// @Tags      Material
// @Summary   自定义素材
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.CustomMaterialReq  true  "自定义素材"
// @Success   200   {object}  response.Response{msg=string}  "自定义素材"
// @Router    /material/customMaterial [post]
func (m *MaterialApi) CustomMaterial(c *gin.Context) {
	var req request.CustomMaterialReq
	req.UserID = utils.GetUserID(c)

	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = materialService.CustomMaterial(req)
	if err != nil {
		global.GVA_LOG.Error("自定义素材失败", zap.Error(err))
		response.FailWithMessage("自定义素材失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("自定义素材成功", c)
}

// GetCustomMaterials 获取自定义素材列表
// @Tags      Material
// @Summary   获取自定义素材列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     request.CustomMaterialSearch  true  "获取自定义素材列表"
// @Success   200   {object}  response.Response{data=response.PageResult,msg=string}  "获取自定义素材列表"
// @Router    /material/customMaterials [get]
func (m *MaterialApi) GetCustomMaterials(c *gin.Context) {
	var req request.CustomMaterialSearch

	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	list, total, err := materialService.GetCustomMaterials(req)
	if err != nil {
		global.GVA_LOG.Error("获取自定义素材列表失败", zap.Error(err))
		response.FailWithMessage("获取自定义素材列表失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取自定义素材列表成功", c)
}

// GetSendRecords 获取用户发送记录
// @Tags      Material
// @Summary   获取用户发送记录
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     request.UserMaterialSendRecordSearch  true  "获取用户发送记录"
// @Success   200   {object}  response.Response{data=response.PageResult,msg=string}  "获取用户发送记录"
// @Router    /material/sendRecords [get]
func (m *MaterialApi) GetSendRecords(c *gin.Context) {
	var req request.UserMaterialSendRecordSearch

	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	list, total, err := materialService.GetSendRecords(&req)
	if err != nil {
		global.GVA_LOG.Error("获取用户发送记录失败", zap.Error(err))
		response.FailWithMessage("获取用户发送记录失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取用户发送记录成功", c)
}

// UploadAndRecognize 上传素材并识别
// @Tags      Material
// @Summary   上传素材并识别
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.MaterialUploadAndRecognize  true  "上传素材并识别"
// @Success   200   {object}  response.Response{msg=string}  "上传素材并识别"
// @Router    /material/uploadAndRecognize [post]
func (m *MaterialApi) UploadAndRecognize(c *gin.Context) {
	var req request.MaterialUploadAndRecognize
	req.UserID = utils.GetUserID(c)

	// 解析表单数据
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err := materialService.UploadAndRecognize(req)
	if err != nil {
		global.GVA_LOG.Error("上传抖音链接失败", zap.Error(err))
		response.FailWithMessage("上传抖音链接失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("上传抖音链接成功", c)
}

// GetMaterialRuleConfig 获取素材规则配置
// @Tags      Material
// @Summary   获取素材规则配置
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     request.MaterialRuleConfigSearch  true  "获取素材规则配置"
// @Success   200   {object}  response.Response{data=response.PageResult,msg=string}  "获取素材规则配置"
// @Router    /material/getRuleConfig [get]
func (m *MaterialApi) GetMaterialRuleConfig(c *gin.Context) {
	var req request.MaterialRuleConfigSearch

	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	list, total, err := materialService.GetMaterialRuleConfig(&req)
	if err != nil {
		global.GVA_LOG.Error("获取素材规则配置失败", zap.Error(err))
		response.FailWithMessage("获取素材规则配置失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取素材规则配置成功", c)
}

// CreateMaterialRuleConfig 创建素材规则配置
// @Tags      Material
// @Summary   创建素材规则配置
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.MaterialRuleConfigCreate  true  "创建素材规则配置"
// @Success   200   {object}  response.Response{msg=string}  "创建素材规则配置"
// @Router    /material/createRuleConfig [post]
func (m *MaterialApi) CreateMaterialRuleConfig(c *gin.Context) {
	var req request.MaterialRuleConfigCreate

	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err := materialService.CreateMaterialRuleConfig(req)
	if err != nil {
		global.GVA_LOG.Error("创建素材规则配置失败", zap.Error(err))
		response.FailWithMessage("创建素材规则配置失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("创建素材规则配置成功", c)
}

// UpdateMaterialRuleConfig 更新素材规则配置
// @Tags      Material
// @Summary   更新素材规则配置
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.MaterialRuleConfigUpdate  true  "更新素材规则配置"
// @Success   200   {object}  response.Response{msg=string}  "更新素材规则配置"
// @Router    /material/updateRuleConfig [put]
func (m *MaterialApi) UpdateMaterialRuleConfig(c *gin.Context) {
	var req request.MaterialRuleConfigUpdate

	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err := materialService.UpdateMaterialRuleConfig(req)
	if err != nil {
		global.GVA_LOG.Error("更新素材规则配置失败", zap.Error(err))
		response.FailWithMessage("更新素材规则配置失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("更新素材规则配置成功", c)
}

// DeleteMaterialRuleConfig 删除素材规则配置
// @Tags      Material
// @Summary   删除素材规则配置
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      commonReq.GetById  true  "删除素材规则配置"
// @Success   200   {object}  response.Response{msg=string}  "删除素材规则配置"
// @Router    /material/deleteRuleConfig [delete]
func (m *MaterialApi) DeleteMaterialRuleConfig(c *gin.Context) {
	var req commonReq.GetById

	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err := materialService.DeleteMaterialRuleConfig(req.Uint())
	if err != nil {
		global.GVA_LOG.Error("删除素材规则配置失败", zap.Error(err))
		response.FailWithMessage("删除素材规则配置失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("删除素材规则配置成功", c)
}

// GetMaterialRuleConfigByID 获取素材规则配置详情
// @Tags      Material
// @Summary   获取素材规则配置详情
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     commonReq.GetById  true  "获取素材规则配置详情"
// @Success   200   {object}  response.Response{data=response.PageResult,msg=string}  "获取素材规则配置详情"
// @Router    /material/getRuleConfigByID [get]
func (m *MaterialApi) GetMaterialRuleConfigByID(c *gin.Context) {
	var req commonReq.GetById

	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	res, err := materialService.GetMaterialRuleConfigByID(req.Uint())
	if err != nil {
		global.GVA_LOG.Error("获取素材规则配置详情失败", zap.Error(err))
		response.FailWithMessage("获取素材规则配置详情失败: "+err.Error(), c)
		return
	}

	response.OkWithData(res, c)
}
