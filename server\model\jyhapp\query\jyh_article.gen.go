// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
)

func newJyhArticle(db *gorm.DB, opts ...gen.DOOption) jyhArticle {
	_jyhArticle := jyhArticle{}

	_jyhArticle.jyhArticleDo.UseDB(db, opts...)
	_jyhArticle.jyhArticleDo.UseModel(&jyhapp.JyhArticle{})

	tableName := _jyhArticle.jyhArticleDo.TableName()
	_jyhArticle.ALL = field.NewAsterisk(tableName)
	_jyhArticle.ID = field.NewUint(tableName, "id")
	_jyhArticle.CreatedAt = field.NewTime(tableName, "created_at")
	_jyhArticle.UpdatedAt = field.NewTime(tableName, "updated_at")
	_jyhArticle.DeletedAt = field.NewField(tableName, "deleted_at")
	_jyhArticle.Title = field.NewString(tableName, "title")
	_jyhArticle.CategoryID = field.NewUint(tableName, "category_id")
	_jyhArticle.Author = field.NewString(tableName, "author")
	_jyhArticle.Summary = field.NewString(tableName, "summary")
	_jyhArticle.Content = field.NewString(tableName, "content")
	_jyhArticle.CoverImage = field.NewString(tableName, "cover_image")
	_jyhArticle.Status = field.NewString(tableName, "status")
	_jyhArticle.IsTop = field.NewBool(tableName, "is_top")
	_jyhArticle.ViewCount = field.NewInt(tableName, "view_count")
	_jyhArticle.PublishedAt = field.NewTime(tableName, "published_at")
	_jyhArticle.Category = jyhArticleBelongsToCategory{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Category", "jyhapp.JyhArticleCategory"),
		Parent: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Category.Parent", "jyhapp.JyhArticleCategory"),
		},
		Children: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Category.Children", "jyhapp.JyhArticleCategory"),
		},
		Articles: struct {
			field.RelationField
			Category struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Category.Articles", "jyhapp.JyhArticle"),
			Category: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Category.Articles.Category", "jyhapp.JyhArticleCategory"),
			},
		},
	}

	_jyhArticle.fillFieldMap()

	return _jyhArticle
}

type jyhArticle struct {
	jyhArticleDo

	ALL         field.Asterisk
	ID          field.Uint
	CreatedAt   field.Time
	UpdatedAt   field.Time
	DeletedAt   field.Field
	Title       field.String
	CategoryID  field.Uint
	Author      field.String
	Summary     field.String
	Content     field.String
	CoverImage  field.String
	Status      field.String
	IsTop       field.Bool
	ViewCount   field.Int
	PublishedAt field.Time
	Category    jyhArticleBelongsToCategory

	fieldMap map[string]field.Expr
}

func (j jyhArticle) Table(newTableName string) *jyhArticle {
	j.jyhArticleDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j jyhArticle) As(alias string) *jyhArticle {
	j.jyhArticleDo.DO = *(j.jyhArticleDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *jyhArticle) updateTableName(table string) *jyhArticle {
	j.ALL = field.NewAsterisk(table)
	j.ID = field.NewUint(table, "id")
	j.CreatedAt = field.NewTime(table, "created_at")
	j.UpdatedAt = field.NewTime(table, "updated_at")
	j.DeletedAt = field.NewField(table, "deleted_at")
	j.Title = field.NewString(table, "title")
	j.CategoryID = field.NewUint(table, "category_id")
	j.Author = field.NewString(table, "author")
	j.Summary = field.NewString(table, "summary")
	j.Content = field.NewString(table, "content")
	j.CoverImage = field.NewString(table, "cover_image")
	j.Status = field.NewString(table, "status")
	j.IsTop = field.NewBool(table, "is_top")
	j.ViewCount = field.NewInt(table, "view_count")
	j.PublishedAt = field.NewTime(table, "published_at")

	j.fillFieldMap()

	return j
}

func (j *jyhArticle) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *jyhArticle) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 15)
	j.fieldMap["id"] = j.ID
	j.fieldMap["created_at"] = j.CreatedAt
	j.fieldMap["updated_at"] = j.UpdatedAt
	j.fieldMap["deleted_at"] = j.DeletedAt
	j.fieldMap["title"] = j.Title
	j.fieldMap["category_id"] = j.CategoryID
	j.fieldMap["author"] = j.Author
	j.fieldMap["summary"] = j.Summary
	j.fieldMap["content"] = j.Content
	j.fieldMap["cover_image"] = j.CoverImage
	j.fieldMap["status"] = j.Status
	j.fieldMap["is_top"] = j.IsTop
	j.fieldMap["view_count"] = j.ViewCount
	j.fieldMap["published_at"] = j.PublishedAt

}

func (j jyhArticle) clone(db *gorm.DB) jyhArticle {
	j.jyhArticleDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j jyhArticle) replaceDB(db *gorm.DB) jyhArticle {
	j.jyhArticleDo.ReplaceDB(db)
	return j
}

type jyhArticleBelongsToCategory struct {
	db *gorm.DB

	field.RelationField

	Parent struct {
		field.RelationField
	}
	Children struct {
		field.RelationField
	}
	Articles struct {
		field.RelationField
		Category struct {
			field.RelationField
		}
	}
}

func (a jyhArticleBelongsToCategory) Where(conds ...field.Expr) *jyhArticleBelongsToCategory {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a jyhArticleBelongsToCategory) WithContext(ctx context.Context) *jyhArticleBelongsToCategory {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a jyhArticleBelongsToCategory) Session(session *gorm.Session) *jyhArticleBelongsToCategory {
	a.db = a.db.Session(session)
	return &a
}

func (a jyhArticleBelongsToCategory) Model(m *jyhapp.JyhArticle) *jyhArticleBelongsToCategoryTx {
	return &jyhArticleBelongsToCategoryTx{a.db.Model(m).Association(a.Name())}
}

type jyhArticleBelongsToCategoryTx struct{ tx *gorm.Association }

func (a jyhArticleBelongsToCategoryTx) Find() (result *jyhapp.JyhArticleCategory, err error) {
	return result, a.tx.Find(&result)
}

func (a jyhArticleBelongsToCategoryTx) Append(values ...*jyhapp.JyhArticleCategory) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a jyhArticleBelongsToCategoryTx) Replace(values ...*jyhapp.JyhArticleCategory) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a jyhArticleBelongsToCategoryTx) Delete(values ...*jyhapp.JyhArticleCategory) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a jyhArticleBelongsToCategoryTx) Clear() error {
	return a.tx.Clear()
}

func (a jyhArticleBelongsToCategoryTx) Count() int64 {
	return a.tx.Count()
}

type jyhArticleDo struct{ gen.DO }

func (j jyhArticleDo) Debug() *jyhArticleDo {
	return j.withDO(j.DO.Debug())
}

func (j jyhArticleDo) WithContext(ctx context.Context) *jyhArticleDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jyhArticleDo) ReadDB() *jyhArticleDo {
	return j.Clauses(dbresolver.Read)
}

func (j jyhArticleDo) WriteDB() *jyhArticleDo {
	return j.Clauses(dbresolver.Write)
}

func (j jyhArticleDo) Session(config *gorm.Session) *jyhArticleDo {
	return j.withDO(j.DO.Session(config))
}

func (j jyhArticleDo) Clauses(conds ...clause.Expression) *jyhArticleDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jyhArticleDo) Returning(value interface{}, columns ...string) *jyhArticleDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jyhArticleDo) Not(conds ...gen.Condition) *jyhArticleDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jyhArticleDo) Or(conds ...gen.Condition) *jyhArticleDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jyhArticleDo) Select(conds ...field.Expr) *jyhArticleDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jyhArticleDo) Where(conds ...gen.Condition) *jyhArticleDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jyhArticleDo) Order(conds ...field.Expr) *jyhArticleDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jyhArticleDo) Distinct(cols ...field.Expr) *jyhArticleDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jyhArticleDo) Omit(cols ...field.Expr) *jyhArticleDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jyhArticleDo) Join(table schema.Tabler, on ...field.Expr) *jyhArticleDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jyhArticleDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jyhArticleDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jyhArticleDo) RightJoin(table schema.Tabler, on ...field.Expr) *jyhArticleDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jyhArticleDo) Group(cols ...field.Expr) *jyhArticleDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jyhArticleDo) Having(conds ...gen.Condition) *jyhArticleDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jyhArticleDo) Limit(limit int) *jyhArticleDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jyhArticleDo) Offset(offset int) *jyhArticleDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jyhArticleDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jyhArticleDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jyhArticleDo) Unscoped() *jyhArticleDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jyhArticleDo) Create(values ...*jyhapp.JyhArticle) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jyhArticleDo) CreateInBatches(values []*jyhapp.JyhArticle, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jyhArticleDo) Save(values ...*jyhapp.JyhArticle) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jyhArticleDo) First() (*jyhapp.JyhArticle, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhArticle), nil
	}
}

func (j jyhArticleDo) Take() (*jyhapp.JyhArticle, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhArticle), nil
	}
}

func (j jyhArticleDo) Last() (*jyhapp.JyhArticle, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhArticle), nil
	}
}

func (j jyhArticleDo) Find() ([]*jyhapp.JyhArticle, error) {
	result, err := j.DO.Find()
	return result.([]*jyhapp.JyhArticle), err
}

func (j jyhArticleDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*jyhapp.JyhArticle, err error) {
	buf := make([]*jyhapp.JyhArticle, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jyhArticleDo) FindInBatches(result *[]*jyhapp.JyhArticle, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jyhArticleDo) Attrs(attrs ...field.AssignExpr) *jyhArticleDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jyhArticleDo) Assign(attrs ...field.AssignExpr) *jyhArticleDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jyhArticleDo) Joins(fields ...field.RelationField) *jyhArticleDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jyhArticleDo) Preload(fields ...field.RelationField) *jyhArticleDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jyhArticleDo) FirstOrInit() (*jyhapp.JyhArticle, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhArticle), nil
	}
}

func (j jyhArticleDo) FirstOrCreate() (*jyhapp.JyhArticle, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhArticle), nil
	}
}

func (j jyhArticleDo) FindByPage(offset int, limit int) (result []*jyhapp.JyhArticle, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jyhArticleDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jyhArticleDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jyhArticleDo) Delete(models ...*jyhapp.JyhArticle) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jyhArticleDo) withDO(do gen.Dao) *jyhArticleDo {
	j.DO = *do.(*gen.DO)
	return j
}
