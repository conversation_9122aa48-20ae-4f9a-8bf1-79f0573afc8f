// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
)

func newJyhCommission(db *gorm.DB, opts ...gen.DOOption) jyhCommission {
	_jyhCommission := jyhCommission{}

	_jyhCommission.jyhCommissionDo.UseDB(db, opts...)
	_jyhCommission.jyhCommissionDo.UseModel(&jyhapp.JyhCommission{})

	tableName := _jyhCommission.jyhCommissionDo.TableName()
	_jyhCommission.ALL = field.NewAsterisk(tableName)
	_jyhCommission.ID = field.NewUint(tableName, "id")
	_jyhCommission.CreatedAt = field.NewTime(tableName, "created_at")
	_jyhCommission.UpdatedAt = field.NewTime(tableName, "updated_at")
	_jyhCommission.DeletedAt = field.NewField(tableName, "deleted_at")
	_jyhCommission.OrderID = field.NewUint(tableName, "order_id")
	_jyhCommission.UserID = field.NewUint(tableName, "user_id")
	_jyhCommission.FromUserID = field.NewUint(tableName, "from_user_id")
	_jyhCommission.Amount = field.NewField(tableName, "amount")
	_jyhCommission.Level = field.NewUint(tableName, "level")
	_jyhCommission.Status = field.NewString(tableName, "status")
	_jyhCommission.Order = jyhCommissionBelongsToOrder{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Order", "jyhapp.JyhOrder"),
		User: struct {
			field.RelationField
			Inviter struct {
				field.RelationField
			}
			JyhUserExt struct {
				field.RelationField
			}
			Invitees struct {
				field.RelationField
			}
			Tags struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}
		}{
			RelationField: field.NewRelation("Order.User", "jyhapp.JyhUser"),
			Inviter: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Order.User.Inviter", "jyhapp.JyhUser"),
			},
			JyhUserExt: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Order.User.JyhUserExt", "jyhapp.JyhUserExt"),
			},
			Invitees: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Order.User.Invitees", "jyhapp.JyhUser"),
			},
			Tags: struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("Order.User.Tags", "jyhapp.JyhUserTag"),
				Users: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Order.User.Tags.Users", "jyhapp.JyhUser"),
				},
			},
		},
	}

	_jyhCommission.User = jyhCommissionBelongsToUser{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("User", "jyhapp.JyhUser"),
	}

	_jyhCommission.FromUser = jyhCommissionBelongsToFromUser{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("FromUser", "jyhapp.JyhUser"),
	}

	_jyhCommission.fillFieldMap()

	return _jyhCommission
}

type jyhCommission struct {
	jyhCommissionDo

	ALL        field.Asterisk
	ID         field.Uint
	CreatedAt  field.Time
	UpdatedAt  field.Time
	DeletedAt  field.Field
	OrderID    field.Uint
	UserID     field.Uint
	FromUserID field.Uint
	Amount     field.Field
	Level      field.Uint
	Status     field.String
	Order      jyhCommissionBelongsToOrder

	User jyhCommissionBelongsToUser

	FromUser jyhCommissionBelongsToFromUser

	fieldMap map[string]field.Expr
}

func (j jyhCommission) Table(newTableName string) *jyhCommission {
	j.jyhCommissionDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j jyhCommission) As(alias string) *jyhCommission {
	j.jyhCommissionDo.DO = *(j.jyhCommissionDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *jyhCommission) updateTableName(table string) *jyhCommission {
	j.ALL = field.NewAsterisk(table)
	j.ID = field.NewUint(table, "id")
	j.CreatedAt = field.NewTime(table, "created_at")
	j.UpdatedAt = field.NewTime(table, "updated_at")
	j.DeletedAt = field.NewField(table, "deleted_at")
	j.OrderID = field.NewUint(table, "order_id")
	j.UserID = field.NewUint(table, "user_id")
	j.FromUserID = field.NewUint(table, "from_user_id")
	j.Amount = field.NewField(table, "amount")
	j.Level = field.NewUint(table, "level")
	j.Status = field.NewString(table, "status")

	j.fillFieldMap()

	return j
}

func (j *jyhCommission) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *jyhCommission) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 13)
	j.fieldMap["id"] = j.ID
	j.fieldMap["created_at"] = j.CreatedAt
	j.fieldMap["updated_at"] = j.UpdatedAt
	j.fieldMap["deleted_at"] = j.DeletedAt
	j.fieldMap["order_id"] = j.OrderID
	j.fieldMap["user_id"] = j.UserID
	j.fieldMap["from_user_id"] = j.FromUserID
	j.fieldMap["amount"] = j.Amount
	j.fieldMap["level"] = j.Level
	j.fieldMap["status"] = j.Status

}

func (j jyhCommission) clone(db *gorm.DB) jyhCommission {
	j.jyhCommissionDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j jyhCommission) replaceDB(db *gorm.DB) jyhCommission {
	j.jyhCommissionDo.ReplaceDB(db)
	return j
}

type jyhCommissionBelongsToOrder struct {
	db *gorm.DB

	field.RelationField

	User struct {
		field.RelationField
		Inviter struct {
			field.RelationField
		}
		JyhUserExt struct {
			field.RelationField
		}
		Invitees struct {
			field.RelationField
		}
		Tags struct {
			field.RelationField
			Users struct {
				field.RelationField
			}
		}
	}
}

func (a jyhCommissionBelongsToOrder) Where(conds ...field.Expr) *jyhCommissionBelongsToOrder {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a jyhCommissionBelongsToOrder) WithContext(ctx context.Context) *jyhCommissionBelongsToOrder {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a jyhCommissionBelongsToOrder) Session(session *gorm.Session) *jyhCommissionBelongsToOrder {
	a.db = a.db.Session(session)
	return &a
}

func (a jyhCommissionBelongsToOrder) Model(m *jyhapp.JyhCommission) *jyhCommissionBelongsToOrderTx {
	return &jyhCommissionBelongsToOrderTx{a.db.Model(m).Association(a.Name())}
}

type jyhCommissionBelongsToOrderTx struct{ tx *gorm.Association }

func (a jyhCommissionBelongsToOrderTx) Find() (result *jyhapp.JyhOrder, err error) {
	return result, a.tx.Find(&result)
}

func (a jyhCommissionBelongsToOrderTx) Append(values ...*jyhapp.JyhOrder) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a jyhCommissionBelongsToOrderTx) Replace(values ...*jyhapp.JyhOrder) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a jyhCommissionBelongsToOrderTx) Delete(values ...*jyhapp.JyhOrder) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a jyhCommissionBelongsToOrderTx) Clear() error {
	return a.tx.Clear()
}

func (a jyhCommissionBelongsToOrderTx) Count() int64 {
	return a.tx.Count()
}

type jyhCommissionBelongsToUser struct {
	db *gorm.DB

	field.RelationField
}

func (a jyhCommissionBelongsToUser) Where(conds ...field.Expr) *jyhCommissionBelongsToUser {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a jyhCommissionBelongsToUser) WithContext(ctx context.Context) *jyhCommissionBelongsToUser {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a jyhCommissionBelongsToUser) Session(session *gorm.Session) *jyhCommissionBelongsToUser {
	a.db = a.db.Session(session)
	return &a
}

func (a jyhCommissionBelongsToUser) Model(m *jyhapp.JyhCommission) *jyhCommissionBelongsToUserTx {
	return &jyhCommissionBelongsToUserTx{a.db.Model(m).Association(a.Name())}
}

type jyhCommissionBelongsToUserTx struct{ tx *gorm.Association }

func (a jyhCommissionBelongsToUserTx) Find() (result *jyhapp.JyhUser, err error) {
	return result, a.tx.Find(&result)
}

func (a jyhCommissionBelongsToUserTx) Append(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a jyhCommissionBelongsToUserTx) Replace(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a jyhCommissionBelongsToUserTx) Delete(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a jyhCommissionBelongsToUserTx) Clear() error {
	return a.tx.Clear()
}

func (a jyhCommissionBelongsToUserTx) Count() int64 {
	return a.tx.Count()
}

type jyhCommissionBelongsToFromUser struct {
	db *gorm.DB

	field.RelationField
}

func (a jyhCommissionBelongsToFromUser) Where(conds ...field.Expr) *jyhCommissionBelongsToFromUser {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a jyhCommissionBelongsToFromUser) WithContext(ctx context.Context) *jyhCommissionBelongsToFromUser {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a jyhCommissionBelongsToFromUser) Session(session *gorm.Session) *jyhCommissionBelongsToFromUser {
	a.db = a.db.Session(session)
	return &a
}

func (a jyhCommissionBelongsToFromUser) Model(m *jyhapp.JyhCommission) *jyhCommissionBelongsToFromUserTx {
	return &jyhCommissionBelongsToFromUserTx{a.db.Model(m).Association(a.Name())}
}

type jyhCommissionBelongsToFromUserTx struct{ tx *gorm.Association }

func (a jyhCommissionBelongsToFromUserTx) Find() (result *jyhapp.JyhUser, err error) {
	return result, a.tx.Find(&result)
}

func (a jyhCommissionBelongsToFromUserTx) Append(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a jyhCommissionBelongsToFromUserTx) Replace(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a jyhCommissionBelongsToFromUserTx) Delete(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a jyhCommissionBelongsToFromUserTx) Clear() error {
	return a.tx.Clear()
}

func (a jyhCommissionBelongsToFromUserTx) Count() int64 {
	return a.tx.Count()
}

type jyhCommissionDo struct{ gen.DO }

func (j jyhCommissionDo) Debug() *jyhCommissionDo {
	return j.withDO(j.DO.Debug())
}

func (j jyhCommissionDo) WithContext(ctx context.Context) *jyhCommissionDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jyhCommissionDo) ReadDB() *jyhCommissionDo {
	return j.Clauses(dbresolver.Read)
}

func (j jyhCommissionDo) WriteDB() *jyhCommissionDo {
	return j.Clauses(dbresolver.Write)
}

func (j jyhCommissionDo) Session(config *gorm.Session) *jyhCommissionDo {
	return j.withDO(j.DO.Session(config))
}

func (j jyhCommissionDo) Clauses(conds ...clause.Expression) *jyhCommissionDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jyhCommissionDo) Returning(value interface{}, columns ...string) *jyhCommissionDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jyhCommissionDo) Not(conds ...gen.Condition) *jyhCommissionDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jyhCommissionDo) Or(conds ...gen.Condition) *jyhCommissionDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jyhCommissionDo) Select(conds ...field.Expr) *jyhCommissionDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jyhCommissionDo) Where(conds ...gen.Condition) *jyhCommissionDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jyhCommissionDo) Order(conds ...field.Expr) *jyhCommissionDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jyhCommissionDo) Distinct(cols ...field.Expr) *jyhCommissionDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jyhCommissionDo) Omit(cols ...field.Expr) *jyhCommissionDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jyhCommissionDo) Join(table schema.Tabler, on ...field.Expr) *jyhCommissionDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jyhCommissionDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jyhCommissionDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jyhCommissionDo) RightJoin(table schema.Tabler, on ...field.Expr) *jyhCommissionDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jyhCommissionDo) Group(cols ...field.Expr) *jyhCommissionDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jyhCommissionDo) Having(conds ...gen.Condition) *jyhCommissionDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jyhCommissionDo) Limit(limit int) *jyhCommissionDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jyhCommissionDo) Offset(offset int) *jyhCommissionDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jyhCommissionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jyhCommissionDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jyhCommissionDo) Unscoped() *jyhCommissionDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jyhCommissionDo) Create(values ...*jyhapp.JyhCommission) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jyhCommissionDo) CreateInBatches(values []*jyhapp.JyhCommission, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jyhCommissionDo) Save(values ...*jyhapp.JyhCommission) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jyhCommissionDo) First() (*jyhapp.JyhCommission, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhCommission), nil
	}
}

func (j jyhCommissionDo) Take() (*jyhapp.JyhCommission, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhCommission), nil
	}
}

func (j jyhCommissionDo) Last() (*jyhapp.JyhCommission, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhCommission), nil
	}
}

func (j jyhCommissionDo) Find() ([]*jyhapp.JyhCommission, error) {
	result, err := j.DO.Find()
	return result.([]*jyhapp.JyhCommission), err
}

func (j jyhCommissionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*jyhapp.JyhCommission, err error) {
	buf := make([]*jyhapp.JyhCommission, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jyhCommissionDo) FindInBatches(result *[]*jyhapp.JyhCommission, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jyhCommissionDo) Attrs(attrs ...field.AssignExpr) *jyhCommissionDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jyhCommissionDo) Assign(attrs ...field.AssignExpr) *jyhCommissionDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jyhCommissionDo) Joins(fields ...field.RelationField) *jyhCommissionDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jyhCommissionDo) Preload(fields ...field.RelationField) *jyhCommissionDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jyhCommissionDo) FirstOrInit() (*jyhapp.JyhCommission, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhCommission), nil
	}
}

func (j jyhCommissionDo) FirstOrCreate() (*jyhapp.JyhCommission, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhCommission), nil
	}
}

func (j jyhCommissionDo) FindByPage(offset int, limit int) (result []*jyhapp.JyhCommission, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jyhCommissionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jyhCommissionDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jyhCommissionDo) Delete(models ...*jyhapp.JyhCommission) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jyhCommissionDo) withDO(do gen.Dao) *jyhCommissionDo {
	j.DO = *do.(*gen.DO)
	return j
}
