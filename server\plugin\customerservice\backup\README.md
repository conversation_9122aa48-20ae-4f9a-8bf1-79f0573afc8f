# 客服系统重构备份

## 备份说明

本目录包含在客服系统重构过程中移除的工单管理和排队管理相关代码，以备将来需要时恢复使用。

## 移除的功能模块

### 1. 工单管理系统
- **数据模型**: `model/sysServiceTicket.go`
- **API接口**: `api/ticketApi.go`
- **服务层**: `service/ticketService.go`
- **相关路由**: 工单管理相关路由配置

### 2. 排队管理系统
- **数据模型**: `model/sysServiceQueue.go`
- **API接口**: `api/queueApi.go`
- **服务层**: `service/queueService.go`
- **相关路由**: 排队管理相关路由配置

## 重构时间
重构时间: 2025-01-16

## 重构原因
简化客服系统架构，专注于核心的聊天和转接功能，移除复杂的工单和排队管理模块。

## 保留的功能
1. 客服与用户实时聊天
2. 客服转接功能（调整为基于会话）
3. 客服管理和分配逻辑
4. WebSocket实时通信

## 恢复说明
如需恢复这些功能，请：
1. 将备份的文件复制回原位置
2. 恢复相关的数据库表结构
3. 更新路由配置
4. 重新注册API接口

## 注意事项
- 转接功能已从基于工单改为基于用户会话
- 客服分配逻辑已简化，不再依赖工单状态
- 聊天记录和客服记录功能保持不变
