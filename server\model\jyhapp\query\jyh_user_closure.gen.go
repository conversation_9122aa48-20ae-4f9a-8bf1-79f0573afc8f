// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
)

func newJyhUserClosure(db *gorm.DB, opts ...gen.DOOption) jyhUserClosure {
	_jyhUserClosure := jyhUserClosure{}

	_jyhUserClosure.jyhUserClosureDo.UseDB(db, opts...)
	_jyhUserClosure.jyhUserClosureDo.UseModel(&jyhapp.JyhUserClosure{})

	tableName := _jyhUserClosure.jyhUserClosureDo.TableName()
	_jyhUserClosure.ALL = field.NewAsterisk(tableName)
	_jyhUserClosure.AncestorID = field.NewUint(tableName, "ancestor_id")
	_jyhUserClosure.DescendantID = field.NewUint(tableName, "descendant_id")
	_jyhUserClosure.Depth = field.NewUint(tableName, "depth")
	_jyhUserClosure.Ancestor = jyhUserClosureBelongsToAncestor{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Ancestor", "jyhapp.JyhUser"),
		Inviter: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Ancestor.Inviter", "jyhapp.JyhUser"),
		},
		JyhUserExt: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Ancestor.JyhUserExt", "jyhapp.JyhUserExt"),
		},
		Invitees: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Ancestor.Invitees", "jyhapp.JyhUser"),
		},
		Tags: struct {
			field.RelationField
			Users struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Ancestor.Tags", "jyhapp.JyhUserTag"),
			Users: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Ancestor.Tags.Users", "jyhapp.JyhUser"),
			},
		},
	}

	_jyhUserClosure.Descendant = jyhUserClosureBelongsToDescendant{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Descendant", "jyhapp.JyhUser"),
	}

	_jyhUserClosure.fillFieldMap()

	return _jyhUserClosure
}

type jyhUserClosure struct {
	jyhUserClosureDo

	ALL          field.Asterisk
	AncestorID   field.Uint
	DescendantID field.Uint
	Depth        field.Uint
	Ancestor     jyhUserClosureBelongsToAncestor

	Descendant jyhUserClosureBelongsToDescendant

	fieldMap map[string]field.Expr
}

func (j jyhUserClosure) Table(newTableName string) *jyhUserClosure {
	j.jyhUserClosureDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j jyhUserClosure) As(alias string) *jyhUserClosure {
	j.jyhUserClosureDo.DO = *(j.jyhUserClosureDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *jyhUserClosure) updateTableName(table string) *jyhUserClosure {
	j.ALL = field.NewAsterisk(table)
	j.AncestorID = field.NewUint(table, "ancestor_id")
	j.DescendantID = field.NewUint(table, "descendant_id")
	j.Depth = field.NewUint(table, "depth")

	j.fillFieldMap()

	return j
}

func (j *jyhUserClosure) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *jyhUserClosure) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 5)
	j.fieldMap["ancestor_id"] = j.AncestorID
	j.fieldMap["descendant_id"] = j.DescendantID
	j.fieldMap["depth"] = j.Depth

}

func (j jyhUserClosure) clone(db *gorm.DB) jyhUserClosure {
	j.jyhUserClosureDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j jyhUserClosure) replaceDB(db *gorm.DB) jyhUserClosure {
	j.jyhUserClosureDo.ReplaceDB(db)
	return j
}

type jyhUserClosureBelongsToAncestor struct {
	db *gorm.DB

	field.RelationField

	Inviter struct {
		field.RelationField
	}
	JyhUserExt struct {
		field.RelationField
	}
	Invitees struct {
		field.RelationField
	}
	Tags struct {
		field.RelationField
		Users struct {
			field.RelationField
		}
	}
}

func (a jyhUserClosureBelongsToAncestor) Where(conds ...field.Expr) *jyhUserClosureBelongsToAncestor {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a jyhUserClosureBelongsToAncestor) WithContext(ctx context.Context) *jyhUserClosureBelongsToAncestor {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a jyhUserClosureBelongsToAncestor) Session(session *gorm.Session) *jyhUserClosureBelongsToAncestor {
	a.db = a.db.Session(session)
	return &a
}

func (a jyhUserClosureBelongsToAncestor) Model(m *jyhapp.JyhUserClosure) *jyhUserClosureBelongsToAncestorTx {
	return &jyhUserClosureBelongsToAncestorTx{a.db.Model(m).Association(a.Name())}
}

type jyhUserClosureBelongsToAncestorTx struct{ tx *gorm.Association }

func (a jyhUserClosureBelongsToAncestorTx) Find() (result *jyhapp.JyhUser, err error) {
	return result, a.tx.Find(&result)
}

func (a jyhUserClosureBelongsToAncestorTx) Append(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a jyhUserClosureBelongsToAncestorTx) Replace(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a jyhUserClosureBelongsToAncestorTx) Delete(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a jyhUserClosureBelongsToAncestorTx) Clear() error {
	return a.tx.Clear()
}

func (a jyhUserClosureBelongsToAncestorTx) Count() int64 {
	return a.tx.Count()
}

type jyhUserClosureBelongsToDescendant struct {
	db *gorm.DB

	field.RelationField
}

func (a jyhUserClosureBelongsToDescendant) Where(conds ...field.Expr) *jyhUserClosureBelongsToDescendant {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a jyhUserClosureBelongsToDescendant) WithContext(ctx context.Context) *jyhUserClosureBelongsToDescendant {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a jyhUserClosureBelongsToDescendant) Session(session *gorm.Session) *jyhUserClosureBelongsToDescendant {
	a.db = a.db.Session(session)
	return &a
}

func (a jyhUserClosureBelongsToDescendant) Model(m *jyhapp.JyhUserClosure) *jyhUserClosureBelongsToDescendantTx {
	return &jyhUserClosureBelongsToDescendantTx{a.db.Model(m).Association(a.Name())}
}

type jyhUserClosureBelongsToDescendantTx struct{ tx *gorm.Association }

func (a jyhUserClosureBelongsToDescendantTx) Find() (result *jyhapp.JyhUser, err error) {
	return result, a.tx.Find(&result)
}

func (a jyhUserClosureBelongsToDescendantTx) Append(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a jyhUserClosureBelongsToDescendantTx) Replace(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a jyhUserClosureBelongsToDescendantTx) Delete(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a jyhUserClosureBelongsToDescendantTx) Clear() error {
	return a.tx.Clear()
}

func (a jyhUserClosureBelongsToDescendantTx) Count() int64 {
	return a.tx.Count()
}

type jyhUserClosureDo struct{ gen.DO }

func (j jyhUserClosureDo) Debug() *jyhUserClosureDo {
	return j.withDO(j.DO.Debug())
}

func (j jyhUserClosureDo) WithContext(ctx context.Context) *jyhUserClosureDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jyhUserClosureDo) ReadDB() *jyhUserClosureDo {
	return j.Clauses(dbresolver.Read)
}

func (j jyhUserClosureDo) WriteDB() *jyhUserClosureDo {
	return j.Clauses(dbresolver.Write)
}

func (j jyhUserClosureDo) Session(config *gorm.Session) *jyhUserClosureDo {
	return j.withDO(j.DO.Session(config))
}

func (j jyhUserClosureDo) Clauses(conds ...clause.Expression) *jyhUserClosureDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jyhUserClosureDo) Returning(value interface{}, columns ...string) *jyhUserClosureDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jyhUserClosureDo) Not(conds ...gen.Condition) *jyhUserClosureDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jyhUserClosureDo) Or(conds ...gen.Condition) *jyhUserClosureDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jyhUserClosureDo) Select(conds ...field.Expr) *jyhUserClosureDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jyhUserClosureDo) Where(conds ...gen.Condition) *jyhUserClosureDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jyhUserClosureDo) Order(conds ...field.Expr) *jyhUserClosureDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jyhUserClosureDo) Distinct(cols ...field.Expr) *jyhUserClosureDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jyhUserClosureDo) Omit(cols ...field.Expr) *jyhUserClosureDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jyhUserClosureDo) Join(table schema.Tabler, on ...field.Expr) *jyhUserClosureDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jyhUserClosureDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jyhUserClosureDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jyhUserClosureDo) RightJoin(table schema.Tabler, on ...field.Expr) *jyhUserClosureDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jyhUserClosureDo) Group(cols ...field.Expr) *jyhUserClosureDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jyhUserClosureDo) Having(conds ...gen.Condition) *jyhUserClosureDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jyhUserClosureDo) Limit(limit int) *jyhUserClosureDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jyhUserClosureDo) Offset(offset int) *jyhUserClosureDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jyhUserClosureDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jyhUserClosureDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jyhUserClosureDo) Unscoped() *jyhUserClosureDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jyhUserClosureDo) Create(values ...*jyhapp.JyhUserClosure) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jyhUserClosureDo) CreateInBatches(values []*jyhapp.JyhUserClosure, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jyhUserClosureDo) Save(values ...*jyhapp.JyhUserClosure) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jyhUserClosureDo) First() (*jyhapp.JyhUserClosure, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserClosure), nil
	}
}

func (j jyhUserClosureDo) Take() (*jyhapp.JyhUserClosure, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserClosure), nil
	}
}

func (j jyhUserClosureDo) Last() (*jyhapp.JyhUserClosure, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserClosure), nil
	}
}

func (j jyhUserClosureDo) Find() ([]*jyhapp.JyhUserClosure, error) {
	result, err := j.DO.Find()
	return result.([]*jyhapp.JyhUserClosure), err
}

func (j jyhUserClosureDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*jyhapp.JyhUserClosure, err error) {
	buf := make([]*jyhapp.JyhUserClosure, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jyhUserClosureDo) FindInBatches(result *[]*jyhapp.JyhUserClosure, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jyhUserClosureDo) Attrs(attrs ...field.AssignExpr) *jyhUserClosureDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jyhUserClosureDo) Assign(attrs ...field.AssignExpr) *jyhUserClosureDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jyhUserClosureDo) Joins(fields ...field.RelationField) *jyhUserClosureDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jyhUserClosureDo) Preload(fields ...field.RelationField) *jyhUserClosureDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jyhUserClosureDo) FirstOrInit() (*jyhapp.JyhUserClosure, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserClosure), nil
	}
}

func (j jyhUserClosureDo) FirstOrCreate() (*jyhapp.JyhUserClosure, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserClosure), nil
	}
}

func (j jyhUserClosureDo) FindByPage(offset int, limit int) (result []*jyhapp.JyhUserClosure, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jyhUserClosureDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jyhUserClosureDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jyhUserClosureDo) Delete(models ...*jyhapp.JyhUserClosure) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jyhUserClosureDo) withDO(do gen.Dao) *jyhUserClosureDo {
	j.DO = *do.(*gen.DO)
	return j
}
