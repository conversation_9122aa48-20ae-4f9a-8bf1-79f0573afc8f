// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
)

func newJyhCourse(db *gorm.DB, opts ...gen.DOOption) jyhCourse {
	_jyhCourse := jyhCourse{}

	_jyhCourse.jyhCourseDo.UseDB(db, opts...)
	_jyhCourse.jyhCourseDo.UseModel(&jyhapp.JyhCourse{})

	tableName := _jyhCourse.jyhCourseDo.TableName()
	_jyhCourse.ALL = field.NewAsterisk(tableName)
	_jyhCourse.ID = field.NewUint(tableName, "id")
	_jyhCourse.CreatedAt = field.NewTime(tableName, "created_at")
	_jyhCourse.UpdatedAt = field.NewTime(tableName, "updated_at")
	_jyhCourse.DeletedAt = field.NewField(tableName, "deleted_at")
	_jyhCourse.Title = field.NewString(tableName, "title")
	_jyhCourse.CourseDate = field.NewTime(tableName, "course_date")
	_jyhCourse.ViewCount = field.NewInt(tableName, "view_count")
	_jyhCourse.ImageUrl = field.NewString(tableName, "image_url")
	_jyhCourse.VideoUrl = field.NewString(tableName, "video_url")
	_jyhCourse.Detail = field.NewString(tableName, "detail")
	_jyhCourse.Duration = field.NewInt(tableName, "duration")
	_jyhCourse.Teacher = field.NewString(tableName, "teacher")
	_jyhCourse.Status = field.NewUint(tableName, "status")
	_jyhCourse.CategoryID = field.NewUint(tableName, "category_id")
	_jyhCourse.Category = jyhCourseBelongsToCategory{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Category", "jyhapp.JyhCourseCategory"),
	}

	_jyhCourse.fillFieldMap()

	return _jyhCourse
}

type jyhCourse struct {
	jyhCourseDo

	ALL        field.Asterisk
	ID         field.Uint
	CreatedAt  field.Time
	UpdatedAt  field.Time
	DeletedAt  field.Field
	Title      field.String
	CourseDate field.Time
	ViewCount  field.Int
	ImageUrl   field.String
	VideoUrl   field.String
	Detail     field.String
	Duration   field.Int
	Teacher    field.String
	Status     field.Uint
	CategoryID field.Uint
	Category   jyhCourseBelongsToCategory

	fieldMap map[string]field.Expr
}

func (j jyhCourse) Table(newTableName string) *jyhCourse {
	j.jyhCourseDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j jyhCourse) As(alias string) *jyhCourse {
	j.jyhCourseDo.DO = *(j.jyhCourseDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *jyhCourse) updateTableName(table string) *jyhCourse {
	j.ALL = field.NewAsterisk(table)
	j.ID = field.NewUint(table, "id")
	j.CreatedAt = field.NewTime(table, "created_at")
	j.UpdatedAt = field.NewTime(table, "updated_at")
	j.DeletedAt = field.NewField(table, "deleted_at")
	j.Title = field.NewString(table, "title")
	j.CourseDate = field.NewTime(table, "course_date")
	j.ViewCount = field.NewInt(table, "view_count")
	j.ImageUrl = field.NewString(table, "image_url")
	j.VideoUrl = field.NewString(table, "video_url")
	j.Detail = field.NewString(table, "detail")
	j.Duration = field.NewInt(table, "duration")
	j.Teacher = field.NewString(table, "teacher")
	j.Status = field.NewUint(table, "status")
	j.CategoryID = field.NewUint(table, "category_id")

	j.fillFieldMap()

	return j
}

func (j *jyhCourse) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *jyhCourse) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 15)
	j.fieldMap["id"] = j.ID
	j.fieldMap["created_at"] = j.CreatedAt
	j.fieldMap["updated_at"] = j.UpdatedAt
	j.fieldMap["deleted_at"] = j.DeletedAt
	j.fieldMap["title"] = j.Title
	j.fieldMap["course_date"] = j.CourseDate
	j.fieldMap["view_count"] = j.ViewCount
	j.fieldMap["image_url"] = j.ImageUrl
	j.fieldMap["video_url"] = j.VideoUrl
	j.fieldMap["detail"] = j.Detail
	j.fieldMap["duration"] = j.Duration
	j.fieldMap["teacher"] = j.Teacher
	j.fieldMap["status"] = j.Status
	j.fieldMap["category_id"] = j.CategoryID

}

func (j jyhCourse) clone(db *gorm.DB) jyhCourse {
	j.jyhCourseDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j jyhCourse) replaceDB(db *gorm.DB) jyhCourse {
	j.jyhCourseDo.ReplaceDB(db)
	return j
}

type jyhCourseBelongsToCategory struct {
	db *gorm.DB

	field.RelationField
}

func (a jyhCourseBelongsToCategory) Where(conds ...field.Expr) *jyhCourseBelongsToCategory {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a jyhCourseBelongsToCategory) WithContext(ctx context.Context) *jyhCourseBelongsToCategory {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a jyhCourseBelongsToCategory) Session(session *gorm.Session) *jyhCourseBelongsToCategory {
	a.db = a.db.Session(session)
	return &a
}

func (a jyhCourseBelongsToCategory) Model(m *jyhapp.JyhCourse) *jyhCourseBelongsToCategoryTx {
	return &jyhCourseBelongsToCategoryTx{a.db.Model(m).Association(a.Name())}
}

type jyhCourseBelongsToCategoryTx struct{ tx *gorm.Association }

func (a jyhCourseBelongsToCategoryTx) Find() (result *jyhapp.JyhCourseCategory, err error) {
	return result, a.tx.Find(&result)
}

func (a jyhCourseBelongsToCategoryTx) Append(values ...*jyhapp.JyhCourseCategory) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a jyhCourseBelongsToCategoryTx) Replace(values ...*jyhapp.JyhCourseCategory) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a jyhCourseBelongsToCategoryTx) Delete(values ...*jyhapp.JyhCourseCategory) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a jyhCourseBelongsToCategoryTx) Clear() error {
	return a.tx.Clear()
}

func (a jyhCourseBelongsToCategoryTx) Count() int64 {
	return a.tx.Count()
}

type jyhCourseDo struct{ gen.DO }

func (j jyhCourseDo) Debug() *jyhCourseDo {
	return j.withDO(j.DO.Debug())
}

func (j jyhCourseDo) WithContext(ctx context.Context) *jyhCourseDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jyhCourseDo) ReadDB() *jyhCourseDo {
	return j.Clauses(dbresolver.Read)
}

func (j jyhCourseDo) WriteDB() *jyhCourseDo {
	return j.Clauses(dbresolver.Write)
}

func (j jyhCourseDo) Session(config *gorm.Session) *jyhCourseDo {
	return j.withDO(j.DO.Session(config))
}

func (j jyhCourseDo) Clauses(conds ...clause.Expression) *jyhCourseDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jyhCourseDo) Returning(value interface{}, columns ...string) *jyhCourseDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jyhCourseDo) Not(conds ...gen.Condition) *jyhCourseDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jyhCourseDo) Or(conds ...gen.Condition) *jyhCourseDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jyhCourseDo) Select(conds ...field.Expr) *jyhCourseDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jyhCourseDo) Where(conds ...gen.Condition) *jyhCourseDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jyhCourseDo) Order(conds ...field.Expr) *jyhCourseDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jyhCourseDo) Distinct(cols ...field.Expr) *jyhCourseDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jyhCourseDo) Omit(cols ...field.Expr) *jyhCourseDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jyhCourseDo) Join(table schema.Tabler, on ...field.Expr) *jyhCourseDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jyhCourseDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jyhCourseDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jyhCourseDo) RightJoin(table schema.Tabler, on ...field.Expr) *jyhCourseDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jyhCourseDo) Group(cols ...field.Expr) *jyhCourseDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jyhCourseDo) Having(conds ...gen.Condition) *jyhCourseDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jyhCourseDo) Limit(limit int) *jyhCourseDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jyhCourseDo) Offset(offset int) *jyhCourseDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jyhCourseDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jyhCourseDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jyhCourseDo) Unscoped() *jyhCourseDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jyhCourseDo) Create(values ...*jyhapp.JyhCourse) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jyhCourseDo) CreateInBatches(values []*jyhapp.JyhCourse, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jyhCourseDo) Save(values ...*jyhapp.JyhCourse) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jyhCourseDo) First() (*jyhapp.JyhCourse, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhCourse), nil
	}
}

func (j jyhCourseDo) Take() (*jyhapp.JyhCourse, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhCourse), nil
	}
}

func (j jyhCourseDo) Last() (*jyhapp.JyhCourse, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhCourse), nil
	}
}

func (j jyhCourseDo) Find() ([]*jyhapp.JyhCourse, error) {
	result, err := j.DO.Find()
	return result.([]*jyhapp.JyhCourse), err
}

func (j jyhCourseDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*jyhapp.JyhCourse, err error) {
	buf := make([]*jyhapp.JyhCourse, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jyhCourseDo) FindInBatches(result *[]*jyhapp.JyhCourse, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jyhCourseDo) Attrs(attrs ...field.AssignExpr) *jyhCourseDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jyhCourseDo) Assign(attrs ...field.AssignExpr) *jyhCourseDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jyhCourseDo) Joins(fields ...field.RelationField) *jyhCourseDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jyhCourseDo) Preload(fields ...field.RelationField) *jyhCourseDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jyhCourseDo) FirstOrInit() (*jyhapp.JyhCourse, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhCourse), nil
	}
}

func (j jyhCourseDo) FirstOrCreate() (*jyhapp.JyhCourse, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhCourse), nil
	}
}

func (j jyhCourseDo) FindByPage(offset int, limit int) (result []*jyhapp.JyhCourse, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jyhCourseDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jyhCourseDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jyhCourseDo) Delete(models ...*jyhapp.JyhCourse) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jyhCourseDo) withDO(do gen.Dao) *jyhCourseDo {
	j.DO = *do.(*gen.DO)
	return j
}
