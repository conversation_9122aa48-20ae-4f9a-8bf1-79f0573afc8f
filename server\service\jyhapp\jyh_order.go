package jyhapp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/errno"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/query"
	jyhReq "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/request"
	jyhRes "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/response"
	"go.uber.org/zap"
	"gorm.io/gen"
)

// JyhOrderService 按天、按周、按月获取订单的收益，数据统计来源表jyh_mcn_order_detail
type JyhOrderService struct{}

// GetOrderStatistics 获取某个用户的每日订单统计
func (s *JyhOrderService) GetOrderStatistics(req jyhReq.OrderStatistics) (stats *jyhRes.JyhOrderStatisticsData, err error) {
	var (
		dbMcnOrder = query.JyhMcnOrderDetail
		dbUser     = query.JyhUser
	)

	// 查询每日订单统计数据
	err = dbMcnOrder.
		LeftJoin(dbUser, dbMcnOrder.AuthorUid.EqCol(dbUser.DouYinId)).
		Select(
			dbMcnOrder.Date.As("order_date"),
			dbMcnOrder.TotalCount.Sum().As("total_count"),
			dbMcnOrder.TotalAmount.Sum().As("total_amount"),
			dbMcnOrder.TotalServiceFee.Sum().As("total_service_fee"),
			dbMcnOrder.TotalSettleAmount.Sum().As("total_settle_amount"),
			dbMcnOrder.SumAuthorFee.Sum().As("total_author_fee"),
			//dbMcnOrder.SumOrganizationFee.Sum().As("total_organization_fee"),
		).Where(dbUser.ID.Eq(req.UserID), dbMcnOrder.Date.Between(req.StartDate, req.EndDate)).Scan(&stats)
	if err != nil {
		global.GVA_LOG.Error("获取每日订单统计失败", zap.Error(err))
		return nil, err
	}

	return
}

// GetOrderDetails 获取抖音订单明细
func (s *JyhOrderService) GetOrderDetails(req jyhReq.OrderDetail) (details []jyhRes.JyhOrderDetail, total int64, err error) {

	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)

	var (
		dbMcnOrder = query.JyhMcnOrderDetail
		dbUser     = query.JyhUser
		cons       []gen.Condition
	)

	if req.UserID > 0 {
		cons = append(cons, dbUser.ID.Eq(req.UserID))
	}

	if req.StartDate != "" && req.EndDate != "" {
		cons = append(cons, dbMcnOrder.Date.Between(req.StartDate, req.EndDate))
	}

	// 查询订单明细数据
	db := dbMcnOrder.
		LeftJoin(dbUser, dbMcnOrder.AuthorUid.EqCol(dbUser.DouYinId)).
		Select(
			dbMcnOrder.Date.As("order_date"),
			dbMcnOrder.TotalCount,
			dbMcnOrder.TotalAmount,
			dbMcnOrder.TotalServiceFee,
			dbMcnOrder.TotalSettleAmount,
			dbMcnOrder.SumAuthorFee.As("total_author_fee"),
			dbMcnOrder.SumOrganizationFee.As("total_organization_fee"),
			dbUser.Username.As("user_name"),
			dbMcnOrder.AuthorUid.As("author_uid"),
		).Where(cons...).
		Order(dbMcnOrder.Date.Asc())

	total, err = db.Count()
	if err != nil {
		global.GVA_LOG.Error("查询达人佣金明细失败", zap.Error(err))
		return nil, 0, errno.QueryFailed.WithError(err)
	}

	// 分页处理
	err = db.Limit(limit).Offset(offset).Scan(&details)
	if err != nil {
		global.GVA_LOG.Error("查询达人佣金明细失败", zap.Error(err))
		return nil, 0, errno.QueryFailed.WithError(err)
	}

	return
}
