package jyhapp

import (
	v1 "github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type JyhUserTagRouter struct{}

func (r *JyhUserTagRouter) InitJyhUserTagRouter(Router *gin.RouterGroup) {
	userTagRouter := Router.Group("admin/user_tag").Use(middleware.OperationRecord())
	userNoRecord := Router.Group("admin/user_tag")
	userTagApi := v1.ApiGroupApp.JyhApiGroup.JyhUserTagApi
	{
		userTagRouter.POST("create", userTagApi.CreateUserTag)       // 创建用户标签
		userTagRouter.PUT("update", userTagApi.UpdateUserTag)        // 更新用户标签
		userTagRouter.DELETE("delete/:id", userTagApi.DeleteUserTag) // 删除用户标签
		userTagRouter.POST("bind", userTagApi.BindUserTag)           // 绑定用户标签
	}
	{
		userNoRecord.GET("list", userTagApi.GetUserTagList)           // 获取用户标签列表
		userNoRecord.GET("detail/:id", userTagApi.GetUserTagDetail)   // 获取用户标签详情
		userNoRecord.GET("user_tags/:userId", userTagApi.GetUserTags) // 获取用户的标签
	}
}
