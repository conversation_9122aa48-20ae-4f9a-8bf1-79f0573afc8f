package jyhapp

import (
	"errors"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
	"go.uber.org/zap"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/query"
	jyhReq "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/request"
)

type CourseCategoryService struct{}

// Create 创建课程分类
func (s *CourseCategoryService) Create(req jyhReq.CourseCategoryCreate) (err error) {
	dbCategory := query.JyhCourseCategory
	// 检查分类名称是否存在
	res := &jyhapp.JyhCourseCategory{}
	err = dbCategory.Where(dbCategory.Name.Eq(req.Name)).Scan(&res)
	if err != nil {
		global.GVA_LOG.Error("查询课程分类失败", zap.Error(err))
		return err
	}

	if res.ID > 0 {
		return errors.New("分类名称重复")
	}

	category := &jyhapp.JyhCourseCategory{
		Name:     req.Name,
		Sort:     req.Sort,
		IsActive: req.IsActive,
		CatDesc:  req.CatDesc,
	}

	return dbCategory.Create(category)
}

// Update 更新课程分类
func (s *CourseCategoryService) Update(req *jyhReq.CourseCategoryUpdate) (err error) {
	dbCategory := query.JyhCourseCategory

	res := &jyhapp.JyhCourseCategory{}
	err = dbCategory.Where(dbCategory.Name.Eq(req.Name), dbCategory.ID.Neq(req.ID)).Scan(&res)
	if err != nil {
		return err
	}
	if res.ID > 0 {
		return errors.New("分类名称重复")
	}

	_, err = dbCategory.Where(dbCategory.ID.Eq(req.ID)).First()
	if err != nil {
		return errors.New("分类不存在")
	}

	// 更新分类信息
	updates := map[string]interface{}{
		"name":      req.Name,
		"sort":      req.Sort,
		"is_active": req.IsActive,
		"cat_desc":  req.CatDesc,
	}

	_, err = dbCategory.Select(dbCategory.Name, dbCategory.Sort, dbCategory.IsActive, dbCategory.CatDesc).
		Where(dbCategory.ID.Eq(req.ID)).Updates(updates)
	return err
}

// Delete 删除课程分类
func (s *CourseCategoryService) Delete(id uint) (err error) {
	dbCategory := query.JyhCourseCategory

	// 检查是否有关联的课程
	courseQuery := query.JyhMaterial
	courseCount, err := courseQuery.Where(courseQuery.CategoryID.Eq(id)).Count()
	if err != nil {
		return err
	}
	if courseCount > 0 {
		return errors.New("存在关联的课程，无法删除")
	}

	_, err = dbCategory.Where(dbCategory.ID.Eq(id)).Unscoped().Delete()
	return err
}

// GetDetail 获取分类详情
func (s *CourseCategoryService) GetDetail(id uint) (category *jyhapp.JyhCourseCategory, err error) {
	dbCategory := query.JyhCourseCategory
	category, err = dbCategory.Where(dbCategory.ID.Eq(id)).First()
	return
}

// GetList 获取分类列表
func (s *CourseCategoryService) GetList(req *jyhReq.CourseCategorySearch) (list []*jyhapp.JyhCourseCategory, total int64, err error) {
	dbCategory := query.JyhCourseCategory
	queryBuilder := dbCategory.WithContext(global.GVA_DB.Statement.Context)

	// 构建查询条件
	if req.Name != "" {
		queryBuilder = queryBuilder.Where(dbCategory.Name.Like("%" + req.Name + "%"))
	}

	// 获取总数
	total, err = queryBuilder.Count()
	if err != nil {
		return
	}

	if req.Page == 0 && req.PageSize == -1 {
		list, err = queryBuilder.Order(dbCategory.Sort).Find()
		return
	}

	// 分页查询
	queryBuilder = queryBuilder.Limit(req.PageSize).Offset((req.Page - 1) * req.PageSize)
	list, err = queryBuilder.Order(dbCategory.Sort).Find()
	return
}
