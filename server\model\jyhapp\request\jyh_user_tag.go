package request

import "github.com/flipped-aurora/gin-vue-admin/server/model/common/request"

// UserTagCreateReq 创建用户标签请求
type UserTagCreateReq struct {
	Name        string `json:"name" binding:"required" validate:"required"` // 标签名称
	Description string `json:"description"`                                 // 标签描述
	Color       string `json:"color" default:"#409EFF"`                     // 标签颜色
	Status      *uint  `json:"status" default:"1"`                          // 标签状态：1-启用, 0-禁用
	Sort        uint   `json:"sort" default:"0"`                            // 排序权重
}

// UserTagUpdateReq 更新用户标签请求
type UserTagUpdateReq struct {
	ID          uint   `json:"id" binding:"required" validate:"required"` // 标签ID
	Name        string `json:"name"`                                      // 标签名称
	Description string `json:"description"`                               // 标签描述
	Color       string `json:"color"`                                     // 标签颜色
	Status      *uint  `json:"status"`                                    // 标签状态：1-启用, 0-禁用
	Sort        uint   `json:"sort"`                                      // 排序权重
}

// UserTagListReq 用户标签列表请求
type UserTagListReq struct {
	Name   string `json:"name" form:"name"`     // 标签名称（模糊搜索）
	Status *uint  `json:"status" form:"status"` // 标签状态：1-启用, 0-禁用
	request.PageInfo
}

// UserTagBindReq 用户绑定标签请求
type UserTagBindReq struct {
	UserID uint   `json:"user_id" binding:"required" validate:"required"` // 用户ID
	TagIDs []uint `json:"tag_ids" binding:"required" validate:"required"` // 标签ID列表
}
