// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
)

func newJyhMaterialCategory(db *gorm.DB, opts ...gen.DOOption) jyhMaterialCategory {
	_jyhMaterialCategory := jyhMaterialCategory{}

	_jyhMaterialCategory.jyhMaterialCategoryDo.UseDB(db, opts...)
	_jyhMaterialCategory.jyhMaterialCategoryDo.UseModel(&jyhapp.JyhMaterialCategory{})

	tableName := _jyhMaterialCategory.jyhMaterialCategoryDo.TableName()
	_jyhMaterialCategory.ALL = field.NewAsterisk(tableName)
	_jyhMaterialCategory.ID = field.NewUint(tableName, "id")
	_jyhMaterialCategory.CreatedAt = field.NewTime(tableName, "created_at")
	_jyhMaterialCategory.UpdatedAt = field.NewTime(tableName, "updated_at")
	_jyhMaterialCategory.DeletedAt = field.NewField(tableName, "deleted_at")
	_jyhMaterialCategory.CatName = field.NewString(tableName, "cat_name")
	_jyhMaterialCategory.CatDesc = field.NewString(tableName, "cat_desc")
	_jyhMaterialCategory.ParentID = field.NewUint(tableName, "parent_id")
	_jyhMaterialCategory.Sort = field.NewInt(tableName, "sort")
	_jyhMaterialCategory.IsActive = field.NewBool(tableName, "is_active")
	_jyhMaterialCategory.DouyinUrl = field.NewString(tableName, "douyin_url")
	_jyhMaterialCategory.Copywriting = field.NewString(tableName, "copywriting")

	_jyhMaterialCategory.fillFieldMap()

	return _jyhMaterialCategory
}

type jyhMaterialCategory struct {
	jyhMaterialCategoryDo

	ALL         field.Asterisk
	ID          field.Uint
	CreatedAt   field.Time
	UpdatedAt   field.Time
	DeletedAt   field.Field
	CatName     field.String
	CatDesc     field.String
	ParentID    field.Uint
	Sort        field.Int
	IsActive    field.Bool
	DouyinUrl   field.String
	Copywriting field.String

	fieldMap map[string]field.Expr
}

func (j jyhMaterialCategory) Table(newTableName string) *jyhMaterialCategory {
	j.jyhMaterialCategoryDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j jyhMaterialCategory) As(alias string) *jyhMaterialCategory {
	j.jyhMaterialCategoryDo.DO = *(j.jyhMaterialCategoryDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *jyhMaterialCategory) updateTableName(table string) *jyhMaterialCategory {
	j.ALL = field.NewAsterisk(table)
	j.ID = field.NewUint(table, "id")
	j.CreatedAt = field.NewTime(table, "created_at")
	j.UpdatedAt = field.NewTime(table, "updated_at")
	j.DeletedAt = field.NewField(table, "deleted_at")
	j.CatName = field.NewString(table, "cat_name")
	j.CatDesc = field.NewString(table, "cat_desc")
	j.ParentID = field.NewUint(table, "parent_id")
	j.Sort = field.NewInt(table, "sort")
	j.IsActive = field.NewBool(table, "is_active")
	j.DouyinUrl = field.NewString(table, "douyin_url")
	j.Copywriting = field.NewString(table, "copywriting")

	j.fillFieldMap()

	return j
}

func (j *jyhMaterialCategory) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *jyhMaterialCategory) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 11)
	j.fieldMap["id"] = j.ID
	j.fieldMap["created_at"] = j.CreatedAt
	j.fieldMap["updated_at"] = j.UpdatedAt
	j.fieldMap["deleted_at"] = j.DeletedAt
	j.fieldMap["cat_name"] = j.CatName
	j.fieldMap["cat_desc"] = j.CatDesc
	j.fieldMap["parent_id"] = j.ParentID
	j.fieldMap["sort"] = j.Sort
	j.fieldMap["is_active"] = j.IsActive
	j.fieldMap["douyin_url"] = j.DouyinUrl
	j.fieldMap["copywriting"] = j.Copywriting
}

func (j jyhMaterialCategory) clone(db *gorm.DB) jyhMaterialCategory {
	j.jyhMaterialCategoryDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j jyhMaterialCategory) replaceDB(db *gorm.DB) jyhMaterialCategory {
	j.jyhMaterialCategoryDo.ReplaceDB(db)
	return j
}

type jyhMaterialCategoryDo struct{ gen.DO }

func (j jyhMaterialCategoryDo) Debug() *jyhMaterialCategoryDo {
	return j.withDO(j.DO.Debug())
}

func (j jyhMaterialCategoryDo) WithContext(ctx context.Context) *jyhMaterialCategoryDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jyhMaterialCategoryDo) ReadDB() *jyhMaterialCategoryDo {
	return j.Clauses(dbresolver.Read)
}

func (j jyhMaterialCategoryDo) WriteDB() *jyhMaterialCategoryDo {
	return j.Clauses(dbresolver.Write)
}

func (j jyhMaterialCategoryDo) Session(config *gorm.Session) *jyhMaterialCategoryDo {
	return j.withDO(j.DO.Session(config))
}

func (j jyhMaterialCategoryDo) Clauses(conds ...clause.Expression) *jyhMaterialCategoryDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jyhMaterialCategoryDo) Returning(value interface{}, columns ...string) *jyhMaterialCategoryDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jyhMaterialCategoryDo) Not(conds ...gen.Condition) *jyhMaterialCategoryDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jyhMaterialCategoryDo) Or(conds ...gen.Condition) *jyhMaterialCategoryDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jyhMaterialCategoryDo) Select(conds ...field.Expr) *jyhMaterialCategoryDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jyhMaterialCategoryDo) Where(conds ...gen.Condition) *jyhMaterialCategoryDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jyhMaterialCategoryDo) Order(conds ...field.Expr) *jyhMaterialCategoryDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jyhMaterialCategoryDo) Distinct(cols ...field.Expr) *jyhMaterialCategoryDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jyhMaterialCategoryDo) Omit(cols ...field.Expr) *jyhMaterialCategoryDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jyhMaterialCategoryDo) Join(table schema.Tabler, on ...field.Expr) *jyhMaterialCategoryDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jyhMaterialCategoryDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jyhMaterialCategoryDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jyhMaterialCategoryDo) RightJoin(table schema.Tabler, on ...field.Expr) *jyhMaterialCategoryDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jyhMaterialCategoryDo) Group(cols ...field.Expr) *jyhMaterialCategoryDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jyhMaterialCategoryDo) Having(conds ...gen.Condition) *jyhMaterialCategoryDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jyhMaterialCategoryDo) Limit(limit int) *jyhMaterialCategoryDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jyhMaterialCategoryDo) Offset(offset int) *jyhMaterialCategoryDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jyhMaterialCategoryDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jyhMaterialCategoryDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jyhMaterialCategoryDo) Unscoped() *jyhMaterialCategoryDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jyhMaterialCategoryDo) Create(values ...*jyhapp.JyhMaterialCategory) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jyhMaterialCategoryDo) CreateInBatches(values []*jyhapp.JyhMaterialCategory, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jyhMaterialCategoryDo) Save(values ...*jyhapp.JyhMaterialCategory) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jyhMaterialCategoryDo) First() (*jyhapp.JyhMaterialCategory, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterialCategory), nil
	}
}

func (j jyhMaterialCategoryDo) Take() (*jyhapp.JyhMaterialCategory, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterialCategory), nil
	}
}

func (j jyhMaterialCategoryDo) Last() (*jyhapp.JyhMaterialCategory, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterialCategory), nil
	}
}

func (j jyhMaterialCategoryDo) Find() ([]*jyhapp.JyhMaterialCategory, error) {
	result, err := j.DO.Find()
	return result.([]*jyhapp.JyhMaterialCategory), err
}

func (j jyhMaterialCategoryDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*jyhapp.JyhMaterialCategory, err error) {
	buf := make([]*jyhapp.JyhMaterialCategory, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jyhMaterialCategoryDo) FindInBatches(result *[]*jyhapp.JyhMaterialCategory, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jyhMaterialCategoryDo) Attrs(attrs ...field.AssignExpr) *jyhMaterialCategoryDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jyhMaterialCategoryDo) Assign(attrs ...field.AssignExpr) *jyhMaterialCategoryDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jyhMaterialCategoryDo) Joins(fields ...field.RelationField) *jyhMaterialCategoryDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jyhMaterialCategoryDo) Preload(fields ...field.RelationField) *jyhMaterialCategoryDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jyhMaterialCategoryDo) FirstOrInit() (*jyhapp.JyhMaterialCategory, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterialCategory), nil
	}
}

func (j jyhMaterialCategoryDo) FirstOrCreate() (*jyhapp.JyhMaterialCategory, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterialCategory), nil
	}
}

func (j jyhMaterialCategoryDo) FindByPage(offset int, limit int) (result []*jyhapp.JyhMaterialCategory, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jyhMaterialCategoryDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jyhMaterialCategoryDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jyhMaterialCategoryDo) Delete(models ...*jyhapp.JyhMaterialCategory) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jyhMaterialCategoryDo) withDO(do gen.Dao) *jyhMaterialCategoryDo {
	j.DO = *do.(*gen.DO)
	return j
}
