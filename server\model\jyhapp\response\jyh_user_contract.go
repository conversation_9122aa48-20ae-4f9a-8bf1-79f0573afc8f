package response

// VerifyContractCodeResp 验证签约验证码响应
type VerifyContractCodeResp struct {
	Ticket string `json:"ticket"` // 验证码票据
}

// MCNUserInfo MCN用户信息
type MCNUserInfo struct {
	AppId       int    `json:"app_id"`
	AuthorLevel string `json:"author_level"`
	AvatarImg   string `json:"avatar_img"`
	AwemeId     string `json:"aweme_id"`
	UserId      string `json:"user_id"`
	UserName    string `json:"user_name"`
}

// AddAccountBindResp 绑定用户账号响应
type AddAccountBindResp struct {
	BindID string `json:"bind_id"` // 绑定ID
}

// AddUserBindPidResp 绑定结算响应
type AddUserBindPidResp struct {
	BindID string `json:"bind_id"` // 绑定ID
}
