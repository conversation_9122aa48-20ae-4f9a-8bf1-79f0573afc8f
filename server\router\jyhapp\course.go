package jyhapp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type CourseRouter struct {
}

// InitCourseRouter 初始化 CourseRouter 路由信息
func (s *CourseRouter) InitCourseRouter(Router *gin.RouterGroup, JyhRouter *gin.RouterGroup) {
	courseRouter := Router.Group("course").Use(middleware.OperationRecord())
	courseRouterWithoutRecord := Router.Group("course")
	var courseApi = v1.ApiGroupApp.JyhApiGroup.CourseApi
	{
		// 课程相关
		courseRouter.POST("create", courseApi.Create)                   // 新建课程
		courseRouter.DELETE("delete", courseApi.Delete)                 // 删除课程
		courseRouter.PUT("update", courseApi.Update)                    // 更新课程
		courseRouter.PUT("incrementView", courseApi.IncrementViewCount) // 更新课程状态
	}
	{
		courseRouterWithoutRecord.GET("detail", courseApi.GetDetail) // 根据ID获取课程
		courseRouterWithoutRecord.GET("list", courseApi.GetList)     // 获取课程列表
	}
	{

	}
}
