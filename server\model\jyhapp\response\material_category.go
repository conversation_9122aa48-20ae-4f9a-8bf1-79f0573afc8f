package response

type MaterialCategoryTree struct {
	ID          uint                    `json:"id"`          // 分类ID
	CatName     string                  `json:"catName"`     // 分类名称
	ParentID    uint                    `json:"parentId"`    // 父级ID
	Sort        int                     `json:"sort"`        // 排序
	IsActive    bool                    `json:"isActive"`    // 状态
	CatDesc     string                  `json:"catDesc"`     // 描述
	DouyinUrl   string                  `json:"douyinUrl"`   // 抖音链接
	Copywriting string                  `json:"copywriting"` // 文案
	Children    []*MaterialCategoryTree `json:"children"`    // 子级
}

type MaterialCategoryParent struct {
	ParentID uint                           `json:"parentId"` // 分类ID
	List     []MaterialCategoryListResponse `json:"list"`     // 子分类列表
}

type MaterialCategoryListResponse struct {
	ID          uint   `json:"id"`          // 分类ID
	CatName     string `json:"catName"`     // 分类名称
	ParentID    uint   `json:"parentId"`    // 父级ID
	Sort        int    `json:"sort"`        // 排序
	CatDesc     string `json:"catDesc"`     // 描述
	DouyinUrl   string `json:"douyinUrl"`   // 抖音链接
	Copywriting string `json:"copywriting"` // 文案
}
