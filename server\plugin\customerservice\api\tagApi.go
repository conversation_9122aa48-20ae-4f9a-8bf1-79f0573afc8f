package api

import (
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/plugin/customerservice/model/request"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/customerservice/model"
	"github.com/gin-gonic/gin"
)

type TagApi struct{}

// 创建分发规则
func (t *TagApi) CreateDistributeRule(c *gin.Context) {
	var req struct {
		RuleName      string  `json:"rule_name" binding:"required"`
		Priority      int     `json:"priority" binding:"required"`
		MaxUsers      int     `json:"max_users" binding:"required"`
		WorkTimeStart string  `json:"work_time_start" binding:"required"`
		WorkTimeEnd   string  `json:"work_time_end" binding:"required"`
		IsDefault     bool    `json:"is_default"`
		TagIDs        []uint  `json:"tag_ids"`     // 关联的标签ID列表
		ServiceIDs    []int64 `json:"service_ids"` // 关联的客服ID列表
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 创建规则
	rule, err := distributeService.CreateDistributeRule(
		req.RuleName,
		req.Priority,
		req.MaxUsers,
		req.WorkTimeStart,
		req.WorkTimeEnd,
		req.IsDefault,
	)

	if err != nil {
		response.FailWithMessage("创建规则失败: "+err.Error(), c)
		return
	}

	// 绑定标签
	if len(req.TagIDs) > 0 {
		if err := distributeService.BindRuleToTags(rule.ID, req.TagIDs); err != nil {
			response.FailWithMessage("绑定标签失败: "+err.Error(), c)
			return
		}
	}

	// 绑定客服
	if len(req.ServiceIDs) > 0 {
		if err := distributeService.BindRuleToServices(rule.ID, req.ServiceIDs); err != nil {
			response.FailWithMessage("绑定客服失败: "+err.Error(), c)
			return
		}
	}

	response.OkWithData(rule, c)
}

// 更新分发规则
func (t *TagApi) UpdateDistributeRule(c *gin.Context) {
	var req struct {
		Id            uint    `json:"id"`
		RuleName      string  `json:"rule_name"`
		Priority      int     `json:"priority"`
		MaxUsers      int     `json:"max_users"`
		WorkTimeStart string  `json:"work_time_start"`
		WorkTimeEnd   string  `json:"work_time_end"`
		IsDefault     bool    `json:"is_default"`
		Status        uint    `json:"status"`
		TagIDs        []uint  `json:"tag_ids"`     // 关联的标签ID列表
		ServiceIDs    []int64 `json:"service_ids"` // 关联的客服ID列表
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 构建更新字段
	updates := make(map[string]interface{})
	if req.RuleName != "" {
		updates["rule_name"] = req.RuleName
	}
	if req.Priority > 0 {
		updates["priority"] = req.Priority
	}
	if req.MaxUsers > 0 {
		updates["max_users"] = req.MaxUsers
	}
	if req.WorkTimeStart != "" {
		updates["work_time_start"] = req.WorkTimeStart
	}
	if req.WorkTimeEnd != "" {
		updates["work_time_end"] = req.WorkTimeEnd
	}
	if req.Status > 0 {
		updates["status"] = req.Status
	}
	updates["is_default"] = req.IsDefault
	ruleID := req.Id
	// 更新规则
	if err := distributeService.UpdateDistributeRule(uint(ruleID), updates); err != nil {
		response.FailWithMessage("更新规则失败: "+err.Error(), c)
		return
	}

	// 更新标签关联
	if len(req.TagIDs) >= 0 { // 允许空数组，表示清空所有关联
		if err := distributeService.BindRuleToTags(uint(ruleID), req.TagIDs); err != nil {
			response.FailWithMessage("更新标签关联失败: "+err.Error(), c)
			return
		}
	}

	// 更新客服关联
	if len(req.ServiceIDs) >= 0 { // 允许空数组，表示清空所有关联
		if err := distributeService.BindRuleToServices(uint(ruleID), req.ServiceIDs); err != nil {
			response.FailWithMessage("更新客服关联失败: "+err.Error(), c)
			return
		}
	}

	response.OkWithMessage("更新成功", c)
}

// 删除分发规则
func (t *TagApi) DeleteDistributeRule(c *gin.Context) {
	ruleID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.FailWithMessage("无效的规则ID", c)
		return
	}

	if err := distributeService.DeleteDistributeRule(uint(ruleID)); err != nil {
		response.FailWithMessage("删除规则失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("删除成功", c)
}

// 获取分发规则列表
func (t *TagApi) GetDistributeRules(c *gin.Context) {
	var req request.GetDistributeRulesReq
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage("分页参数错误:"+err.Error(), c)
		return
	}
	db := global.GVA_DB.Model(&model.SysServiceDistributeRule{})
	if req.RuleName != "" {
		db = db.Where("rule_name LIKE ?", "%"+req.RuleName+"%")
	}
	if req.Status > 0 {
		db = db.Where("status = ?", req.Status)
	}

	var total int64
	db.Count(&total)
	limit := req.PageSize
	offset := (req.Page - 1) * limit
	var rules []model.SysServiceDistributeRule
	db.Order("id desc").Limit(limit).
		Offset(offset).Find(&rules)
	type TagInfo struct {
		ID   uint   `json:"id"`
		Name string `json:"name"`
	}

	type ServiceInfo struct {
		ID       int64  `json:"id"`
		Nickname string `json:"nickname"`
		Priority int    `json:"priority"`
	}

	// 为每个规则获取关联的标签和客服
	type RuleWithDetails struct {
		model.SysServiceDistributeRule
		Tags     []TagInfo     `json:"tags"`
		Services []ServiceInfo `json:"services"`
	}

	var result []RuleWithDetails

	if len(rules) == 0 {
		response.OkWithDetailed(response.PageResult{
			List:     result,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
		return
	}

	// 收集所有规则ID
	var ruleIDs []uint
	for _, rule := range rules {
		ruleIDs = append(ruleIDs, rule.ID)
	}

	// 批量查询所有规则的标签关联
	var tagRelations []model.SysServiceRuleTagRelation
	global.GVA_DB.Where("rule_id IN ?", ruleIDs).Find(&tagRelations)

	// 批量查询所有规则的客服关联
	var serviceRelations []model.SysServiceRuleServiceRelation
	global.GVA_DB.Where("rule_id IN ?", ruleIDs).Order("priority ASC").Find(&serviceRelations)

	// 收集所有标签ID
	var tagIDs []uint
	tagIDSet := make(map[uint]bool)
	for _, relation := range tagRelations {
		if !tagIDSet[relation.TagID] {
			tagIDs = append(tagIDs, relation.TagID)
			tagIDSet[relation.TagID] = true
		}
	}

	// 收集所有客服ID
	var serviceIDs []int64
	serviceIDSet := make(map[int64]bool)
	for _, relation := range serviceRelations {
		if !serviceIDSet[relation.ServiceID] {
			serviceIDs = append(serviceIDs, relation.ServiceID)
			serviceIDSet[relation.ServiceID] = true
		}
	}

	// 批量查询所有标签信息
	var userTags []jyhapp.JyhUserTag
	if len(tagIDs) > 0 {
		global.GVA_DB.Where("id IN ?", tagIDs).Find(&userTags)
	}

	// 批量查询所有客服信息
	var services []model.SysService
	if len(serviceIDs) > 0 {
		global.GVA_DB.Where("id IN ?", serviceIDs).Find(&services)
	}

	// 构建标签映射
	tagMap := make(map[uint]TagInfo)
	for _, tag := range userTags {
		tagMap[tag.ID] = TagInfo{
			ID:   tag.ID,
			Name: tag.Name,
		}
	}

	// 构建客服映射
	serviceMap := make(map[int64]model.SysService)
	for _, service := range services {
		serviceMap[service.Id] = service
	}

	// 构建规则-标签关联映射
	ruleTagMap := make(map[uint][]TagInfo)
	for _, relation := range tagRelations {
		if tagInfo, exists := tagMap[relation.TagID]; exists {
			ruleTagMap[relation.RuleID] = append(ruleTagMap[relation.RuleID], tagInfo)
		}
	}

	// 构建规则-客服关联映射
	ruleServiceMap := make(map[uint][]ServiceInfo)
	for _, relation := range serviceRelations {
		if service, exists := serviceMap[relation.ServiceID]; exists {
			serviceInfo := ServiceInfo{
				ID:       service.Id,
				Nickname: service.Nickname,
				Priority: relation.Priority,
			}
			ruleServiceMap[relation.RuleID] = append(ruleServiceMap[relation.RuleID], serviceInfo)
		}
	}

	// 组装最终结果
	for _, rule := range rules {
		tags := ruleTagMap[rule.ID]
		if tags == nil {
			tags = []TagInfo{}
		}
		services := ruleServiceMap[rule.ID]
		if services == nil {
			services = []ServiceInfo{}
		}
		result = append(result, RuleWithDetails{
			SysServiceDistributeRule: rule,
			Tags:                     tags,
			Services:                 services,
		})
	}

	response.OkWithDetailed(response.PageResult{
		List:     result,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取成功", c)
}

// 获取规则详情
func (t *TagApi) GetDistributeRuleDetail(c *gin.Context) {
	ruleID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.FailWithMessage("无效的规则ID", c)
		return
	}

	var rule model.SysServiceDistributeRule
	if err := global.GVA_DB.Where("id = ?", ruleID).First(&rule).Error; err != nil {
		response.FailWithMessage("规则不存在", c)
		return
	}

	// 定义本地类型
	type ServiceDetail struct {
		ID       int64  `json:"id"`
		Nickname string `json:"nickname"`
		Priority int    `json:"priority"`
	}

	// 获取关联的标签
	var tagRelations []model.SysServiceRuleTagRelation
	global.GVA_DB.Where("rule_id = ?", ruleID).Find(&tagRelations)

	var tagIDs []uint
	for _, relation := range tagRelations {
		tagIDs = append(tagIDs, relation.TagID)
	}

	// 获取关联的客服
	var serviceRelations []model.SysServiceRuleServiceRelation
	global.GVA_DB.Where("rule_id = ?", ruleID).Order("priority ASC").Find(&serviceRelations)

	var serviceIDs []int64
	var serviceDetails []ServiceDetail
	for _, relation := range serviceRelations {
		serviceIDs = append(serviceIDs, relation.ServiceID)

		// 获取客服详细信息
		var service model.SysService
		if err := global.GVA_DB.Where("id = ?", relation.ServiceID).First(&service).Error; err == nil {
			serviceDetails = append(serviceDetails, ServiceDetail{
				ID:       service.Id,
				Nickname: service.Nickname,
				Priority: relation.Priority,
			})
		}
	}

	response.OkWithData(gin.H{
		"rule":            rule,
		"tag_ids":         tagIDs,
		"service_ids":     serviceIDs,
		"service_details": serviceDetails,
	}, c)
}

// 获取标签关联的规则
func (t *TagApi) GetRulesByTag(c *gin.Context) {
	tagID, err := strconv.ParseUint(c.Param("tag_id"), 10, 32)
	if err != nil {
		response.FailWithMessage("无效的标签ID", c)
		return
	}

	rules, err := distributeService.GetRulesByTag(uint(tagID))
	if err != nil {
		response.FailWithMessage("获取规则失败: "+err.Error(), c)
		return
	}

	response.OkWithData(rules, c)
}

// 绑定客服标签
func (t *TagApi) BindServiceTags(c *gin.Context) {
	var req struct {
		ServiceID int64  `json:"service_id" binding:"required"`
		TagIDs    []uint `json:"tag_ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := distributeService.BindServiceTags(req.ServiceID, req.TagIDs); err != nil {
		response.FailWithMessage("绑定标签失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("绑定成功", c)
}

// 获取客服标签
func (t *TagApi) GetServiceTags(c *gin.Context) {
	serviceID, err := strconv.ParseInt(c.Param("service_id"), 10, 64)
	if err != nil {
		response.FailWithMessage("无效的客服ID", c)
		return
	}

	tags, err := distributeService.GetServiceTags(serviceID)
	if err != nil {
		response.FailWithMessage("获取标签失败: "+err.Error(), c)
		return
	}

	response.OkWithData(tags, c)
}

// 获取可用标签列表
func (t *TagApi) GetAvailableTags(c *gin.Context) {
	tags, err := distributeService.GetAvailableTags()
	if err != nil {
		response.FailWithMessage("获取标签失败: "+err.Error(), c)
		return
	}

	response.OkWithData(tags, c)
}

// 获取有指定标签的客服列表
func (t *TagApi) GetServicesByTag(c *gin.Context) {
	tagID, err := strconv.ParseUint(c.Param("tag_id"), 10, 32)
	if err != nil {
		response.FailWithMessage("无效的标签ID", c)
		return
	}

	services, err := distributeService.GetServicesByTag(uint(tagID))
	if err != nil {
		response.FailWithMessage("获取客服列表失败: "+err.Error(), c)
		return
	}

	response.OkWithData(services, c)
}

// 获取可分配的客服列表（所有客服，用于规则配置时选择）
func (t *TagApi) GetAvailableServices(c *gin.Context) {
	var services []model.SysService
	err := global.GVA_DB.Where("status = 1").
		Order("add_time DESC").Find(&services).Error

	if err != nil {
		response.FailWithMessage("获取客服列表失败: "+err.Error(), c)
		return
	}

	response.OkWithData(services, c)
}
