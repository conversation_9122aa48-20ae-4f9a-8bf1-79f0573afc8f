package model

import "github.com/flipped-aurora/gin-vue-admin/server/global"

// SysServiceDistributeRule 客服分配规则模板
type SysServiceDistributeRule struct {
	global.Model1
	RuleName      string `json:"rule_name" gorm:"comment:规则名称;not null"`
	Priority      int    `json:"priority" gorm:"comment:优先级，数字越小优先级越高"`
	MaxUsers      int    `json:"max_users" gorm:"comment:最大同时服务用户数"`
	WorkTimeStart string `json:"work_time_start" gorm:"comment:工作开始时间"`
	WorkTimeEnd   string `json:"work_time_end" gorm:"comment:工作结束时间"`
	Status        uint   `json:"status" gorm:"comment:状态(1:启用 2:禁用);default:1"`
	IsDefault     bool   `json:"is_default" gorm:"comment:是否为默认规则;default:false"`
}

func (SysServiceDistributeRule) TableName() string {
	return "sys_service_distribute_rule"
}

// SysServiceRuleTagRelation 规则与标签的关联表
type SysServiceRuleTagRelation struct {
	ID     uint `gorm:"primarykey" json:"id"`
	RuleID uint `json:"rule_id" gorm:"comment:规则ID;not null"`
	TagID  uint `json:"tag_id" gorm:"comment:标签ID，关联jyh_user_tag表;not null"`
}

func (SysServiceRuleTagRelation) TableName() string {
	return "sys_service_rule_tag_relation"
}

// SysServiceRuleServiceRelation 规则与客服的关联表
type SysServiceRuleServiceRelation struct {
	ID        uint  `gorm:"primarykey" json:"id"`
	RuleID    uint  `json:"rule_id" gorm:"comment:规则ID;not null"`
	ServiceID int64 `json:"service_id" gorm:"comment:客服ID;not null"`
	Priority  int   `json:"priority" gorm:"comment:在该规则下的优先级;default:1"`
}

func (SysServiceRuleServiceRelation) TableName() string {
	return "sys_service_rule_service_relation"
}

// 分发规则状态常量
const (
	RuleStatusDisabled = 2 // 禁用
	RuleStatusEnabled  = 1 // 启用
)
