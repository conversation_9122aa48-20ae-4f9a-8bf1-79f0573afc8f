// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
)

func newJyhCommissionRate(db *gorm.DB, opts ...gen.DOOption) jyhCommissionRate {
	_jyhCommissionRate := jyhCommissionRate{}

	_jyhCommissionRate.jyhCommissionRateDo.UseDB(db, opts...)
	_jyhCommissionRate.jyhCommissionRateDo.UseModel(&jyhapp.JyhCommissionRate{})

	tableName := _jyhCommissionRate.jyhCommissionRateDo.TableName()
	_jyhCommissionRate.ALL = field.NewAsterisk(tableName)
	_jyhCommissionRate.Level = field.NewUint(tableName, "level")
	_jyhCommissionRate.Rate = field.NewFloat64(tableName, "rate")

	_jyhCommissionRate.fillFieldMap()

	return _jyhCommissionRate
}

type jyhCommissionRate struct {
	jyhCommissionRateDo

	ALL   field.Asterisk
	Level field.Uint
	Rate  field.Float64

	fieldMap map[string]field.Expr
}

func (j jyhCommissionRate) Table(newTableName string) *jyhCommissionRate {
	j.jyhCommissionRateDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j jyhCommissionRate) As(alias string) *jyhCommissionRate {
	j.jyhCommissionRateDo.DO = *(j.jyhCommissionRateDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *jyhCommissionRate) updateTableName(table string) *jyhCommissionRate {
	j.ALL = field.NewAsterisk(table)
	j.Level = field.NewUint(table, "level")
	j.Rate = field.NewFloat64(table, "rate")

	j.fillFieldMap()

	return j
}

func (j *jyhCommissionRate) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *jyhCommissionRate) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 2)
	j.fieldMap["level"] = j.Level
	j.fieldMap["rate"] = j.Rate
}

func (j jyhCommissionRate) clone(db *gorm.DB) jyhCommissionRate {
	j.jyhCommissionRateDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j jyhCommissionRate) replaceDB(db *gorm.DB) jyhCommissionRate {
	j.jyhCommissionRateDo.ReplaceDB(db)
	return j
}

type jyhCommissionRateDo struct{ gen.DO }

func (j jyhCommissionRateDo) Debug() *jyhCommissionRateDo {
	return j.withDO(j.DO.Debug())
}

func (j jyhCommissionRateDo) WithContext(ctx context.Context) *jyhCommissionRateDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jyhCommissionRateDo) ReadDB() *jyhCommissionRateDo {
	return j.Clauses(dbresolver.Read)
}

func (j jyhCommissionRateDo) WriteDB() *jyhCommissionRateDo {
	return j.Clauses(dbresolver.Write)
}

func (j jyhCommissionRateDo) Session(config *gorm.Session) *jyhCommissionRateDo {
	return j.withDO(j.DO.Session(config))
}

func (j jyhCommissionRateDo) Clauses(conds ...clause.Expression) *jyhCommissionRateDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jyhCommissionRateDo) Returning(value interface{}, columns ...string) *jyhCommissionRateDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jyhCommissionRateDo) Not(conds ...gen.Condition) *jyhCommissionRateDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jyhCommissionRateDo) Or(conds ...gen.Condition) *jyhCommissionRateDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jyhCommissionRateDo) Select(conds ...field.Expr) *jyhCommissionRateDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jyhCommissionRateDo) Where(conds ...gen.Condition) *jyhCommissionRateDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jyhCommissionRateDo) Order(conds ...field.Expr) *jyhCommissionRateDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jyhCommissionRateDo) Distinct(cols ...field.Expr) *jyhCommissionRateDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jyhCommissionRateDo) Omit(cols ...field.Expr) *jyhCommissionRateDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jyhCommissionRateDo) Join(table schema.Tabler, on ...field.Expr) *jyhCommissionRateDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jyhCommissionRateDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jyhCommissionRateDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jyhCommissionRateDo) RightJoin(table schema.Tabler, on ...field.Expr) *jyhCommissionRateDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jyhCommissionRateDo) Group(cols ...field.Expr) *jyhCommissionRateDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jyhCommissionRateDo) Having(conds ...gen.Condition) *jyhCommissionRateDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jyhCommissionRateDo) Limit(limit int) *jyhCommissionRateDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jyhCommissionRateDo) Offset(offset int) *jyhCommissionRateDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jyhCommissionRateDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jyhCommissionRateDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jyhCommissionRateDo) Unscoped() *jyhCommissionRateDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jyhCommissionRateDo) Create(values ...*jyhapp.JyhCommissionRate) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jyhCommissionRateDo) CreateInBatches(values []*jyhapp.JyhCommissionRate, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jyhCommissionRateDo) Save(values ...*jyhapp.JyhCommissionRate) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jyhCommissionRateDo) First() (*jyhapp.JyhCommissionRate, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhCommissionRate), nil
	}
}

func (j jyhCommissionRateDo) Take() (*jyhapp.JyhCommissionRate, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhCommissionRate), nil
	}
}

func (j jyhCommissionRateDo) Last() (*jyhapp.JyhCommissionRate, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhCommissionRate), nil
	}
}

func (j jyhCommissionRateDo) Find() ([]*jyhapp.JyhCommissionRate, error) {
	result, err := j.DO.Find()
	return result.([]*jyhapp.JyhCommissionRate), err
}

func (j jyhCommissionRateDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*jyhapp.JyhCommissionRate, err error) {
	buf := make([]*jyhapp.JyhCommissionRate, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jyhCommissionRateDo) FindInBatches(result *[]*jyhapp.JyhCommissionRate, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jyhCommissionRateDo) Attrs(attrs ...field.AssignExpr) *jyhCommissionRateDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jyhCommissionRateDo) Assign(attrs ...field.AssignExpr) *jyhCommissionRateDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jyhCommissionRateDo) Joins(fields ...field.RelationField) *jyhCommissionRateDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jyhCommissionRateDo) Preload(fields ...field.RelationField) *jyhCommissionRateDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jyhCommissionRateDo) FirstOrInit() (*jyhapp.JyhCommissionRate, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhCommissionRate), nil
	}
}

func (j jyhCommissionRateDo) FirstOrCreate() (*jyhapp.JyhCommissionRate, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhCommissionRate), nil
	}
}

func (j jyhCommissionRateDo) FindByPage(offset int, limit int) (result []*jyhapp.JyhCommissionRate, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jyhCommissionRateDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jyhCommissionRateDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jyhCommissionRateDo) Delete(models ...*jyhapp.JyhCommissionRate) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jyhCommissionRateDo) withDO(do gen.Dao) *jyhCommissionRateDo {
	j.DO = *do.(*gen.DO)
	return j
}
