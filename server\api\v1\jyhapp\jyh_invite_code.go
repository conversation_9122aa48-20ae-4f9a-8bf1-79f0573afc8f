package jyhapp

import (
	"strconv"

	jyhResp "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/response"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	jyhReq "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type JyhInviteCodeApi struct{}

// CreateInviteCode 创建邀请码
// @Tags JyhInviteCode
// @Summary 创建邀请码
// @Description 根据指定参数创建单个邀请码，支持平台生成和用户生成两种类型
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body jyhReq.CreateInviteCodeReq true "创建邀请码请求参数"
// @Failure 200 {object} response.Response{msg=string} "创建成功"
// @Router /admin/invite_code/code [post]
func (api *JyhInviteCodeApi) CreateInviteCode(c *gin.Context) {
	var req jyhReq.CreateInviteCodeReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := jyhInviteCodeService.CreateInviteCode(&req); err != nil {
		global.GVA_LOG.Error("创建邀请码失败", zap.Error(err))
		response.FailWithMessage("创建邀请码失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("创建邀请码成功", c)
}

// BatchCreateInviteCodes 批量创建邀请码
// @Tags JyhInviteCode
// @Summary 批量创建邀请码
// @Description 根据指定参数批量创建多个邀请码，支持平台生成和用户生成两种类型
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body jyhReq.BatchCreateInviteCodeReq true "批量创建邀请码请求参数"
// @Failure 400 {object} response.Response{msg=string} "批量创建邀请码成功"
// @Router /admin/invite_code/batch [post]
func (api *JyhInviteCodeApi) BatchCreateInviteCodes(c *gin.Context) {
	var req jyhReq.BatchCreateInviteCodeReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := jyhInviteCodeService.BatchCreateInviteCodes(&req); err != nil {
		global.GVA_LOG.Error("批量创建邀请码失败", zap.Error(err))
		response.FailWithMessage("批量创建邀请码失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("批量创建邀请码成功", c)
}

// GetInviteCodeList 获取邀请码列表
// @Tags JyhInviteCode
// @Summary 获取邀请码列表
// @Description 分页查询邀请码列表，支持多种筛选条件
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query jyhReq.InviteCodeListReq true "分页查询邀请码列表"
// @Success 200 {object} response.Response{data=jyhResp.InviteCodeListResp,msg=string} "获取成功"
// @Router /admin/invite_code/list [get]
func (api *JyhInviteCodeApi) GetInviteCodeList(c *gin.Context) {
	var req jyhReq.InviteCodeListReq
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	list := &jyhResp.InviteCodeListResp{}
	list, err := jyhInviteCodeService.GetInviteCodeList(&req)
	if err != nil {
		global.GVA_LOG.Error("获取邀请码列表失败", zap.Error(err))
		response.FailWithMessage("获取邀请码列表失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(list, "获取邀请码列表成功", c)
}

// DeleteInviteCode 删除邀请码
// @Tags JyhInviteCode
// @Summary 删除邀请码
// @Description 根据邀请码ID删除指定的邀请码
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "邀请码ID"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /admin/invite_code/code/{id} [delete]
func (api *JyhInviteCodeApi) DeleteInviteCode(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("邀请码ID格式错误", c)
		return
	}

	if err := jyhInviteCodeService.DeleteInviteCode(uint(id)); err != nil {
		global.GVA_LOG.Error("删除邀请码失败", zap.Error(err))
		response.FailWithMessage("删除邀请码失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("删除邀请码成功", c)
}

// UpdateInviteCodeStatus 更新邀请码状态
// @Tags JyhInviteCode
// @Summary 更新邀请码状态
// @Description 更新指定邀请码的状态(如禁用、过期等)
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body jyhReq.UpdateInviteCodeStatusReq true "更新邀请码状态请求参数"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /admin/invite_code/status [put]
func (api *JyhInviteCodeApi) UpdateInviteCodeStatus(c *gin.Context) {
	var req jyhReq.UpdateInviteCodeStatusReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := jyhInviteCodeService.UpdateInviteCodeStatus(req.ID, req.Status); err != nil {
		global.GVA_LOG.Error("更新邀请码状态失败", zap.Error(err))
		response.FailWithMessage("更新邀请码状态失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("更新邀请码状态成功", c)
}

// GetInviteCodeStatistics 获取邀请码统计信息
// @Tags JyhInviteCode
// @Summary 获取邀请码统计信息
// @Description 获取邀请码的各种统计数据，包括总数、已使用、未使用、已过期等
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=jyhResp.InviteCodeStatistics,msg=string} "获取成功"
// @Router /admin/invite_code/statistics [get]
func (api *JyhInviteCodeApi) GetInviteCodeStatistics(c *gin.Context) {
	statistics, err := jyhInviteCodeService.GetInviteCodeStatistics()
	if err != nil {
		global.GVA_LOG.Error("获取邀请码统计信息失败", zap.Error(err))
		response.FailWithMessage("获取邀请码统计信息失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(statistics, "获取邀请码统计信息成功", c)
}

// ValidateInviteCode 验证邀请码
// @Tags JyhInviteCode
// @Summary 验证邀请码
// @Description 验证邀请码是否有效、可用，用于用户注册或购买会员时的验证
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param code query string true "邀请码"
// @Success 200 {object} response.Response{data=jyhapp.JyhInviteCode,msg=string} "验证成功"
// @Router /admin/invite_code/validate [get]
func (api *JyhInviteCodeApi) ValidateInviteCode(c *gin.Context) {
	code := c.Query("code")
	if code == "" {
		response.FailWithMessage("邀请码不能为空", c)
		return
	}

	inviteCode, err := jyhInviteCodeService.ValidateInviteCode(code)
	if err != nil {
		global.GVA_LOG.Error("验证邀请码失败", zap.Error(err))
		response.FailWithMessage("验证邀请码失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(inviteCode, "邀请码验证成功", c)
}

// GetUserInviteCodesByLevel 客户端获取用户邀请码列表（按等级分类）
// @Tags JyhInviteCode
// @Summary 获取用户邀请码列表（按等级分类）
// @Description 客户端接口：获取当前用户拥有的邀请码，按等级分类显示，只返回未使用的邀请码
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=jyhResp.UserInviteCodeListResp,msg=string} "获取成功"
// @Router /jyh/invite_code/list [get]
func (api *JyhInviteCodeApi) GetUserInviteCodesByLevel(c *gin.Context) {
	// 获取当前用户ID
	userID := utils.GetUserID(c)
	if userID == 0 {
		response.FailWithMessage("获取用户信息失败", c)
		return
	}

	result, err := jyhInviteCodeService.GetUserInviteCodesByLevel(userID)
	if err != nil {
		global.GVA_LOG.Error("获取用户邀请码失败", zap.Error(err))
		response.FailWithMessage("获取用户邀请码失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(result, "获取用户邀请码成功", c)
}
