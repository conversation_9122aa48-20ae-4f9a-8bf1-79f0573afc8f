package request

import "github.com/flipped-aurora/gin-vue-admin/server/model/common/request"

type OrderStatistics struct {
	StartDate string `json:"startDate"` // 统计开始日期，格式为 YYYY-MM-DD
	EndDate   string `json:"endDate"`   // 统计结束日期，格式为 YYYY-MM-DD
	UserID    uint   `json:"userId"`    // 用户ID
}

type OrderDetail struct {
	request.PageInfo
	Type      int    `json:"type"`      // 订单类型，1 抖音，2 今音荟
	StartDate string `json:"startDate"` // 订单明细开始日期，格式为 YYYY-MM-DD
	EndDate   string `json:"endDate"`   // 订单明细结束日期，格式为 YYYY-MM-DD
	UserID    uint   `json:"userId"`    // 用户ID
}
