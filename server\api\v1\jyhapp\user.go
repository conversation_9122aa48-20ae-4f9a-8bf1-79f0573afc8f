package jyhapp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/request"
	jyhappResp "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type UserApi struct{}

// Register 用户注册
// @Tags     JyhUser
// @Summary   用户注册
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.UserRegister  true  "用户注册"
// @Success   200   {object}  response.Response{msg=string}  "用户注册"
// @Router    /jyh/user/register [post]
func (m *UserApi) Register(c *gin.Context) {
	var req request.UserRegister
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = userService.Register(&req)
	if err != nil {
		global.GVA_LOG.Error("用户注册失败!", zap.Error(err))
		response.FailWithMessage("用户注册失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("用户注册成功", c)
}

// Login 用户登录
// @Tags      JyhUser
// @Summary   用户登录
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.UserLogin  true  "用户登录"
// @Success   200   {object}  response.Response{msg=string}  "用户登录"
// @Router    /jyh/user/login [post]
func (m *UserApi) Login(c *gin.Context) {
	var req request.UserLogin
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	user, err := userService.Login(&req, authGenJwtToken)
	if err != nil {
		global.GVA_LOG.Error("用户登录失败!", zap.Error(err))
		response.FailWithMessage("用户登录失败:"+err.Error(), c)
		return
	}
	response.OkWithData(user, c)
}

// GetUserInfo 获取用户信息
// @Tags      JyhUser
// @Summary   获取用户信息
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Success   200   {object}  response.Response{data=jyhappResp.JyhUserInfo,msg=string}  "获取用户信息"
// @Router    /jyh/user/info [get]
func (m *UserApi) GetUserInfo(c *gin.Context) {
	uid := utils.GetUserID(c)
	resp := &jyhappResp.JyhUserInfo{}
	resp, err := userService.GetUserInfo(uid)
	if err != nil {
		global.GVA_LOG.Error("获取用户信息失败!", zap.Error(err))
		response.FailWithMessage("获取用户信息失败:"+err.Error(), c)
		return
	}
	response.OkWithData(resp, c)
}

// SendVerificationCode 发送验证码
// @Tags      JyhUser
// @Summary   发送验证码
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     request.SendSmsCode  true  "发送验证码"
// @Success   200   {object}  response.Response{msg=string}  "发送验证码"
// @Router    /jyh/user/send_code [post]
func (m *UserApi) SendVerificationCode(c *gin.Context) {
	req := request.SendSmsCode{}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err := userService.SendVerificationCode(req.Phone, req.Scene)
	if err != nil {
		global.GVA_LOG.Error("发送验证码失败!", zap.Error(err))
		response.FailWithMessage("发送验证码失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("发送验证码成功", c)
}

// UpdateUserInfo 更新用户信息
// @Tags     JyhUser
// @Summary  更新用户信息
// @Security ApiKeyAuth
// @accept   application/json
// @Produce  application/json
// @Param    data  body      request.UpdateUserInfoReq  true  "更新用户信息"
// @Success  200   {object}  response.Response{msg=string}  "更新用户信息"
// @Router   /jyh/user/update_info [post]
func (m *UserApi) UpdateUserInfo(c *gin.Context) {
	var req request.UpdateUserInfoReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	uid := utils.GetUserID(c)
	if uid == 0 {
		response.FailWithMessage("用户未登录", c)
		return
	}

	// 转换为UserExtUpdateInfo
	userExtInfo := &request.UserExtUpdateInfo{
		WeChatNickname: req.WeChatNickname,
		WeChat:         req.WeChat,
		DouYinNickname: req.DouYinNickname,
		DouYin:         req.DouYin,
		DouYinPhone:    req.DouYinPhone,
		QQ:             req.QQ,
		IdCard:         req.IdCard,
		Age:            req.Age,
		Sex:            req.Sex,
		City:           req.City,
		Occupation:     req.Occupation,
		Address:        req.Address,
	}

	err = userService.UpdateUserInfo(uid, userExtInfo)
	if err != nil {
		global.GVA_LOG.Error("更新用户信息失败!", zap.Error(err))
		response.FailWithMessage("更新用户信息失败:"+err.Error(), c)
		return
	}

	response.OkWithMessage("更新用户信息成功", c)
}

// UploadCertificate 上传粉丝凭证
// @Tags      JyhUser
// @Summary   上传粉丝凭证
// @Security  ApiKeyAuth
// @accept    multipart/form-data
// @Produce   application/json
// @Param     data  body  request.UploadCertificateReq  true  "粉丝凭证上传请求"
// @Success   200   {object}  response.Response{msg=string}  "上传粉丝凭证"
// @Router    /jyh/user/upload_certificate [post]
func (m *UserApi) UploadCertificate(c *gin.Context) {
	var req request.UploadCertificateReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	uid := utils.GetUserID(c)
	if uid == 0 {
		response.FailWithMessage("用户未登录", c)
		return
	}

	err = userService.CreateCertificate(uid, req)
	if err != nil {
		global.GVA_LOG.Error("上传粉丝凭证失败!", zap.Error(err))
		response.FailWithMessage("上传粉丝凭证失败:"+err.Error(), c)
		return
	}

	response.OkWithMessage("上传粉丝凭证成功", c)
}
