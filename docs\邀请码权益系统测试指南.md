# 邀请码权益系统测试指南

## 测试环境准备

### 1. 数据库准备

#### 创建权益类型
```sql
INSERT INTO jyh_user_benefit (key, name, description, created_at, updated_at) 
VALUES ('referral_code_once', '单个推荐码兑换权', '用户可生成指定等级的邀请码', NOW(), NOW());
```

#### 创建测试等级
```sql
-- 创建星辰达人等级（ID=10）
INSERT INTO jyh_user_ship_level (name, code, price_cents, duration_days, sort, description, created_at, updated_at)
VALUES ('星辰达人', 'STAR', 0, 365, 10, '星辰达人等级', NOW(), NOW());

-- 创建炬火达人等级（ID=12）
INSERT INTO jyh_user_ship_level (name, code, price_cents, duration_days, sort, description, created_at, updated_at)
VALUES ('炬火达人', 'TORCH', 0, 365, 12, '炬火达人等级', NOW(), NOW());
```

#### 配置等级权益映射（免费权益）
```sql
INSERT INTO jyh_user_ship_level_benefit (level_id, benefit_id, value, condition, created_at, updated_at)
VALUES (
  10,  -- 星辰达人等级ID
  (SELECT id FROM jyh_user_benefit WHERE key = 'referral_code_once'),  -- 权益ID
  6.0000,  -- 生成6个邀请码
  '{"target_level_id": 12, "expire_days": 30, "price_cents": 0}',  -- 免费权益配置
  NOW(), 
  NOW()
);
```

#### 配置等级权益映射（付费权益）
```sql
INSERT INTO jyh_user_ship_level_benefit (level_id, benefit_id, value, condition, created_at, updated_at)
VALUES (
  10,  -- 星辰达人等级ID
  (SELECT id FROM jyh_user_benefit WHERE key = 'referral_code_once'),  -- 权益ID
  1.0000,  -- 可购买1个邀请码
  '{"target_level_id": 8, "expire_days": 90, "price_cents": 11900}',  -- 付费权益配置
  NOW(), 
  NOW()
);
```

## 测试用例

### 测试用例1：免费权益自动生成邀请码

#### 测试步骤
1. 创建测试用户
2. 使用邀请码注册（触发等级分配）
3. 验证邀请码是否自动生成

#### 期望结果
- 用户获得星辰达人等级
- 系统自动生成6个炬火达人等级的邀请码
- 邀请码有效期30天
- 邀请码类型为'user'
- 邀请码价格为0

#### 验证SQL
```sql
-- 检查用户等级记录
SELECT * FROM jyh_user_level WHERE user_id = ? AND level_id = 10;

-- 检查自动生成的邀请码
SELECT * FROM jyh_invite_code 
WHERE user_id = ? 
  AND type = 'user' 
  AND level_id = 12 
  AND sale_price = 0;
```

### 测试用例2：付费权益不自动生成

#### 测试步骤
1. 配置付费权益（price_cents > 0）
2. 分配用户等级
3. 验证是否不会自动生成邀请码

#### 期望结果
- 用户获得等级成功
- 不会自动生成付费权益的邀请码
- 日志显示"非免费邀请码权益，跳过自动生成"

### 测试用例3：权益配置错误处理

#### 测试步骤
1. 配置错误的权益条件（如target_level_id为0）
2. 分配用户等级
3. 检查错误处理

#### 期望结果
- 系统记录错误日志
- 不影响用户等级分配的主流程
- 不生成邀请码

### 测试用例4：目标等级不存在

#### 测试步骤
1. 配置不存在的target_level_id
2. 分配用户等级
3. 检查错误处理

#### 期望结果
- 系统记录"目标等级不存在"错误
- 不影响用户等级分配
- 不生成邀请码

## 自动化测试脚本

### 完整测试流程脚本
```go
func TestInviteCodeBenefitGeneration(t *testing.T) {
    // 准备测试数据
    userID := createTestUser()
    levelID := uint(10) // 星辰达人等级
    
    // 执行等级分配
    err := userService.assignUserLevel(tx, userID, levelID)
    assert.NoError(t, err)
    
    // 验证邀请码生成
    inviteCodes := getInviteCodesByUser(userID)
    assert.Equal(t, 6, len(inviteCodes)) // 应该生成6个邀请码
    
    for _, code := range inviteCodes {
        assert.Equal(t, "user", code.Type)
        assert.Equal(t, uint(12), code.LevelID) // 炬火达人等级
        assert.Equal(t, uint64(0), code.SalePrice) // 免费
        assert.False(t, code.IsUsed)
        assert.NotNil(t, code.ExpiredAt)
    }
}
```

## 监控和日志

### 关键日志检查点

1. **权益检查**：
   ```
   INFO: 未找到 referral_code_once 权益类型
   INFO: 当前等级未配置 referral_code_once 权益
   ```

2. **条件验证**：
   ```
   INFO: 非免费邀请码权益，跳过自动生成
   ERROR: 邀请码权益缺少目标等级ID
   ERROR: 目标等级不存在
   ```

3. **生成过程**：
   ```
   INFO: 成功生成免费邀请码
   INFO: 邀请码权益处理完成
   ERROR: 生成邀请码失败
   ERROR: 保存邀请码失败
   ```

### 性能监控

- 邀请码生成耗时
- 事务执行时间
- 数据库查询次数
- 错误率统计

## 常见问题排查

### 1. 邀请码没有自动生成
- 检查权益类型是否存在
- 检查等级权益映射配置
- 检查price_cents是否为0
- 检查target_level_id是否有效

### 2. 生成数量不正确
- 检查value字段配置
- 检查权益条件JSON格式
- 检查数据类型转换

### 3. 邀请码过期时间错误
- 检查expire_days配置
- 检查时间计算逻辑
- 检查时区设置

## 回滚方案

如果发现问题需要回滚：

```sql
-- 删除错误生成的邀请码
DELETE FROM jyh_invite_code 
WHERE user_id = ? 
  AND created_at > '2024-01-01 00:00:00'
  AND type = 'user'
  AND sale_price = 0;

-- 重置用户等级（如果需要）
UPDATE jyh_user_level 
SET status = 'cancelled' 
WHERE user_id = ? AND level_id = ?;
```

通过这个测试指南，可以全面验证邀请码权益自动生成功能的正确性和稳定性。 