---

# 余额系统设计文档 (带乐观锁版本字段)

## 概述

本文档旨在设计一个支持多种来源和类型的余额系统，核心需求是管理用户余额，其中**账户总余额 = 佣金A余额 + 佣金B余额 - 累计提现总额**。系统设计将重点关注数据安全、可追溯性、高性能及可扩展性，并引入**乐观锁机制**来处理并发更新。

---

## 1. 系统需求

一个健壮的余额系统需要满足以下核心需求：

* **账户余额管理：** 支持用户账户余额的增加和减少操作。
* **多来源支持：** 能够区分不同资金来源，如佣金A、佣金B。
* **多业务类型支持：** 能够区分不同业务类型，如收入、提现。
* **账户余额构成：** 明确账户总余额由佣金A和佣金B的总和，减去累计提现金额构成。
* **数据安全：** 确保交易的原子性、一致性、隔离性、持久性（ACID），防止篡改和重复交易。**特别地，通过乐观锁避免并发更新导致的数据丢失。
  **
* **可追溯性：** 所有账户余额变动都应有详细记录，便于审计和问题排查。
* **高性能与可扩展性：** 能够支持大量用户和高并发交易。

---

## 2. 数据库表结构设计

为了满足上述需求，我们设计了以下核心数据库表：

### 2.1. `jyh_user_accounts` 表 (用户账户表)

此表存储用户的核心账户余额信息，并增加了**版本字段 `version`**。

| 字段名                 | 类型             | 描述           | 备注                                                             |
|:--------------------|:---------------|:-------------|:---------------------------------------------------------------|
| `account_id`        | BIGINT         | 用户账户唯一ID     | 主键，索引                                                          |
| `user_id`           | BIGINT         | 关联的用户ID      |                                                                |
| `dy_balance`        | DECIMAL(18, 2) | 用户佣金A可用余额    | 默认0.00                                                         |
| `jyh_balance`       | DECIMAL(18, 2) | 用户佣金B可用余额    | 默认0.00                                                         |
| `withdrawn_balance` | DECIMAL(18, 2) | 用户累计已成功提现总金额 | 默认0.00                                                         |
| `current_balance`   | DECIMAL(18, 2) | 用户当前账户总余额    | **冗余字段**，由 `dy_balance + jyh_balance - withdrawn_balance` 计算得来 |
| `version`           | INT            | **乐观锁版本号**   | 默认0，每次更新递增                                                     |
| `created_at`        | TIMESTAMP      | 创建时间         |                                                                |
| `updated_at`        | TIMESTAMP      | 更新时间         |                                                                |
| `last_updated_at`   | TIMESTAMP      | 最后更新时间       |                                                                |

**设计说明：**

* `version` 字段用于实现乐观锁。每次更新 `user_accounts`
  表时，这个版本号都会递增。在更新操作中，我们会比较读取时的版本号和当前数据库中的版本号是否一致，不一致则表示有其他事务已修改该记录，需要重试。
* `current_balance` 字段作为**冗余字段**，目的是提高查询效率。在任何佣金或提现操作时，此字段都需要同步更新，并通过事务保证一致性。
* `dy_balance` 和 `jyh_balance` 独立存储，方便追踪各自的来源。
* `withdrawn_balance` 字段记录了所有成功的提现总额，是计算账户总余额的关键组成部分。

### 2.2. `jyh_user_account_transactions` 表 (账户交易流水表)

此表的结构保持不变，它主要记录所有账户余额变动的详细信息，是系统审计和可追溯性的基础。

| 字段名                       | 类型             | 描述        | 备注                                            |
|:--------------------------|:---------------|:----------|:----------------------------------------------|
| `transaction_id`          | BIGINT         | 交易唯一ID    | 主键，索引                                         |
| `account_id`              | BIGINT         | 关联的用户账户ID | 外键，索引                                         |
| `amount`                  | DECIMAL(18, 2) | 交易金额      | **正数**表示变动金额，提现时也记录提现的绝对金额                    |
| `transaction_type`        | VARCHAR(50)    | 交易类型      | 例如：`COMMISSION_INCOME`, `WITHDRAWAL`          |
| `source_type`             | VARCHAR(50)    | 资金来源/影响项  | 例如：`dy`, `jyh`, `WITHDRAWAL_OUT`              |
| `related_business_id`     | BIGINT         | 相关业务ID    | 可选，用于关联具体业务                                   |
| `transaction_description` | VARCHAR(255)   | 交易描述      | 例如："佣金A收入"、"提现成功"                             |
| `transaction_status`      | VARCHAR(20)    | 交易状态      | 例如：`PENDING`, `SUCCESS`, `FAILED`, `ROLLBACK` |
| `created_at`              | TIMESTAMP      | 交易创建时间    | 索引                                            |
| `completed_at`            | TIMESTAMP      | 交易完成/更新时间 |                                               |
| `balance_before`          | DECIMAL(18, 2) | 交易前账户总余额  | 用于数据校对                                        |
| `balance_after`           | DECIMAL(18, 2) | 交易后账户总余额  | 用于数据校对                                        |

**设计说明：**

* 每笔账户余额变动都会插入一条记录，提供详细的审计路径。
* `transaction_type` 和 `source_type` 字段提供高度灵活性，用于区分业务类型和资金来源。
* `transaction_status` 字段对于处理异步或可能失败的交易（如提现）至关重要。
* `balance_before` 和 `balance_after` 字段是**数据一致性**的重要保障，有助于快速校验和恢复。

---

## 3. 账户余额计算和操作逻辑

根据新的核心逻辑：**`current_balance` = `dy_balance` + `jyh_balance` - `withdrawn_balance`**。

---

## 4. 数据安全策略

数据安全是余额系统的核心，我们将采用以下策略：

### 4.1. 事务 (Transactions)

所有涉及账户余额变动的数据库操作（更新 `user_accounts` 表和插入 `account_transactions` 记录）都必须封装在**数据库事务**
中，确保这些操作的原子性。这意味着这些操作要么全部成功提交，要么全部失败回滚。

### 4.2. 乐观锁 (Optimistic Locking)

通过在 `user_accounts` 表中增加 `version` 字段实现乐观锁。每次对账户余额进行更新时，都会在 `WHERE`
子句中包含读取时的 `version` 值，并且将 `version` 字段加一。如果更新失败（影响行数为0），则表示数据已被其他并发事务修改，当前操作需要重试。此策略在读多写少的场景下，能有效提高并发性能。

### 4.3. 数据校验与审计

* **金额校验：** 确保交易金额的有效性，例如提现金额不能超过当前可用余额。
* **数据冗余校验：** 定期或实时校验 `user_accounts.current_account_balance`
  是否严格等于 `user_accounts.commission_a_balance + user_accounts.commission_b_balance - user_accounts.total_withdrawn_amount`
  ，并与 `account_transactions` 中所有相关交易记录的总和进行核对，确保数据一致性。
* **幂等性处理：** 所有对账户余额进行操作的API接口和业务逻辑都应具备**幂等性**
  。通过引入唯一请求ID或业务流水号，在处理前检查该ID是否已处理过，防止重复提交导致重复加款或扣款。
* **详细日志记录：** 记录所有系统操作日志，包括操作发起者、时间、操作类型、结果以及任何异常信息，便于问题排查和安全审计。

### 4.4. 异常处理与回滚机制

* 提供**明确的错误码和异常信息**，以便快速定位问题。
* 建立完善的**回滚策略**。当交易失败时，能够安全地回滚到交易前的状态。
* 设置**报警机制**，对关键交易失败或数据异常进行即时告警，并支持人工介入处理。

### 4.5. 权限控制

实施严格的**权限控制**，确保只有经过授权的用户和服务才能对账户余额数据进行读写操作。

### 4.6. 数据备份与恢复

制定并执行**定期数据库备份**策略，并建立完善的数据恢复方案，以应对极端情况下的数据丢失或损坏。

---

## 5. 总结

本设计方案通过清晰的表结构、严格的事务管理、**引入乐观锁机制**和多层数据安全策略，旨在构建一个**安全、可追溯且灵活**
的余额系统。它能够准确地反映 `账户总余额 = 佣金A余额 + 佣金B余额 - 累计提现总额` 的业务逻辑，并为未来的业务扩展奠定基础。

在实际开发中，还需要考虑服务层的业务逻辑封装、缓存策略、消息队列（用于异步处理和解耦），以及全面的监控报警体系，以确保系统的稳定运行。