// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
)

func newJyhMaterialTag(db *gorm.DB, opts ...gen.DOOption) jyhMaterialTag {
	_jyhMaterialTag := jyhMaterialTag{}

	_jyhMaterialTag.jyhMaterialTagDo.UseDB(db, opts...)
	_jyhMaterialTag.jyhMaterialTagDo.UseModel(&jyhapp.JyhMaterialTag{})

	tableName := _jyhMaterialTag.jyhMaterialTagDo.TableName()
	_jyhMaterialTag.ALL = field.NewAsterisk(tableName)
	_jyhMaterialTag.MaterialID = field.NewUint(tableName, "material_id")
	_jyhMaterialTag.TagID = field.NewUint(tableName, "tag_id")

	_jyhMaterialTag.fillFieldMap()

	return _jyhMaterialTag
}

type jyhMaterialTag struct {
	jyhMaterialTagDo

	ALL        field.Asterisk
	MaterialID field.Uint
	TagID      field.Uint

	fieldMap map[string]field.Expr
}

func (j jyhMaterialTag) Table(newTableName string) *jyhMaterialTag {
	j.jyhMaterialTagDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j jyhMaterialTag) As(alias string) *jyhMaterialTag {
	j.jyhMaterialTagDo.DO = *(j.jyhMaterialTagDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *jyhMaterialTag) updateTableName(table string) *jyhMaterialTag {
	j.ALL = field.NewAsterisk(table)
	j.MaterialID = field.NewUint(table, "material_id")
	j.TagID = field.NewUint(table, "tag_id")

	j.fillFieldMap()

	return j
}

func (j *jyhMaterialTag) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *jyhMaterialTag) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 2)
	j.fieldMap["material_id"] = j.MaterialID
	j.fieldMap["tag_id"] = j.TagID
}

func (j jyhMaterialTag) clone(db *gorm.DB) jyhMaterialTag {
	j.jyhMaterialTagDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j jyhMaterialTag) replaceDB(db *gorm.DB) jyhMaterialTag {
	j.jyhMaterialTagDo.ReplaceDB(db)
	return j
}

type jyhMaterialTagDo struct{ gen.DO }

func (j jyhMaterialTagDo) Debug() *jyhMaterialTagDo {
	return j.withDO(j.DO.Debug())
}

func (j jyhMaterialTagDo) WithContext(ctx context.Context) *jyhMaterialTagDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jyhMaterialTagDo) ReadDB() *jyhMaterialTagDo {
	return j.Clauses(dbresolver.Read)
}

func (j jyhMaterialTagDo) WriteDB() *jyhMaterialTagDo {
	return j.Clauses(dbresolver.Write)
}

func (j jyhMaterialTagDo) Session(config *gorm.Session) *jyhMaterialTagDo {
	return j.withDO(j.DO.Session(config))
}

func (j jyhMaterialTagDo) Clauses(conds ...clause.Expression) *jyhMaterialTagDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jyhMaterialTagDo) Returning(value interface{}, columns ...string) *jyhMaterialTagDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jyhMaterialTagDo) Not(conds ...gen.Condition) *jyhMaterialTagDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jyhMaterialTagDo) Or(conds ...gen.Condition) *jyhMaterialTagDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jyhMaterialTagDo) Select(conds ...field.Expr) *jyhMaterialTagDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jyhMaterialTagDo) Where(conds ...gen.Condition) *jyhMaterialTagDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jyhMaterialTagDo) Order(conds ...field.Expr) *jyhMaterialTagDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jyhMaterialTagDo) Distinct(cols ...field.Expr) *jyhMaterialTagDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jyhMaterialTagDo) Omit(cols ...field.Expr) *jyhMaterialTagDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jyhMaterialTagDo) Join(table schema.Tabler, on ...field.Expr) *jyhMaterialTagDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jyhMaterialTagDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jyhMaterialTagDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jyhMaterialTagDo) RightJoin(table schema.Tabler, on ...field.Expr) *jyhMaterialTagDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jyhMaterialTagDo) Group(cols ...field.Expr) *jyhMaterialTagDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jyhMaterialTagDo) Having(conds ...gen.Condition) *jyhMaterialTagDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jyhMaterialTagDo) Limit(limit int) *jyhMaterialTagDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jyhMaterialTagDo) Offset(offset int) *jyhMaterialTagDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jyhMaterialTagDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jyhMaterialTagDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jyhMaterialTagDo) Unscoped() *jyhMaterialTagDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jyhMaterialTagDo) Create(values ...*jyhapp.JyhMaterialTag) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jyhMaterialTagDo) CreateInBatches(values []*jyhapp.JyhMaterialTag, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jyhMaterialTagDo) Save(values ...*jyhapp.JyhMaterialTag) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jyhMaterialTagDo) First() (*jyhapp.JyhMaterialTag, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterialTag), nil
	}
}

func (j jyhMaterialTagDo) Take() (*jyhapp.JyhMaterialTag, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterialTag), nil
	}
}

func (j jyhMaterialTagDo) Last() (*jyhapp.JyhMaterialTag, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterialTag), nil
	}
}

func (j jyhMaterialTagDo) Find() ([]*jyhapp.JyhMaterialTag, error) {
	result, err := j.DO.Find()
	return result.([]*jyhapp.JyhMaterialTag), err
}

func (j jyhMaterialTagDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*jyhapp.JyhMaterialTag, err error) {
	buf := make([]*jyhapp.JyhMaterialTag, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jyhMaterialTagDo) FindInBatches(result *[]*jyhapp.JyhMaterialTag, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jyhMaterialTagDo) Attrs(attrs ...field.AssignExpr) *jyhMaterialTagDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jyhMaterialTagDo) Assign(attrs ...field.AssignExpr) *jyhMaterialTagDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jyhMaterialTagDo) Joins(fields ...field.RelationField) *jyhMaterialTagDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jyhMaterialTagDo) Preload(fields ...field.RelationField) *jyhMaterialTagDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jyhMaterialTagDo) FirstOrInit() (*jyhapp.JyhMaterialTag, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterialTag), nil
	}
}

func (j jyhMaterialTagDo) FirstOrCreate() (*jyhapp.JyhMaterialTag, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterialTag), nil
	}
}

func (j jyhMaterialTagDo) FindByPage(offset int, limit int) (result []*jyhapp.JyhMaterialTag, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jyhMaterialTagDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jyhMaterialTagDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jyhMaterialTagDo) Delete(models ...*jyhapp.JyhMaterialTag) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jyhMaterialTagDo) withDO(do gen.Dao) *jyhMaterialTagDo {
	j.DO = *do.(*gen.DO)
	return j
}
