// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
)

func newJyhUserLiveRoom(db *gorm.DB, opts ...gen.DOOption) jyhUserLiveRoom {
	_jyhUserLiveRoom := jyhUserLiveRoom{}

	_jyhUserLiveRoom.jyhUserLiveRoomDo.UseDB(db, opts...)
	_jyhUserLiveRoom.jyhUserLiveRoomDo.UseModel(&jyhapp.JyhUserLiveRoom{})

	tableName := _jyhUserLiveRoom.jyhUserLiveRoomDo.TableName()
	_jyhUserLiveRoom.ALL = field.NewAsterisk(tableName)
	_jyhUserLiveRoom.ID = field.NewUint(tableName, "id")
	_jyhUserLiveRoom.CreatedAt = field.NewTime(tableName, "created_at")
	_jyhUserLiveRoom.UpdatedAt = field.NewTime(tableName, "updated_at")
	_jyhUserLiveRoom.DeletedAt = field.NewField(tableName, "deleted_at")
	_jyhUserLiveRoom.UserID = field.NewUint(tableName, "user_id")
	_jyhUserLiveRoom.ChannelId = field.NewInt64(tableName, "channel_id")
	_jyhUserLiveRoom.ChannelName = field.NewString(tableName, "channel_name")
	_jyhUserLiveRoom.PolyvUserId = field.NewString(tableName, "polyv_user_id")
	_jyhUserLiveRoom.Scene = field.NewString(tableName, "scene")
	_jyhUserLiveRoom.ChannelPasswd = field.NewString(tableName, "channel_passwd")
	_jyhUserLiveRoom.SeminarHostPassword = field.NewString(tableName, "seminar_host_password")
	_jyhUserLiveRoom.SeminarAttendeePassword = field.NewString(tableName, "seminar_attendee_password")
	_jyhUserLiveRoom.Status = field.NewString(tableName, "status")
	_jyhUserLiveRoom.LastUsedAt = field.NewTime(tableName, "last_used_at")
	_jyhUserLiveRoom.User = jyhUserLiveRoomBelongsToUser{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("User", "jyhapp.JyhUser"),
		Inviter: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("User.Inviter", "jyhapp.JyhUser"),
		},
		JyhUserExt: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("User.JyhUserExt", "jyhapp.JyhUserExt"),
		},
		Invitees: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("User.Invitees", "jyhapp.JyhUser"),
		},
		Tags: struct {
			field.RelationField
			Users struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("User.Tags", "jyhapp.JyhUserTag"),
			Users: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("User.Tags.Users", "jyhapp.JyhUser"),
			},
		},
	}

	_jyhUserLiveRoom.fillFieldMap()

	return _jyhUserLiveRoom
}

type jyhUserLiveRoom struct {
	jyhUserLiveRoomDo

	ALL                     field.Asterisk
	ID                      field.Uint
	CreatedAt               field.Time
	UpdatedAt               field.Time
	DeletedAt               field.Field
	UserID                  field.Uint
	ChannelId               field.Int64
	ChannelName             field.String
	PolyvUserId             field.String
	Scene                   field.String
	ChannelPasswd           field.String
	SeminarHostPassword     field.String
	SeminarAttendeePassword field.String
	Status                  field.String
	LastUsedAt              field.Time
	User                    jyhUserLiveRoomBelongsToUser

	fieldMap map[string]field.Expr
}

func (j jyhUserLiveRoom) Table(newTableName string) *jyhUserLiveRoom {
	j.jyhUserLiveRoomDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j jyhUserLiveRoom) As(alias string) *jyhUserLiveRoom {
	j.jyhUserLiveRoomDo.DO = *(j.jyhUserLiveRoomDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *jyhUserLiveRoom) updateTableName(table string) *jyhUserLiveRoom {
	j.ALL = field.NewAsterisk(table)
	j.ID = field.NewUint(table, "id")
	j.CreatedAt = field.NewTime(table, "created_at")
	j.UpdatedAt = field.NewTime(table, "updated_at")
	j.DeletedAt = field.NewField(table, "deleted_at")
	j.UserID = field.NewUint(table, "user_id")
	j.ChannelId = field.NewInt64(table, "channel_id")
	j.ChannelName = field.NewString(table, "channel_name")
	j.PolyvUserId = field.NewString(table, "polyv_user_id")
	j.Scene = field.NewString(table, "scene")
	j.ChannelPasswd = field.NewString(table, "channel_passwd")
	j.SeminarHostPassword = field.NewString(table, "seminar_host_password")
	j.SeminarAttendeePassword = field.NewString(table, "seminar_attendee_password")
	j.Status = field.NewString(table, "status")
	j.LastUsedAt = field.NewTime(table, "last_used_at")

	j.fillFieldMap()

	return j
}

func (j *jyhUserLiveRoom) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *jyhUserLiveRoom) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 15)
	j.fieldMap["id"] = j.ID
	j.fieldMap["created_at"] = j.CreatedAt
	j.fieldMap["updated_at"] = j.UpdatedAt
	j.fieldMap["deleted_at"] = j.DeletedAt
	j.fieldMap["user_id"] = j.UserID
	j.fieldMap["channel_id"] = j.ChannelId
	j.fieldMap["channel_name"] = j.ChannelName
	j.fieldMap["polyv_user_id"] = j.PolyvUserId
	j.fieldMap["scene"] = j.Scene
	j.fieldMap["channel_passwd"] = j.ChannelPasswd
	j.fieldMap["seminar_host_password"] = j.SeminarHostPassword
	j.fieldMap["seminar_attendee_password"] = j.SeminarAttendeePassword
	j.fieldMap["status"] = j.Status
	j.fieldMap["last_used_at"] = j.LastUsedAt

}

func (j jyhUserLiveRoom) clone(db *gorm.DB) jyhUserLiveRoom {
	j.jyhUserLiveRoomDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j jyhUserLiveRoom) replaceDB(db *gorm.DB) jyhUserLiveRoom {
	j.jyhUserLiveRoomDo.ReplaceDB(db)
	return j
}

type jyhUserLiveRoomBelongsToUser struct {
	db *gorm.DB

	field.RelationField

	Inviter struct {
		field.RelationField
	}
	JyhUserExt struct {
		field.RelationField
	}
	Invitees struct {
		field.RelationField
	}
	Tags struct {
		field.RelationField
		Users struct {
			field.RelationField
		}
	}
}

func (a jyhUserLiveRoomBelongsToUser) Where(conds ...field.Expr) *jyhUserLiveRoomBelongsToUser {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a jyhUserLiveRoomBelongsToUser) WithContext(ctx context.Context) *jyhUserLiveRoomBelongsToUser {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a jyhUserLiveRoomBelongsToUser) Session(session *gorm.Session) *jyhUserLiveRoomBelongsToUser {
	a.db = a.db.Session(session)
	return &a
}

func (a jyhUserLiveRoomBelongsToUser) Model(m *jyhapp.JyhUserLiveRoom) *jyhUserLiveRoomBelongsToUserTx {
	return &jyhUserLiveRoomBelongsToUserTx{a.db.Model(m).Association(a.Name())}
}

type jyhUserLiveRoomBelongsToUserTx struct{ tx *gorm.Association }

func (a jyhUserLiveRoomBelongsToUserTx) Find() (result *jyhapp.JyhUser, err error) {
	return result, a.tx.Find(&result)
}

func (a jyhUserLiveRoomBelongsToUserTx) Append(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a jyhUserLiveRoomBelongsToUserTx) Replace(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a jyhUserLiveRoomBelongsToUserTx) Delete(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a jyhUserLiveRoomBelongsToUserTx) Clear() error {
	return a.tx.Clear()
}

func (a jyhUserLiveRoomBelongsToUserTx) Count() int64 {
	return a.tx.Count()
}

type jyhUserLiveRoomDo struct{ gen.DO }

func (j jyhUserLiveRoomDo) Debug() *jyhUserLiveRoomDo {
	return j.withDO(j.DO.Debug())
}

func (j jyhUserLiveRoomDo) WithContext(ctx context.Context) *jyhUserLiveRoomDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jyhUserLiveRoomDo) ReadDB() *jyhUserLiveRoomDo {
	return j.Clauses(dbresolver.Read)
}

func (j jyhUserLiveRoomDo) WriteDB() *jyhUserLiveRoomDo {
	return j.Clauses(dbresolver.Write)
}

func (j jyhUserLiveRoomDo) Session(config *gorm.Session) *jyhUserLiveRoomDo {
	return j.withDO(j.DO.Session(config))
}

func (j jyhUserLiveRoomDo) Clauses(conds ...clause.Expression) *jyhUserLiveRoomDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jyhUserLiveRoomDo) Returning(value interface{}, columns ...string) *jyhUserLiveRoomDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jyhUserLiveRoomDo) Not(conds ...gen.Condition) *jyhUserLiveRoomDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jyhUserLiveRoomDo) Or(conds ...gen.Condition) *jyhUserLiveRoomDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jyhUserLiveRoomDo) Select(conds ...field.Expr) *jyhUserLiveRoomDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jyhUserLiveRoomDo) Where(conds ...gen.Condition) *jyhUserLiveRoomDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jyhUserLiveRoomDo) Order(conds ...field.Expr) *jyhUserLiveRoomDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jyhUserLiveRoomDo) Distinct(cols ...field.Expr) *jyhUserLiveRoomDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jyhUserLiveRoomDo) Omit(cols ...field.Expr) *jyhUserLiveRoomDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jyhUserLiveRoomDo) Join(table schema.Tabler, on ...field.Expr) *jyhUserLiveRoomDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jyhUserLiveRoomDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jyhUserLiveRoomDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jyhUserLiveRoomDo) RightJoin(table schema.Tabler, on ...field.Expr) *jyhUserLiveRoomDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jyhUserLiveRoomDo) Group(cols ...field.Expr) *jyhUserLiveRoomDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jyhUserLiveRoomDo) Having(conds ...gen.Condition) *jyhUserLiveRoomDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jyhUserLiveRoomDo) Limit(limit int) *jyhUserLiveRoomDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jyhUserLiveRoomDo) Offset(offset int) *jyhUserLiveRoomDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jyhUserLiveRoomDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jyhUserLiveRoomDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jyhUserLiveRoomDo) Unscoped() *jyhUserLiveRoomDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jyhUserLiveRoomDo) Create(values ...*jyhapp.JyhUserLiveRoom) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jyhUserLiveRoomDo) CreateInBatches(values []*jyhapp.JyhUserLiveRoom, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jyhUserLiveRoomDo) Save(values ...*jyhapp.JyhUserLiveRoom) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jyhUserLiveRoomDo) First() (*jyhapp.JyhUserLiveRoom, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserLiveRoom), nil
	}
}

func (j jyhUserLiveRoomDo) Take() (*jyhapp.JyhUserLiveRoom, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserLiveRoom), nil
	}
}

func (j jyhUserLiveRoomDo) Last() (*jyhapp.JyhUserLiveRoom, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserLiveRoom), nil
	}
}

func (j jyhUserLiveRoomDo) Find() ([]*jyhapp.JyhUserLiveRoom, error) {
	result, err := j.DO.Find()
	return result.([]*jyhapp.JyhUserLiveRoom), err
}

func (j jyhUserLiveRoomDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*jyhapp.JyhUserLiveRoom, err error) {
	buf := make([]*jyhapp.JyhUserLiveRoom, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jyhUserLiveRoomDo) FindInBatches(result *[]*jyhapp.JyhUserLiveRoom, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jyhUserLiveRoomDo) Attrs(attrs ...field.AssignExpr) *jyhUserLiveRoomDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jyhUserLiveRoomDo) Assign(attrs ...field.AssignExpr) *jyhUserLiveRoomDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jyhUserLiveRoomDo) Joins(fields ...field.RelationField) *jyhUserLiveRoomDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jyhUserLiveRoomDo) Preload(fields ...field.RelationField) *jyhUserLiveRoomDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jyhUserLiveRoomDo) FirstOrInit() (*jyhapp.JyhUserLiveRoom, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserLiveRoom), nil
	}
}

func (j jyhUserLiveRoomDo) FirstOrCreate() (*jyhapp.JyhUserLiveRoom, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserLiveRoom), nil
	}
}

func (j jyhUserLiveRoomDo) FindByPage(offset int, limit int) (result []*jyhapp.JyhUserLiveRoom, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jyhUserLiveRoomDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jyhUserLiveRoomDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jyhUserLiveRoomDo) Delete(models ...*jyhapp.JyhUserLiveRoom) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jyhUserLiveRoomDo) withDO(do gen.Dao) *jyhUserLiveRoomDo {
	j.DO = *do.(*gen.DO)
	return j
}
