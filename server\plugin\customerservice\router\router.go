package router

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/customerservice/api"
	serMiddleware "github.com/flipped-aurora/gin-vue-admin/server/plugin/customerservice/middleware"
	"github.com/gin-gonic/gin"
)

type CustomerServiceRouter struct {
}

func (s *CustomerServiceRouter) InitCustomerServiceRouter(Router *gin.RouterGroup) {
	wsRouter := Router.Group("")
	plugServiceRouter := Router.Group("").Use(serMiddleware.JWTAuthMiddleware()).Use(middleware.Cors())
	//plugRouter := Router.Group("").Use(middleware.JWTAuth())
	privateRouter := Router.Group("").Use(middleware.JWTAuth()).Use(middleware.CasbinHandler())
	customerServiceApi := api.ApiGroupApp.CustomerServiceApi
	plugAdminApi := api.ApiGroupApp.AdminServiceApi
	// 初始化各个API实例
	tagApi := &api.TagApi{}
	transferApi := &api.TransferApi{}

	// WebSocket路由 - 无权限验证
	{
		wsRouter.GET("/ws", customerServiceApi.ServeWs)
		wsRouter.GET("/ws/kefu", customerServiceApi.ServeWsForKefu)
	}
	wsRouter.GET("/service/serve_ws", customerServiceApi.ServeWsForKefu)
	wsRouter.GET("/service/ws", customerServiceApi.ServeWs)
	wsRouter.POST("/service/account_login", plugAdminApi.AccountServiceLogin)
	// 客服相关路由 - 需要JWT验证
	{
		plugServiceRouter.POST("/service/send_msg", customerServiceApi.SendMsg)
		plugServiceRouter.GET("/service/get_msg_list", customerServiceApi.GetMsgList)
		plugServiceRouter.GET("/service/get_kf_info", customerServiceApi.GetKefuInfo)
		plugServiceRouter.POST("/service/upload_file", customerServiceApi.UploadFile)
		plugServiceRouter.GET("/service/get_user_info", customerServiceApi.GetUserInfo)
		plugServiceRouter.GET("/service/get_msg_user", customerServiceApi.GetMsgUser)
		plugServiceRouter.GET("/service/get_service_script", customerServiceApi.GetServiceScript)
		plugServiceRouter.GET("/service/set_msg_view", customerServiceApi.SetMsgView)
	}
	// 管理员路由 - 需要JWT验证
	{
		privateRouter.GET("/service/get_service_list", plugAdminApi.GetServiceList)
		privateRouter.POST("/service/save_service", plugAdminApi.SaveService)
		privateRouter.DELETE("/service/delete_service", plugAdminApi.DeleteService)
		privateRouter.GET("/service/find_service", plugAdminApi.FindService)
		privateRouter.GET("/service/admin_login", plugAdminApi.AdminServiceLogin)
		privateRouter.GET("/service/get_script_list", plugAdminApi.GetScriptList)
		privateRouter.POST("/service/save_script", plugAdminApi.SaveScript)
		privateRouter.DELETE("/service/delete_script", plugAdminApi.DeleteScript)
		privateRouter.GET("/service/find_script", plugAdminApi.FindScript)
		privateRouter.GET("/service/auto_reply_list", plugAdminApi.AutoReplyList)
		privateRouter.POST("/service/save_reply", plugAdminApi.SaveReply)
		privateRouter.DELETE("/service/delete_reply", plugAdminApi.DeleteReply)
		privateRouter.GET("/service/find_reply", plugAdminApi.FindReply)
	}
	// 管理员路由 - 需要JWT和权限验证
	{
		// 分发规则管理
		privateRouter.POST("/services/distribute_rules", tagApi.CreateDistributeRule)
		privateRouter.PUT("/services/distribute_rules", tagApi.UpdateDistributeRule)
		privateRouter.DELETE("/distribute_rules/:id", tagApi.DeleteDistributeRule)
		privateRouter.GET("/services/distribute_rules", tagApi.GetDistributeRules)
		privateRouter.GET("/distribute_rules/:id", tagApi.GetDistributeRuleDetail)
		privateRouter.GET("/tags/:tag_id/rules", tagApi.GetRulesByTag)

		// 标签管理
		privateRouter.POST("/services/tags", tagApi.BindServiceTags)
		privateRouter.GET("/services/:service_id/tags", tagApi.GetServiceTags)
		privateRouter.GET("/tags", tagApi.GetAvailableTags)
		privateRouter.GET("/tags/:tag_id/services", tagApi.GetServicesByTag)

		// 辅助查询接口
		privateRouter.GET("/services/available", tagApi.GetAvailableServices)

		// 转接管理
		plugServiceRouter.POST("/transfers/request", transferApi.RequestTransfer)                // 发起转接申请
		plugServiceRouter.POST("/transfers/accept", transferApi.AcceptTransfer)                  // 接受转接
		plugServiceRouter.POST("/transfers/reject", transferApi.RejectTransfer)                  // 拒绝转接
		plugServiceRouter.POST("/transfers/cancel", transferApi.CancelTransfer)                  // 取消转接
		plugServiceRouter.POST("/transfers/batch", transferApi.BatchTransfer)                    // 批量转接
		plugServiceRouter.GET("/transfers", transferApi.GetTransferList)                         // 获取转接列表
		plugServiceRouter.GET("/transfers/:transfer_id", transferApi.GetTransferDetail)          // 获取转接详情
		plugServiceRouter.GET("/transfers/pending", transferApi.GetPendingTransfers)             // 获取待处理转接
		plugServiceRouter.GET("/transfers/status/:status", transferApi.GetTransfersByStatus)     // 按状态获取转接
		plugServiceRouter.GET("/transfers/history", transferApi.GetTransferHistory)              // 获取转接历史
		plugServiceRouter.GET("/transfers/statistics", transferApi.GetTransferStatistics)        // 获取转接统计
		plugServiceRouter.GET("/transfers/metrics", transferApi.GetTransferMetrics)              // 获取转接指标
		plugServiceRouter.GET("/transfers/services/available", transferApi.GetAvailableServices) // 获取可转接客服
		plugServiceRouter.POST("/transfers/cleanup", transferApi.CleanupExpiredTransfers)        // 清理过期转接（管理员）
	}
}
