package model

// SysServiceQueue 排队表
type SysServiceQueue struct {
	ID          uint   `gorm:"primarykey" json:"id"`
	UserID      uint   `json:"user_id" gorm:"comment:用户ID"`
	ServiceID   int64  `json:"service_id" gorm:"comment:分配的客服ID"`
	TicketID    uint   `json:"ticket_id" gorm:"comment:关联工单ID"`
	UserTagIDs  string `json:"user_tag_ids" gorm:"comment:用户标签，JSON格式"`
	QueueNo     int64  `json:"queue_no" gorm:"comment:排队号"`
	Status      uint   `json:"status" gorm:"comment:状态(1:排队中 2:服务中 3:已完成);default:1"`
	Priority    int    `json:"priority" gorm:"comment:优先级;default:3"`
	JoinTime    int64  `json:"join_time" gorm:"comment:入队时间"`
	StartTime   int64  `json:"start_time" gorm:"comment:开始服务时间"`
	EndTime     int64  `json:"end_time" gorm:"comment:结束服务时间"`
	WaitTime    int64  `json:"wait_time" gorm:"comment:等待时间(秒)"`
	ServiceTime int64  `json:"service_time" gorm:"comment:服务时长(秒)"`
}

func (SysServiceQueue) TableName() string {
	return "sys_service_queue"
}

// 排队状态常量
const (
	QueueStatusWaiting  = 1 // 排队中
	QueueStatusServing  = 2 // 服务中
	QueueStatusFinished = 3 // 已完成
)

// 优先级常量
const (
	PriorityHigh   = 1 // 高优先级
	PriorityMedium = 2 // 中优先级
	PriorityLow    = 3 // 低优先级
)
