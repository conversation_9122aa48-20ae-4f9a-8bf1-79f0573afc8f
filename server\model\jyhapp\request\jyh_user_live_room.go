package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
)

// JyhUserLiveRoomSearch 用户直播房间搜索条件
type JyhUserLiveRoomSearch struct {
	request.PageInfo
	UserID      *uint  `json:"user_id" form:"user_id"`           // 用户ID
	ChannelName string `json:"channel_name" form:"channel_name"` // 直播间名称
	Status      string `json:"status" form:"status"`             // 状态
	StartTime   string `json:"start_time" form:"start_time"`     // 开始时间
	EndTime     string `json:"end_time" form:"end_time"`         // 结束时间
}

// CreateJyhUserLiveRoomReq 创建用户直播房间请求
type CreateJyhUserLiveRoomReq struct {
	ChannelName string `json:"channel_name" binding:"required"` // 直播间名称，必填
	NewScene    string `json:"new_scene" default:"topclass"`    // 直播场景
	Template    string `json:"template" default:"topclass"`     // 直播模板
	Password    string `json:"password"`                        // 直播密码
}

// GetUserAvailableRoomReq 获取用户可用房间请求
type GetUserAvailableRoomReq struct {
	UserID uint `json:"user_id" form:"user_id"` // 用户ID，如果为空则从token中获取
}
