package jyhapp

import (
	v1 "github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type UserRouter struct{}

func (r *UserRouter) InitUserRouter(publicGroup, JyhappGroup *gin.RouterGroup) {
	noRecord := JyhappGroup.Group("jyh/user")
	pbuNoRecord := publicGroup.Group("jyh/user")
	pbuRecord := publicGroup.Group("jyh/user").Use(middleware.OperationRecord())
	//priuRecord := JyhappGroup.Group("client/user").Use(middleware.OperationRecord())
	userBenefitApi := v1.ApiGroupApp.JyhApiGroup.JyhUserBenefitApi
	userApi := v1.ApiGroupApp.JyhApiGroup.UserApi
	{
		pbuRecord.POST("register", userApi.Register) // 用户注册
		pbuRecord.POST("login", userApi.Login)       // 用户登录
	}
	{
		pbuNoRecord.POST("send_code", userApi.SendVerificationCode) // 发送验证码
	}
	{
		use := noRecord.Use(middleware.OperationRecord())
		use.POST("user_levels", userBenefitApi.CreateUserLevel)   // 用户开通等级（创建记录并快照权益）
		use.POST("update_info", userApi.UpdateUserInfo)           // 更新用户信息
		use.POST("upload_certificate", userApi.UploadCertificate) // 更新用户信息
	}
	{
		noRecord.GET("levels", userBenefitApi.GetUserShipLevelList) // 获取会员等级列表
		noRecord.GET("info", userApi.GetUserInfo)                   // 获取用户信息
	}
}
