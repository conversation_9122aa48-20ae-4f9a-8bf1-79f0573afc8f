package request

type MaterialSync struct {
	MaterialId       uint           `json:"materialId"`                  // 素材ID 如果有值就是更新
	Name             string         `json:"name"`                        // 素材名称
	Topic            string         `json:"topic"`                       // 话题
	Category         string         `json:"category" binding:"required"` // 分类名称
	Copywriting      string         `json:"copywriting"`                 // 文案
	DouyinProductUrl string         `json:"douyinProductUrl"`            // 抖音商品链接
	MusicUrl         string         `json:"musicUrl"`                    // 背景音乐链接
	Description      string         `json:"description"`                 // 描述
	FileUrl          string         `json:"fileUrl"`                     // 素材首图URL
	Status           uint           `json:"status"`                      // 状态
	Tags             []string       `json:"tags"`                        // 标签列表
	Files            []MaterialFile `json:"files"`                       // 素材文件列表
}
