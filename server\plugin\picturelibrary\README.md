## GVA 图库功能

### 手动安装方法

    1.解压zip获得picturelibrary文件夹
    2.将 picturelibrary/web/plugin/picturelibrary 放置在web/plugin下
    3.将 picturelibrary/server/plugin/picturelibrary 放置在server/plugin下

#### 执行如下注册方法

### 注册（手动自动都需要）

#### 1. 前往GVA主程序下的initialize/router.go 在Routers 方法最末尾按照你需要的及安全模式添加本插件
    PluginInit(PrivateGroup, picturelibrary.CreatePictureLibraryPlug())
    到gva系统，角色管理，分配角色的api权限即可，插件会自动注册api，需要手动分配。
    注：会生成一个表：sys_attachment_category，表exa_file_upload_and_downloads会新增一个cat_id字段
### 2. 配置说明

#### 2-1 全局配置结构体说明

    无配置

#### 2-2 使用说明

    在你需要图片选择的前端页面引入图库组件，如下：
      <div class="selected-images">
        <div class="selected-image" v-for="image in selectedImages" :key="image">
          <el-image v-if="fileTypeList.includes(image.tag) === true" :src="image.url" style="width: 100%; height: 100%; object-fit: cover;margin-right: 10px;"></el-image>
          <video v-else controls style="width: 100%; height: 100%;">
            <source :src="image.url" />
          </video>
          <span class="remove-icon" @click="removeSelectedImage(image)"><el-icon><circle-close></circle-close></el-icon></span>
        </div>
        <el-icon  v-if="isMultiple || selectedImages.length === 0" class="avatar-uploader-icon" @click="openImageLibrary"><Plus /></el-icon>
      </div>

    图库弹窗：
      <el-dialog v-model="isDialogVisible" title="图片库" width="950px" destroy-on-close>
         <ImageLibrary @select="handleImageSelect" :multiple="isMultiple"/>
      </el-dialog>
    
    js代码：
        import {CircleClose, Plus} from '@element-plus/icons-vue'
        import ImageLibrary from "@/plugin/picturelibrary/view/components/imageLibrary.vue";
        
        const isDialogVisible = ref(false)
        const isMultiple = ref(false) // 设置是否允许多选
        const selectedImages = ref([])
        
        const openImageLibrary = () => {
            isDialogVisible.value = true
        }
        const fileTypeList = ['png', 'jpg', 'jpeg', 'gif']
        const handleImageSelect = (images) => {
        if (isMultiple.value) {
            selectedImages.value = [...selectedImages.value, ...images]
        } else {
            selectedImages.value = Array.isArray(images) ? images : [images]
        }

        // 此处是测试项目里上传头像的参数，根据实际情况进行修改
        formData.value.avatar = selectedImages.value[0]
            isDialogVisible.value = false
        }
        
        const removeSelectedImage = (image) => {
            const index = selectedImages.value.indexOf(image)
            if (index !== -1) {
                selectedImages.value.splice(index, 1)
            }
        }
    
    style代码：
        .selected-images {
            position: relative;
            display: flex;
            flex-wrap: wrap;
        }
        
        .selected-image {
            position: relative;
            margin-right: 10px;
            margin-bottom:10px;
            width: 100px;
            height: 100px;
        }
        
        .selected-image .remove-icon {
            position: absolute;
            top: 0; /* 微调位置 */
            right: 0; /* 微调位置 */
            color: black;
            padding: 5px;
            cursor: pointer;
            font-size: 22px;
            line-height: 22px;
            text-align: center;
        }
        
        .avatar-uploader-icon {
            font-size: 28px;
            color: #8c939d;
            width: 100px;
            height: 100px;
            text-align: center;
            border: 1px dashed #d4d9e1;
        }
    
#### 2-3 参数说明
    isMultiple 是控制能不能选择多张图的参数，false:只能选一张；true:可以选择多张
### 3. 方法API
    无

