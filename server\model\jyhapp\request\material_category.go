package request

import "github.com/flipped-aurora/gin-vue-admin/server/model/common/request"

// MaterialCategorySearch 素材分类查询参数
type MaterialCategorySearch struct {
	request.PageInfo
	CatName  string `json:"catName" form:"name"`      // 分类名称
	ParentID *uint  `json:"parentId" form:"parentId"` // 父级ID
	IsActive bool   `json:"status" form:"status"`     // 状态
}

// MaterialCategoryCreate 素材分类创建参数
type MaterialCategoryCreate struct {
	CatName     string `json:"catName" binding:"required"`  // 分类名称
	ParentID    uint   `json:"parentId"`                    // 父级ID
	Sort        int    `json:"sort"`                        // 排序
	IsActive    bool   `json:"isActive" binding:"required"` // 状态
	CatDesc     string `json:"catDesc"`                     // 描述
	DouyinUrl   string `json:"douyinUrl"`                   // 抖音链接
	Copywriting string `json:"copywriting"`                 // 文案
}

// MaterialCategoryUpdate 素材分类更新参数
type MaterialCategoryUpdate struct {
	ID          uint   `json:"id" binding:"required"`      // ID
	CatName     string `json:"catName" binding:"required"` // 分类名称
	ParentID    uint   `json:"parentId"`                   // 父级ID
	Sort        int    `json:"sort"`                       // 排序
	IsActive    bool   `json:"isActive"`                   // 状态
	CatDesc     string `json:"catDesc"`                    // 描述
	DouyinUrl   string `json:"douyinUrl"`                  // 抖音链接
	Copywriting string `json:"copywriting"`                // 文案
}

// MaterialCategoryParentId 素材分类父级ID参数
type MaterialCategoryParentId struct {
	ParentIds []uint `json:"parentIds" form:"parentIds" binding:"required"` // 父级ID
}
