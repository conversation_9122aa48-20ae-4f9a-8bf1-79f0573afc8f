package jyhapp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"gorm.io/datatypes"
	"time"
)

// JyhPointsRule 代表积分规则表
type JyhPointsRule struct {
	global.GVA_MODEL
	RuleKey     string `gorm:"type:varchar(50);uniqueIndex;not null" json:"ruleKey"`
	RuleName    string `gorm:"type:varchar(100);not null" json:"ruleName"`
	Points      int    `gorm:"not null" json:"points"`
	Description string `gorm:"type:varchar(255)" json:"description"`
	DailyLimit  int    `gorm:"default:0" json:"dailyLimit"`
	TotalLimit  int    `gorm:"default:0" json:"totalLimit"`
	IsEnabled   bool   `gorm:"default:true" json:"isEnabled"`
}

func (JyhPointsRule) TableName() string {
	return "jyh_points_rule"
}

// JyhPointsRecord 代表积分记录表
type JyhPointsRecord struct {
	ID        uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	UserID    uint      `gorm:"not null;index" json:"userID"`
	RuleID    uint      `gorm:"not null;index" json:"ruleID"`
	Points    int       `gorm:"not null" json:"points"`
	Source    string    `gorm:"type:varchar(50);not null" json:"source"`
	Remark    string    `gorm:"type:varchar(255)" json:"remark"`
	CreatedAt time.Time `gorm:"autoCreateTime" json:"createdAt"`

	// 关联关系
	User JyhUser       `gorm:"foreignKey:UserID" json:"user"`
	Rule JyhPointsRule `gorm:"foreignKey:RuleID" json:"rule"`
}

func (JyhPointsRecord) TableName() string {
	return "jyh_points_record"
}

type JyhPointsExchange struct {
	ID           uint           `gorm:"primaryKey;autoIncrement" json:"id"`
	UserID       uint           `gorm:"not null;index" json:"userID"`
	MaterialID   uint           `gorm:"not null;index" json:"materialID"`
	Quantity     int            `gorm:"default:1" json:"quantity"`
	PointsTotal  int            `gorm:"not null" json:"pointsTotal"`
	ExchangeCode string         `gorm:"type:varchar(50);uniqueIndex" json:"exchangeCode"`
	Status       string         `gorm:"type:enum('pending','completed','cancelled','expired');default:'pending'" json:"status"`
	ExchangeTime *time.Time     `gorm:"index" json:"exchangeTime"`
	CompletedAt  *time.Time     `gorm:"index" json:"completedAt"`
	Address      datatypes.JSON `gorm:"type:json" json:"address"` // 存储收货地址信息
	Remark       string         `gorm:"type:varchar(255)" json:"remark"`
	CreatedAt    time.Time      `gorm:"autoCreateTime" json:"createdAt"`
	UpdatedAt    time.Time      `gorm:"autoUpdateTime" json:"updatedAt"`

	// 关联关系
	User     JyhUser     `gorm:"foreignKey:UserID" json:"user"`
	Material JyhMaterial `gorm:"foreignKey:MaterialID" json:"material"`
}

func (JyhPointsExchange) TableName() string {
	return "jyh_points_exchange"
}
