package jyhapp

import (
	v1 "github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type JyhUserLiveRoomRouter struct{}

// InitJyhUserLiveRoomRouter 初始化 用户直播房间 路由信息
func (s *JyhUserLiveRoomRouter) InitJyhUserLiveRoomRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	jyhUserLiveRoomRouter := Router.Group("admin/live").Use(middleware.OperationRecord())
	jyhUserLiveRoomRouterWithoutRecord := Router.Group("admin/live")
	jyhUserLiveRoomApi := v1.ApiGroupApp.JyhApiGroup.JyhUserLiveRoomApi
	{
		jyhUserLiveRoomRouter.POST("create", jyhUserLiveRoomApi.CreateJyhUserLiveRoom)            // 创建用户直播房间
		jyhUserLiveRoomRouter.DELETE("delete/:id", jyhUserLiveRoomApi.DeleteJyhUserLiveRoom)      // 删除用户直播房间
		jyhUserLiveRoomRouter.PUT("updateStatus", jyhUserLiveRoomApi.UpdateJyhUserLiveRoomStatus) // 更新用户直播房间状态
	}
	{
		jyhUserLiveRoomRouterWithoutRecord.GET("detail/:id", jyhUserLiveRoomApi.FindJyhUserLiveRoom) // 获取用户直播房间详情
		jyhUserLiveRoomRouterWithoutRecord.GET("list", jyhUserLiveRoomApi.GetJyhUserLiveRoomList)    // 获取用户直播房间列表
		jyhUserLiveRoomRouterWithoutRecord.GET("available", jyhUserLiveRoomApi.GetUserAvailableRoom) // 获取用户可用的直播房间
	}
}
