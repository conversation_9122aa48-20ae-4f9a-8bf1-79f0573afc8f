-- 创建规则与客服关联表
CREATE TABLE IF NOT EXISTS `sys_service_rule_service_relation` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `rule_id` bigint(20) unsigned NOT NULL COMMENT '规则ID',
  `service_id` bigint(20) NOT NULL COMMENT '客服ID',
  `priority` int(11) NOT NULL DEFAULT '1' COMMENT '在该规则下的优先级',
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_rule_service_relation_rule_id` (`rule_id`),
  KEY `idx_rule_service_relation_service_id` (`service_id`),
  UNIQUE KEY `uk_rule_service` (`rule_id`, `service_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='规则与客服关联表';

-- 为现有的规则添加示例数据（可选）
-- INSERT INTO `sys_service_rule_service_relation` (`rule_id`, `service_id`, `priority`) VALUES
-- (1, 1, 1),  -- 规则1关联客服1，优先级1
-- (1, 2, 2),  -- 规则1关联客服2，优先级2
-- (2, 3, 1);  -- 规则2关联客服3，优先级1
