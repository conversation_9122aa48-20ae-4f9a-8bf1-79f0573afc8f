package jyhapp

import (
	"time"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// JyhUserAccount 用户账户表 - 管理用户余额信息（带乐观锁）
type JyhUserAccount struct {
	AccountID        uint            `json:"account_id" gorm:"primaryKey;autoIncrement;comment:用户账户唯一ID"`
	UserID           uint            `json:"user_id" gorm:"not null;uniqueIndex;comment:关联的用户ID"`
	DyBalance        decimal.Decimal `json:"dy_balance" gorm:"type:decimal(18,2);not null;default:0.00;comment:用户佣金A可用余额"`
	JyhBalance       decimal.Decimal `json:"jyh_balance" gorm:"type:decimal(18,2);not null;default:0.00;comment:用户佣金B可用余额"`
	WithdrawnBalance decimal.Decimal `json:"withdrawn_balance" gorm:"type:decimal(18,2);not null;default:0.00;comment:用户累计已成功提现总金额"`
	CurrentBalance   decimal.Decimal `json:"current_balance" gorm:"type:decimal(18,2);not null;default:0.00;comment:用户当前账户总余额(冗余字段)"`
	Version          int             `json:"version" gorm:"not null;default:0;comment:乐观锁版本号"`
	CreatedAt        time.Time       `json:"created_at" gorm:"comment:创建时间"`
	UpdatedAt        time.Time       `json:"updated_at" gorm:"comment:更新时间"`
	LastUpdatedAt    *time.Time      `json:"last_updated_at" gorm:"comment:最后更新时间"`

	// 关联关系
	User         *JyhUser                    `json:"user,omitempty" gorm:"foreignKey:UserID;references:ID"`
	Transactions []JyhUserAccountTransaction `json:"transactions,omitempty" gorm:"foreignKey:AccountID;references:AccountID"`
}

// TableName 指定表名
func (JyhUserAccount) TableName() string {
	return "jyh_user_accounts"
}

// BeforeUpdate GORM钩子 - 更新前自动递增版本号
func (ua *JyhUserAccount) BeforeUpdate(tx *gorm.DB) error {
	ua.Version++
	now := time.Now()
	ua.LastUpdatedAt = &now
	return nil
}

// BeforeCreate GORM钩子 - 创建前初始化
func (ua *JyhUserAccount) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	ua.LastUpdatedAt = &now
	if ua.Version == 0 {
		ua.Version = 1
	}
	return nil
}

// CalculateCurrentBalance 计算当前账户总余额
// current_balance = jyh_balance - withdrawn_balance (dy_balance 只做记录，不计入可用余额)
func (ua *JyhUserAccount) CalculateCurrentBalance() decimal.Decimal {
	return ua.JyhBalance.Sub(ua.WithdrawnBalance)
}

// UpdateCurrentBalance 更新当前账户总余额
func (ua *JyhUserAccount) UpdateCurrentBalance() {
	ua.CurrentBalance = ua.CalculateCurrentBalance()
}

// GetAvailableBalance 获取可用余额（可提现余额）
// 只有 jyh_balance 可以提现，dy_balance 只做记录
func (ua *JyhUserAccount) GetAvailableBalance() decimal.Decimal {
	return ua.JyhBalance
}

// CanWithdraw 检查是否可以提现指定金额
func (ua *JyhUserAccount) CanWithdraw(amount decimal.Decimal) bool {
	return ua.GetAvailableBalance().GreaterThanOrEqual(amount) && amount.GreaterThan(decimal.Zero)
}

// ValidateBalance 验证余额数据一致性
func (ua *JyhUserAccount) ValidateBalance() bool {
	calculated := ua.CalculateCurrentBalance()
	return ua.CurrentBalance.Equal(calculated)
}
