package request

import "github.com/flipped-aurora/gin-vue-admin/server/model/common/request"

// TagsSearch 标签查询参数
type TagsSearch struct {
	request.PageInfo
	Name string `json:"name" form:"name"` // 名称
	Type string `json:"type" form:"type"` // 类型
}

// TagsCreate 标签创建参数
type TagsCreate struct {
	Name string `json:"name" binding:"required"` // 分类名称
	Type string `json:"type" binding:"required"` // 类型
}

// TagsUpdate 标签更新参数
type TagsUpdate struct {
	ID   uint   `json:"id" binding:"required"`   // ID
	Name string `json:"name" binding:"required"` // 分类名称
	Type string `json:"type" binding:"required"` // 类型
}

// TagsFilterReq 标签查询
type TagsFilterReq struct {
	Name string `json:"name" form:"name"` // 名称
	Type string `json:"type" form:"type"` // 标签类型
}
