// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
)

func newJyhUserBenefitSnapshot(db *gorm.DB, opts ...gen.DOOption) jyhUserBenefitSnapshot {
	_jyhUserBenefitSnapshot := jyhUserBenefitSnapshot{}

	_jyhUserBenefitSnapshot.jyhUserBenefitSnapshotDo.UseDB(db, opts...)
	_jyhUserBenefitSnapshot.jyhUserBenefitSnapshotDo.UseModel(&jyhapp.JyhUserBenefitSnapshot{})

	tableName := _jyhUserBenefitSnapshot.jyhUserBenefitSnapshotDo.TableName()
	_jyhUserBenefitSnapshot.ALL = field.NewAsterisk(tableName)
	_jyhUserBenefitSnapshot.ID = field.NewUint(tableName, "id")
	_jyhUserBenefitSnapshot.CreatedAt = field.NewTime(tableName, "created_at")
	_jyhUserBenefitSnapshot.UpdatedAt = field.NewTime(tableName, "updated_at")
	_jyhUserBenefitSnapshot.UserID = field.NewUint(tableName, "user_id")
	_jyhUserBenefitSnapshot.UserLevelID = field.NewUint(tableName, "user_level_id")
	_jyhUserBenefitSnapshot.LevelID = field.NewUint(tableName, "level_id")
	_jyhUserBenefitSnapshot.BenefitID = field.NewUint(tableName, "benefit_id")
	_jyhUserBenefitSnapshot.Value = field.NewField(tableName, "value")
	_jyhUserBenefitSnapshot.Condition = field.NewField(tableName, "condition")
	_jyhUserBenefitSnapshot.StartAt = field.NewTime(tableName, "start_at")
	_jyhUserBenefitSnapshot.EndAt = field.NewTime(tableName, "end_at")
	_jyhUserBenefitSnapshot.BenefitKey = field.NewString(tableName, "benefit_key")
	_jyhUserBenefitSnapshot.BenefitName = field.NewString(tableName, "benefit_name")

	_jyhUserBenefitSnapshot.fillFieldMap()

	return _jyhUserBenefitSnapshot
}

type jyhUserBenefitSnapshot struct {
	jyhUserBenefitSnapshotDo

	ALL         field.Asterisk
	ID          field.Uint
	CreatedAt   field.Time
	UpdatedAt   field.Time
	UserID      field.Uint
	UserLevelID field.Uint
	LevelID     field.Uint
	BenefitID   field.Uint
	Value       field.Field
	Condition   field.Field
	StartAt     field.Time
	EndAt       field.Time
	BenefitKey  field.String
	BenefitName field.String

	fieldMap map[string]field.Expr
}

func (j jyhUserBenefitSnapshot) Table(newTableName string) *jyhUserBenefitSnapshot {
	j.jyhUserBenefitSnapshotDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j jyhUserBenefitSnapshot) As(alias string) *jyhUserBenefitSnapshot {
	j.jyhUserBenefitSnapshotDo.DO = *(j.jyhUserBenefitSnapshotDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *jyhUserBenefitSnapshot) updateTableName(table string) *jyhUserBenefitSnapshot {
	j.ALL = field.NewAsterisk(table)
	j.ID = field.NewUint(table, "id")
	j.CreatedAt = field.NewTime(table, "created_at")
	j.UpdatedAt = field.NewTime(table, "updated_at")
	j.UserID = field.NewUint(table, "user_id")
	j.UserLevelID = field.NewUint(table, "user_level_id")
	j.LevelID = field.NewUint(table, "level_id")
	j.BenefitID = field.NewUint(table, "benefit_id")
	j.Value = field.NewField(table, "value")
	j.Condition = field.NewField(table, "condition")
	j.StartAt = field.NewTime(table, "start_at")
	j.EndAt = field.NewTime(table, "end_at")
	j.BenefitKey = field.NewString(table, "benefit_key")
	j.BenefitName = field.NewString(table, "benefit_name")

	j.fillFieldMap()

	return j
}

func (j *jyhUserBenefitSnapshot) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *jyhUserBenefitSnapshot) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 13)
	j.fieldMap["id"] = j.ID
	j.fieldMap["created_at"] = j.CreatedAt
	j.fieldMap["updated_at"] = j.UpdatedAt
	j.fieldMap["user_id"] = j.UserID
	j.fieldMap["user_level_id"] = j.UserLevelID
	j.fieldMap["level_id"] = j.LevelID
	j.fieldMap["benefit_id"] = j.BenefitID
	j.fieldMap["value"] = j.Value
	j.fieldMap["condition"] = j.Condition
	j.fieldMap["start_at"] = j.StartAt
	j.fieldMap["end_at"] = j.EndAt
	j.fieldMap["benefit_key"] = j.BenefitKey
	j.fieldMap["benefit_name"] = j.BenefitName
}

func (j jyhUserBenefitSnapshot) clone(db *gorm.DB) jyhUserBenefitSnapshot {
	j.jyhUserBenefitSnapshotDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j jyhUserBenefitSnapshot) replaceDB(db *gorm.DB) jyhUserBenefitSnapshot {
	j.jyhUserBenefitSnapshotDo.ReplaceDB(db)
	return j
}

type jyhUserBenefitSnapshotDo struct{ gen.DO }

func (j jyhUserBenefitSnapshotDo) Debug() *jyhUserBenefitSnapshotDo {
	return j.withDO(j.DO.Debug())
}

func (j jyhUserBenefitSnapshotDo) WithContext(ctx context.Context) *jyhUserBenefitSnapshotDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jyhUserBenefitSnapshotDo) ReadDB() *jyhUserBenefitSnapshotDo {
	return j.Clauses(dbresolver.Read)
}

func (j jyhUserBenefitSnapshotDo) WriteDB() *jyhUserBenefitSnapshotDo {
	return j.Clauses(dbresolver.Write)
}

func (j jyhUserBenefitSnapshotDo) Session(config *gorm.Session) *jyhUserBenefitSnapshotDo {
	return j.withDO(j.DO.Session(config))
}

func (j jyhUserBenefitSnapshotDo) Clauses(conds ...clause.Expression) *jyhUserBenefitSnapshotDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jyhUserBenefitSnapshotDo) Returning(value interface{}, columns ...string) *jyhUserBenefitSnapshotDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jyhUserBenefitSnapshotDo) Not(conds ...gen.Condition) *jyhUserBenefitSnapshotDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jyhUserBenefitSnapshotDo) Or(conds ...gen.Condition) *jyhUserBenefitSnapshotDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jyhUserBenefitSnapshotDo) Select(conds ...field.Expr) *jyhUserBenefitSnapshotDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jyhUserBenefitSnapshotDo) Where(conds ...gen.Condition) *jyhUserBenefitSnapshotDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jyhUserBenefitSnapshotDo) Order(conds ...field.Expr) *jyhUserBenefitSnapshotDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jyhUserBenefitSnapshotDo) Distinct(cols ...field.Expr) *jyhUserBenefitSnapshotDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jyhUserBenefitSnapshotDo) Omit(cols ...field.Expr) *jyhUserBenefitSnapshotDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jyhUserBenefitSnapshotDo) Join(table schema.Tabler, on ...field.Expr) *jyhUserBenefitSnapshotDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jyhUserBenefitSnapshotDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jyhUserBenefitSnapshotDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jyhUserBenefitSnapshotDo) RightJoin(table schema.Tabler, on ...field.Expr) *jyhUserBenefitSnapshotDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jyhUserBenefitSnapshotDo) Group(cols ...field.Expr) *jyhUserBenefitSnapshotDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jyhUserBenefitSnapshotDo) Having(conds ...gen.Condition) *jyhUserBenefitSnapshotDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jyhUserBenefitSnapshotDo) Limit(limit int) *jyhUserBenefitSnapshotDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jyhUserBenefitSnapshotDo) Offset(offset int) *jyhUserBenefitSnapshotDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jyhUserBenefitSnapshotDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jyhUserBenefitSnapshotDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jyhUserBenefitSnapshotDo) Unscoped() *jyhUserBenefitSnapshotDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jyhUserBenefitSnapshotDo) Create(values ...*jyhapp.JyhUserBenefitSnapshot) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jyhUserBenefitSnapshotDo) CreateInBatches(values []*jyhapp.JyhUserBenefitSnapshot, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jyhUserBenefitSnapshotDo) Save(values ...*jyhapp.JyhUserBenefitSnapshot) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jyhUserBenefitSnapshotDo) First() (*jyhapp.JyhUserBenefitSnapshot, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserBenefitSnapshot), nil
	}
}

func (j jyhUserBenefitSnapshotDo) Take() (*jyhapp.JyhUserBenefitSnapshot, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserBenefitSnapshot), nil
	}
}

func (j jyhUserBenefitSnapshotDo) Last() (*jyhapp.JyhUserBenefitSnapshot, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserBenefitSnapshot), nil
	}
}

func (j jyhUserBenefitSnapshotDo) Find() ([]*jyhapp.JyhUserBenefitSnapshot, error) {
	result, err := j.DO.Find()
	return result.([]*jyhapp.JyhUserBenefitSnapshot), err
}

func (j jyhUserBenefitSnapshotDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*jyhapp.JyhUserBenefitSnapshot, err error) {
	buf := make([]*jyhapp.JyhUserBenefitSnapshot, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jyhUserBenefitSnapshotDo) FindInBatches(result *[]*jyhapp.JyhUserBenefitSnapshot, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jyhUserBenefitSnapshotDo) Attrs(attrs ...field.AssignExpr) *jyhUserBenefitSnapshotDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jyhUserBenefitSnapshotDo) Assign(attrs ...field.AssignExpr) *jyhUserBenefitSnapshotDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jyhUserBenefitSnapshotDo) Joins(fields ...field.RelationField) *jyhUserBenefitSnapshotDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jyhUserBenefitSnapshotDo) Preload(fields ...field.RelationField) *jyhUserBenefitSnapshotDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jyhUserBenefitSnapshotDo) FirstOrInit() (*jyhapp.JyhUserBenefitSnapshot, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserBenefitSnapshot), nil
	}
}

func (j jyhUserBenefitSnapshotDo) FirstOrCreate() (*jyhapp.JyhUserBenefitSnapshot, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserBenefitSnapshot), nil
	}
}

func (j jyhUserBenefitSnapshotDo) FindByPage(offset int, limit int) (result []*jyhapp.JyhUserBenefitSnapshot, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jyhUserBenefitSnapshotDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jyhUserBenefitSnapshotDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jyhUserBenefitSnapshotDo) Delete(models ...*jyhapp.JyhUserBenefitSnapshot) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jyhUserBenefitSnapshotDo) withDO(do gen.Dao) *jyhUserBenefitSnapshotDo {
	j.DO = *do.(*gen.DO)
	return j
}
