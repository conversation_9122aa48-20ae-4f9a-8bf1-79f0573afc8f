// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
)

func newJyhMaterialFile(db *gorm.DB, opts ...gen.DOOption) jyhMaterialFile {
	_jyhMaterialFile := jyhMaterialFile{}

	_jyhMaterialFile.jyhMaterialFileDo.UseDB(db, opts...)
	_jyhMaterialFile.jyhMaterialFileDo.UseModel(&jyhapp.JyhMaterialFile{})

	tableName := _jyhMaterialFile.jyhMaterialFileDo.TableName()
	_jyhMaterialFile.ALL = field.NewAsterisk(tableName)
	_jyhMaterialFile.ID = field.NewUint(tableName, "id")
	_jyhMaterialFile.CreatedAt = field.NewTime(tableName, "created_at")
	_jyhMaterialFile.UpdatedAt = field.NewTime(tableName, "updated_at")
	_jyhMaterialFile.DeletedAt = field.NewField(tableName, "deleted_at")
	_jyhMaterialFile.MaterialID = field.NewUint(tableName, "material_id")
	_jyhMaterialFile.FileUrl = field.NewString(tableName, "file_url")
	_jyhMaterialFile.FileType = field.NewString(tableName, "file_type")
	_jyhMaterialFile.IsPrimary = field.NewBool(tableName, "is_primary")
	_jyhMaterialFile.Sort = field.NewInt(tableName, "sort")
	_jyhMaterialFile.FileName = field.NewString(tableName, "file_name")

	_jyhMaterialFile.fillFieldMap()

	return _jyhMaterialFile
}

type jyhMaterialFile struct {
	jyhMaterialFileDo

	ALL        field.Asterisk
	ID         field.Uint
	CreatedAt  field.Time
	UpdatedAt  field.Time
	DeletedAt  field.Field
	MaterialID field.Uint
	FileUrl    field.String
	FileType   field.String
	IsPrimary  field.Bool
	Sort       field.Int
	FileName   field.String

	fieldMap map[string]field.Expr
}

func (j jyhMaterialFile) Table(newTableName string) *jyhMaterialFile {
	j.jyhMaterialFileDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j jyhMaterialFile) As(alias string) *jyhMaterialFile {
	j.jyhMaterialFileDo.DO = *(j.jyhMaterialFileDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *jyhMaterialFile) updateTableName(table string) *jyhMaterialFile {
	j.ALL = field.NewAsterisk(table)
	j.ID = field.NewUint(table, "id")
	j.CreatedAt = field.NewTime(table, "created_at")
	j.UpdatedAt = field.NewTime(table, "updated_at")
	j.DeletedAt = field.NewField(table, "deleted_at")
	j.MaterialID = field.NewUint(table, "material_id")
	j.FileUrl = field.NewString(table, "file_url")
	j.FileType = field.NewString(table, "file_type")
	j.IsPrimary = field.NewBool(table, "is_primary")
	j.Sort = field.NewInt(table, "sort")
	j.FileName = field.NewString(table, "file_name")

	j.fillFieldMap()

	return j
}

func (j *jyhMaterialFile) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *jyhMaterialFile) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 10)
	j.fieldMap["id"] = j.ID
	j.fieldMap["created_at"] = j.CreatedAt
	j.fieldMap["updated_at"] = j.UpdatedAt
	j.fieldMap["deleted_at"] = j.DeletedAt
	j.fieldMap["material_id"] = j.MaterialID
	j.fieldMap["file_url"] = j.FileUrl
	j.fieldMap["file_type"] = j.FileType
	j.fieldMap["is_primary"] = j.IsPrimary
	j.fieldMap["sort"] = j.Sort
	j.fieldMap["file_name"] = j.FileName
}

func (j jyhMaterialFile) clone(db *gorm.DB) jyhMaterialFile {
	j.jyhMaterialFileDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j jyhMaterialFile) replaceDB(db *gorm.DB) jyhMaterialFile {
	j.jyhMaterialFileDo.ReplaceDB(db)
	return j
}

type jyhMaterialFileDo struct{ gen.DO }

func (j jyhMaterialFileDo) Debug() *jyhMaterialFileDo {
	return j.withDO(j.DO.Debug())
}

func (j jyhMaterialFileDo) WithContext(ctx context.Context) *jyhMaterialFileDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jyhMaterialFileDo) ReadDB() *jyhMaterialFileDo {
	return j.Clauses(dbresolver.Read)
}

func (j jyhMaterialFileDo) WriteDB() *jyhMaterialFileDo {
	return j.Clauses(dbresolver.Write)
}

func (j jyhMaterialFileDo) Session(config *gorm.Session) *jyhMaterialFileDo {
	return j.withDO(j.DO.Session(config))
}

func (j jyhMaterialFileDo) Clauses(conds ...clause.Expression) *jyhMaterialFileDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jyhMaterialFileDo) Returning(value interface{}, columns ...string) *jyhMaterialFileDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jyhMaterialFileDo) Not(conds ...gen.Condition) *jyhMaterialFileDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jyhMaterialFileDo) Or(conds ...gen.Condition) *jyhMaterialFileDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jyhMaterialFileDo) Select(conds ...field.Expr) *jyhMaterialFileDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jyhMaterialFileDo) Where(conds ...gen.Condition) *jyhMaterialFileDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jyhMaterialFileDo) Order(conds ...field.Expr) *jyhMaterialFileDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jyhMaterialFileDo) Distinct(cols ...field.Expr) *jyhMaterialFileDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jyhMaterialFileDo) Omit(cols ...field.Expr) *jyhMaterialFileDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jyhMaterialFileDo) Join(table schema.Tabler, on ...field.Expr) *jyhMaterialFileDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jyhMaterialFileDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jyhMaterialFileDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jyhMaterialFileDo) RightJoin(table schema.Tabler, on ...field.Expr) *jyhMaterialFileDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jyhMaterialFileDo) Group(cols ...field.Expr) *jyhMaterialFileDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jyhMaterialFileDo) Having(conds ...gen.Condition) *jyhMaterialFileDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jyhMaterialFileDo) Limit(limit int) *jyhMaterialFileDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jyhMaterialFileDo) Offset(offset int) *jyhMaterialFileDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jyhMaterialFileDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jyhMaterialFileDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jyhMaterialFileDo) Unscoped() *jyhMaterialFileDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jyhMaterialFileDo) Create(values ...*jyhapp.JyhMaterialFile) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jyhMaterialFileDo) CreateInBatches(values []*jyhapp.JyhMaterialFile, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jyhMaterialFileDo) Save(values ...*jyhapp.JyhMaterialFile) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jyhMaterialFileDo) First() (*jyhapp.JyhMaterialFile, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterialFile), nil
	}
}

func (j jyhMaterialFileDo) Take() (*jyhapp.JyhMaterialFile, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterialFile), nil
	}
}

func (j jyhMaterialFileDo) Last() (*jyhapp.JyhMaterialFile, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterialFile), nil
	}
}

func (j jyhMaterialFileDo) Find() ([]*jyhapp.JyhMaterialFile, error) {
	result, err := j.DO.Find()
	return result.([]*jyhapp.JyhMaterialFile), err
}

func (j jyhMaterialFileDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*jyhapp.JyhMaterialFile, err error) {
	buf := make([]*jyhapp.JyhMaterialFile, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jyhMaterialFileDo) FindInBatches(result *[]*jyhapp.JyhMaterialFile, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jyhMaterialFileDo) Attrs(attrs ...field.AssignExpr) *jyhMaterialFileDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jyhMaterialFileDo) Assign(attrs ...field.AssignExpr) *jyhMaterialFileDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jyhMaterialFileDo) Joins(fields ...field.RelationField) *jyhMaterialFileDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jyhMaterialFileDo) Preload(fields ...field.RelationField) *jyhMaterialFileDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jyhMaterialFileDo) FirstOrInit() (*jyhapp.JyhMaterialFile, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterialFile), nil
	}
}

func (j jyhMaterialFileDo) FirstOrCreate() (*jyhapp.JyhMaterialFile, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterialFile), nil
	}
}

func (j jyhMaterialFileDo) FindByPage(offset int, limit int) (result []*jyhapp.JyhMaterialFile, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jyhMaterialFileDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jyhMaterialFileDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jyhMaterialFileDo) Delete(models ...*jyhapp.JyhMaterialFile) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jyhMaterialFileDo) withDO(do gen.Dao) *jyhMaterialFileDo {
	j.DO = *do.(*gen.DO)
	return j
}
