package jyhapp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"time"
)

// JyhCourse 课程表
type JyhCourse struct {
	global.GVA_MODEL
	Title      string    `gorm:"type:varchar(255);not null;comment:课程标题" json:"title"`
	CourseDate time.Time `gorm:"type:DATETIME;not null;comment:课程日期" json:"courseDate"`
	ViewCount  int       `gorm:"default:0;comment:观看人数" json:"viewCount"`
	ImageUrl   string    `gorm:"type:varchar(500);comment:课程封面图" json:"imageUrl"`
	VideoUrl   string    `gorm:"type:varchar(500);not null;comment:课程视频地址" json:"videoUrl"`
	Detail     string    `gorm:"type:text;comment:课程详情" json:"detail"`
	Duration   int       `gorm:"default:0;comment:课程时长（分钟）" json:"duration"` // 分钟
	Teacher    string    `gorm:"type:varchar(100);comment:讲师姓名" json:"teacher"`
	Status     uint      `gorm:"default:0;COMMENT:课程状态; 0 未发布，1 已发布" json:"status"`
	CategoryID uint      `gorm:"not null;index;comment:课程分类ID" json:"categoryId"`

	// 关联关系
	Category JyhCourseCategory `gorm:"foreignKey:CategoryID" json:"category"`
}

// TableName 课程管理
func (JyhCourse) TableName() string {
	return "jyh_course"
}

// JyhCourseCategory 课程分类模型
type JyhCourseCategory struct {
	global.GVA_MODEL
	Name     string `gorm:"type:varchar(100);not null" json:"name"`
	Sort     int    `gorm:"default:0" json:"sort"`
	IsActive bool   `gorm:"default:true" json:"isActive"`                // 是否启用
	CatDesc  string `gorm:"type:varchar(255);default:''" json:"catDesc"` // 分类描述
}

// TableName 课程分类
func (JyhCourseCategory) TableName() string {
	return "jyh_course_category"
}
