package jyhapp

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/errno"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/query"
	jyhReq "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/request"
	"github.com/google/uuid"
	"go.uber.org/zap"
	"gorm.io/gen"
	"gorm.io/gorm"
	"time"
)

type JyhPointsService struct{}

// AddPoints 添加积分
func (s *JyhPointsService) AddPoints(userID uint, ruleKey, source, remark string) error {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 1. 获取积分规则
		var rule jyhapp.JyhPointsRule
		if err := tx.Where("rule_key = ? AND is_enabled = ?", ruleKey, true).First(&rule).Error; err != nil {
			return errors.New("积分规则不存在或未启用")
		}

		// 2. 检查每日限制
		if rule.DailyLimit > 0 {
			var todayCount int64
			startOfDay := time.Now().Truncate(24 * time.Hour)
			if err := tx.Model(&jyhapp.JyhPointsRecord{}).
				Where("user_id = ? AND rule_id = ? AND created_at >= ?", userID, rule.ID, startOfDay).
				Count(&todayCount).Error; err != nil {
				return err
			}

			if int(todayCount) >= rule.DailyLimit {
				return errors.New("今日积分获取已达上限")
			}
		}

		// 3. 检查总限制
		if rule.TotalLimit > 0 {
			var totalCount int64
			if err := tx.Model(&jyhapp.JyhPointsRecord{}).
				Where("user_id = ? AND rule_id = ?", userID, rule.ID).
				Count(&totalCount).Error; err != nil {
				return err
			}

			if int(totalCount) >= rule.TotalLimit {
				return errors.New("该积分获取已达总上限")
			}
		}

		// 4. 创建积分记录
		record := jyhapp.JyhPointsRecord{
			UserID: userID,
			RuleID: rule.ID,
			Points: rule.Points,
			Source: source,
			Remark: remark,
		}

		if err := tx.Create(&record).Error; err != nil {
			return err
		}

		// 5. 更新用户积分
		result := tx.Model(&jyhapp.JyhUser{}).
			Where("id = ?", userID).
			Update("points", gorm.Expr("points + ?", rule.Points))

		if result.Error != nil {
			return result.Error
		}

		if result.RowsAffected == 0 {
			return errors.New("用户不存在")
		}

		return nil
	})
}

// GetPointsRecords 获取积分明细
func (s *JyhPointsService) GetPointsRecords(req jyhReq.GetPointsRecords) ([]jyhapp.JyhPointsRecord, int64, error) {

	db := global.GVA_DB

	var records []jyhapp.JyhPointsRecord
	query := db.Model(&jyhapp.JyhPointsRecord{}).
		Preload("Rule").
		Order("created_at DESC")

	if req.UserID > 0 {
		query = query.Where("user_id = ?", req.UserID)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页
	if req.Page > 0 && req.PageSize > 0 {
		offset := (req.Page - 1) * req.PageSize
		query = query.Offset(offset).Limit(req.PageSize)
	}

	// 查询
	if err := query.Find(&records).Error; err != nil {
		return nil, 0, err
	}

	return records, total, nil
}

// GetUserPointsRecords 获取用户积分明细
func (s *JyhPointsService) GetUserPointsRecords(req jyhReq.GetPointsRecords) ([]jyhapp.JyhPointsRecord, int64, error) {

	db := global.GVA_DB

	var records []jyhapp.JyhPointsRecord
	query := db.Model(&jyhapp.JyhPointsRecord{}).
		Preload("Rule").
		Where("user_id = ?", req.UserID).
		Order("created_at DESC")

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页
	if req.Page > 0 && req.PageSize > 0 {
		offset := (req.Page - 1) * req.PageSize
		query = query.Offset(offset).Limit(req.PageSize)
	}

	// 查询
	if err := query.Find(&records).Error; err != nil {
		return nil, 0, err
	}

	return records, total, nil
}

// GetPointsRules 获取积分规则列表
func (s *JyhPointsService) GetPointsRules(req jyhReq.GetPointsRules) (rules []jyhapp.JyhPointsRule, total int64, err error) {
	var dbPointsRule = query.JyhPointsRule
	var cons []gen.Condition

	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)

	db := dbPointsRule.Where(cons...)
	total, err = db.Count()
	if err != nil {
		global.GVA_LOG.Error("查询积分规则列表失败", zap.Error(err))
		return nil, 0, errno.QueryFailed.WithError(err)
	}

	// 分页处理
	err = db.Limit(limit).Offset(offset).Scan(&rules)
	if err != nil {
		global.GVA_LOG.Error("查询积分规则列表失败", zap.Error(err))
		return nil, 0, errno.QueryFailed.WithError(err)
	}
	return
}

// GetPointsRuleByID 根据积分ID，获取积分详情数据
func (s *JyhPointsService) GetPointsRuleByID(ruleID uint) (*jyhapp.JyhPointsRule, error) {
	var rule jyhapp.JyhPointsRule
	err := global.GVA_DB.First(&rule, ruleID).Error
	if err != nil {
		return nil, err
	}
	return &rule, nil
}

// UpdatePointsRule 更新积分规则
func (s *JyhPointsService) UpdatePointsRule(rule *jyhapp.JyhPointsRule) error {
	return global.GVA_DB.Save(rule).Error
}

// ExchangeMaterial 兑换商品
func (s *JyhPointsService) ExchangeMaterial(userID, materialID uint, quantity int, address map[string]interface{}) (*jyhapp.JyhPointsExchange, error) {
	var exchange *jyhapp.JyhPointsExchange
	err := global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 1. 获取商品信息
		var material jyhapp.JyhMaterial
		if err := tx.First(&material, materialID).Error; err != nil {
			return errors.New("素材不存在")
		}

		// 2. 检查素材是否已兑换
		if !material.IsSend {
			return errors.New("素材已兑换")
		}

		// 3. 检查库存
		// 4. 获取用户信息
		var user jyhapp.JyhUser
		if err := tx.First(&user, userID).Error; err != nil {
			return errors.New("用户不存在")
		}

		// 5. 计算所需积分
		pointsRequired := material.PointsRequired * quantity
		if user.Points < pointsRequired {
			return fmt.Errorf("积分不足，需要 %d 积分，当前 %d 积分", pointsRequired, user.Points)
		}

		// 6. 检查限购
		/*if material.DailyLimit > 0 {
		  	startOfDay := time.Now().Truncate(24 * time.Hour)
		  	var todayCount int64
		  	if err := tx.Model(&jyhapp.JyhPointsExchange{}).
		  		Where("user_id = ? AND product_id = ? AND created_at >= ?", userID, productID, startOfDay).
		  		Count(&todayCount).Error; err != nil {
		  		return err
		  	}

		  	if int(todayCount)+quantity > material.DailyLimit {
		  		return fmt.Errorf("今日已兑换 %d 次，最多可兑换 %d 次", todayCount, material.DailyLimit)
		  	}
		  }

		  if material.TotalLimit > 0 {
		  	var totalCount int64
		  	if err := tx.Model(&jyhapp.JyhPointsExchange{}).
		  		Where("user_id = ? AND product_id = ?", userID, productID).
		  		Count(&totalCount).Error; err != nil {
		  		return err
		  	}

		  	if int(totalCount)+quantity > material.TotalLimit {
		  		return fmt.Errorf("已兑换 %d 次，最多可兑换 %d 次", totalCount, material.TotalLimit)
		  	}
		  }
		*/

		jsonAddress, _ := json.Marshal(address)
		// 7. 创建兑换记录
		exchange = &jyhapp.JyhPointsExchange{
			UserID:       userID,
			MaterialID:   materialID,
			Quantity:     quantity,
			PointsTotal:  pointsRequired,
			ExchangeCode: uuid.New().String(),
			Address:      jsonAddress,
			Status:       "pending",
		}

		if err := tx.Create(exchange).Error; err != nil {
			return err
		}

		// 8. 扣除用户积分
		if err := tx.Model(&user).
			Update("points", gorm.Expr("points - ?", pointsRequired)).Error; err != nil {
			return err
		}

		// 9. 减少商品库存
		/*if err := tx.Model(&material).
			Update("stock", gorm.Expr("stock - ?", quantity)).Error; err != nil {
			return err
		}*/

		// 10. 创建积分扣除记录
		record := jyhapp.JyhPointsRecord{
			UserID: userID,
			Points: -pointsRequired,
			Source: "exchange",
			Remark: fmt.Sprintf("兑换商品: %s (ID: %d)", material.Name, materialID),
		}
		if err := tx.Create(&record).Error; err != nil {
			return err
		}

		return nil
	})

	return exchange, err
}

// CompleteExchange 完成兑换
func (s *JyhPointsService) CompleteExchange(exchangeID uint) error {
	now := time.Now()
	return global.GVA_DB.Model(&jyhapp.JyhPointsExchange{}).
		Where("id = ? AND status = 'pending'", exchangeID).
		Updates(map[string]interface{}{
			"status":       "completed",
			"completed_at": &now,
		}).Error
}

// CancelExchange 取消兑换
func (s *JyhPointsService) CancelExchange(exchangeID uint) error {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 获取兑换记录
		var exchange jyhapp.JyhPointsExchange
		if err := tx.First(&exchange, exchangeID).Error; err != nil {
			return err
		}

		if exchange.Status != "pending" {
			return errors.New("只能取消待处理的兑换")
		}

		// 更新状态为已取消
		if err := tx.Model(&exchange).
			Update("status", "cancelled").Error; err != nil {
			return err
		}

		// 返还用户积分
		if err := tx.Model(&jyhapp.JyhUser{}).
			Where("id = ?", exchange.UserID).
			Update("points", gorm.Expr("points + ?", exchange.PointsTotal)).Error; err != nil {
			return err
		}

		// 返还商品库存
		if err := tx.Model(&jyhapp.JyhMaterial{}).
			Where("id = ?", exchange.MaterialID).
			Update("stock", gorm.Expr("stock + ?", exchange.Quantity)).Error; err != nil {
			return err
		}

		// 创建积分返还记录
		record := jyhapp.JyhPointsRecord{
			UserID: exchange.UserID,
			Points: exchange.PointsTotal,
			Source: "exchange_cancel",
			Remark: fmt.Sprintf("取消兑换: %d", exchange.ID),
		}
		return tx.Create(&record).Error
	})
}

// GetUserExchanges 获取用户兑换记录
func (s *JyhPointsService) GetUserExchanges(userID uint, req jyhReq.GetUserExchanges) ([]jyhapp.JyhPointsExchange, int64, error) {
	db := global.GVA_DB

	query := db.Model(&jyhapp.JyhPointsExchange{}).
		Preload("Material").
		Where("user_id = ?", userID).
		Order("created_at DESC")

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页
	if req.Page > 0 && req.PageSize > 0 {
		offset := (req.Page - 1) * req.PageSize
		query = query.Offset(offset).Limit(req.PageSize)
	}

	// 查询结果
	var exchanges []jyhapp.JyhPointsExchange
	if err := query.Find(&exchanges).Error; err != nil {
		return nil, 0, err
	}

	return exchanges, total, nil
}

// GetExchangeRecord 获取兑换记录
func (s *JyhPointsService) GetExchangeRecord(req jyhReq.GetUserExchanges) ([]jyhapp.JyhPointsExchange, int64, error) {
	db := global.GVA_DB

	query := db.Model(&jyhapp.JyhPointsExchange{}).
		Preload("Material").
		Order("created_at DESC")

	if req.UserID > 0 {
		query = query.Where("user_id = ?", req.UserID)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页
	if req.Page > 0 && req.PageSize > 0 {
		offset := (req.Page - 1) * req.PageSize
		query = query.Offset(offset).Limit(req.PageSize)
	}

	// 查询结果
	var exchanges []jyhapp.JyhPointsExchange
	if err := query.Find(&exchanges).Error; err != nil {
		return nil, 0, err
	}

	return exchanges, total, nil
}

// GetExchangeDetail 获取兑换详情
func (s *JyhPointsService) GetExchangeDetail(exchangeID uint) (*jyhapp.JyhPointsExchange, error) {
	var exchange jyhapp.JyhPointsExchange
	err := global.GVA_DB.
		Preload("User").
		Preload("Material").
		First(&exchange, exchangeID).Error
	return &exchange, err
}
