package jyhapp

import (
	v1 "github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type JyhExternalRouter struct {
}

// InitExternalRouter 初始化
func (m *JyhExternalRouter) InitExternalRouter(Pub *gin.RouterGroup, Pri *gin.RouterGroup) {
	r0 := Pub.Group("ext", middleware.AppTokenAuth())
	{
		var api = v1.ApiGroupApp.JyhApiGroup.ExternalApi
		r0.POST("material/sync", api.MaterialSync) // 储存素材
	}
}
