package jyhapp

import (
	v1 "github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type JyhUserAccountRouter struct{}

// InitJyhUserAccountRouter 初始化用户账户路由信息
func (s *JyhUserAccountRouter) InitJyhUserAccountRouter(PrivateGroup *gin.RouterGroup, JyhappGroup *gin.RouterGroup) {
	userAccountApi := v1.ApiGroupApp.JyhApiGroup.JyhUserAccountApi

	// ================================ 用户端路由（JyhappGroup - 用户Token验证）================================
	userAccountRouter := JyhappGroup.Group("jyh/account")
	{
		userAccountRouter.GET("summary", userAccountApi.GetUserAccountSummary)       // 用户端余额汇总
		userAccountRouter.GET("transactions", userAccountApi.GetUserTransactionList) // 用户端交易记录
	}
	userAccountRouterRecord := JyhappGroup.Group("jyh/account").Use(middleware.OperationRecord())
	{
		userAccountRouterRecord.POST("withdrawal", userAccountApi.ProcessWithdrawal) // 提现
	}

	// ================================ 管理端路由（PrivateGroup - 管理员Token验证）================================
	adminAccountRouter := PrivateGroup.Group("admin/user").Use(middleware.OperationRecord())
	{
		// 用户余额管理
		adminAccountRouter.GET("accounts/:id", userAccountApi.GetAdminUserAccountDetail)                // 管理端用户余额明细
		adminAccountRouter.GET("accounts/transactions/:id", userAccountApi.GetAdminUserTransactionList) // 管理端用户交易记录

		adminAccountRouter.GET("accounts/statistics", userAccountApi.GetAccountStatistics) // 管理端账户统计信息
		// 余额操作
		adminAccountRouter.POST("accounts/adjust", userAccountApi.AdjustUserBalance) // 管理端调整用户余额
	}
}
