package jyhapp

import (
	"testing"
)

// TestPolyvLiveServiceEncryption 测试保利威认证信息RSA加解密功能
func TestPolyvLiveServiceEncryption(t *testing.T) {
	// 创建测试服务实例（使用空密钥对，让其自动生成）
	service := NewPolyvLiveService("test_app_id", "test_secret_key", "", "")

	// 验证密钥对是否生成
	if service.PrivateKey == "" || service.PublicKey == "" {
		t.Fatal("密钥对生成失败")
	}

	// 测试加密
	encryptedInfo, err := service.encryptAuthInfo()
	if err != nil {
		t.Fatalf("加密失败: %v", err)
	}

	if encryptedInfo.Data == "" {
		t.<PERSON>al("加密数据为空")
	}

	if encryptedInfo.PublicKey == "" {
		t.<PERSON>("公钥为空")
	}

	// 测试解密
	decryptedInfo, err := service.DecryptAuthInfo(encryptedInfo.Data)
	if err != nil {
		t.Fatalf("解密失败: %v", err)
	}

	// 验证解密结果
	if decryptedInfo.AppId != "test_app_id" {
		t.Errorf("AppId 不匹配: expected 'test_app_id', got '%s'", decryptedInfo.AppId)
	}

	if decryptedInfo.SecretKey != "test_secret_key" {
		t.Errorf("SecretKey 不匹配: expected 'test_secret_key', got '%s'", decryptedInfo.SecretKey)
	}

	t.Logf("RSA加解密测试通过")
	t.Logf("公钥长度: %d", len(encryptedInfo.PublicKey))
	t.Logf("加密数据长度: %d", len(encryptedInfo.Data))
	t.Logf("解密结果: AppId=%s, SecretKey=%s, Timestamp=%d",
		decryptedInfo.AppId, decryptedInfo.SecretKey, decryptedInfo.Timestamp)
}

// TestPolyvLiveServiceWithProvidedKeys 测试使用提供的密钥对
func TestPolyvLiveServiceWithProvidedKeys(t *testing.T) {
	// 首先生成一个密钥对
	tempService := NewPolyvLiveService("", "", "", "")
	privateKey := tempService.PrivateKey
	publicKey := tempService.PublicKey

	// 使用生成的密钥对创建新的服务实例
	service := NewPolyvLiveService("test_app_id", "test_secret_key", privateKey, publicKey)

	// 验证密钥对是否正确设置
	if service.PrivateKey != privateKey {
		t.Fatal("私钥设置失败")
	}
	if service.PublicKey != publicKey {
		t.Fatal("公钥设置失败")
	}

	// 测试加解密
	encryptedInfo, err := service.encryptAuthInfo()
	if err != nil {
		t.Fatalf("加密失败: %v", err)
	}

	decryptedInfo, err := service.DecryptAuthInfo(encryptedInfo.Data)
	if err != nil {
		t.Fatalf("解密失败: %v", err)
	}

	if decryptedInfo.AppId != "test_app_id" {
		t.Errorf("AppId 不匹配: expected 'test_app_id', got '%s'", decryptedInfo.AppId)
	}

	t.Logf("提供密钥对测试通过")
}

// TestPolyvLiveServiceInvalidDecryption 测试错误的加密数据
func TestPolyvLiveServiceInvalidDecryption(t *testing.T) {
	// 创建测试服务实例
	service := NewPolyvLiveService("test_app_id", "test_secret_key", "", "")

	// 测试解密无效数据
	_, err := service.DecryptAuthInfo("invalid_encrypted_data")
	if err == nil {
		t.Fatal("解密无效数据应该失败")
	}

	t.Logf("无效数据测试通过: %v", err)
}
