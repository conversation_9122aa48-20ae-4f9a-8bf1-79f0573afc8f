package response

import (
	"github.com/shopspring/decimal"
)

// UserAccountItem 用户账户列表项
type UserAccountItem struct {
	AccountID        uint            `json:"account_id"`        // 账户ID
	UserID           uint            `json:"user_id"`           // 用户ID
	Username         string          `json:"username"`          // 用户名
	Phone            string          `json:"phone"`             // 手机号（脱敏）
	DyBalance        decimal.Decimal `json:"dy_balance"`        // 抖音佣金余额
	JyhBalance       decimal.Decimal `json:"jyh_balance"`       // JYH佣金余额
	WithdrawnBalance decimal.Decimal `json:"withdrawn_balance"` // 累计提现金额
	CurrentBalance   decimal.Decimal `json:"current_balance"`   // 当前总余额
	AvailableBalance decimal.Decimal `json:"available_balance"` // 可用余额（可提现）
	Version          int             `json:"version"`           // 版本号
	CreatedAt        string          `json:"created_at"`        // 创建时间
	UpdatedAt        string          `json:"updated_at"`        // 更新时间
	LastUpdatedAt    string          `json:"last_updated_at"`   // 最后更新时间
}

// UserAccountListResp 用户账户列表响应
type UserAccountListResp struct {
	List     []*UserAccountItem `json:"list"`      // 账户列表
	Total    int64              `json:"total"`     // 总数
	Page     int                `json:"page"`      // 当前页
	PageSize int                `json:"page_size"` // 每页大小
}

// UserAccountDetail 用户账户详情
type UserAccountDetail struct {
	AccountID        uint            `json:"account_id"`        // 账户ID
	UserID           uint            `json:"user_id"`           // 用户ID
	DyBalance        decimal.Decimal `json:"dy_balance"`        // 抖音佣金余额
	JyhBalance       decimal.Decimal `json:"jyh_balance"`       // JYH佣金余额
	WithdrawnBalance decimal.Decimal `json:"withdrawn_balance"` // 累计提现金额
	CurrentBalance   decimal.Decimal `json:"current_balance"`   // 当前总余额
	LastUpdatedAt    string          `json:"last_updated_at"`   // 最后更新时间

	// 统计信息
	TotalIncomeAmount decimal.Decimal `json:"total_income_amount"` // 总收入金额
	TotalWithdrawals  decimal.Decimal `json:"total_withdrawals"`   // 总提现金额
	TransactionCount  int64           `json:"transaction_count"`   // 交易次数

	// 用户积分
	TotalPoints uint `json:"total_points"` // 总积分
}

// TransactionItem 交易记录列表项
type TransactionItem struct {
	TransactionID          uint            `json:"transaction_id"` // 交易ID
	AccountID              uint            `json:"account_id"`     // 账户ID
	UserID                 uint            `json:"user_id"`        // 用户ID
	Amount                 decimal.Decimal `json:"amount"`         // 交易金额
	ChangeType             int             `json:"change_type"`
	TransactionType        string          `json:"transaction_type"`        // 交易类型
	TransactionTypeName    string          `json:"transaction_type_name"`   // 交易类型名称
	SourceType             string          `json:"source_type"`             // 资金来源
	SourceTypeName         string          `json:"source_type_name"`        // 资金来源名称
	RelatedBusinessID      *uint           `json:"related_business_id"`     // 相关业务ID
	TransactionDescription string          `json:"transaction_description"` // 交易描述
	TransactionStatus      string          `json:"transaction_status"`      // 交易状态
	TransactionStatusName  string          `json:"transaction_status_name"` // 交易状态名称
	BalanceBefore          decimal.Decimal `json:"balance_before"`          // 交易前余额
	BalanceAfter           decimal.Decimal `json:"balance_after"`           // 交易后余额
	CreatedAt              string          `json:"created_at"`              // 创建时间
	CompletedAt            *string         `json:"completed_at"`            // 完成时间
}

// TransactionListResp 交易记录列表响应
type TransactionListResp struct {
	List     []*TransactionItem `json:"list"`      // 交易列表
	Total    int64              `json:"total"`     // 总数
	Page     int                `json:"page"`      // 当前页
	PageSize int                `json:"page_size"` // 每页大小
}

// UserAccountStatistics 用户账户统计信息
type UserAccountStatistics struct {
	TotalAccounts       int64           `json:"total_accounts"`        // 总账户数
	TotalCurrentBalance decimal.Decimal `json:"total_current_balance"` // 总当前余额
	TotalDyBalance      decimal.Decimal `json:"total_dy_balance"`      // 总抖音佣金余额
	TotalJyhBalance     decimal.Decimal `json:"total_jyh_balance"`     // 总JYH佣金余额
	TotalWithdrawn      decimal.Decimal `json:"total_withdrawn"`       // 总提现金额
	TotalTransactions   int64           `json:"total_transactions"`    // 总交易数
	TodayTransactions   int64           `json:"today_transactions"`    // 今日交易数
	TodayIncomeAmount   decimal.Decimal `json:"today_income_amount"`   // 今日收入金额
	TodayWithdrawAmount decimal.Decimal `json:"today_withdraw_amount"` // 今日提现金额
}

// BalanceValidationResult 余额验证结果
type BalanceValidationResult struct {
	AccountID         uint            `json:"account_id"`         // 账户ID
	UserID            uint            `json:"user_id"`            // 用户ID
	Username          string          `json:"username"`           // 用户名
	IsValid           bool            `json:"is_valid"`           // 是否验证通过
	CurrentBalance    decimal.Decimal `json:"current_balance"`    // 当前存储的余额
	CalculatedBalance decimal.Decimal `json:"calculated_balance"` // 计算得出的余额
	Difference        decimal.Decimal `json:"difference"`         // 差异金额
	ErrorMessage      string          `json:"error_message"`      // 错误信息
}

// BalanceValidationResp 余额验证响应
type BalanceValidationResp struct {
	TotalAccounts   int64                      `json:"total_accounts"`   // 总验证账户数
	ValidAccounts   int64                      `json:"valid_accounts"`   // 验证通过账户数
	InvalidAccounts int64                      `json:"invalid_accounts"` // 验证失败账户数
	Results         []*BalanceValidationResult `json:"results"`          // 详细结果（仅返回验证失败的）
}

// AddCommissionIncomeResp 添加佣金收入响应
type AddCommissionIncomeResp struct {
	TransactionID  uint            `json:"transaction_id"`  // 交易ID
	CurrentBalance decimal.Decimal `json:"current_balance"` // 更新后的当前余额
}

// ProcessWithdrawalResp 处理提现响应
type ProcessWithdrawalResp struct {
	TransactionID  uint            `json:"transaction_id"`  // 交易ID
	CurrentBalance decimal.Decimal `json:"current_balance"` // 更新后的当前余额
}
