package jyhapp

import (
	"errors"
	"github.com/xtulnx/jkit-go/jtime"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/query"
	jyhappReq "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/request"
	jyhappRes "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/jgorm"
	"go.uber.org/zap"
	"gorm.io/gen"
	"gorm.io/gorm"
)

type JyhUserLiveRoomService struct{}

// CreateUserLiveRoom 创建用户直播房间
func (jyhUserLiveRoomService *JyhUserLiveRoomService) CreateUserLiveRoom(userID uint, req jyhappReq.CreateJyhUserLiveRoomReq) (jyhappRes.CreateJyhUserLiveRoomResponse, error) {
	var response jyhappRes.CreateJyhUserLiveRoomResponse

	// 检查用户是否已有可用的直播间
	existingRoom, err := jyhUserLiveRoomService.GetUserAvailableRoom(userID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		global.GVA_LOG.Error("检查用户现有直播间失败", zap.Error(err), zap.Uint("userID", userID))
		return response, err
	}
	polyvService := NewPolyvLiveService(global.GVA_CONFIG.Polyv.AppId, global.GVA_CONFIG.Polyv.SecretKey, global.GVA_CONFIG.Polyv.UserId, global.GVA_CONFIG.Polyv.PrivateKey, global.GVA_CONFIG.Polyv.PublicKey)
	// 如果有可用的直播间，直接返回
	if existingRoom != nil {
		global.GVA_LOG.Info("用户已有可用直播间，直接返回", zap.Uint("userID", userID), zap.Int64("channelId", existingRoom.ChannelId))
		response = jyhappRes.CreateJyhUserLiveRoomResponse{
			ID:                      existingRoom.ID,
			ChannelId:               existingRoom.ChannelId,
			ChannelName:             existingRoom.ChannelName,
			PolyvUserId:             existingRoom.PolyvUserId,
			Scene:                   existingRoom.Scene,
			ChannelPasswd:           existingRoom.ChannelPasswd,
			SeminarHostPassword:     existingRoom.SeminarHostPassword,
			SeminarAttendeePassword: existingRoom.SeminarAttendeePassword,
			Status:                  existingRoom.Status,
		}
		return response, nil
	}
	if req.NewScene == "" {
		req.NewScene = "topclass"
	}
	if req.Template == "" {
		req.Template = "portrait_alone"
	}

	// 创建保利威频道请求
	channelReq := &CreateChannelReq{
		Name:           req.ChannelName,
		ChannelPasswd:  req.Password,
		NewScene:       req.NewScene,
		Template:       req.Template,
		PureRtcEnabled: "N", // 普通延迟
	}

	// 调用保利威API创建频道
	polyvResp, err := polyvService.CreateChannel(channelReq)
	if err != nil {
		global.GVA_LOG.Error("创建保利威频道失败", zap.Error(err))
		return response, err
	}

	// 保存到数据库
	liveRoom := jyhapp.JyhUserLiveRoom{
		UserID:                  userID,
		ChannelId:               polyvResp.Data.ChannelId,
		ChannelName:             req.ChannelName,
		PolyvUserId:             polyvResp.Data.UserId,
		Scene:                   polyvResp.Data.Scene,
		ChannelPasswd:           polyvResp.Data.ChannelPasswd,
		SeminarHostPassword:     polyvResp.Data.SeminarHostPassword,
		SeminarAttendeePassword: polyvResp.Data.SeminarAttendeePassword,
		Status:                  "active",
	}

	if err := global.GVA_DB.Create(&liveRoom).Error; err != nil {
		global.GVA_LOG.Error("保存用户直播房间失败", zap.Error(err))
		return response, err
	}

	response = jyhappRes.CreateJyhUserLiveRoomResponse{
		ID:                      liveRoom.ID,
		ChannelId:               liveRoom.ChannelId,
		ChannelName:             liveRoom.ChannelName,
		PolyvUserId:             liveRoom.PolyvUserId,
		Scene:                   liveRoom.Scene,
		ChannelPasswd:           liveRoom.ChannelPasswd,
		SeminarHostPassword:     liveRoom.SeminarHostPassword,
		SeminarAttendeePassword: liveRoom.SeminarAttendeePassword,
		Status:                  liveRoom.Status,
	}

	global.GVA_LOG.Info("用户直播房间创建成功",
		zap.Uint("userID", userID),
		zap.Int64("channelId", liveRoom.ChannelId),
		zap.Uint("roomID", liveRoom.ID))

	return response, nil
}

// GetUserAvailableRoom 获取用户可用的直播房间
func (jyhUserLiveRoomService *JyhUserLiveRoomService) GetUserAvailableRoom(userID uint) (*jyhapp.JyhUserLiveRoom, error) {
	var liveRoom jyhapp.JyhUserLiveRoom
	err := global.GVA_DB.Where("user_id = ? AND status = ?", userID, "active").First(&liveRoom).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // 没有找到可用房间，返回nil而不是错误
		}
		return nil, err
	}
	return &liveRoom, nil
}

// LiveRoomInfoList 分页获取用户直播房间列表
func (jyhUserLiveRoomService *JyhUserLiveRoomService) LiveRoomInfoList(info jyhappReq.JyhUserLiveRoomSearch) (list []*jyhappRes.JyhUserLiveRoomListItem, total int64, err error) {
	var (
		dbLiveRoom = query.JyhUserLiveRoom
		dbUser     = query.JyhUser
	)

	// 构建查询条件
	var conditions []gen.Condition
	{
		// 动态条件
		if info.UserID != nil {
			conditions = append(conditions, dbLiveRoom.UserID.Eq(*info.UserID))
		}
		if info.ChannelName != "" {
			conditions = append(conditions, dbLiveRoom.ChannelName.Like("%"+info.ChannelName+"%"))
		}
		if info.Status != "" {
			conditions = append(conditions, dbLiveRoom.Status.Eq(info.Status))
		}

		// 时间条件
		if !jtime.Str2Time(info.StartTime).IsZero() {
			conditions = append(conditions, dbLiveRoom.CreatedAt.Date().Gte(jtime.Str2Time(info.StartTime)))
		}
		if !jtime.Str2Time(info.EndTime).IsZero() {
			conditions = append(conditions, dbLiveRoom.CreatedAt.Date().Lte(jtime.Str2Time(info.EndTime)))
		}
	}

	// 构建查询
	dao := dbLiveRoom.WithContext(global.GVA_DB.Statement.Context).
		LeftJoin(dbUser, dbLiveRoom.UserID.EqCol(dbUser.ID)).
		Where(conditions...)

	// 选择字段
	jgorm.SelectAppend(&dao.DO,
		dbLiveRoom.ID,
		dbLiveRoom.UserID,
		dbUser.Username,
		dbUser.Phone.As("user_phone"),
		dbLiveRoom.ChannelId,
		dbLiveRoom.ChannelName,
		dbLiveRoom.PolyvUserId,
		dbLiveRoom.Scene,
		dbLiveRoom.ChannelPasswd,
		dbLiveRoom.SeminarHostPassword,
		dbLiveRoom.SeminarAttendeePassword,
		dbLiveRoom.Status,
		dbLiveRoom.LastUsedAt.DateFormat("%Y-%m-%d %H:%i:%s").As("last_used_at"),
		dbLiveRoom.CreatedAt.DateFormat("%Y-%m-%d %H:%i:%s").As("created_at"),
		dbLiveRoom.UpdatedAt.DateFormat("%Y-%m-%d %H:%i:%s").As("updated_at"),
	)

	// 获取总数
	total, err = dao.Count()
	if err != nil {
		global.GVA_LOG.Error("获取用户直播房间列表总数失败", zap.Error(err))
		return nil, 0, err
	}

	// 分页查询
	offset := (info.Page - 1) * info.PageSize
	dao = dao.Offset(offset).
		Limit(info.PageSize).
		Order(dbLiveRoom.CreatedAt.Desc())

	var result = make([]*jyhappRes.JyhUserLiveRoomListItem, 0)
	err = dao.Scan(&result)
	if err != nil {
		global.GVA_LOG.Error("获取用户直播房间列表失败", zap.Error(err))
		return nil, 0, err
	}

	// 处理数据脱敏
	for _, item := range result {
		item.UserPhone = HideIdent(item.UserPhone)
	}

	return result, total, nil
}

// UpdateUserLiveRoomStatus 更新用户直播房间状态
func (jyhUserLiveRoomService *JyhUserLiveRoomService) UpdateUserLiveRoomStatus(id uint, status string) error {
	return global.GVA_DB.Model(&jyhapp.JyhUserLiveRoom{}).Where("id = ?", id).Updates(map[string]interface{}{
		"status": status,
	}).Error
}

// UpdateLastUsedTime 更新最后使用时间
func (jyhUserLiveRoomService *JyhUserLiveRoomService) UpdateLastUsedTime(userID uint) error {
	now := time.Now()
	return global.GVA_DB.Model(&jyhapp.JyhUserLiveRoom{}).
		Where("user_id = ? AND status = ?", userID, "active").
		Update("last_used_at", now).Error
}

// DeleteJyhUserLiveRoom 删除用户直播房间
func (jyhUserLiveRoomService *JyhUserLiveRoomService) DeleteJyhUserLiveRoom(id uint) error {
	return global.GVA_DB.Delete(&jyhapp.JyhUserLiveRoom{}, id).Error
}

// GetJyhUserLiveRoom 根据ID获取用户直播房间
func (jyhUserLiveRoomService *JyhUserLiveRoomService) GetJyhUserLiveRoom(id uint) (jyhapp.JyhUserLiveRoom, error) {
	var liveRoom jyhapp.JyhUserLiveRoom
	err := global.GVA_DB.Preload("User").Where("id = ?", id).First(&liveRoom).Error
	return liveRoom, err
}

// UserLiveRoom 获取当前用户的直播房间（包含认证信息）
func (jyhUserLiveRoomService *JyhUserLiveRoomService) UserLiveRoom(userID uint) (*jyhappRes.CreateJyhUserLiveRoomResponse, error) {
	// 获取用户可用的直播房间
	liveRoom, err := jyhUserLiveRoomService.GetUserAvailableRoom(userID)
	if err != nil {
		global.GVA_LOG.Error("获取用户直播房间失败", zap.Error(err), zap.Uint("userID", userID))
		return nil, err
	}

	// 如果没有可用的直播房间
	if liveRoom == nil {
		return nil, nil
	}
	// 更新最后使用时间
	_ = jyhUserLiveRoomService.UpdateLastUsedTime(userID)
	// 构建响应
	response := &jyhappRes.CreateJyhUserLiveRoomResponse{
		ID:                      liveRoom.ID,
		ChannelId:               liveRoom.ChannelId,
		ChannelName:             liveRoom.ChannelName,
		PolyvUserId:             liveRoom.PolyvUserId,
		Scene:                   liveRoom.Scene,
		ChannelPasswd:           liveRoom.ChannelPasswd,
		SeminarHostPassword:     liveRoom.SeminarHostPassword,
		SeminarAttendeePassword: liveRoom.SeminarAttendeePassword,
		Status:                  liveRoom.Status,
	}

	global.GVA_LOG.Info("获取用户直播房间成功",
		zap.Uint("userID", userID),
		zap.Int64("channelId", liveRoom.ChannelId),
		zap.Uint("roomID", liveRoom.ID))

	return response, nil
}
