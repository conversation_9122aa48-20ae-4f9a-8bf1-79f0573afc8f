package router

import (
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/picturelibrary/api"
	"github.com/gin-gonic/gin"
)

type PictureLibraryRouter struct {
}

func (s *PictureLibraryRouter) InitPictureLibraryRouter(Router *gin.RouterGroup) {
	plugRouter := Router.Use()
	plugApi := api.ApiGroupApp.PictureLibraryApi
	{
		plugRouter.POST("/pic_library/list", plugApi.GetFileList)
		plugRouter.POST("/pic_library/cat_list", plugApi.GetCategoryList)
		plugRouter.POST("/pic_library/add_cat", plugApi.AddCategory)
		plugRouter.POST("/pic_library/upload_handler", plugApi.UploadHandler)
		plugRouter.POST("/pic_library/delete_file", plugApi.DeleteFile)
		plugRouter.POST("/pic_library/delete_cat", plugApi.DeleteCategory)
	}
}
