// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
)

func newJyhUserAccount(db *gorm.DB, opts ...gen.DOOption) jyhUserAccount {
	_jyhUserAccount := jyhUserAccount{}

	_jyhUserAccount.jyhUserAccountDo.UseDB(db, opts...)
	_jyhUserAccount.jyhUserAccountDo.UseModel(&jyhapp.JyhUserAccount{})

	tableName := _jyhUserAccount.jyhUserAccountDo.TableName()
	_jyhUserAccount.ALL = field.NewAsterisk(tableName)
	_jyhUserAccount.AccountID = field.NewUint(tableName, "account_id")
	_jyhUserAccount.UserID = field.NewUint(tableName, "user_id")
	_jyhUserAccount.DyBalance = field.NewField(tableName, "dy_balance")
	_jyhUserAccount.JyhBalance = field.NewField(tableName, "jyh_balance")
	_jyhUserAccount.WithdrawnBalance = field.NewField(tableName, "withdrawn_balance")
	_jyhUserAccount.CurrentBalance = field.NewField(tableName, "current_balance")
	_jyhUserAccount.Version = field.NewInt(tableName, "version")
	_jyhUserAccount.CreatedAt = field.NewTime(tableName, "created_at")
	_jyhUserAccount.UpdatedAt = field.NewTime(tableName, "updated_at")
	_jyhUserAccount.LastUpdatedAt = field.NewTime(tableName, "last_updated_at")
	_jyhUserAccount.Transactions = jyhUserAccountHasManyTransactions{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Transactions", "jyhapp.JyhUserAccountTransaction"),
		Account: struct {
			field.RelationField
			User struct {
				field.RelationField
				Inviter struct {
					field.RelationField
				}
				JyhUserExt struct {
					field.RelationField
				}
				Invitees struct {
					field.RelationField
				}
				Tags struct {
					field.RelationField
					Users struct {
						field.RelationField
					}
				}
			}
			Transactions struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Transactions.Account", "jyhapp.JyhUserAccount"),
			User: struct {
				field.RelationField
				Inviter struct {
					field.RelationField
				}
				JyhUserExt struct {
					field.RelationField
				}
				Invitees struct {
					field.RelationField
				}
				Tags struct {
					field.RelationField
					Users struct {
						field.RelationField
					}
				}
			}{
				RelationField: field.NewRelation("Transactions.Account.User", "jyhapp.JyhUser"),
				Inviter: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Transactions.Account.User.Inviter", "jyhapp.JyhUser"),
				},
				JyhUserExt: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Transactions.Account.User.JyhUserExt", "jyhapp.JyhUserExt"),
				},
				Invitees: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("Transactions.Account.User.Invitees", "jyhapp.JyhUser"),
				},
				Tags: struct {
					field.RelationField
					Users struct {
						field.RelationField
					}
				}{
					RelationField: field.NewRelation("Transactions.Account.User.Tags", "jyhapp.JyhUserTag"),
					Users: struct {
						field.RelationField
					}{
						RelationField: field.NewRelation("Transactions.Account.User.Tags.Users", "jyhapp.JyhUser"),
					},
				},
			},
			Transactions: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Transactions.Account.Transactions", "jyhapp.JyhUserAccountTransaction"),
			},
		},
	}

	_jyhUserAccount.User = jyhUserAccountBelongsToUser{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("User", "jyhapp.JyhUser"),
	}

	_jyhUserAccount.fillFieldMap()

	return _jyhUserAccount
}

type jyhUserAccount struct {
	jyhUserAccountDo

	ALL              field.Asterisk
	AccountID        field.Uint
	UserID           field.Uint
	DyBalance        field.Field
	JyhBalance       field.Field
	WithdrawnBalance field.Field
	CurrentBalance   field.Field
	Version          field.Int
	CreatedAt        field.Time
	UpdatedAt        field.Time
	LastUpdatedAt    field.Time
	Transactions     jyhUserAccountHasManyTransactions

	User jyhUserAccountBelongsToUser

	fieldMap map[string]field.Expr
}

func (j jyhUserAccount) Table(newTableName string) *jyhUserAccount {
	j.jyhUserAccountDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j jyhUserAccount) As(alias string) *jyhUserAccount {
	j.jyhUserAccountDo.DO = *(j.jyhUserAccountDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *jyhUserAccount) updateTableName(table string) *jyhUserAccount {
	j.ALL = field.NewAsterisk(table)
	j.AccountID = field.NewUint(table, "account_id")
	j.UserID = field.NewUint(table, "user_id")
	j.DyBalance = field.NewField(table, "dy_balance")
	j.JyhBalance = field.NewField(table, "jyh_balance")
	j.WithdrawnBalance = field.NewField(table, "withdrawn_balance")
	j.CurrentBalance = field.NewField(table, "current_balance")
	j.Version = field.NewInt(table, "version")
	j.CreatedAt = field.NewTime(table, "created_at")
	j.UpdatedAt = field.NewTime(table, "updated_at")
	j.LastUpdatedAt = field.NewTime(table, "last_updated_at")

	j.fillFieldMap()

	return j
}

func (j *jyhUserAccount) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *jyhUserAccount) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 12)
	j.fieldMap["account_id"] = j.AccountID
	j.fieldMap["user_id"] = j.UserID
	j.fieldMap["dy_balance"] = j.DyBalance
	j.fieldMap["jyh_balance"] = j.JyhBalance
	j.fieldMap["withdrawn_balance"] = j.WithdrawnBalance
	j.fieldMap["current_balance"] = j.CurrentBalance
	j.fieldMap["version"] = j.Version
	j.fieldMap["created_at"] = j.CreatedAt
	j.fieldMap["updated_at"] = j.UpdatedAt
	j.fieldMap["last_updated_at"] = j.LastUpdatedAt

}

func (j jyhUserAccount) clone(db *gorm.DB) jyhUserAccount {
	j.jyhUserAccountDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j jyhUserAccount) replaceDB(db *gorm.DB) jyhUserAccount {
	j.jyhUserAccountDo.ReplaceDB(db)
	return j
}

type jyhUserAccountHasManyTransactions struct {
	db *gorm.DB

	field.RelationField

	Account struct {
		field.RelationField
		User struct {
			field.RelationField
			Inviter struct {
				field.RelationField
			}
			JyhUserExt struct {
				field.RelationField
			}
			Invitees struct {
				field.RelationField
			}
			Tags struct {
				field.RelationField
				Users struct {
					field.RelationField
				}
			}
		}
		Transactions struct {
			field.RelationField
		}
	}
}

func (a jyhUserAccountHasManyTransactions) Where(conds ...field.Expr) *jyhUserAccountHasManyTransactions {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a jyhUserAccountHasManyTransactions) WithContext(ctx context.Context) *jyhUserAccountHasManyTransactions {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a jyhUserAccountHasManyTransactions) Session(session *gorm.Session) *jyhUserAccountHasManyTransactions {
	a.db = a.db.Session(session)
	return &a
}

func (a jyhUserAccountHasManyTransactions) Model(m *jyhapp.JyhUserAccount) *jyhUserAccountHasManyTransactionsTx {
	return &jyhUserAccountHasManyTransactionsTx{a.db.Model(m).Association(a.Name())}
}

type jyhUserAccountHasManyTransactionsTx struct{ tx *gorm.Association }

func (a jyhUserAccountHasManyTransactionsTx) Find() (result []*jyhapp.JyhUserAccountTransaction, err error) {
	return result, a.tx.Find(&result)
}

func (a jyhUserAccountHasManyTransactionsTx) Append(values ...*jyhapp.JyhUserAccountTransaction) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a jyhUserAccountHasManyTransactionsTx) Replace(values ...*jyhapp.JyhUserAccountTransaction) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a jyhUserAccountHasManyTransactionsTx) Delete(values ...*jyhapp.JyhUserAccountTransaction) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a jyhUserAccountHasManyTransactionsTx) Clear() error {
	return a.tx.Clear()
}

func (a jyhUserAccountHasManyTransactionsTx) Count() int64 {
	return a.tx.Count()
}

type jyhUserAccountBelongsToUser struct {
	db *gorm.DB

	field.RelationField
}

func (a jyhUserAccountBelongsToUser) Where(conds ...field.Expr) *jyhUserAccountBelongsToUser {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a jyhUserAccountBelongsToUser) WithContext(ctx context.Context) *jyhUserAccountBelongsToUser {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a jyhUserAccountBelongsToUser) Session(session *gorm.Session) *jyhUserAccountBelongsToUser {
	a.db = a.db.Session(session)
	return &a
}

func (a jyhUserAccountBelongsToUser) Model(m *jyhapp.JyhUserAccount) *jyhUserAccountBelongsToUserTx {
	return &jyhUserAccountBelongsToUserTx{a.db.Model(m).Association(a.Name())}
}

type jyhUserAccountBelongsToUserTx struct{ tx *gorm.Association }

func (a jyhUserAccountBelongsToUserTx) Find() (result *jyhapp.JyhUser, err error) {
	return result, a.tx.Find(&result)
}

func (a jyhUserAccountBelongsToUserTx) Append(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a jyhUserAccountBelongsToUserTx) Replace(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a jyhUserAccountBelongsToUserTx) Delete(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a jyhUserAccountBelongsToUserTx) Clear() error {
	return a.tx.Clear()
}

func (a jyhUserAccountBelongsToUserTx) Count() int64 {
	return a.tx.Count()
}

type jyhUserAccountDo struct{ gen.DO }

func (j jyhUserAccountDo) Debug() *jyhUserAccountDo {
	return j.withDO(j.DO.Debug())
}

func (j jyhUserAccountDo) WithContext(ctx context.Context) *jyhUserAccountDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jyhUserAccountDo) ReadDB() *jyhUserAccountDo {
	return j.Clauses(dbresolver.Read)
}

func (j jyhUserAccountDo) WriteDB() *jyhUserAccountDo {
	return j.Clauses(dbresolver.Write)
}

func (j jyhUserAccountDo) Session(config *gorm.Session) *jyhUserAccountDo {
	return j.withDO(j.DO.Session(config))
}

func (j jyhUserAccountDo) Clauses(conds ...clause.Expression) *jyhUserAccountDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jyhUserAccountDo) Returning(value interface{}, columns ...string) *jyhUserAccountDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jyhUserAccountDo) Not(conds ...gen.Condition) *jyhUserAccountDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jyhUserAccountDo) Or(conds ...gen.Condition) *jyhUserAccountDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jyhUserAccountDo) Select(conds ...field.Expr) *jyhUserAccountDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jyhUserAccountDo) Where(conds ...gen.Condition) *jyhUserAccountDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jyhUserAccountDo) Order(conds ...field.Expr) *jyhUserAccountDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jyhUserAccountDo) Distinct(cols ...field.Expr) *jyhUserAccountDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jyhUserAccountDo) Omit(cols ...field.Expr) *jyhUserAccountDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jyhUserAccountDo) Join(table schema.Tabler, on ...field.Expr) *jyhUserAccountDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jyhUserAccountDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jyhUserAccountDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jyhUserAccountDo) RightJoin(table schema.Tabler, on ...field.Expr) *jyhUserAccountDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jyhUserAccountDo) Group(cols ...field.Expr) *jyhUserAccountDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jyhUserAccountDo) Having(conds ...gen.Condition) *jyhUserAccountDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jyhUserAccountDo) Limit(limit int) *jyhUserAccountDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jyhUserAccountDo) Offset(offset int) *jyhUserAccountDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jyhUserAccountDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jyhUserAccountDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jyhUserAccountDo) Unscoped() *jyhUserAccountDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jyhUserAccountDo) Create(values ...*jyhapp.JyhUserAccount) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jyhUserAccountDo) CreateInBatches(values []*jyhapp.JyhUserAccount, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jyhUserAccountDo) Save(values ...*jyhapp.JyhUserAccount) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jyhUserAccountDo) First() (*jyhapp.JyhUserAccount, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserAccount), nil
	}
}

func (j jyhUserAccountDo) Take() (*jyhapp.JyhUserAccount, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserAccount), nil
	}
}

func (j jyhUserAccountDo) Last() (*jyhapp.JyhUserAccount, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserAccount), nil
	}
}

func (j jyhUserAccountDo) Find() ([]*jyhapp.JyhUserAccount, error) {
	result, err := j.DO.Find()
	return result.([]*jyhapp.JyhUserAccount), err
}

func (j jyhUserAccountDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*jyhapp.JyhUserAccount, err error) {
	buf := make([]*jyhapp.JyhUserAccount, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jyhUserAccountDo) FindInBatches(result *[]*jyhapp.JyhUserAccount, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jyhUserAccountDo) Attrs(attrs ...field.AssignExpr) *jyhUserAccountDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jyhUserAccountDo) Assign(attrs ...field.AssignExpr) *jyhUserAccountDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jyhUserAccountDo) Joins(fields ...field.RelationField) *jyhUserAccountDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jyhUserAccountDo) Preload(fields ...field.RelationField) *jyhUserAccountDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jyhUserAccountDo) FirstOrInit() (*jyhapp.JyhUserAccount, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserAccount), nil
	}
}

func (j jyhUserAccountDo) FirstOrCreate() (*jyhapp.JyhUserAccount, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserAccount), nil
	}
}

func (j jyhUserAccountDo) FindByPage(offset int, limit int) (result []*jyhapp.JyhUserAccount, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jyhUserAccountDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jyhUserAccountDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jyhUserAccountDo) Delete(models ...*jyhapp.JyhUserAccount) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jyhUserAccountDo) withDO(do gen.Dao) *jyhUserAccountDo {
	j.DO = *do.(*gen.DO)
	return j
}
