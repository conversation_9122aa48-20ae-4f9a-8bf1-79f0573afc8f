// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
)

func newJyhPointsRule(db *gorm.DB, opts ...gen.DOOption) jyhPointsRule {
	_jyhPointsRule := jyhPointsRule{}

	_jyhPointsRule.jyhPointsRuleDo.UseDB(db, opts...)
	_jyhPointsRule.jyhPointsRuleDo.UseModel(&jyhapp.JyhPointsRule{})

	tableName := _jyhPointsRule.jyhPointsRuleDo.TableName()
	_jyhPointsRule.ALL = field.NewAsterisk(tableName)
	_jyhPointsRule.ID = field.NewUint(tableName, "id")
	_jyhPointsRule.CreatedAt = field.NewTime(tableName, "created_at")
	_jyhPointsRule.UpdatedAt = field.NewTime(tableName, "updated_at")
	_jyhPointsRule.DeletedAt = field.NewField(tableName, "deleted_at")
	_jyhPointsRule.RuleKey = field.NewString(tableName, "rule_key")
	_jyhPointsRule.RuleName = field.NewString(tableName, "rule_name")
	_jyhPointsRule.Points = field.NewInt(tableName, "points")
	_jyhPointsRule.Description = field.NewString(tableName, "description")
	_jyhPointsRule.DailyLimit = field.NewInt(tableName, "daily_limit")
	_jyhPointsRule.TotalLimit = field.NewInt(tableName, "total_limit")
	_jyhPointsRule.IsEnabled = field.NewBool(tableName, "is_enabled")

	_jyhPointsRule.fillFieldMap()

	return _jyhPointsRule
}

type jyhPointsRule struct {
	jyhPointsRuleDo

	ALL         field.Asterisk
	ID          field.Uint
	CreatedAt   field.Time
	UpdatedAt   field.Time
	DeletedAt   field.Field
	RuleKey     field.String
	RuleName    field.String
	Points      field.Int
	Description field.String
	DailyLimit  field.Int
	TotalLimit  field.Int
	IsEnabled   field.Bool

	fieldMap map[string]field.Expr
}

func (j jyhPointsRule) Table(newTableName string) *jyhPointsRule {
	j.jyhPointsRuleDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j jyhPointsRule) As(alias string) *jyhPointsRule {
	j.jyhPointsRuleDo.DO = *(j.jyhPointsRuleDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *jyhPointsRule) updateTableName(table string) *jyhPointsRule {
	j.ALL = field.NewAsterisk(table)
	j.ID = field.NewUint(table, "id")
	j.CreatedAt = field.NewTime(table, "created_at")
	j.UpdatedAt = field.NewTime(table, "updated_at")
	j.DeletedAt = field.NewField(table, "deleted_at")
	j.RuleKey = field.NewString(table, "rule_key")
	j.RuleName = field.NewString(table, "rule_name")
	j.Points = field.NewInt(table, "points")
	j.Description = field.NewString(table, "description")
	j.DailyLimit = field.NewInt(table, "daily_limit")
	j.TotalLimit = field.NewInt(table, "total_limit")
	j.IsEnabled = field.NewBool(table, "is_enabled")

	j.fillFieldMap()

	return j
}

func (j *jyhPointsRule) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *jyhPointsRule) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 11)
	j.fieldMap["id"] = j.ID
	j.fieldMap["created_at"] = j.CreatedAt
	j.fieldMap["updated_at"] = j.UpdatedAt
	j.fieldMap["deleted_at"] = j.DeletedAt
	j.fieldMap["rule_key"] = j.RuleKey
	j.fieldMap["rule_name"] = j.RuleName
	j.fieldMap["points"] = j.Points
	j.fieldMap["description"] = j.Description
	j.fieldMap["daily_limit"] = j.DailyLimit
	j.fieldMap["total_limit"] = j.TotalLimit
	j.fieldMap["is_enabled"] = j.IsEnabled
}

func (j jyhPointsRule) clone(db *gorm.DB) jyhPointsRule {
	j.jyhPointsRuleDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j jyhPointsRule) replaceDB(db *gorm.DB) jyhPointsRule {
	j.jyhPointsRuleDo.ReplaceDB(db)
	return j
}

type jyhPointsRuleDo struct{ gen.DO }

func (j jyhPointsRuleDo) Debug() *jyhPointsRuleDo {
	return j.withDO(j.DO.Debug())
}

func (j jyhPointsRuleDo) WithContext(ctx context.Context) *jyhPointsRuleDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jyhPointsRuleDo) ReadDB() *jyhPointsRuleDo {
	return j.Clauses(dbresolver.Read)
}

func (j jyhPointsRuleDo) WriteDB() *jyhPointsRuleDo {
	return j.Clauses(dbresolver.Write)
}

func (j jyhPointsRuleDo) Session(config *gorm.Session) *jyhPointsRuleDo {
	return j.withDO(j.DO.Session(config))
}

func (j jyhPointsRuleDo) Clauses(conds ...clause.Expression) *jyhPointsRuleDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jyhPointsRuleDo) Returning(value interface{}, columns ...string) *jyhPointsRuleDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jyhPointsRuleDo) Not(conds ...gen.Condition) *jyhPointsRuleDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jyhPointsRuleDo) Or(conds ...gen.Condition) *jyhPointsRuleDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jyhPointsRuleDo) Select(conds ...field.Expr) *jyhPointsRuleDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jyhPointsRuleDo) Where(conds ...gen.Condition) *jyhPointsRuleDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jyhPointsRuleDo) Order(conds ...field.Expr) *jyhPointsRuleDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jyhPointsRuleDo) Distinct(cols ...field.Expr) *jyhPointsRuleDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jyhPointsRuleDo) Omit(cols ...field.Expr) *jyhPointsRuleDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jyhPointsRuleDo) Join(table schema.Tabler, on ...field.Expr) *jyhPointsRuleDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jyhPointsRuleDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jyhPointsRuleDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jyhPointsRuleDo) RightJoin(table schema.Tabler, on ...field.Expr) *jyhPointsRuleDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jyhPointsRuleDo) Group(cols ...field.Expr) *jyhPointsRuleDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jyhPointsRuleDo) Having(conds ...gen.Condition) *jyhPointsRuleDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jyhPointsRuleDo) Limit(limit int) *jyhPointsRuleDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jyhPointsRuleDo) Offset(offset int) *jyhPointsRuleDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jyhPointsRuleDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jyhPointsRuleDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jyhPointsRuleDo) Unscoped() *jyhPointsRuleDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jyhPointsRuleDo) Create(values ...*jyhapp.JyhPointsRule) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jyhPointsRuleDo) CreateInBatches(values []*jyhapp.JyhPointsRule, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jyhPointsRuleDo) Save(values ...*jyhapp.JyhPointsRule) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jyhPointsRuleDo) First() (*jyhapp.JyhPointsRule, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhPointsRule), nil
	}
}

func (j jyhPointsRuleDo) Take() (*jyhapp.JyhPointsRule, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhPointsRule), nil
	}
}

func (j jyhPointsRuleDo) Last() (*jyhapp.JyhPointsRule, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhPointsRule), nil
	}
}

func (j jyhPointsRuleDo) Find() ([]*jyhapp.JyhPointsRule, error) {
	result, err := j.DO.Find()
	return result.([]*jyhapp.JyhPointsRule), err
}

func (j jyhPointsRuleDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*jyhapp.JyhPointsRule, err error) {
	buf := make([]*jyhapp.JyhPointsRule, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jyhPointsRuleDo) FindInBatches(result *[]*jyhapp.JyhPointsRule, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jyhPointsRuleDo) Attrs(attrs ...field.AssignExpr) *jyhPointsRuleDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jyhPointsRuleDo) Assign(attrs ...field.AssignExpr) *jyhPointsRuleDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jyhPointsRuleDo) Joins(fields ...field.RelationField) *jyhPointsRuleDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jyhPointsRuleDo) Preload(fields ...field.RelationField) *jyhPointsRuleDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jyhPointsRuleDo) FirstOrInit() (*jyhapp.JyhPointsRule, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhPointsRule), nil
	}
}

func (j jyhPointsRuleDo) FirstOrCreate() (*jyhapp.JyhPointsRule, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhPointsRule), nil
	}
}

func (j jyhPointsRuleDo) FindByPage(offset int, limit int) (result []*jyhapp.JyhPointsRule, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jyhPointsRuleDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jyhPointsRuleDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jyhPointsRuleDo) Delete(models ...*jyhapp.JyhPointsRule) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jyhPointsRuleDo) withDO(do gen.Dao) *jyhPointsRuleDo {
	j.DO = *do.(*gen.DO)
	return j
}
