// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
)

func newJyhRecommendPosition(db *gorm.DB, opts ...gen.DOOption) jyhRecommendPosition {
	_jyhRecommendPosition := jyhRecommendPosition{}

	_jyhRecommendPosition.jyhRecommendPositionDo.UseDB(db, opts...)
	_jyhRecommendPosition.jyhRecommendPositionDo.UseModel(&jyhapp.JyhRecommendPosition{})

	tableName := _jyhRecommendPosition.jyhRecommendPositionDo.TableName()
	_jyhRecommendPosition.ALL = field.NewAsterisk(tableName)
	_jyhRecommendPosition.ID = field.NewUint(tableName, "id")
	_jyhRecommendPosition.CreatedAt = field.NewTime(tableName, "created_at")
	_jyhRecommendPosition.UpdatedAt = field.NewTime(tableName, "updated_at")
	_jyhRecommendPosition.DeletedAt = field.NewField(tableName, "deleted_at")
	_jyhRecommendPosition.Name = field.NewString(tableName, "name")
	_jyhRecommendPosition.PositionKey = field.NewString(tableName, "position_key")
	_jyhRecommendPosition.Description = field.NewString(tableName, "description")
	_jyhRecommendPosition.Width = field.NewInt(tableName, "width")
	_jyhRecommendPosition.Height = field.NewInt(tableName, "height")
	_jyhRecommendPosition.IsEnabled = field.NewBool(tableName, "is_enabled")
	_jyhRecommendPosition.MaxItems = field.NewInt(tableName, "max_items")
	_jyhRecommendPosition.Items = jyhRecommendPositionHasManyItems{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Items", "jyhapp.JyhRecommendItem"),
		Position: struct {
			field.RelationField
			Items struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Items.Position", "jyhapp.JyhRecommendPosition"),
			Items: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Items.Position.Items", "jyhapp.JyhRecommendItem"),
			},
		},
	}

	_jyhRecommendPosition.fillFieldMap()

	return _jyhRecommendPosition
}

type jyhRecommendPosition struct {
	jyhRecommendPositionDo

	ALL         field.Asterisk
	ID          field.Uint
	CreatedAt   field.Time
	UpdatedAt   field.Time
	DeletedAt   field.Field
	Name        field.String
	PositionKey field.String
	Description field.String
	Width       field.Int
	Height      field.Int
	IsEnabled   field.Bool
	MaxItems    field.Int
	Items       jyhRecommendPositionHasManyItems

	fieldMap map[string]field.Expr
}

func (j jyhRecommendPosition) Table(newTableName string) *jyhRecommendPosition {
	j.jyhRecommendPositionDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j jyhRecommendPosition) As(alias string) *jyhRecommendPosition {
	j.jyhRecommendPositionDo.DO = *(j.jyhRecommendPositionDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *jyhRecommendPosition) updateTableName(table string) *jyhRecommendPosition {
	j.ALL = field.NewAsterisk(table)
	j.ID = field.NewUint(table, "id")
	j.CreatedAt = field.NewTime(table, "created_at")
	j.UpdatedAt = field.NewTime(table, "updated_at")
	j.DeletedAt = field.NewField(table, "deleted_at")
	j.Name = field.NewString(table, "name")
	j.PositionKey = field.NewString(table, "position_key")
	j.Description = field.NewString(table, "description")
	j.Width = field.NewInt(table, "width")
	j.Height = field.NewInt(table, "height")
	j.IsEnabled = field.NewBool(table, "is_enabled")
	j.MaxItems = field.NewInt(table, "max_items")

	j.fillFieldMap()

	return j
}

func (j *jyhRecommendPosition) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *jyhRecommendPosition) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 12)
	j.fieldMap["id"] = j.ID
	j.fieldMap["created_at"] = j.CreatedAt
	j.fieldMap["updated_at"] = j.UpdatedAt
	j.fieldMap["deleted_at"] = j.DeletedAt
	j.fieldMap["name"] = j.Name
	j.fieldMap["position_key"] = j.PositionKey
	j.fieldMap["description"] = j.Description
	j.fieldMap["width"] = j.Width
	j.fieldMap["height"] = j.Height
	j.fieldMap["is_enabled"] = j.IsEnabled
	j.fieldMap["max_items"] = j.MaxItems

}

func (j jyhRecommendPosition) clone(db *gorm.DB) jyhRecommendPosition {
	j.jyhRecommendPositionDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j jyhRecommendPosition) replaceDB(db *gorm.DB) jyhRecommendPosition {
	j.jyhRecommendPositionDo.ReplaceDB(db)
	return j
}

type jyhRecommendPositionHasManyItems struct {
	db *gorm.DB

	field.RelationField

	Position struct {
		field.RelationField
		Items struct {
			field.RelationField
		}
	}
}

func (a jyhRecommendPositionHasManyItems) Where(conds ...field.Expr) *jyhRecommendPositionHasManyItems {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a jyhRecommendPositionHasManyItems) WithContext(ctx context.Context) *jyhRecommendPositionHasManyItems {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a jyhRecommendPositionHasManyItems) Session(session *gorm.Session) *jyhRecommendPositionHasManyItems {
	a.db = a.db.Session(session)
	return &a
}

func (a jyhRecommendPositionHasManyItems) Model(m *jyhapp.JyhRecommendPosition) *jyhRecommendPositionHasManyItemsTx {
	return &jyhRecommendPositionHasManyItemsTx{a.db.Model(m).Association(a.Name())}
}

type jyhRecommendPositionHasManyItemsTx struct{ tx *gorm.Association }

func (a jyhRecommendPositionHasManyItemsTx) Find() (result []*jyhapp.JyhRecommendItem, err error) {
	return result, a.tx.Find(&result)
}

func (a jyhRecommendPositionHasManyItemsTx) Append(values ...*jyhapp.JyhRecommendItem) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a jyhRecommendPositionHasManyItemsTx) Replace(values ...*jyhapp.JyhRecommendItem) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a jyhRecommendPositionHasManyItemsTx) Delete(values ...*jyhapp.JyhRecommendItem) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a jyhRecommendPositionHasManyItemsTx) Clear() error {
	return a.tx.Clear()
}

func (a jyhRecommendPositionHasManyItemsTx) Count() int64 {
	return a.tx.Count()
}

type jyhRecommendPositionDo struct{ gen.DO }

func (j jyhRecommendPositionDo) Debug() *jyhRecommendPositionDo {
	return j.withDO(j.DO.Debug())
}

func (j jyhRecommendPositionDo) WithContext(ctx context.Context) *jyhRecommendPositionDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jyhRecommendPositionDo) ReadDB() *jyhRecommendPositionDo {
	return j.Clauses(dbresolver.Read)
}

func (j jyhRecommendPositionDo) WriteDB() *jyhRecommendPositionDo {
	return j.Clauses(dbresolver.Write)
}

func (j jyhRecommendPositionDo) Session(config *gorm.Session) *jyhRecommendPositionDo {
	return j.withDO(j.DO.Session(config))
}

func (j jyhRecommendPositionDo) Clauses(conds ...clause.Expression) *jyhRecommendPositionDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jyhRecommendPositionDo) Returning(value interface{}, columns ...string) *jyhRecommendPositionDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jyhRecommendPositionDo) Not(conds ...gen.Condition) *jyhRecommendPositionDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jyhRecommendPositionDo) Or(conds ...gen.Condition) *jyhRecommendPositionDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jyhRecommendPositionDo) Select(conds ...field.Expr) *jyhRecommendPositionDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jyhRecommendPositionDo) Where(conds ...gen.Condition) *jyhRecommendPositionDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jyhRecommendPositionDo) Order(conds ...field.Expr) *jyhRecommendPositionDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jyhRecommendPositionDo) Distinct(cols ...field.Expr) *jyhRecommendPositionDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jyhRecommendPositionDo) Omit(cols ...field.Expr) *jyhRecommendPositionDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jyhRecommendPositionDo) Join(table schema.Tabler, on ...field.Expr) *jyhRecommendPositionDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jyhRecommendPositionDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jyhRecommendPositionDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jyhRecommendPositionDo) RightJoin(table schema.Tabler, on ...field.Expr) *jyhRecommendPositionDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jyhRecommendPositionDo) Group(cols ...field.Expr) *jyhRecommendPositionDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jyhRecommendPositionDo) Having(conds ...gen.Condition) *jyhRecommendPositionDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jyhRecommendPositionDo) Limit(limit int) *jyhRecommendPositionDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jyhRecommendPositionDo) Offset(offset int) *jyhRecommendPositionDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jyhRecommendPositionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jyhRecommendPositionDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jyhRecommendPositionDo) Unscoped() *jyhRecommendPositionDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jyhRecommendPositionDo) Create(values ...*jyhapp.JyhRecommendPosition) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jyhRecommendPositionDo) CreateInBatches(values []*jyhapp.JyhRecommendPosition, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jyhRecommendPositionDo) Save(values ...*jyhapp.JyhRecommendPosition) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jyhRecommendPositionDo) First() (*jyhapp.JyhRecommendPosition, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhRecommendPosition), nil
	}
}

func (j jyhRecommendPositionDo) Take() (*jyhapp.JyhRecommendPosition, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhRecommendPosition), nil
	}
}

func (j jyhRecommendPositionDo) Last() (*jyhapp.JyhRecommendPosition, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhRecommendPosition), nil
	}
}

func (j jyhRecommendPositionDo) Find() ([]*jyhapp.JyhRecommendPosition, error) {
	result, err := j.DO.Find()
	return result.([]*jyhapp.JyhRecommendPosition), err
}

func (j jyhRecommendPositionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*jyhapp.JyhRecommendPosition, err error) {
	buf := make([]*jyhapp.JyhRecommendPosition, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jyhRecommendPositionDo) FindInBatches(result *[]*jyhapp.JyhRecommendPosition, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jyhRecommendPositionDo) Attrs(attrs ...field.AssignExpr) *jyhRecommendPositionDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jyhRecommendPositionDo) Assign(attrs ...field.AssignExpr) *jyhRecommendPositionDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jyhRecommendPositionDo) Joins(fields ...field.RelationField) *jyhRecommendPositionDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jyhRecommendPositionDo) Preload(fields ...field.RelationField) *jyhRecommendPositionDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jyhRecommendPositionDo) FirstOrInit() (*jyhapp.JyhRecommendPosition, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhRecommendPosition), nil
	}
}

func (j jyhRecommendPositionDo) FirstOrCreate() (*jyhapp.JyhRecommendPosition, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhRecommendPosition), nil
	}
}

func (j jyhRecommendPositionDo) FindByPage(offset int, limit int) (result []*jyhapp.JyhRecommendPosition, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jyhRecommendPositionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jyhRecommendPositionDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jyhRecommendPositionDo) Delete(models ...*jyhapp.JyhRecommendPosition) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jyhRecommendPositionDo) withDO(do gen.Dao) *jyhRecommendPositionDo {
	j.DO = *do.(*gen.DO)
	return j
}
