package jyhapp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/shopspring/decimal"
	"gorm.io/datatypes"
)

// 邀请码类型常量
const (
	InviteCodeTypeUser     = "user"     // 用户生成的邀请码
	InviteCodeTypePlatform = "platform" // 平台生成的邀请码
)

// 邀请码状态常量
const (
	InviteCodeStatusUnused  = 0 // 未使用
	InviteCodeStatusUsed    = 1 // 已使用
	InviteCodeStatusExpired = 2 // 已过期
)

// JyhUser 用户表
type JyhUser struct {
	global.GVA_MODEL
	Username  string `gorm:"type:varchar(150);not null;comment:用户名" json:"user_name"`
	Phone     string `gorm:"type:varchar(20);not null;unique;comment:手机号" json:"phone"`
	Password  string `gorm:"type:varchar(255);not null;comment:密码" json:"password"`
	InvitedBy uint   `gorm:"index;comment:邀请人ID" json:"invited_by"`
	IsAgent   bool   `gorm:"default:false;comment:是否为代理" json:"is_agent"`

	Avatar       string    `gorm:"type:varchar(255);comment:头像" json:"avatar"`
	RegisteredAt time.Time `gorm:"type:DATETIME;not null;default:CURRENT_TIMESTAMP;comment:注册时间" json:"registered_at"`
	LastLoginAt  time.Time `gorm:"type:DATETIME;not null;default:CURRENT_TIMESTAMP;comment:最后登录时间" json:"last_login_at"`
	Status       uint      `gorm:"not null;default:1;comment:状态（1-正常，0-禁用）" json:"status"`
	UserType     string    `gorm:"type:enum('user','admin');default:'user';comment:用户类型：用户还是管理员" json:"user_type"`
	// 邀请码
	InviteCode     string         `gorm:"type:varchar(36);unique;comment:邀请码" json:"invite_code"`
	ContractStatus int            `gorm:"type:int(4);comment:签约状态 0 未签约 1 签约成功;default:0" json:"contract_status"`
	CanLiveStream  bool           `gorm:"default:false;comment:是否可以直播" json:"can_live_stream"`
	Ext            datatypes.JSON `gorm:"type:json;comment:扩展信息" json:"ext"`
	DouYinExt      datatypes.JSON `gorm:"type:json;comment:抖音扩展信息" json:"douyin_ext"`
	DouYinId       string         `gorm:"type:varchar(100);comment:抖音ID" json:"douyin_id"`
	// 用户积分
	Points int `gorm:"default:0;comment:用户积分" json:"points"` // 用户积分

	// 关联关系
	Inviter    *JyhUser     `gorm:"foreignKey:InvitedBy" json:"inviter"`  // 邀请人
	Invitees   []JyhUser    `gorm:"foreignKey:InvitedBy" json:"invitees"` // 直接邀请的下级
	JyhUserExt *JyhUserExt  `gorm:"foreignKey:UserID" json:"user_ext"`    // 用户扩展信息
	Tags       []JyhUserTag `json:"tags" gorm:"many2many:jyh_user_tag_relation;joinForeignKey:user_id"`
}

// TableName JyhUser 表名
func (JyhUser) TableName() string {
	return "jyh_user"
}

// JyhUserClosure 代理关系闭包表
type JyhUserClosure struct {
	AncestorID   uint `gorm:"primaryKey;index;comment:祖先ID" json:"ancestorID"`
	DescendantID uint `gorm:"primaryKey;index;comment:后代ID" json:"descendantID"`
	Depth        uint `gorm:"not null;index;comment:层级深度" json:"depth"`

	// 关联关系（可选）
	Ancestor   JyhUser `gorm:"foreignKey:AncestorID" json:"ancestor"`
	Descendant JyhUser `gorm:"foreignKey:DescendantID" json:"descendant"`
}

// TableName JyhUser 表名
func (JyhUserClosure) TableName() string {
	return "jyh_user_closure"
}

// JyhInviteCode 邀请码表
type JyhInviteCode struct {
	global.GVA_MODEL
	Type      string     `gorm:"type:varchar(20);not null;default:'user';index;comment:邀请码类型：user-用户生成 platform-平台生成" json:"type"`
	UserID    *uint      `gorm:"index;comment:生成邀请码的用户ID（平台生成时为空）" json:"user_id"`
	Code      string     `gorm:"type:varchar(36);unique;not null;comment:唯一邀请码" json:"code"`
	LevelID   uint       `gorm:"type:int unsigned;not null;index;comment:会员等级ID" json:"level_id"`
	SalePrice uint64     `gorm:"type:bigint unsigned;not null;default:0;comment:出售价格(单位分)" json:"sale_price"`
	UsedAt    *time.Time `gorm:"type:datetime;comment:使用时间" json:"used_at"`
	UsedByUID *uint      `gorm:"type:int unsigned;index;comment:使用者用户ID" json:"used_by_uid"`
	Status    int        `gorm:"type:int unsigned;not null;default:0;comment:状态：0 未使用 1 已使用 2 已过期" json:"status"`
	IsUsed    bool       `gorm:"default:false;comment:是否已被使用" json:"is_used"`
	ExpiredAt *time.Time `gorm:"type:datetime;index;comment:过期时间" json:"expired_at"`

	// 关联关系
	User JyhUser `gorm:"foreignKey:UserID" json:"user"`
}

// TableName JyhUser 表名
func (JyhInviteCode) TableName() string {
	return "jyh_invite_code"
}

// JyhInviteRecord 邀请记录表
type JyhInviteRecord struct {
	global.GVA_MODEL
	InviterID uint   `gorm:"not null;index;comment:邀请人ID" json:"inviterID"`
	InviteeID uint   `gorm:"not null;uniqueIndex;comment:被邀请人ID" json:"inviteeID"`
	CodeUsed  string `gorm:"type:varchar(36);not null;comment:使用的邀请码" json:"codeUsed"`

	// 关联关系
	Inviter JyhUser `gorm:"foreignKey:InviterID" json:"inviter"`
	Invitee JyhUser `gorm:"foreignKey:InviteeID" json:"invitee"`
}

// TableName JyhInviteRecord 表名
func (JyhInviteRecord) TableName() string {
	return "jyh_invite_record"
}

// JyhUserExt 用户扩展信息表
type JyhUserExt struct {
	UserID     uint   `gorm:"not null;index:uidcard,unique;index:user_id;comment:用户ID" json:"userId"`
	IdCard     string `gorm:"type:varchar(18);index:uidcard,unique;comment:身份证号" json:"idCard"`
	Age        int    `gorm:"not null;comment:年龄" json:"age"`
	Sex        string `gorm:"type:enum('男','女');default:'男'" json:"status"`
	City       string `gorm:"type:varchar(100);comment:城市" json:"city"`
	Occupation string `gorm:"type:varchar(100);comment:职业" json:"occupation"`
	Address    string `gorm:"type:varchar(255);comment:收货地址" json:"address"`

	// 社交媒体信息
	WeChat         string `gorm:"type:varchar(100);comment:微信号" json:"weChat"`
	WeChatNickname string `gorm:"type:varchar(100);comment:微信昵称" json:"weChatNickname"`
	DouYin         string `gorm:"type:varchar(100);comment:抖音号" json:"douYin"`
	DouYinNickname string `gorm:"type:varchar(100);comment:抖音昵称" json:"douYinNickname"`
	DouYinPhone    string `gorm:"type:varchar(20);comment:抖音绑定手机号" json:"douYinPhone"`
	QQ             string `gorm:"type:varchar(20);comment:QQ号" json:"qq"`

	// 是否挂车
	TrackType    int       `gorm:"not null;default:1;comment:测赛道（1 挂车，2 拉流）" json:"trackType"`
	IsCarOwner   bool      `gorm:"default:false;comment:是否挂车" json:"isCarOwner"`
	CarOwnerTime time.Time `gorm:"comment:挂车时间" json:"carOwnerTime"`

	// 粉丝数， 有效粉丝数
	FansCount      int `gorm:"default:0;comment:粉丝数" json:"fansCount"`
	ValidFansCount int `gorm:"default:0;comment:有效粉丝数" json:"validFansCount"`
}

// TableName JyhUserExt 表名
func (JyhUserExt) TableName() string {
	return "jyh_user_ext"
}

// JyhUserLevel 用户-会员等级关系
type JyhUserLevel struct {
	Model1
	UserID  uint      `gorm:"type:int(11) unsigned;not null;index;comment:用户ID" json:"userId"`
	LevelID uint      `gorm:"type:int(11) unsigned;not null;index;comment:会员等级ID" json:"levelId"`
	OrderID *uint     `gorm:"type:int(11) unsigned;index;comment:对应的订单/兑换码记录，可空" json:"orderId"`
	StartAt time.Time `gorm:"type:datetime;not null;comment:生效时间" json:"startAt"`
	EndAt   time.Time `gorm:"type:datetime;not null;comment:过期时间" json:"endAt"`
	Status  string    `gorm:"type:varchar(20);not null;default:'active';comment:状态：active-有效 expired-过期 cancelled-取消" json:"status"`
}

// TableName JyhUserLevel 表名
func (JyhUserLevel) TableName() string {
	return "jyh_user_level"
}

// JyhUserBenefitSnapshot 用户-权益快照
type JyhUserBenefitSnapshot struct {
	Model1
	UserID      uint            `gorm:"type:int(11) unsigned;not null;index;comment:用户ID" json:"userId"`
	UserLevelID uint            `gorm:"type:int(11) unsigned;not null;index;comment:对应 jyh_user_level.id" json:"userLevelId"`
	LevelID     uint            `gorm:"type:int(11) unsigned;not null;index;comment:来源会员等级ID" json:"levelId"`
	BenefitID   uint            `gorm:"type:int(11) unsigned;not null;index;comment:权益类型ID" json:"benefitId"`
	Value       decimal.Decimal `gorm:"type:decimal(10,4);not null;comment:具体权益值" json:"value"`
	Condition   datatypes.JSON  `gorm:"type:json;comment:触发条件(复制自模板)" json:"condition"`
	StartAt     time.Time       `gorm:"type:datetime;not null;comment:生效时间" json:"startAt"`
	EndAt       time.Time       `gorm:"type:datetime;not null;comment:失效时间" json:"endAt"`

	// 冗余字段，避免 JOIN
	BenefitKey  string `gorm:"type:varchar(64);not null;comment:冗余: jyh_user_benefit.key" json:"benefitKey"`
	BenefitName string `gorm:"type:varchar(100);not null;comment:冗余: jyh_user_benefit.name" json:"benefitName"`
}

// TableName JyhUserBenefitSnapshot 表名
func (JyhUserBenefitSnapshot) TableName() string {
	return "jyh_user_benefit_snapshot"
}

// JyhUserShipLevel 会员用户等级
type JyhUserShipLevel struct {
	global.GVA_MODEL
	// 等级归属
	Name         string `gorm:"type:varchar(50);not null;uniqueIndex;comment:会员等级名称" json:"name"`
	Code         string `gorm:"type:varchar(32);not null;uniqueIndex;comment:内部唯一编码, 如 JH, CG" json:"code"`
	Icon         string `gorm:"type:varchar(255);comment:图标" json:"icon"` //图标
	PriceCents   uint64 `gorm:"type:bigint(20) unsigned;not null;default:0;comment:官方售价(单位分)" json:"priceCents"`
	DurationDays uint   `gorm:"type:int(10) unsigned;not null;default:0;comment:有效期(天)，0 表示永久" json:"durationDays"`
	Sort         int    `gorm:"type:int(11);not null;default:0;comment:前端展示排序(越小越靠前)" json:"sort"`
	Description  string `gorm:"type:text;comment:等级描述" json:"description"`
	Hide         bool   `gorm:"default:false;comment:是否在前端隐藏该等级" json:"hide"`
	// 自己所辖团队所有达人的出单佣金的8%+分成比例
	CommissionRate decimal.Decimal `gorm:"type:decimal(10,4);not null;default:0.0000;comment:团队佣金分成比例(如 0.08 表示 8%，0.00 表示不参与分成)" json:"commissionRate"`
	// 除自己团队外全平台达人出单佣金的0.5%平分，佣金分成比例，
	PlatformCommissionRate decimal.Decimal           `gorm:"type:decimal(10,4);not null;default:0.0000;comment:平台佣金分成比例(如 0.005 表示 0.5%，0.00 表示不参与分成)" json:"platformCommissionRate"`
	Benefits               []JyhUserShipLevelBenefit `gorm:"foreignKey:LevelID" json:"benefits"`
}

// TableName JyhUserShipLevel 表名
func (JyhUserShipLevel) TableName() string {
	return "jyh_user_ship_level"
}

// JyhUserBenefit 会员权益
type JyhUserBenefit struct {
	Model1
	Key         string `gorm:"type:varchar(64);not null;uniqueIndex;comment:唯一键,如 window_commission_rate" json:"key"`
	Name        string `gorm:"type:varchar(100);not null;comment:权益名称" json:"name"`
	Description string `gorm:"type:text;comment:权益描述" json:"description"`
}

// TableName JyhUserBenefit 表名
func (JyhUserBenefit) TableName() string {
	return "jyh_user_benefit"
}

// JyhUserShipLevelBenefit 会员权益映射
type JyhUserShipLevelBenefit struct {
	Model1
	LevelID   uint            `gorm:"type:int(11) unsigned;not null;index;comment:所属会员等级ID" json:"levelId"`
	BenefitID uint            `gorm:"type:int(11) unsigned;not null;index;comment:权益类型ID" json:"benefitId"`
	Value     decimal.Decimal `gorm:"type:decimal(10,4);not null;comment:权益值 (如 0.60 表示 60%)" json:"value"`
	Condition datatypes.JSON  `gorm:"type:json;comment:附加条件, 结构自定义" json:"condition"`
}

// TableName JyhUserShipLevelBenefit 表名
func (JyhUserShipLevelBenefit) TableName() string {
	return "jyh_user_ship_level_benefit"
}

// PromotionCode 推荐码/兑换码
type PromotionCode struct {
	Model1
	Code      string     `gorm:"type:varchar(64);not null;uniqueIndex;comment:用户购买获得的兑换码" json:"code"`
	LevelID   uint       `gorm:"type:int(11) unsigned;not null;index;comment:对应会员等级" json:"levelId"`
	SalePrice uint64     `gorm:"type:int(11) unsigned;not null;default:0;comment:出售价格(分)" json:"salePrice"`
	UsedByUID *uint      `gorm:"type:int(11) unsigned;index;comment:使用者用户ID, nil 未使用" json:"usedByUid"`
	Status    string     `gorm:"type:varchar(16);not null;default:'unused';comment:状态：unused-未使用 used-已使用 expired-已过期" json:"status"`
	ExpiredAt *time.Time `gorm:"type:datetime;comment:过期时间" json:"expiredAt"`
}

// TableName PromotionCode 表名
func (PromotionCode) TableName() string {
	return "jyh_promotion_code"
}

type JyhUserCertificate struct {
	ID              uint       `gorm:"primaryKey;autoIncrement" json:"id"`
	UserID          uint       `gorm:"not null;index" json:"user_id"`
	CertificateFile string     `gorm:"type:varchar(255)" json:"certificateFile"`   // 凭证文件
	FansCount       int        `gorm:"not null;default:0" json:"fans_count"`       // 粉丝数
	ValidFansCount  int        `gorm:"not null;default:0" json:"valid_fans_count"` // 有效粉丝数
	Description     string     `gorm:"type:text" json:"description"`               // 凭证描述
	Status          string     `gorm:"type:enum('pending','approved','rejected');default:'pending'" json:"status"`
	Remark          string     `gorm:"type:varchar(500)" json:"remark"` // 审核备注
	CreatedAt       time.Time  `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt       time.Time  `gorm:"autoUpdateTime" json:"updated_at"`
	ReviewerID      *uint      `gorm:"index" json:"reviewer_id"` // 审核人ID
	ReviewedAt      *time.Time `gorm:"index" json:"reviewed_at"` // 审核时间

	// 关联关系
	User     JyhUser        `gorm:"foreignKey:UserID" json:"user"`
	Reviewer system.SysUser `gorm:"foreignKey:ReviewerID" json:"reviewer"`
}

// TableName JyhUserCertificate 表名
func (JyhUserCertificate) TableName() string {
	return "jyh_user_certificate"
}
