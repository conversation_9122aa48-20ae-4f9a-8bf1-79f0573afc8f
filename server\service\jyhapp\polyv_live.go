package jyhapp

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"time"

	jyhResp "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/response"

	// 改为 RSA 非对称加密相关包
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/base64"
	"encoding/pem"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"go.uber.org/zap"
)

// 保利威API常量
const (
	// API基础URL
	PolyvBaseURL = "https://api.polyv.net/live/"

	EndpointChannelDetailList = "v4/channel/basic/list" // 获取频道基础信息列表
	EndpointChannelCreate     = "v4/channel/create"     // 创建频道
)

// RSA 密钥长度常量
const (
	RSAKeySize = 2048 // RSA密钥长度
)

// PolyvLiveService 保利威直播服务
type PolyvLiveService struct {
	AppId       string // 应用ID
	SecretKey   string // 应用密钥
	UserId      string //开发者id
	BaseURL     string // API基础URL
	PrivateKey  string // RSA私钥（PEM格式）
	PrivateBase string // RSA私钥（base格式）
	PublicKey   string // RSA公钥（PEM格式）
}

// NewPolyvLiveService 创建保利威直播服务实例
func NewPolyvLiveService(appId, secretKey, userId, privateKey, publicKey string) *PolyvLiveService {
	service := &PolyvLiveService{
		AppId:       appId,
		SecretKey:   secretKey,
		UserId:      userId,
		BaseURL:     PolyvBaseURL,
		PrivateKey:  privateKey,
		PrivateBase: privateKey,
		PublicKey:   publicKey,
	}

	// 如果密钥是base64格式，则解码为PEM格式
	if privateKey != "" {
		keyBytes, err := base64.StdEncoding.DecodeString(privateKey)
		if err != nil {
			global.GVA_LOG.Warn("私钥base64解码失败，使用原始格式", zap.Error(err))
		}
		service.PrivateKey = string(keyBytes)
	}

	if publicKey != "" {
		keyBytes, err := base64.StdEncoding.DecodeString(publicKey)
		if err == nil {
			global.GVA_LOG.Warn("公钥base64解码失败，使用原始格式", zap.Error(err))
		}
		service.PublicKey = string(keyBytes)
	}

	return service
}

// decodeBase64ToPEM 将base64编码的密钥解码为PEM格式
func (s *PolyvLiveService) decodeBase64ToPEM(base64Key, keyType string) (string, error) {
	// 如果已经是PEM格式，直接返回
	if strings.Contains(base64Key, "-----BEGIN") {
		return base64Key, nil
	}

	// Base64解码
	keyBytes, err := base64.StdEncoding.DecodeString(base64Key)
	if err != nil {
		return "", fmt.Errorf("base64解码失败: %v", err)
	}

	// 根据密钥类型构建PEM格式
	var pemType string
	switch keyType {
	case "PRIVATE":
		pemType = "RSA PRIVATE KEY"
	case "PUBLIC":
		pemType = "PUBLIC KEY"
	default:
		return "", fmt.Errorf("不支持的密钥类型: %s", keyType)
	}

	// 构建PEM块
	pemBlock := &pem.Block{
		Type:  pemType,
		Bytes: keyBytes,
	}

	return string(pem.EncodeToMemory(pemBlock)), nil
}

// encodePEMToBase64 将PEM格式的密钥编码为base64格式（用于存储到配置文件）
func (s *PolyvLiveService) encodePEMToBase64(pemKey string) (string, error) {
	// 解析PEM块
	block, _ := pem.Decode([]byte(pemKey))
	if block == nil {
		return "", fmt.Errorf("解析PEM格式失败")
	}

	// Base64编码
	return base64.StdEncoding.EncodeToString(block.Bytes), nil
}

// GetBase64Keys 获取base64格式的密钥对（用于保存到配置文件）
func (s *PolyvLiveService) GetBase64Keys() (privateKeyBase64, publicKeyBase64 string, err error) {
	if s.PrivateKey == "" || s.PublicKey == "" {
		return "", "", fmt.Errorf("密钥对未初始化")
	}

	privateKeyBase64, err = s.encodePEMToBase64(s.PrivateKey)
	if err != nil {
		return "", "", fmt.Errorf("编码私钥失败: %v", err)
	}

	publicKeyBase64, err = s.encodePEMToBase64(s.PublicKey)
	if err != nil {
		return "", "", fmt.Errorf("编码公钥失败: %v", err)
	}

	return privateKeyBase64, publicKeyBase64, nil
}

// GenerateRSAKeyPair 生成RSA密钥对
func (s *PolyvLiveService) GenerateRSAKeyPair() (privateKeyPEM, publicKeyPEM string, err error) {
	// 生成私钥
	privateKey, err := rsa.GenerateKey(rand.Reader, RSAKeySize)
	if err != nil {
		return "", "", fmt.Errorf("生成RSA私钥失败: %v", err)
	}

	// 编码私钥为PEM格式
	privateKeyDER := x509.MarshalPKCS1PrivateKey(privateKey)
	privateKeyBlock := &pem.Block{
		Type:  "RSA PRIVATE KEY",
		Bytes: privateKeyDER,
	}
	privateKeyPEM = string(pem.EncodeToMemory(privateKeyBlock))

	// 编码公钥为PEM格式
	publicKey := &privateKey.PublicKey
	publicKeyDER, err := x509.MarshalPKIXPublicKey(publicKey)
	if err != nil {
		return "", "", fmt.Errorf("编码RSA公钥失败: %v", err)
	}
	publicKeyBlock := &pem.Block{
		Type:  "PUBLIC KEY",
		Bytes: publicKeyDER,
	}
	publicKeyPEM = string(pem.EncodeToMemory(publicKeyBlock))

	return privateKeyPEM, publicKeyPEM, nil
}

// PolyvAuthInfo 保利威认证信息
type PolyvAuthInfo struct {
	AppId     string `json:"appId"`     // 应用ID
	SecretKey string `json:"secretKey"` // 应用密钥
	UserId    string `json:"userId"`    //开发者id
	Timestamp int64  `json:"timestamp"` // 时间戳，用于防重放攻击
}

// encryptAuthInfo 使用RSA私钥加密认证信息
func (s *PolyvLiveService) EncryptAuthInfo() (*jyhResp.EncryptedAuthInfo, error) {
	// 构建认证信息
	authInfo := PolyvAuthInfo{
		AppId:     s.AppId,
		SecretKey: s.SecretKey,
		UserId:    s.UserId,
		Timestamp: time.Now().Unix(),
	}

	// 序列化为JSON
	jsonData, err := json.Marshal(authInfo)
	if err != nil {
		return nil, fmt.Errorf("序列化认证信息失败: %v", err)
	}

	// RSA加密
	encryptedData, err := s.rsaEncrypt(jsonData)
	if err != nil {
		return nil, fmt.Errorf("加密认证信息失败: %v", err)
	}
	return &jyhResp.EncryptedAuthInfo{
		Data:       encryptedData,
		PrivateKey: s.PrivateBase,
		Timestamp:  authInfo.Timestamp,
	}, nil
}

// DecryptAuthInfo 使用RSA公钥解密认证信息（主要用于测试）
func (s *PolyvLiveService) DecryptAuthInfo(encryptedData string) (*PolyvAuthInfo, error) {
	// RSA解密
	decryptedData, err := s.rsaDecrypt(encryptedData)
	if err != nil {
		return nil, fmt.Errorf("解密认证信息失败: %v", err)
	}

	// 反序列化JSON
	var authInfo PolyvAuthInfo
	if err := json.Unmarshal(decryptedData, &authInfo); err != nil {
		return nil, fmt.Errorf("反序列化认证信息失败: %v", err)
	}

	// 检查时间戳，防止重放攻击（30分钟有效期）
	if time.Now().Unix()-authInfo.Timestamp > 1800 {
		return nil, fmt.Errorf("认证信息已过期")
	}

	return &authInfo, nil
}

// rsaEncrypt 使用RSA公钥加密（服务端加密，前端用私钥解密）
func (s *PolyvLiveService) rsaEncrypt(plaintext []byte) (string, error) {
	// 解析公钥
	block, _ := pem.Decode([]byte(s.PublicKey))
	if block == nil {
		return "", fmt.Errorf("解析公钥PEM失败")
	}
	pubInterface, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return "", err
	}
	pub := pubInterface.(*rsa.PublicKey)

	encryptedBytes, err := rsa.EncryptPKCS1v15(rand.Reader, pub, []byte(plaintext))
	if err != nil {
		return "", err
	}
	return base64.StdEncoding.EncodeToString(encryptedBytes), nil
}

// rsaDecrypt 使用RSA私钥解密（主要用于测试）
func (s *PolyvLiveService) rsaDecrypt(encryptedText string) ([]byte, error) {
	cipherBytes, _ := base64.StdEncoding.DecodeString(encryptedText)
	block, _ := pem.Decode([]byte(s.PrivateKey))
	priv, _ := x509.ParsePKCS1PrivateKey(block.Bytes)

	decryptedBytes, err := rsa.DecryptPKCS1v15(rand.Reader, priv, cipherBytes)
	if err != nil {
		return []byte{}, err
	}
	return decryptedBytes, nil
}

// generateSign 生成API签名
func (s *PolyvLiveService) generateSign(params map[string]interface{}) string {
	// 添加公共参数
	params["appId"] = s.AppId
	params["timestamp"] = time.Now().UnixMilli()

	// 参数排序
	var keys []string
	for k, v := range params {
		// 剔除值为null的参数
		if v != nil && v != "" {
			keys = append(keys, k)
		}
	}
	sort.Strings(keys)

	// 构建签名字符串 key1value1key2value2...keyNvalueN
	var signStr strings.Builder

	// 首部添加密钥
	signStr.WriteString(s.SecretKey)

	// 拼接参数
	for _, k := range keys {
		signStr.WriteString(k)
		signStr.WriteString(fmt.Sprintf("%v", params[k]))
	}

	// 尾部添加密钥
	signStr.WriteString(s.SecretKey)

	// MD5加密
	hasher := md5.New()
	hasher.Write([]byte(signStr.String()))
	return strings.ToUpper(hex.EncodeToString(hasher.Sum(nil)))
}

// makeRequest 发起HTTP请求（GET方式，保持向后兼容）
func (s *PolyvLiveService) makeRequest(endpoint string, params map[string]interface{}) ([]byte, error) {
	return s.makeRequestWithMethod("GET", endpoint, params)
}

// makeRequestWithMethod 发起HTTP请求，支持指定HTTP方法
func (s *PolyvLiveService) makeRequestWithMethod(method, endpoint string, params map[string]interface{}) ([]byte, error) {
	// 构建请求URL
	requestURL := fmt.Sprintf("%s%s", s.BaseURL, endpoint)
	var req *http.Request
	var err error

	if method == "POST" {
		re := make(map[string]interface{})
		sign := s.generateSign(re)
		re["sign"] = sign
		// 分离公共参数和业务参数
		commonParams := url.Values{}
		for k, v := range re {
			commonParams.Add(k, fmt.Sprintf("%v", v))
		}
		// 添加公共参数到URL
		if len(commonParams) > 0 {
			requestURL += "?" + commonParams.Encode()
		}
		// POST请求：参数放在JSON请求体中
		jsonData, err := json.Marshal(params)
		if err != nil {
			global.GVA_LOG.Error("序列化JSON参数失败", zap.Error(err))
			return nil, err
		}
		req, err = http.NewRequest("POST", requestURL, strings.NewReader(string(jsonData)))
		if err != nil {
			global.GVA_LOG.Error("创建POST请求失败", zap.Error(err))
			return nil, err
		}
		req.Header.Set("Content-Type", "application/json")

		global.GVA_LOG.Debug("发起POST请求",
			zap.String("url", requestURL),
			zap.String("json_data", string(jsonData)))
	} else {
		sign := s.generateSign(params)
		params["sign"] = sign
		// GET请求：参数放在URL查询字符串中
		urlParams := url.Values{}
		for k, v := range params {
			urlParams.Add(k, fmt.Sprintf("%v", v))
		}

		if len(urlParams) > 0 {
			requestURL += "?" + urlParams.Encode()
		}

		req, err = http.NewRequest("GET", requestURL, nil)
		if err != nil {
			global.GVA_LOG.Error("创建GET请求失败", zap.Error(err))
			return nil, err
		}

		global.GVA_LOG.Debug("发起GET请求", zap.String("url", requestURL))
	}

	// 发起HTTP请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		global.GVA_LOG.Error("保利威API请求失败", zap.Error(err), zap.String("url", requestURL))
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		global.GVA_LOG.Error("读取响应失败", zap.Error(err))
		return nil, err
	}

	if resp.StatusCode != http.StatusOK {
		global.GVA_LOG.Error("API响应错误",
			zap.Int("status_code", resp.StatusCode),
			zap.String("response", string(body)))
		return nil, fmt.Errorf("API响应错误: %d", resp.StatusCode)
	}

	return body, nil
}

// GetChannelDetailListReq 获取频道详细信息列表请求
type GetChannelDetailListReq struct {
	Page     int    `form:"page"`      // 页数，默认为1
	PageSize int    `form:"page_size"` // 每页显示的数据条数，默认为20，最大值为100
	Status   string `form:"status"`    // 频道状态：live(直播中)、end(已结束)、waiting(等待中)，不传则查询所有状态
	Keyword  string `form:"keyword"`   // 搜索关键字，可以根据频道名称搜索
}

// ChannelDetailInfo 频道详细信息
type ChannelDetailInfo struct {
	ChannelId       int64       `json:"channelId"`
	Name            string      `json:"name"`
	ChannelPasswd   string      `json:"channelPasswd"`
	Publisher       string      `json:"publisher"`
	StartTime       int64       `json:"startTime"`
	CoverImg        string      `json:"coverImg"`
	PureRtcEnabled  string      `json:"pureRtcEnabled"`
	SplashImg       interface{} `json:"splashImg"`
	SplashEnabled   string      `json:"splashEnabled"`
	Desc            interface{} `json:"desc"`
	WatchStatus     string      `json:"watchStatus"`
	WatchStatusText string      `json:"watchStatusText"`
	WarmUpEnabled   string      `json:"warmUpEnabled"`
	WarmUpType      string      `json:"warmUpType"`
	WarmUpFlv       interface{} `json:"warmUpFlv"`
	BgImg           interface{} `json:"bgImg"`
	BgLargeImg      interface{} `json:"bgLargeImg"`
	CategoryId      int         `json:"categoryId"`
	PageView        int         `json:"pageView"`
	Likes           int         `json:"likes"`
	MaxViewer       int         `json:"maxViewer"`
	OnlineNum       int         `json:"onlineNum"`
	VideoList       interface{} `json:"videoList"`
	Scene           string      `json:"scene"`
	NewScene        string      `json:"newScene"`
	Template        string      `json:"template"`
	CreatorName     string      `json:"creatorName"`
}

// GetChannelDetailListResp 获取频道详细信息列表响应
type GetChannelDetailListResp struct {
	Code    int    `json:"code"`
	Status  string `json:"status"`
	Message string `json:"message"`
	Data    struct {
		Contents   []ChannelDetailInfo `json:"contents"`   // 频道列表
		PageNum    int                 `json:"pageNum"`    // 当前页数
		PageSize   int                 `json:"pageSize"`   // 每页条数
		TotalItems int                 `json:"totalItems"` // 总条数
		TotalPage  int                 `json:"totalPage"`  // 总页数
	} `json:"data"`
}

// GetChannelDetailList 获取频道详细信息列表
func (s *PolyvLiveService) GetChannelDetailList(req *GetChannelDetailListReq) (*GetChannelDetailListResp, error) {
	params := map[string]interface{}{}

	// 设置分页参数
	if req.Page > 0 {
		params["page"] = req.Page
	} else {
		params["page"] = 1
	}

	if req.PageSize > 0 {
		params["pageSize"] = req.PageSize
	} else {
		params["pageSize"] = 20
	}

	// 设置可选参数
	if req.Status != "" {
		params["status"] = req.Status
	}

	if req.Keyword != "" {
		params["keyword"] = req.Keyword
	}

	body, err := s.makeRequest(EndpointChannelDetailList, params)
	if err != nil {
		return nil, err
	}

	var resp GetChannelDetailListResp
	if err := json.Unmarshal(body, &resp); err != nil {
		global.GVA_LOG.Error("解析响应失败", zap.Error(err), zap.String("body", string(body)))
		return nil, err
	}

	if resp.Code != 200 {
		return nil, fmt.Errorf("获取频道列表失败: %s", resp.Message)
	}

	return &resp, nil
}

// CreateUserLiveChannel 为用户创建直播频道
func (s *PolyvLiveService) CreateUserLiveChannel(userName, channelName string) (*CreateChannelResp, error) {
	req := &CreateChannelReq{
		Name:     channelName,
		NewScene: "topclass", // 活动营销场景
		Template: "topclass", // 纯视频-横屏模板

		// 直播配置
		PureRtcEnabled: "N", // 普通延迟
	}

	return s.CreateChannel(req)
}

// CreatePrivateLiveChannel 创建私密直播频道（需要密码）
func (s *PolyvLiveService) CreatePrivateLiveChannel(userName, channelName, password string) (*CreateChannelResp, error) {
	req := &CreateChannelReq{
		Name:          channelName,
		NewScene:      "topclass", // 活动营销场景
		Template:      "topclass", // 纯视频-横屏模板
		ChannelPasswd: password,

		// 直播配置
		PureRtcEnabled: "N", // 普通延迟
	}

	//绑定开播用户以及房间号 用户下次进来可以直接查看继续开播
	return s.CreateChannel(req)
}

// CreateChannelReq 创建频道请求
type CreateChannelReq struct {
	// 必填参数
	Name     string `json:"name" binding:"required"`     // 直播名称，最大长度100，必填
	NewScene string `json:"newScene" binding:"required"` // 直播场景，必填：topclass(大班课)/double(双师课)/train(企业培训)/alone(活动营销)/seminar(研讨会)/guide(导播)
	Template string `json:"template" binding:"required"` // 直播模板，必填：ppt(三分屏-横屏)/portrait_ppt(三分屏-竖屏)/alone(纯视频-横屏)/portrait_alone(纯视频-竖屏)/topclass(纯视频极速-横屏)/portrait_topclass(纯视频极速-竖屏)/seminar(研讨会)

	// 密码相关（可选）
	ChannelPasswd           string `json:"channelPasswd"`           // 讲师登录密码，长度6-16位，不传则系统随机生成
	SeminarHostPassword     string `json:"seminarHostPassword"`     // 研讨会主持人密码，仅研讨会场景有效，长度6-16位
	SeminarAttendeePassword string `json:"seminarAttendeePassword"` // 研讨会参会人密码，仅研讨会场景有效，长度6-16位

	// 直播配置（可选）
	PureRtcEnabled         string `json:"pureRtcEnabled"`         // 直播延迟：Y无延时，N普通延迟
	Type                   string `json:"type"`                   // 转播类型：normal不开启、transmit发起转播、receive接收转播
	DoubleTeacherType      string `json:"doubleTeacherType"`      // 线上双师：transmit大房间、receive小房间
	CnAndEnLiveEnabled     string `json:"cnAndEnLiveEnabled"`     // 中英双语直播开关：Y开、N关
	LinkMicLimit           int    `json:"linkMicLimit"`           // 连麦人数限制，最多16人
	H5LowLatencyFlvEnabled string `json:"h5LowLatencyFlvEnabled"` // 手机H5观看页低延迟开关：Y开启，N关闭

	// 频道信息（可选）
	CategoryId      int    `json:"categoryId"`      // 分类ID
	StartTime       int64  `json:"startTime"`       // 开始时间戳，毫秒
	EndTime         int64  `json:"endTime"`         // 结束时间戳，毫秒
	SubAccount      string `json:"subAccount"`      // 子账号邮箱
	CustomTeacherId string `json:"customTeacherId"` // 自定义讲师ID，32个以内ASCII码可见字符
	LabelData       []int  `json:"labelData"`       // 标签ID数组

	// 装修相关（可选）
	SplashImg                        string `json:"splashImg"`                        // 引导页图片地址
	ClientAloneTemplateBackgroundUrl string `json:"clientAloneTemplateBackgroundUrl"` // 客户端模版背景图URL
	LiveCdnBackgroundUrl             string `json:"liveCdnBackgroundUrl"`             // 视频混流背景图URL
}

// CreateChannelResp 创建频道响应
type CreateChannelResp struct {
	Code      int    `json:"code"`
	Status    string `json:"status"`
	RequestId string `json:"requestId"`
	Data      struct {
		ChannelId               int64   `json:"channelId"`               // 频道ID
		UserId                  string  `json:"userId"`                  // 用户ID
		Scene                   *string `json:"scene"`                   // 场景，可能为null
		ChannelPasswd           string  `json:"channelPasswd"`           // 频道密码
		SeminarHostPassword     *string `json:"seminarHostPassword"`     // 研讨会主持人密码，可能为null
		SeminarAttendeePassword *string `json:"seminarAttendeePassword"` // 研讨会参会人密码，可能为null
	} `json:"data"`
	Success bool `json:"success"`
}

// CreateChannel 创建直播频道
func (s *PolyvLiveService) CreateChannel(req *CreateChannelReq) (*CreateChannelResp, error) {
	params := map[string]interface{}{
		"name":     req.Name,     // 直播名称，必填
		"newScene": req.NewScene, // 直播场景，必填
		"template": req.Template, // 直播模板，必填
	}

	// 设置密码相关参数
	if req.ChannelPasswd != "" {
		params["channelPasswd"] = req.ChannelPasswd
	}
	if req.SeminarHostPassword != "" {
		params["seminarHostPassword"] = req.SeminarHostPassword
	}
	if req.SeminarAttendeePassword != "" {
		params["seminarAttendeePassword"] = req.SeminarAttendeePassword
	}

	// 设置直播配置参数
	if req.PureRtcEnabled != "" {
		params["pureRtcEnabled"] = req.PureRtcEnabled
	}
	if req.Type != "" {
		params["type"] = req.Type
	}
	if req.DoubleTeacherType != "" {
		params["doubleTeacherType"] = req.DoubleTeacherType
	}
	if req.CnAndEnLiveEnabled != "" {
		params["cnAndEnLiveEnabled"] = req.CnAndEnLiveEnabled
	}
	if req.LinkMicLimit > 0 {
		params["linkMicLimit"] = req.LinkMicLimit
	}
	if req.H5LowLatencyFlvEnabled != "" {
		params["h5LowLatencyFlvEnabled"] = req.H5LowLatencyFlvEnabled
	}

	// 设置频道信息参数
	if req.CategoryId > 0 {
		params["categoryId"] = req.CategoryId
	}
	if req.StartTime > 0 {
		params["startTime"] = req.StartTime
	}
	if req.EndTime > 0 {
		params["endTime"] = req.EndTime
	}
	if req.SubAccount != "" {
		params["subAccount"] = req.SubAccount
	}
	if req.CustomTeacherId != "" {
		params["customTeacherId"] = req.CustomTeacherId
	}
	if len(req.LabelData) > 0 {
		params["labelData"] = req.LabelData
	}

	// 设置装修相关参数
	if req.SplashImg != "" {
		params["splashImg"] = req.SplashImg
	}
	if req.ClientAloneTemplateBackgroundUrl != "" {
		params["clientAloneTemplateBackgroundUrl"] = req.ClientAloneTemplateBackgroundUrl
	}
	if req.LiveCdnBackgroundUrl != "" {
		params["liveCdnBackgroundUrl"] = req.LiveCdnBackgroundUrl
	}

	body, err := s.makeRequestWithMethod("POST", EndpointChannelCreate, params)
	if err != nil {
		global.GVA_LOG.Error("创建频道请求失败", zap.Error(err))
		return nil, err
	}

	var resp CreateChannelResp
	if err := json.Unmarshal(body, &resp); err != nil {
		global.GVA_LOG.Error("解析创建频道响应失败", zap.Error(err), zap.String("body", string(body)))
		return nil, err
	}

	if resp.Code != 200 {
		global.GVA_LOG.Error("创建频道失败",
			zap.Int("code", resp.Code),
			zap.String("status", resp.Status))
		return nil, fmt.Errorf("创建频道失败，错误码: %d", resp.Code)
	}

	global.GVA_LOG.Info("频道创建成功",
		zap.Int64("channelId", resp.Data.ChannelId),
		zap.String("userId", resp.Data.UserId),
		zap.String("channelPasswd", resp.Data.ChannelPasswd))

	return &resp, nil
}
