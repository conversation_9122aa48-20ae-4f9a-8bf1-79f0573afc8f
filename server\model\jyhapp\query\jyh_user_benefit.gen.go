// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
)

func newJyhUserBenefit(db *gorm.DB, opts ...gen.DOOption) jyhUserBenefit {
	_jyhUserBenefit := jyhUserBenefit{}

	_jyhUserBenefit.jyhUserBenefitDo.UseDB(db, opts...)
	_jyhUserBenefit.jyhUserBenefitDo.UseModel(&jyhapp.JyhUserBenefit{})

	tableName := _jyhUserBenefit.jyhUserBenefitDo.TableName()
	_jyhUserBenefit.ALL = field.NewAsterisk(tableName)
	_jyhUserBenefit.ID = field.NewUint(tableName, "id")
	_jyhUserBenefit.CreatedAt = field.NewTime(tableName, "created_at")
	_jyhUserBenefit.UpdatedAt = field.NewTime(tableName, "updated_at")
	_jyhUserBenefit.Key = field.NewString(tableName, "key")
	_jyhUserBenefit.Name = field.NewString(tableName, "name")
	_jyhUserBenefit.Description = field.NewString(tableName, "description")

	_jyhUserBenefit.fillFieldMap()

	return _jyhUserBenefit
}

type jyhUserBenefit struct {
	jyhUserBenefitDo

	ALL         field.Asterisk
	ID          field.Uint
	CreatedAt   field.Time
	UpdatedAt   field.Time
	Key         field.String
	Name        field.String
	Description field.String

	fieldMap map[string]field.Expr
}

func (j jyhUserBenefit) Table(newTableName string) *jyhUserBenefit {
	j.jyhUserBenefitDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j jyhUserBenefit) As(alias string) *jyhUserBenefit {
	j.jyhUserBenefitDo.DO = *(j.jyhUserBenefitDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *jyhUserBenefit) updateTableName(table string) *jyhUserBenefit {
	j.ALL = field.NewAsterisk(table)
	j.ID = field.NewUint(table, "id")
	j.CreatedAt = field.NewTime(table, "created_at")
	j.UpdatedAt = field.NewTime(table, "updated_at")
	j.Key = field.NewString(table, "key")
	j.Name = field.NewString(table, "name")
	j.Description = field.NewString(table, "description")

	j.fillFieldMap()

	return j
}

func (j *jyhUserBenefit) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *jyhUserBenefit) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 6)
	j.fieldMap["id"] = j.ID
	j.fieldMap["created_at"] = j.CreatedAt
	j.fieldMap["updated_at"] = j.UpdatedAt
	j.fieldMap["key"] = j.Key
	j.fieldMap["name"] = j.Name
	j.fieldMap["description"] = j.Description
}

func (j jyhUserBenefit) clone(db *gorm.DB) jyhUserBenefit {
	j.jyhUserBenefitDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j jyhUserBenefit) replaceDB(db *gorm.DB) jyhUserBenefit {
	j.jyhUserBenefitDo.ReplaceDB(db)
	return j
}

type jyhUserBenefitDo struct{ gen.DO }

func (j jyhUserBenefitDo) Debug() *jyhUserBenefitDo {
	return j.withDO(j.DO.Debug())
}

func (j jyhUserBenefitDo) WithContext(ctx context.Context) *jyhUserBenefitDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jyhUserBenefitDo) ReadDB() *jyhUserBenefitDo {
	return j.Clauses(dbresolver.Read)
}

func (j jyhUserBenefitDo) WriteDB() *jyhUserBenefitDo {
	return j.Clauses(dbresolver.Write)
}

func (j jyhUserBenefitDo) Session(config *gorm.Session) *jyhUserBenefitDo {
	return j.withDO(j.DO.Session(config))
}

func (j jyhUserBenefitDo) Clauses(conds ...clause.Expression) *jyhUserBenefitDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jyhUserBenefitDo) Returning(value interface{}, columns ...string) *jyhUserBenefitDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jyhUserBenefitDo) Not(conds ...gen.Condition) *jyhUserBenefitDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jyhUserBenefitDo) Or(conds ...gen.Condition) *jyhUserBenefitDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jyhUserBenefitDo) Select(conds ...field.Expr) *jyhUserBenefitDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jyhUserBenefitDo) Where(conds ...gen.Condition) *jyhUserBenefitDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jyhUserBenefitDo) Order(conds ...field.Expr) *jyhUserBenefitDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jyhUserBenefitDo) Distinct(cols ...field.Expr) *jyhUserBenefitDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jyhUserBenefitDo) Omit(cols ...field.Expr) *jyhUserBenefitDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jyhUserBenefitDo) Join(table schema.Tabler, on ...field.Expr) *jyhUserBenefitDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jyhUserBenefitDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jyhUserBenefitDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jyhUserBenefitDo) RightJoin(table schema.Tabler, on ...field.Expr) *jyhUserBenefitDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jyhUserBenefitDo) Group(cols ...field.Expr) *jyhUserBenefitDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jyhUserBenefitDo) Having(conds ...gen.Condition) *jyhUserBenefitDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jyhUserBenefitDo) Limit(limit int) *jyhUserBenefitDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jyhUserBenefitDo) Offset(offset int) *jyhUserBenefitDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jyhUserBenefitDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jyhUserBenefitDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jyhUserBenefitDo) Unscoped() *jyhUserBenefitDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jyhUserBenefitDo) Create(values ...*jyhapp.JyhUserBenefit) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jyhUserBenefitDo) CreateInBatches(values []*jyhapp.JyhUserBenefit, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jyhUserBenefitDo) Save(values ...*jyhapp.JyhUserBenefit) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jyhUserBenefitDo) First() (*jyhapp.JyhUserBenefit, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserBenefit), nil
	}
}

func (j jyhUserBenefitDo) Take() (*jyhapp.JyhUserBenefit, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserBenefit), nil
	}
}

func (j jyhUserBenefitDo) Last() (*jyhapp.JyhUserBenefit, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserBenefit), nil
	}
}

func (j jyhUserBenefitDo) Find() ([]*jyhapp.JyhUserBenefit, error) {
	result, err := j.DO.Find()
	return result.([]*jyhapp.JyhUserBenefit), err
}

func (j jyhUserBenefitDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*jyhapp.JyhUserBenefit, err error) {
	buf := make([]*jyhapp.JyhUserBenefit, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jyhUserBenefitDo) FindInBatches(result *[]*jyhapp.JyhUserBenefit, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jyhUserBenefitDo) Attrs(attrs ...field.AssignExpr) *jyhUserBenefitDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jyhUserBenefitDo) Assign(attrs ...field.AssignExpr) *jyhUserBenefitDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jyhUserBenefitDo) Joins(fields ...field.RelationField) *jyhUserBenefitDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jyhUserBenefitDo) Preload(fields ...field.RelationField) *jyhUserBenefitDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jyhUserBenefitDo) FirstOrInit() (*jyhapp.JyhUserBenefit, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserBenefit), nil
	}
}

func (j jyhUserBenefitDo) FirstOrCreate() (*jyhapp.JyhUserBenefit, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserBenefit), nil
	}
}

func (j jyhUserBenefitDo) FindByPage(offset int, limit int) (result []*jyhapp.JyhUserBenefit, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jyhUserBenefitDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jyhUserBenefitDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jyhUserBenefitDo) Delete(models ...*jyhapp.JyhUserBenefit) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jyhUserBenefitDo) withDO(do gen.Dao) *jyhUserBenefitDo {
	j.DO = *do.(*gen.DO)
	return j
}
