-- 更新转接表中SessionID字段类型
-- 执行时间：2025-01-16
-- 目的：将SessionID从string类型改为uint类型，对应SysServiceRecord表的ID

-- ========================================
-- 第一部分：备份现有数据
-- ========================================

-- 备份转接表数据（可选）
-- CREATE TABLE `sys_service_transfer_backup_sessionid` AS SELECT * FROM `sys_service_transfer`;

-- ========================================
-- 第二部分：更新SessionID字段类型
-- ========================================

-- 1. 检查当前SessionID字段的数据类型
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    COLUMN_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'sys_service_transfer' 
  AND COLUMN_NAME = 'session_id';

-- 2. 如果SessionID字段是varchar/string类型，需要先清理数据
-- 删除无效的SessionID记录（非数字的记录）
DELETE FROM `sys_service_transfer` 
WHERE `session_id` IS NULL 
   OR `session_id` = '' 
   OR `session_id` NOT REGEXP '^[0-9]+$';

-- 3. 修改SessionID字段类型为unsigned int
ALTER TABLE `sys_service_transfer` 
MODIFY COLUMN `session_id` int(11) unsigned NOT NULL COMMENT '会话记录ID(关联SysServiceRecord.ID)';

-- 4. 验证字段类型修改结果
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    COLUMN_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'sys_service_transfer' 
  AND COLUMN_NAME = 'session_id';

-- ========================================
-- 第三部分：更新索引
-- ========================================

-- 1. 删除旧的session_id索引（如果存在）
DROP INDEX IF EXISTS `idx_transfer_session` ON `sys_service_transfer`;

-- 2. 重新创建优化的索引
CREATE INDEX `idx_transfer_session_id` ON `sys_service_transfer` (`session_id`);
CREATE INDEX `idx_transfer_session_status` ON `sys_service_transfer` (`session_id`, `status`);

-- ========================================
-- 第四部分：数据验证和修复
-- ========================================

-- 1. 验证SessionID字段的数据完整性
SELECT 
    COUNT(*) as total_transfers,
    COUNT(CASE WHEN session_id > 0 THEN 1 END) as valid_session_ids,
    MIN(session_id) as min_session_id,
    MAX(session_id) as max_session_id
FROM `sys_service_transfer`;

-- 2. 检查SessionID是否对应有效的SysServiceRecord记录
SELECT 
    t.session_id,
    t.user_id,
    t.from_service_id,
    r.id as record_id,
    r.uid as record_user_id,
    r.service_id as record_service_id
FROM `sys_service_transfer` t
LEFT JOIN `sys_service_record` r ON t.session_id = r.id
WHERE r.id IS NULL
LIMIT 10;

-- 3. 如果有不匹配的记录，可以选择删除或修复
-- 删除无效的转接记录（SessionID不对应任何SysServiceRecord）
-- DELETE t FROM `sys_service_transfer` t
-- LEFT JOIN `sys_service_record` r ON t.session_id = r.id
-- WHERE r.id IS NULL;

-- ========================================
-- 第五部分：更新相关统计视图
-- ========================================

-- 重新创建转接统计视图
DROP VIEW IF EXISTS `v_transfer_statistics`;

CREATE VIEW `v_transfer_statistics` AS
SELECT 
    DATE(FROM_UNIXTIME(transfer_time)) as transfer_date,
    from_service_id,
    to_service_id,
    status,
    priority,
    transfer_type,
    COUNT(*) as transfer_count,
    AVG(CASE WHEN accept_time > 0 THEN accept_time - transfer_time ELSE NULL END) as avg_response_time,
    COUNT(DISTINCT session_id) as unique_sessions,
    COUNT(DISTINCT user_id) as unique_users
FROM `sys_service_transfer`
WHERE transfer_time > 0
GROUP BY transfer_date, from_service_id, to_service_id, status, priority, transfer_type;

-- ========================================
-- 第六部分：性能优化
-- ========================================

-- 1. 分析表结构，优化查询性能
ANALYZE TABLE `sys_service_transfer`;

-- 2. 检查索引使用情况
SHOW INDEX FROM `sys_service_transfer`;

-- ========================================
-- 第七部分：验证修改结果
-- ========================================

-- 1. 验证字段类型修改成功
SELECT 
    'session_id字段类型修改验证' as check_type,
    CASE 
        WHEN DATA_TYPE = 'int' AND COLUMN_TYPE LIKE '%unsigned%' THEN '✓ 成功'
        ELSE '✗ 失败'
    END as result,
    CONCAT(DATA_TYPE, ' ', COLUMN_TYPE) as current_type
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'sys_service_transfer' 
  AND COLUMN_NAME = 'session_id';

-- 2. 验证数据完整性
SELECT 
    '数据完整性验证' as check_type,
    CASE 
        WHEN COUNT(*) = COUNT(CASE WHEN session_id > 0 THEN 1 END) THEN '✓ 所有记录的session_id都有效'
        ELSE CONCAT('✗ 有 ', COUNT(*) - COUNT(CASE WHEN session_id > 0 THEN 1 END), ' 条无效记录')
    END as result,
    COUNT(*) as total_records
FROM `sys_service_transfer`;

-- 3. 验证关联关系
SELECT 
    '关联关系验证' as check_type,
    CASE 
        WHEN invalid_count = 0 THEN '✓ 所有session_id都对应有效的记录'
        ELSE CONCAT('✗ 有 ', invalid_count, ' 条记录的session_id无效')
    END as result,
    invalid_count
FROM (
    SELECT COUNT(*) as invalid_count
    FROM `sys_service_transfer` t
    LEFT JOIN `sys_service_record` r ON t.session_id = r.id
    WHERE r.id IS NULL
) as validation;

-- ========================================
-- 完成提示
-- ========================================

SELECT 
    'SessionID字段类型更新完成!' as message,
    'SessionID已从string类型更新为uint类型' as details,
    '现在SessionID对应SysServiceRecord表的ID字段' as explanation;

-- ========================================
-- 回滚脚本（紧急情况使用）
-- ========================================

/*
-- 如果需要回滚SessionID字段类型，可以使用以下脚本：

-- 1. 将SessionID字段改回varchar类型
ALTER TABLE `sys_service_transfer` 
MODIFY COLUMN `session_id` varchar(100) NOT NULL DEFAULT '' COMMENT '会话ID';

-- 2. 更新数据格式（将数字转换为字符串格式）
UPDATE `sys_service_transfer` 
SET `session_id` = CONCAT('session_', `session_id`, '_', `user_id`)
WHERE `session_id` REGEXP '^[0-9]+$';

-- 3. 重新创建原来的索引
DROP INDEX IF EXISTS `idx_transfer_session_id` ON `sys_service_transfer`;
DROP INDEX IF EXISTS `idx_transfer_session_status` ON `sys_service_transfer`;
CREATE INDEX `idx_transfer_session` ON `sys_service_transfer` (`session_id`);

-- 4. 从备份表恢复数据（如果有备份）
-- TRUNCATE TABLE `sys_service_transfer`;
-- INSERT INTO `sys_service_transfer` SELECT * FROM `sys_service_transfer_backup_sessionid`;
*/
