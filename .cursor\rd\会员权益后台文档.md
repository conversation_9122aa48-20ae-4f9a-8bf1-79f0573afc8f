# 会员权益后台接口文档

> 目标：让运营人员可视化、新增、编辑、删除并查询  
> **① 权益类型 (Benefit)**　② 会员等级 (Level)　③ 等级-权益映射 (LevelBenefit)  
> **④ 用户等级记录 (UserLevel)**　⑤ 用户权益快照 (BenefitSnapshot，用户开通时自动快照)

---

## 0. 术语与缩写

| 名称 | 说明 |
|------|------|
| **权益类型（Benefit）** | 概念级元数据：如 `window_commission_rate`（橱窗分佣）、`material_daily_qty`（素材条数） |
| **会员等级（Level）** | 运营打包售卖的"炬火达人""辰光达人"等 |
| **等级-权益映射（LevelBenefit）** | 把"炬火达人 → 橱窗分佣 60%"这类信息记录下来 |
| **用户等级记录（UserLevel）** | 用户购买/领取一次等级产生一行，含生效与失效时间 |
| **用户权益快照（BenefitSnapshot）** | **关键**：用户开通时自动快照权益配置，确保权益稳定性 |

### 0.3 权益快照业务流程

**权益快照的正确理解**：
1. 管理员配置等级权益：VIP1等级分佣比例5%，素材条数100条
2. 用户开通VIP1：系统创建用户等级记录，同时快照当前权益配置（5%, 100条）
3. 管理员调整等级权益：将VIP1调整为分佣比例8%，素材条数200条
4. **结果**：
   - 已开通的用户仍享受快照的权益（5%, 100条）
   - 新开通的用户享受新的权益配置（8%, 200条）

### 0.4 设计原则

- **权益类型**：预设数据，主要用于等级配置时选择，极少外部管理接口
- **会员等级**：主要管理界面，创建和更新包含权益配置，使用事务保证一致性
- **用户等级记录**：用户开通等级时自动创建权益快照
- **权益快照**：主要用于查询，保证用户权益不受后续调整影响

---

## 1. 权限设定

| 角色 | 能力 |
|------|------|
| `admin` | 全量 CRUD + 审核 |
| `operator` | 新增 / 编辑 / 禁用，但不能删除（软删） |
| `viewer` | 只读 |

---

## 2. 通用约定

| 项 | 约定 |
|----|------|
| **域名** | `https://api.example.com/admin` |
| **授权** | Header `Authorization: Bearer <token>` |
| **日期时间** | ISO-8601 字符串，统一 **UTC+8** |
| **分页** | Query `page`, `page_size`（默认 1, 20） |
| **软删** | `DELETE` 操作置 `deleted_at` 字段，数据保留 |
| **统一响应** | `{ code:int, message:string, data:any }` |
| **错误码** | `0` 成功；`4xx` 业务错误；`5xx` 服务异常 |
| **路由命名** | 使用下划线`_`分割，不使用短横线`-` |
| **路径前缀** | 会员权益相关接口统一使用 `/admin/user_benefit/` 前缀 |

---

## 3. 接口清单（重构后）

### 3.1 权益类型管理（预设数据，极少管理接口）

| # | 方法 | 路径 | 描述 |
|---|------|------|------|
| 3.1 | `GET` | `/admin/user_benefit/benefits` | 获取权益类型列表（主要用于等级配置时选择） |

### 3.2 会员等级管理（主要管理界面，包含权益配置）

| # | 方法 | 路径 | 描述 |
|---|------|------|------|
| 3.2 | `GET` | `/admin/user_benefit/levels` | 获取会员等级列表 |
| 3.3 | `POST` | `/admin/user_benefit/levels` | 创建会员等级（包含权益配置，事务处理） |
| 3.4 | `GET` | `/admin/user_benefit/levels/{id}` | 获取会员等级详情（包含权益配置） |
| 3.5 | `PUT` | `/admin/user_benefit/levels/{id}` | 更新会员等级（包含权益配置，事务处理） |
| 3.6 | `DELETE` | `/admin/user_benefit/levels/{id}` | 删除会员等级 |

### 3.3 用户等级记录管理（用户开通等级）

| # | 方法 | 路径 | 描述 |
|---|------|------|------|
| 3.7 | `GET` | `/admin/user_benefit/user_levels` | 获取用户等级记录列表 |
| 3.8 | `POST` | `/admin/user_benefit/user_levels` | 用户开通等级（创建记录并自动快照权益） |
| 3.9 | `PUT` | `/admin/user_benefit/user_levels/{id}` | 更新用户等级记录 |
| 3.10 | `DELETE` | `/admin/user_benefit/user_levels/{id}` | 删除用户等级记录 |

### 3.4 用户权益快照查询（主要用于查询）

| # | 方法 | 路径 | 描述 |
|---|------|------|------|
| 3.11 | `GET` | `/admin/user_benefit/user_benefit_snapshots` | 获取权益快照列表 |
| 3.12 | `GET` | `/admin/user_benefit/users/{userId}/benefit_snapshots` | 查询指定用户的权益快照 |


## 模型文件参考

文件路径:server\model\jyhapp\jyh_user.go

模型:
```
JyhUserShipLevelBenefit
JyhUserBenefit
JyhUserShipLevel
JyhUserBenefitSnapshot
JyhUserLevel
```
---

## 4. 详细字段

### 4.1 权益类型（Benefit）

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| `key` | string | ✔ | 唯一英文编码 |
| `name` | string | ✔ | 展示名 |
| `unit` | string | ✖ | %, 条/天, 次, 元… |
| `description` | string | ✖ | 运营备注 |
| `icon_url` | string | ✖ | 前端小图标 |

**示例 – 创建**

```json
POST /admin/user_benefit/benefits
{
  "key": "team_commission_rate",
  "name": "团队成员出单分佣",
  "unit": "%",
  "description": "按团队成员成交额提成"
}
```

---

## 5. 接口示例

### 5.1 创建会员等级（包含权益配置）

**POST** `/admin/user_benefit/levels`

**请求体**：
```json
{
  "name": "VIP1",
  "code": "vip1",
  "priceCents": 9900,
  "durationDays": 30,
  "sort": 1,
  "description": "初级VIP会员",
  "benefits": [
    {
      "benefitId": 1,
      "value": "5%",
      "condition": ""
    },
    {
      "benefitId": 2,
      "value": "100",
      "condition": ""
    }
  ]
}
```

**响应示例**：
```json
{
  "code": 0,
  "message": "创建会员等级成功"
}
```

### 5.2 用户开通等级

**POST** `/admin/user_benefit/user_levels`

**请求体**：
```json
{
  "userId": 123,
  "levelId": 1,
  "orderId": "ORDER123456",
  "startAt": "2024-01-01T00:00:00+08:00",
  "endAt": "2024-01-31T23:59:59+08:00",
  "status": "active"
}
```

---

## 6. 数据流示例

### 场景：用户购买VIP1会员，后续管理员调整权益

1. **管理员配置VIP1等级**：
   ```bash
   POST /admin/user_benefit/levels
   {
     "name": "VIP1",
     "code": "vip1",
     "priceCents": 9900,
     "durationDays": 30,
     "benefits": [
       {"benefitId": 1, "value": "5%"},    // 分佣比例5%
       {"benefitId": 2, "value": "100"}    // 素材条数100条
     ]
   }
   ```

2. **用户123购买VIP1会员**：
   ```bash
   POST /admin/user_benefit/user_levels
   {
     "userId": 123,
     "levelId": 1,
     "orderId": "ORDER123456",
     "startAt": "2024-01-01T00:00:00+08:00",
     "endAt": "2024-01-31T23:59:59+08:00"
   }
   ```
   **系统自动操作**：
   - 创建用户等级记录
   - 快照当前VIP1的权益配置（分佣5%，素材100条）

3. **管理员调整VIP1权益配置**：
   ```bash
   PUT /admin/user_benefit/levels/1
   {
     "benefits": [
       {"benefitId": 1, "value": "8%"},    // 分佣比例提升到8%
       {"benefitId": 2, "value": "200"}    // 素材条数提升到200条
     ]
   }
   ```

4. **用户456购买VIP1会员**：
   ```bash
   POST /admin/user_benefit/user_levels
   {
     "userId": 456,
     "levelId": 1,
     "startAt": "2024-02-01T00:00:00+08:00",
     "endAt": "2024-02-28T23:59:59+08:00"
   }
   ```
   **系统自动操作**：
   - 创建用户等级记录
   - 快照当前VIP1的权益配置（分佣8%，素材200条）

5. **最终结果**：
   - 用户123仍享受原快照权益：分佣5%，素材100条
   - 用户456享受新的权益配置：分佣8%，素材200条
   - 保证了用户权益的稳定性和公平性

---

## 7. 技术实现要点

### 7.1 事务处理
- **等级创建/更新**：等级信息和权益配置在同一事务中处理
- **用户开通**：用户等级记录和权益快照在同一事务中处理

### 7.2 数据一致性
- **权益快照**：确保用户权益不受后续调整影响
- **删除检查**：删除等级前检查是否被用户使用
- **外键约束**：确保数据关联的完整性

### 7.3 性能优化
- **查询接口**：不使用操作记录中间件
- **分页查询**：支持条件筛选
- **索引优化**：用户ID、等级ID、时间范围等常用查询字段

### 7.4 路由命名规范
- **使用下划线**：所有路由路径使用下划线`_`分割，不使用短横线`-`
- **路径前缀**：会员权益相关接口统一使用 `/admin/user_benefit/` 前缀
- **RESTful设计**：遵循RESTful API设计规范
- **一致性原则**：保持与项目其他模块的命名风格一致