package ws

import (
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/plugin/customerservice/model"
)

// TransferNotificationService WebSocket转接通知服务
type TransferNotificationService struct{}

// TransferNotification 转接通知消息结构
type TransferNotification struct {
	Type      string      `json:"type"`      // 通知类型
	Data      interface{} `json:"data"`      // 通知数据
	Timestamp int64       `json:"timestamp"` // 时间戳
}

// 通知类型常量
const (
	NotifyTransferRequest  = "transfer_request"  // 转接请求
	NotifyTransferAccepted = "transfer_accepted" // 转接已接受
	NotifyTransferRejected = "transfer_rejected" // 转接已拒绝
	NotifyTransferCanceled = "transfer_canceled" // 转接已取消
	NotifyTransferExpired  = "transfer_expired"  // 转接已过期
	NotifyServiceChanged   = "service_changed"   // 客服变更（通知用户）
)

// SendTransferRequest 发送转接请求通知
func (s *TransferNotificationService) SendTransferRequest(transfer *model.SysServiceTransfer) {
	// 通知目标客服
	targetNotification := TransferNotification{
		Type: NotifyTransferRequest,
		Data: map[string]interface{}{
			"transfer_id":       transfer.ID,
			"from_service_id":   transfer.FromServiceID,
			"user_id":           transfer.UserID,
			"session_id":        transfer.SessionID, // SysServiceRecord表的ID
			"reason":            transfer.Reason,
			"priority":          transfer.Priority,
			"expire_time":       transfer.ExpireTime,
			"user_tags":         transfer.UserTags,
			"from_service_info": transfer.FromServiceInfo,
		},
		Timestamp: time.Now().Unix(),
	}

	s.sendToService(transfer.ToServiceID, targetNotification)

	// 通知转出客服（确认消息）
	confirmNotification := TransferNotification{
		Type: "transfer_sent",
		Data: map[string]interface{}{
			"transfer_id":   transfer.ID,
			"to_service_id": transfer.ToServiceID,
			"status":        "sent",
		},
		Timestamp: time.Now().Unix(),
	}

	s.sendToService(transfer.FromServiceID, confirmNotification)
}

// SendTransferAccepted 发送转接已接受通知
func (s *TransferNotificationService) SendTransferAccepted(transfer *model.SysServiceTransfer) {
	// 通知转出客服
	notification := TransferNotification{
		Type: NotifyTransferAccepted,
		Data: map[string]interface{}{
			"transfer_id":   transfer.ID,
			"to_service_id": transfer.ToServiceID,
			"accept_time":   transfer.AcceptTime,
		},
		Timestamp: time.Now().Unix(),
	}

	s.sendToService(transfer.FromServiceID, notification)

	// 通知用户客服变更
	userNotification := TransferNotification{
		Type: NotifyServiceChanged,
		Data: map[string]interface{}{
			"new_service_id":  transfer.ToServiceID,
			"session_id":      transfer.SessionID, // SysServiceRecord表的ID
			"to_service_info": transfer.ToServiceInfo,
		},
		Timestamp: time.Now().Unix(),
	}

	s.sendToUser(transfer.UserID, userNotification)
}

// SendTransferRejected 发送转接已拒绝通知
func (s *TransferNotificationService) SendTransferRejected(transfer *model.SysServiceTransfer, reason string) {
	notification := TransferNotification{
		Type: NotifyTransferRejected,
		Data: map[string]interface{}{
			"transfer_id":   transfer.ID,
			"to_service_id": transfer.ToServiceID,
			"reject_reason": reason,
		},
		Timestamp: time.Now().Unix(),
	}

	s.sendToService(transfer.FromServiceID, notification)
}

// SendTransferCanceled 发送转接已取消通知
func (s *TransferNotificationService) SendTransferCanceled(transfer *model.SysServiceTransfer, reason string) {
	notification := TransferNotification{
		Type: NotifyTransferCanceled,
		Data: map[string]interface{}{
			"transfer_id":   transfer.ID,
			"cancel_reason": reason,
		},
		Timestamp: time.Now().Unix(),
	}

	s.sendToService(transfer.ToServiceID, notification)
}

// SendTransferExpired 发送转接已过期通知
func (s *TransferNotificationService) SendTransferExpired(transfer *model.SysServiceTransfer) {
	notification := TransferNotification{
		Type: NotifyTransferExpired,
		Data: map[string]interface{}{
			"transfer_id": transfer.ID,
		},
		Timestamp: time.Now().Unix(),
	}

	// 通知双方客服
	s.sendToService(transfer.FromServiceID, notification)
	s.sendToService(transfer.ToServiceID, notification)
}

// SendBatchTransferNotification 发送批量转接通知
func (s *TransferNotificationService) SendBatchTransferNotification(fromServiceID, toServiceID int64, sessionCount int) {
	notification := TransferNotification{
		Type: "batch_transfer_request",
		Data: map[string]interface{}{
			"from_service_id": fromServiceID,
			"session_count":   sessionCount,
		},
		Timestamp: time.Now().Unix(),
	}

	s.sendToService(toServiceID, notification)
}

// sendToService 发送通知给客服
func (s *TransferNotificationService) sendToService(serviceID int64, notification TransferNotification) {
	clientKey := fmt.Sprintf("kf%d", serviceID)
	s.sendNotification(clientKey, notification)
}

// sendToUser 发送通知给用户
func (s *TransferNotificationService) sendToUser(userID uint, notification TransferNotification) {
	clientKey := fmt.Sprintf("user%d", userID)
	s.sendNotification(clientKey, notification)
}

// sendNotification 发送WebSocket通知的核心方法
func (s *TransferNotificationService) sendNotification(clientKey string, notification TransferNotification) {
	// 这里需要与实际的WebSocket管理器集成
	// 以下是示例实现，需要根据实际的WebSocket系统调整

	messageBytes, err := json.Marshal(notification)
	if err != nil {
		log.Printf("转接通知序列化失败: %v", err)
		return
	}

	// 示例：假设有一个全局的WebSocket管理器
	/*
		if manager := GetWebSocketManager(); manager != nil {
			if client, exists := manager.GetClient(clientKey); exists && client != nil {
				select {
				case client.Send <- messageBytes:
					log.Printf("转接通知已发送给 %s: %s", clientKey, notification.Type)
				default:
					log.Printf("转接通知发送失败，客户端 %s 可能已断开", clientKey)
				}
			} else {
				log.Printf("客户端 %s 不在线，转接通知未发送", clientKey)
			}
		}
	*/

	// 临时实现：记录日志
	log.Printf("WebSocket通知 [%s] -> %s: %s", clientKey, notification.Type, string(messageBytes))
}

// BroadcastTransferStatistics 广播转接统计信息
func (s *TransferNotificationService) BroadcastTransferStatistics(stats map[string]interface{}) {
	notification := TransferNotification{
		Type:      "transfer_statistics_update",
		Data:      stats,
		Timestamp: time.Now().Unix(),
	}

	// 广播给所有在线客服
	s.broadcastToAllServices(notification)
}

// broadcastToAllServices 广播给所有在线客服
func (s *TransferNotificationService) broadcastToAllServices(notification TransferNotification) {
	// 这里需要获取所有在线客服列表并发送通知
	// 示例实现
	log.Printf("广播转接通知: %s", notification.Type)
}

// GetNotificationHistory 获取通知历史（可选功能）
func (s *TransferNotificationService) GetNotificationHistory(clientKey string, limit int) []TransferNotification {
	// 这里可以实现通知历史记录功能
	// 返回最近的通知记录
	return []TransferNotification{}
}

// 全局转接通知服务实例
var TransferNotifier = &TransferNotificationService{}

// 使用示例：
/*
// 在转接服务中使用
func (s *TransferService) notifyTransferRequest(transfer *model.SysServiceTransfer) {
	TransferNotifier.SendTransferRequest(transfer)
}

func (s *TransferService) notifyTransferAccepted(transfer *model.SysServiceTransfer) {
	TransferNotifier.SendTransferAccepted(transfer)
}
*/
