package jyhapp

import (
	"context"
	"crypto/rand"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/utils/jgorm"
	"github.com/xtulnx/jkit-go/jtime"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/query"
	jyhReq "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/request"
	jyhResp "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/response"
	"go.uber.org/zap"
)

type JyhInviteCodeService struct{}

// CreateInviteCode 创建邀请码
func (s *JyhInviteCodeService) CreateInviteCode(req *jyhReq.CreateInviteCodeReq) error {
	// 生成唯一邀请码
	code, err := s.generateUniqueInviteCode()
	if err != nil {
		return err
	}

	// 计算过期时间
	var expiredAt *time.Time
	if req.ValidityDays > 0 {
		expireTime := time.Now().Add(time.Duration(req.ValidityDays) * 24 * time.Hour)
		expiredAt = &expireTime
	}

	inviteCode := &jyhapp.JyhInviteCode{
		Type:      req.Type,
		UserID:    req.UserID,
		Code:      code,
		LevelID:   req.LevelID,
		SalePrice: req.SalePrice,
		Status:    jyhapp.InviteCodeStatusUnused,
		IsUsed:    false,
		ExpiredAt: expiredAt,
	}

	return query.JyhInviteCode.Create(inviteCode)
}

// BatchCreateInviteCodes 批量创建邀请码
func (s *JyhInviteCodeService) BatchCreateInviteCodes(req *jyhReq.BatchCreateInviteCodeReq) error {
	var inviteCodes []*jyhapp.JyhInviteCode

	for i := 0; i < int(req.Quantity); i++ {
		// 生成唯一邀请码
		code, err := s.generateUniqueInviteCode()
		if err != nil {
			return err
		}

		// 计算过期时间
		var expiredAt *time.Time
		if req.ValidityDays > 0 {
			expireTime := time.Now().Add(time.Duration(req.ValidityDays) * 24 * time.Hour)
			expiredAt = &expireTime
		}

		inviteCode := &jyhapp.JyhInviteCode{
			Type:      req.Type,
			UserID:    req.UserID,
			Code:      code,
			LevelID:   req.LevelID,
			SalePrice: req.SalePrice,
			Status:    jyhapp.InviteCodeStatusUnused,
			IsUsed:    false,
			ExpiredAt: expiredAt,
		}
		inviteCodes = append(inviteCodes, inviteCode)
	}

	return query.JyhInviteCode.CreateInBatches(inviteCodes, 100)
}

// GetInviteCodeList 获取邀请码列表
func (s *JyhInviteCodeService) GetInviteCodeList(req *jyhReq.InviteCodeListReq) (*jyhResp.InviteCodeListResp, error) {
	dbCode := query.JyhInviteCode
	dbLevel := query.JyhUserShipLevel
	dbUser := query.JyhUser
	dbUseUser := query.JyhUser.As("use_user")
	db := dbCode.WithContext(context.Background())
	// 构建查询条件
	if req.Type != "" {
		db = db.Where(query.JyhInviteCode.Type.Eq(req.Type))
	}
	if req.UserID != nil && *req.UserID > 0 {
		db = db.Where(query.JyhInviteCode.UserID.Eq(*req.UserID))
	}
	if req.LevelID != nil && *req.LevelID > 0 {
		db = db.Where(query.JyhInviteCode.LevelID.Eq(*req.LevelID))
	}
	if req.Status != nil {
		db = db.Where(query.JyhInviteCode.Status.Eq(*req.Status))
	}
	if req.IsUsed != nil {
		db = db.Where(query.JyhInviteCode.IsUsed.Is(*req.IsUsed))
	}
	if req.Code != "" {
		db = db.Where(query.JyhInviteCode.Code.Like("%" + req.Code + "%"))
	}
	if !jtime.Str2Date(req.StartTime).IsZero() {
		db = db.Where(query.JyhInviteCode.CreatedAt.Date().Gte(jtime.Str2Date(req.StartTime)))
	}
	if !jtime.Str2Date(req.EndTime).IsZero() {
		db = db.Where(query.JyhInviteCode.CreatedAt.Date().Lte(jtime.Str2Date(req.EndTime)))
	}

	dao1 := db.
		LeftJoin(dbLevel, dbCode.LevelID.EqCol(dbLevel.ID)).
		LeftJoin(dbUser, dbCode.UserID.EqCol(dbUser.ID)).
		LeftJoin(dbUseUser, dbCode.UsedByUID.EqCol(dbUser.ID))
	jgorm.SelectAppend(&dao1.DO,
		dbCode.ID,
		dbCode.Type,
		dbCode.UserID,
		dbCode.Code,
		dbCode.LevelID,
		dbLevel.Name.As("level_name"),
		dbCode.SalePrice,
		dbCode.UsedAt,
		dbCode.UsedByUID,
		dbCode.Status,
		dbCode.IsUsed,
		dbCode.ExpiredAt,
		dbCode.CreatedAt,
		dbUser.Username.As("user_name"),
		dbUser.Phone.As("user_phone"),
		dbUseUser.Username.As("used_by_name"),
	)
	// 获取总数
	count, err := db.Count()
	if err != nil {
		return nil, err
	}
	var items []*jyhResp.InviteCodeItem
	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	err = dao1.
		Offset(offset).
		Limit(req.PageSize).
		Order(dbCode.CreatedAt.Desc()).
		Scan(&items)
	for i, item := range items {
		items[i].UserPhone = HideIdent(item.UserPhone)
	}
	if err != nil {
		return nil, err
	}
	return &jyhResp.InviteCodeListResp{
		List:     items,
		Total:    count,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, nil
}

// UseInviteCode 使用邀请码
func (s *JyhInviteCodeService) UseInviteCode(code string, userID uint) (*jyhapp.JyhInviteCode, error) {
	// 查找邀请码
	inviteCode, err := query.JyhInviteCode.Where(query.JyhInviteCode.Code.Eq(code)).First()
	if err != nil {
		global.GVA_LOG.Error("查询邀请码失败", zap.Error(err))
		return nil, errors.New("邀请码不存在")
	}

	// 检查邀请码状态
	if inviteCode.IsUsed {
		return nil, errors.New("邀请码已被使用")
	}

	if inviteCode.Status == jyhapp.InviteCodeStatusExpired {
		return nil, errors.New("邀请码已过期")
	}

	// 检查是否过期
	if inviteCode.ExpiredAt != nil && time.Now().After(*inviteCode.ExpiredAt) {
		// 更新状态为过期
		_, err = query.JyhInviteCode.Where(query.JyhInviteCode.ID.Eq(inviteCode.ID)).
			Updates(map[string]interface{}{
				"status": jyhapp.InviteCodeStatusExpired,
			})
		if err != nil {
			global.GVA_LOG.Error("更新邀请码状态失败", zap.Error(err))
		}
		return nil, errors.New("邀请码已过期")
	}

	// 使用事务更新邀请码状态
	err = TryTransaction(func(tx *query.Query) error {
		return s.useInviteCodeInTx(tx, inviteCode, userID)
	})

	if err != nil {
		return nil, err
	}

	// 更新返回的邀请码信息
	inviteCode.IsUsed = true
	inviteCode.Status = jyhapp.InviteCodeStatusUsed
	now := time.Now()
	inviteCode.UsedAt = &now
	inviteCode.UsedByUID = &userID

	return inviteCode, nil
}

// UseInviteCodeTx 在指定事务中使用邀请码
func (s *JyhInviteCodeService) UseInviteCodeTx(tx *query.Query, code string, userID uint) (*jyhapp.JyhInviteCode, error) {
	// 查找邀请码
	inviteCode, err := tx.JyhInviteCode.Where(tx.JyhInviteCode.Code.Eq(code)).First()
	if err != nil {
		global.GVA_LOG.Error("查询邀请码失败", zap.Error(err))
		return nil, errors.New("邀请码不存在")
	}

	// 检查邀请码状态
	if inviteCode.IsUsed {
		return nil, errors.New("邀请码已被使用")
	}

	if inviteCode.Status == jyhapp.InviteCodeStatusExpired {
		return nil, errors.New("邀请码已过期")
	}

	// 检查是否过期
	if inviteCode.ExpiredAt != nil && time.Now().After(*inviteCode.ExpiredAt) {
		// 更新状态为过期
		_, err = tx.JyhInviteCode.Where(tx.JyhInviteCode.ID.Eq(inviteCode.ID)).
			Updates(map[string]interface{}{
				"status": jyhapp.InviteCodeStatusExpired,
			})
		if err != nil {
			global.GVA_LOG.Error("更新邀请码状态失败", zap.Error(err))
		}
		return nil, errors.New("邀请码已过期")
	}

	// 在传入的事务中使用邀请码
	err = s.useInviteCodeInTx(tx, inviteCode, userID)
	if err != nil {
		return nil, err
	}

	// 更新返回的邀请码信息
	inviteCode.IsUsed = true
	inviteCode.Status = jyhapp.InviteCodeStatusUsed
	now := time.Now()
	inviteCode.UsedAt = &now
	inviteCode.UsedByUID = &userID

	return inviteCode, nil
}

// useInviteCodeInTx 在事务中执行邀请码使用的核心逻辑
func (s *JyhInviteCodeService) useInviteCodeInTx(tx *query.Query, inviteCode *jyhapp.JyhInviteCode, userID uint) error {
	// 更新邀请码状态
	now := time.Now()
	_, err := tx.JyhInviteCode.Where(tx.JyhInviteCode.ID.Eq(inviteCode.ID)).
		Updates(map[string]interface{}{
			"used_at":     &now,
			"used_by_uid": userID,
			"status":      jyhapp.InviteCodeStatusUsed,
			"is_used":     true,
		})
	if err != nil {
		return err
	}

	// 创建邀请记录
	inviteRecord := &jyhapp.JyhInviteRecord{
		InviterID: 0, // 如果是平台邀请码，邀请人ID为0
		InviteeID: userID,
		CodeUsed:  inviteCode.Code,
	}
	if inviteCode.UserID != nil {
		inviteRecord.InviterID = *inviteCode.UserID
	}

	return tx.JyhInviteRecord.Create(inviteRecord)
}

// ValidateInviteCode 验证邀请码
func (s *JyhInviteCodeService) ValidateInviteCode(code string) (*jyhapp.JyhInviteCode, error) {
	if code == "" {
		return nil, errors.New("邀请码不能为空")
	}

	// 查找邀请码
	inviteCode, err := query.JyhInviteCode.Where(query.JyhInviteCode.Code.Eq(code)).First()
	if err != nil {
		global.GVA_LOG.Error("查询邀请码失败", zap.Error(err))
		return nil, errors.New("邀请码不存在")
	}

	// 检查邀请码状态
	if inviteCode.IsUsed {
		return nil, errors.New("邀请码已被使用")
	}

	if inviteCode.Status == jyhapp.InviteCodeStatusExpired {
		return nil, errors.New("邀请码已过期")
	}

	// 检查是否过期
	if inviteCode.ExpiredAt != nil && time.Now().After(*inviteCode.ExpiredAt) {
		// 更新状态为过期
		_, err = query.JyhInviteCode.Where(query.JyhInviteCode.ID.Eq(inviteCode.ID)).
			Updates(map[string]interface{}{
				"status": jyhapp.InviteCodeStatusExpired,
			})
		if err != nil {
			global.GVA_LOG.Error("更新邀请码状态失败", zap.Error(err))
		}
		return nil, errors.New("邀请码已过期")
	}

	return inviteCode, nil
}

// DeleteInviteCode 删除邀请码
func (s *JyhInviteCodeService) DeleteInviteCode(id uint) error {
	// 检查邀请码是否已被使用
	inviteCode, err := query.JyhInviteCode.Where(query.JyhInviteCode.ID.Eq(id)).First()
	if err != nil {
		return errors.New("邀请码不存在")
	}

	if inviteCode.IsUsed {
		return errors.New("已使用的邀请码不能删除")
	}

	// 软删除
	_, err = query.JyhInviteCode.Where(query.JyhInviteCode.ID.Eq(id)).Delete()
	return err
}

// UpdateInviteCodeStatus 更新邀请码状态
func (s *JyhInviteCodeService) UpdateInviteCodeStatus(id uint, status int) error {
	// 检查邀请码是否存在
	inviteCode, err := query.JyhInviteCode.Where(query.JyhInviteCode.ID.Eq(id)).First()
	if err != nil {
		return errors.New("邀请码不存在")
	}

	if inviteCode.IsUsed {
		return errors.New("已使用的邀请码不能修改状态")
	}

	// 更新状态
	_, err = query.JyhInviteCode.Where(query.JyhInviteCode.ID.Eq(id)).
		Update(query.JyhInviteCode.Status, status)
	return err
}

// GetUserInviteCodes 获取用户的邀请码列表
func (s *JyhInviteCodeService) GetUserInviteCodes(userID uint, req *request.PageInfo) (*jyhResp.InviteCodeListResp, error) {
	db := query.JyhInviteCode.Where(query.JyhInviteCode.UserID.Eq(userID))

	// 获取总数
	count, err := db.Count()
	if err != nil {
		return nil, err
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	inviteCodes, err := db.Offset(offset).
		Limit(req.PageSize).
		Order(query.JyhInviteCode.CreatedAt.Desc()).
		Find()
	if err != nil {
		return nil, err
	}

	// 构建响应数据
	var items []*jyhResp.InviteCodeItem
	for _, inviteCode := range inviteCodes {
		item := &jyhResp.InviteCodeItem{
			ID:        inviteCode.ID,
			Type:      inviteCode.Type,
			UserID:    inviteCode.UserID,
			Code:      inviteCode.Code,
			LevelID:   inviteCode.LevelID,
			SalePrice: inviteCode.SalePrice,
			UsedAt:    inviteCode.UsedAt,
			UsedByUID: inviteCode.UsedByUID,
			Status:    inviteCode.Status,
			IsUsed:    inviteCode.IsUsed,
			ExpiredAt: inviteCode.ExpiredAt,
			CreatedAt: inviteCode.CreatedAt,
		}
		items = append(items, item)
	}

	return &jyhResp.InviteCodeListResp{
		List:     items,
		Total:    count,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, nil
}

// generateUniqueInviteCode 生成唯一邀请码
func (s *JyhInviteCodeService) generateUniqueInviteCode() (string, error) {
	for i := 0; i < 10; i++ { // 最多尝试10次
		// 生成8位随机字符串（字母+数字）
		code := s.generateRandomCode(8)

		// 检查邀请码是否已存在
		count, err := query.JyhInviteCode.Where(query.JyhInviteCode.Code.Eq(code)).Count()
		if err != nil {
			return "", err
		}
		if count == 0 {
			return code, nil
		}
	}

	return "", errors.New("无法生成唯一邀请码")
}

// generateRandomCode 生成指定长度的随机字符串（字母+数字）
func (s *JyhInviteCodeService) generateRandomCode(length int) string {
	const charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	_, err := rand.Read(b)
	if err != nil {
		// 如果加密随机数生成失败，使用时间戳作为种子
		return fmt.Sprintf("%08X", time.Now().UnixNano()%100000000)[:length]
	}
	for i := range b {
		b[i] = charset[b[i]%byte(len(charset))]
	}
	return string(b)
}

// GetInviteCodeStatistics 获取邀请码统计信息
func (s *JyhInviteCodeService) GetInviteCodeStatistics() (*jyhResp.InviteCodeStatistics, error) {
	db := query.JyhInviteCode

	// 总邀请码数
	total, err := db.Count()
	if err != nil {
		return nil, err
	}

	// 已使用数量
	used, err := db.Where(db.IsUsed.Is(true)).Count()
	if err != nil {
		return nil, err
	}

	// 未使用数量
	unused, err := db.Where(db.IsUsed.Is(false), db.Status.Eq(jyhapp.InviteCodeStatusUnused)).Count()
	if err != nil {
		return nil, err
	}

	// 已过期数量
	expired, err := db.Where(db.Status.Eq(jyhapp.InviteCodeStatusExpired)).Count()
	if err != nil {
		return nil, err
	}

	// 平台邀请码数量
	platform, err := db.Where(db.Type.Eq(jyhapp.InviteCodeTypePlatform)).Count()
	if err != nil {
		return nil, err
	}

	// 用户邀请码数量
	user, err := db.Where(db.Type.Eq(jyhapp.InviteCodeTypeUser)).Count()
	if err != nil {
		return nil, err
	}

	return &jyhResp.InviteCodeStatistics{
		Total:    total,
		Used:     used,
		Unused:   unused,
		Expired:  expired,
		Platform: platform,
		User:     user,
	}, nil
}

// ProcessInviteCodeBenefit 处理邀请码生成权益
// 检查用户当前等级是否有 referral_code_once 权益，如果有且 price_cents=0，则生成对应等级的邀请码
func (s *JyhInviteCodeService) ProcessInviteCodeBenefit(tx *query.Query, userID uint, levelID uint, benefitEndAt time.Time) error {
	benefitDb := tx.JyhUserBenefit
	benefitMappingDb := tx.JyhUserShipLevelBenefit
	inviteCodeDb := tx.JyhInviteCode

	// 查找 referral_code_once 权益类型
	benefit, err := benefitDb.Where(benefitDb.Key.Eq("referral_code_once")).First()
	if err != nil {
		// 没有找到该权益类型，这是正常情况，直接返回
		global.GVA_LOG.Info("未找到 referral_code_once 权益类型", zap.Uint("levelID", levelID))
		return nil
	}

	// 查找当前等级是否配置了该权益
	benefitMapping, err := benefitMappingDb.Where(
		benefitMappingDb.LevelID.Eq(levelID),
		benefitMappingDb.BenefitID.Eq(benefit.ID),
	).First()
	if err != nil {
		// 该等级没有配置 referral_code_once 权益，这是正常情况
		global.GVA_LOG.Info("当前等级未配置 referral_code_once 权益",
			zap.Uint("levelID", levelID),
			zap.Uint("benefitID", benefit.ID))
		return nil
	}

	// 解析权益条件中的 price_cents 和 target_level_id
	var condition struct {
		TargetLevelID uint   `json:"target_level_id"`
		ExpireDays    int    `json:"expire_days"`
		PriceCents    uint64 `json:"price_cents"`
	}

	if benefitMapping.Condition != nil {
		err = json.Unmarshal(benefitMapping.Condition, &condition)
		if err != nil {
			global.GVA_LOG.Error("解析权益条件失败", zap.Error(err))
			return err
		}
	}

	// 检查是否为免费权益 (price_cents = 0)
	if condition.PriceCents != 0 {
		global.GVA_LOG.Info("非免费邀请码权益，跳过自动生成",
			zap.Uint("levelID", levelID),
			zap.Uint64("priceCents", condition.PriceCents))
		return nil
	}

	// 检查目标等级ID是否有效
	if condition.TargetLevelID == 0 {
		global.GVA_LOG.Error("邀请码权益缺少目标等级ID", zap.Uint("levelID", levelID))
		return errors.New("邀请码权益配置错误：缺少目标等级ID")
	}

	// 验证目标等级是否存在
	levelDb := tx.JyhUserShipLevel
	_, err = levelDb.Where(levelDb.ID.Eq(condition.TargetLevelID)).First()
	if err != nil {
		global.GVA_LOG.Error("目标等级不存在",
			zap.Uint("targetLevelID", condition.TargetLevelID))
		return errors.New("目标等级不存在")
	}

	// 根据权益值决定生成邀请码的数量（Value 表示数量）
	codeCount := int(benefitMapping.Value.IntPart())
	if codeCount <= 0 {
		global.GVA_LOG.Info("邀请码数量为0，跳过生成", zap.Uint("levelID", levelID))
		return nil
	}

	// 计算邀请码过期时间
	var expiredAt *time.Time
	if condition.ExpireDays > 0 {
		expireTime := time.Now().Add(time.Duration(condition.ExpireDays) * 24 * time.Hour)
		expiredAt = &expireTime
	} else {
		// 如果没有设置过期天数，使用权益结束时间
		expiredAt = &benefitEndAt
	}

	// 批量生成邀请码
	for i := 0; i < codeCount; i++ {
		// 生成唯一邀请码
		code, err := s.generateUniqueInviteCodeInTx(tx)
		if err != nil {
			global.GVA_LOG.Error("生成邀请码失败", zap.Error(err))
			continue // 继续生成其他邀请码
		}

		// 创建邀请码记录
		inviteCode := &jyhapp.JyhInviteCode{
			Type:      jyhapp.InviteCodeTypeUser, // 用户权益生成的邀请码
			UserID:    &userID,
			Code:      code,
			LevelID:   condition.TargetLevelID,
			SalePrice: 0, // 免费权益生成的邀请码
			Status:    jyhapp.InviteCodeStatusUnused,
			IsUsed:    false,
			ExpiredAt: expiredAt,
		}

		err = inviteCodeDb.Create(inviteCode)
		if err != nil {
			global.GVA_LOG.Error("保存邀请码失败", zap.Error(err))
			continue // 继续生成其他邀请码
		}

		global.GVA_LOG.Info("成功生成免费邀请码",
			zap.Uint("userID", userID),
			zap.String("code", code),
			zap.Uint("targetLevelID", condition.TargetLevelID))
	}

	global.GVA_LOG.Info("邀请码权益处理完成",
		zap.Uint("userID", userID),
		zap.Uint("levelID", levelID),
		zap.Int("codeCount", codeCount))

	return nil
}

// generateUniqueInviteCodeInTx 在事务中生成唯一邀请码
func (s *JyhInviteCodeService) generateUniqueInviteCodeInTx(tx *query.Query) (string, error) {
	inviteCodeDb := tx.JyhInviteCode

	for i := 0; i < 10; i++ { // 最多尝试10次
		// 生成8位随机字符串（字母+数字）
		code := s.generateRandomInviteCode(8)

		// 检查邀请码是否已存在
		count, err := inviteCodeDb.Where(inviteCodeDb.Code.Eq(code)).Count()
		if err != nil {
			return "", err
		}
		if count == 0 {
			return code, nil
		}
	}

	return "", errors.New("无法生成唯一邀请码")
}

// generateRandomInviteCode 生成指定长度的随机字符串（字母+数字）
func (s *JyhInviteCodeService) generateRandomInviteCode(length int) string {
	const charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	_, err := rand.Read(b)
	if err != nil {
		// 如果加密随机数生成失败，使用时间戳作为种子
		return fmt.Sprintf("%08X", time.Now().UnixNano()%100000000)[:length]
	}
	for i := range b {
		b[i] = charset[b[i]%byte(len(charset))]
	}
	return string(b)
}

// GetUserInviteCodesByLevel 获取用户的邀请码列表（按等级分类，只返回未使用的）
func (s *JyhInviteCodeService) GetUserInviteCodesByLevel(userID uint) (*jyhResp.UserInviteCodeListResp, error) {
	dbCode := query.JyhInviteCode
	dbLevel := query.JyhUserShipLevel

	// 查询用户未使用的邀请码，并关联等级信息
	var results []struct {
		jyhapp.JyhInviteCode
		LevelName string `gorm:"column:level_name"`
	}

	err := dbCode.
		LeftJoin(dbLevel, dbCode.LevelID.EqCol(dbLevel.ID)).
		Where(
			dbCode.UserID.Eq(userID),
			dbCode.IsUsed.Is(false),
			dbCode.Status.Eq(jyhapp.InviteCodeStatusUnused),
		).
		Select(dbCode.ALL, dbLevel.Name.As("level_name")).
		Order(dbCode.LevelID.Asc(), dbCode.CreatedAt.Desc()).
		Scan(&results)

	if err != nil {
		global.GVA_LOG.Error("查询用户邀请码失败", zap.Error(err))
		return nil, err
	}

	// 按等级分组
	levelMap := make(map[uint]*jyhResp.UserInviteCodeLevel)
	totalCount := 0

	for _, result := range results {
		levelID := result.LevelID

		// 如果等级不存在，创建新的等级分组
		if _, exists := levelMap[levelID]; !exists {
			levelMap[levelID] = &jyhResp.UserInviteCodeLevel{
				LevelID:     levelID,
				LevelName:   result.LevelName,
				CodeCount:   0,
				InviteCodes: make([]*jyhResp.UserInviteCodeItem, 0),
			}
		}

		// 添加邀请码到对应等级
		codeItem := &jyhResp.UserInviteCodeItem{
			ID:        result.ID,
			Code:      result.Code,
			SalePrice: int64(result.SalePrice),
			ExpiredAt: result.ExpiredAt,
			CreatedAt: result.CreatedAt,
		}

		levelMap[levelID].InviteCodes = append(levelMap[levelID].InviteCodes, codeItem)
		levelMap[levelID].CodeCount++
		totalCount++
	}

	// 转换为数组并按等级ID排序
	var levels []*jyhResp.UserInviteCodeLevel
	for _, level := range levelMap {
		levels = append(levels, level)
	}

	// 按等级ID排序
	for i := 0; i < len(levels)-1; i++ {
		for j := i + 1; j < len(levels); j++ {
			if levels[i].LevelID > levels[j].LevelID {
				levels[i], levels[j] = levels[j], levels[i]
			}
		}
	}

	return &jyhResp.UserInviteCodeListResp{
		TotalCount: totalCount,
		Levels:     levels,
	}, nil
}

// TransferInviteCode 转让邀请码
func (s *JyhInviteCodeService) TransferInviteCode(req *jyhReq.TransferInviteCodeReq, adminID uint) (*jyhResp.TransferInviteCodeResp, error) {
	// 开始事务
	tx := global.GVA_DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. 验证邀请码是否存在且可转让
	var inviteCode jyhapp.JyhInviteCode
	if err := tx.Where("id = ?", req.InviteCodeID).First(&inviteCode).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("邀请码不存在: %v", err)
	}

	// 检查邀请码状态
	if inviteCode.IsUsed {
		tx.Rollback()
		return nil, errors.New("邀请码已被使用，无法转让")
	}

	if inviteCode.Status == jyhapp.InviteCodeStatusExpired {
		tx.Rollback()
		return nil, errors.New("邀请码已过期，无法转让")
	}

	// 检查是否已过期
	if inviteCode.ExpiredAt != nil && inviteCode.ExpiredAt.Before(time.Now()) {
		tx.Rollback()
		return nil, errors.New("邀请码已过期，无法转让")
	}

	// 2. 验证目标用户是否存在
	var toUser jyhapp.JyhUser
	if err := tx.Where("id = ?", req.ToUserID).First(&toUser).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("目标用户不存在: %v", err)
	}

	// 3. 验证管理员是否存在
	var admin jyhapp.JyhUser
	if err := tx.Where("id = ?", adminID).First(&admin).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("管理员不存在: %v", err)
	}

	// 4. 检查是否转让给同一用户
	if inviteCode.UserID != nil && *inviteCode.UserID == req.ToUserID {
		tx.Rollback()
		return nil, errors.New("不能转让给当前持有用户")
	}

	// 5. 获取会员等级信息（用于记录）
	var level jyhapp.JyhUserLevel
	if err := tx.Where("id = ?", inviteCode.LevelID).First(&level).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("会员等级不存在: %v", err)
	}

	// 6. 创建转让记录
	transferRecord := &jyhapp.JyhInviteCodeTransfer{
		InviteCodeID: req.InviteCodeID,
		FromUserID:   inviteCode.UserID,
		ToUserID:     req.ToUserID,
		AdminID:      adminID,
		Reason:       req.Reason,
		TransferTime: time.Now(),
		InviteCode:   inviteCode.Code,
		LevelID:      inviteCode.LevelID,
		SalePrice:    inviteCode.SalePrice,
	}

	if err := tx.Create(transferRecord).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("创建转让记录失败: %v", err)
	}

	// 7. 更新邀请码的持有用户
	if err := tx.Model(&inviteCode).Update("user_id", req.ToUserID).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("更新邀请码持有用户失败: %v", err)
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("提交事务失败: %v", err)
	}

	global.GVA_LOG.Info("邀请码转让成功",
		zap.Uint("invite_code_id", req.InviteCodeID),
		zap.String("invite_code", inviteCode.Code),
		zap.Any("from_user_id", inviteCode.UserID),
		zap.Uint("to_user_id", req.ToUserID),
		zap.Uint("admin_id", adminID),
		zap.String("reason", req.Reason),
	)

	return &jyhResp.TransferInviteCodeResp{
		TransferID: transferRecord.ID,
	}, nil
}
