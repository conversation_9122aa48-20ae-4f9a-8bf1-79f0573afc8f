package jyhapp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	jyhReq "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type AdminUserApi struct{}

// GetUserList 获取用户列表
// @Tags      AdminUser
// @Summary   获取用户列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     jyhReq.AdminUserListReq  true  "获取用户列表请求"
// @Success   200   {object}  response.Response{data=response.PageResult,msg=string}  "获取用户列表"
// @Router    /admin/user/list [get]
func (m *AdminUserApi) GetUserList(c *gin.Context) {
	var req jyhReq.AdminUserListReq
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := userService.GetUserList(&req)
	if err != nil {
		global.GVA_LOG.Error("获取用户列表失败！", zap.Error(err))
		response.FailWithMessage("获取用户列表失败:"+err.Error(), c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取用户列表成功", c)
}

// CreateUser 创建用户
// @Tags      AdminUser
// @Summary   创建用户
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      jyhReq.AdminUserReq  true  "创建用户请求"
// @Success   200   {object}  response.Response{data=response.AdminUserCreateResp,msg=string}  "创建用户"
// @Router    /admin/user/create [post]
func (m *AdminUserApi) CreateUser(c *gin.Context) {
	var req jyhReq.AdminUserReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	resp, err := userService.AdminCreateUser(&req)
	if err != nil {
		global.GVA_LOG.Error("创建用户失败！", zap.Error(err))
		response.FailWithMessage("创建用户失败:"+err.Error(), c)
		return
	}

	response.OkWithDetailed(resp, "创建用户成功", c)
}

// UpdateUser 更新用户
// @Tags      AdminUser
// @Summary   更新用户
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      jyhReq.AdminUserReq  true  "更新用户请求"
// @Success   200   {object}  response.Response{msg=string}  "更新用户"
// @Router    /admin/user/update [put]
func (m *AdminUserApi) UpdateUser(c *gin.Context) {
	var req jyhReq.AdminUserReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = userService.AdminUpdateUser(&req)
	if err != nil {
		global.GVA_LOG.Error("更新用户失败！", zap.Error(err))
		response.FailWithMessage("更新用户失败:"+err.Error(), c)
		return
	}

	response.OkWithMessage("更新用户成功", c)
}

// GetUserDetail 获取用户详情
// @Tags      AdminUser
// @Summary   获取用户详情
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     request.GetById  true  "获取用户详情请求"
// @Success   200   {object}  response.Response{data=response.AdminUserDetailResp,msg=string}  "获取用户详情"
// @Router    /admin/user/detail [get]
func (m *AdminUserApi) GetUserDetail(c *gin.Context) {
	var req request.GetById
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	resp, err := userService.AdminGetUserDetail(req.GetUintId())
	if err != nil {
		global.GVA_LOG.Error("获取用户详情失败！", zap.Error(err))
		response.FailWithMessage("获取用户详情失败:"+err.Error(), c)
		return
	}

	response.OkWithDetailed(resp, "获取用户详情成功", c)
}

// ToggleUserStatus 切换用户状态
// @Tags      AdminUser
// @Summary   切换用户状态
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      jyhReq.AdminUserStatusReq  true  "切换用户状态请求"
// @Success   200   {object}  response.Response{msg=string}  "切换用户状态"
// @Router    /admin/user/toggle_status [post]
func (m *AdminUserApi) ToggleUserStatus(c *gin.Context) {
	var req jyhReq.AdminUserStatusReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = userService.AdminToggleUserStatus(req.ID, req.Status)
	if err != nil {
		global.GVA_LOG.Error("切换用户状态失败！", zap.Error(err))
		response.FailWithMessage("切换用户状态失败:"+err.Error(), c)
		return
	}

	response.OkWithMessage("操作成功", c)
}

// ReviewCertificate 审核凭证
// @Tags AdminUser
// @Summary 审核凭证
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body jyhReq.CertificateReview true "审核信息"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"审核成功"}"
// @Router /admin/certificate/review [post]
func (m *AdminUserApi) ReviewCertificate(c *gin.Context) {
	var req jyhReq.CertificateReview
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	currentUserID := utils.GetUserID(c)
	if currentUserID == 0 {
		response.FailWithMessage("用户未登录", c)
		return
	}

	if err := userService.ReviewCertificate(currentUserID, &req); err != nil {
		global.GVA_LOG.Error("审核失败", zap.Error(err))
		response.FailWithMessage("审核失败: "+err.Error(), c)
	} else {
		response.OkWithMessage("审核成功", c)
	}
}

// GetAllCertificates 获取所有凭证列表
// @Tags AdminUser
// @Summary 获取所有凭证列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query jyhReq.CertificateSearch true "查询参数"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /admin/certificate/list [get]
func (m *AdminUserApi) GetAllCertificates(c *gin.Context) {
	var req jyhReq.CertificateSearch
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	list, total, err := userService.GetAllCertificates(&req)
	if err != nil {
		global.GVA_LOG.Error("获取凭证列表失败", zap.Error(err))
		response.FailWithMessage("获取凭证列表失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}

// GetCertificateDetail 获取凭证详情
// @Tags AdminUser
// @Summary 获取凭证详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id query uint true "凭证ID"
// @Success 200 {object} response.Response{data=response.PageResult} "获取成功"
// @Router /certificate/detail [get]
func (m *AdminUserApi) GetCertificateDetail(c *gin.Context) {
	userID := utils.GetUserID(c)
	if userID == 0 {
		response.FailWithMessage("未登录", c)
		return
	}

	var req request.GetById
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	certificate, err := userService.GetCertificateDetail(uint(req.ID))
	if err != nil {
		global.GVA_LOG.Error("获取凭证详情失败", zap.Error(err))
		response.FailWithMessage("获取凭证详情失败", c)
		return
	}

	// 检查权限
	if certificate.UserID != userID {
		response.FailWithMessage("无权访问此凭证", c)
		return
	}

	response.OkWithData(certificate, c)
}
