// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
)

func newJyhUserTag(db *gorm.DB, opts ...gen.DOOption) jyhUserTag {
	_jyhUserTag := jyhUserTag{}

	_jyhUserTag.jyhUserTagDo.UseDB(db, opts...)
	_jyhUserTag.jyhUserTagDo.UseModel(&jyhapp.JyhUserTag{})

	tableName := _jyhUserTag.jyhUserTagDo.TableName()
	_jyhUserTag.ALL = field.NewAsterisk(tableName)
	_jyhUserTag.ID = field.NewUint(tableName, "id")
	_jyhUserTag.CreatedAt = field.NewTime(tableName, "created_at")
	_jyhUserTag.UpdatedAt = field.NewTime(tableName, "updated_at")
	_jyhUserTag.DeletedAt = field.NewField(tableName, "deleted_at")
	_jyhUserTag.Name = field.NewString(tableName, "name")
	_jyhUserTag.Description = field.NewString(tableName, "description")
	_jyhUserTag.Color = field.NewString(tableName, "color")
	_jyhUserTag.Status = field.NewUint(tableName, "status")
	_jyhUserTag.Sort = field.NewUint(tableName, "sort")
	_jyhUserTag.Users = jyhUserTagManyToManyUsers{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Users", "jyhapp.JyhUser"),
		Inviter: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Users.Inviter", "jyhapp.JyhUser"),
		},
		JyhUserExt: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Users.JyhUserExt", "jyhapp.JyhUserExt"),
		},
		Invitees: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Users.Invitees", "jyhapp.JyhUser"),
		},
		Tags: struct {
			field.RelationField
			Users struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Users.Tags", "jyhapp.JyhUserTag"),
			Users: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Users.Tags.Users", "jyhapp.JyhUser"),
			},
		},
	}

	_jyhUserTag.fillFieldMap()

	return _jyhUserTag
}

type jyhUserTag struct {
	jyhUserTagDo

	ALL         field.Asterisk
	ID          field.Uint
	CreatedAt   field.Time
	UpdatedAt   field.Time
	DeletedAt   field.Field
	Name        field.String
	Description field.String
	Color       field.String
	Status      field.Uint
	Sort        field.Uint
	Users       jyhUserTagManyToManyUsers

	fieldMap map[string]field.Expr
}

func (j jyhUserTag) Table(newTableName string) *jyhUserTag {
	j.jyhUserTagDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j jyhUserTag) As(alias string) *jyhUserTag {
	j.jyhUserTagDo.DO = *(j.jyhUserTagDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *jyhUserTag) updateTableName(table string) *jyhUserTag {
	j.ALL = field.NewAsterisk(table)
	j.ID = field.NewUint(table, "id")
	j.CreatedAt = field.NewTime(table, "created_at")
	j.UpdatedAt = field.NewTime(table, "updated_at")
	j.DeletedAt = field.NewField(table, "deleted_at")
	j.Name = field.NewString(table, "name")
	j.Description = field.NewString(table, "description")
	j.Color = field.NewString(table, "color")
	j.Status = field.NewUint(table, "status")
	j.Sort = field.NewUint(table, "sort")

	j.fillFieldMap()

	return j
}

func (j *jyhUserTag) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *jyhUserTag) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 10)
	j.fieldMap["id"] = j.ID
	j.fieldMap["created_at"] = j.CreatedAt
	j.fieldMap["updated_at"] = j.UpdatedAt
	j.fieldMap["deleted_at"] = j.DeletedAt
	j.fieldMap["name"] = j.Name
	j.fieldMap["description"] = j.Description
	j.fieldMap["color"] = j.Color
	j.fieldMap["status"] = j.Status
	j.fieldMap["sort"] = j.Sort

}

func (j jyhUserTag) clone(db *gorm.DB) jyhUserTag {
	j.jyhUserTagDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j jyhUserTag) replaceDB(db *gorm.DB) jyhUserTag {
	j.jyhUserTagDo.ReplaceDB(db)
	return j
}

type jyhUserTagManyToManyUsers struct {
	db *gorm.DB

	field.RelationField

	Inviter struct {
		field.RelationField
	}
	JyhUserExt struct {
		field.RelationField
	}
	Invitees struct {
		field.RelationField
	}
	Tags struct {
		field.RelationField
		Users struct {
			field.RelationField
		}
	}
}

func (a jyhUserTagManyToManyUsers) Where(conds ...field.Expr) *jyhUserTagManyToManyUsers {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a jyhUserTagManyToManyUsers) WithContext(ctx context.Context) *jyhUserTagManyToManyUsers {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a jyhUserTagManyToManyUsers) Session(session *gorm.Session) *jyhUserTagManyToManyUsers {
	a.db = a.db.Session(session)
	return &a
}

func (a jyhUserTagManyToManyUsers) Model(m *jyhapp.JyhUserTag) *jyhUserTagManyToManyUsersTx {
	return &jyhUserTagManyToManyUsersTx{a.db.Model(m).Association(a.Name())}
}

type jyhUserTagManyToManyUsersTx struct{ tx *gorm.Association }

func (a jyhUserTagManyToManyUsersTx) Find() (result []*jyhapp.JyhUser, err error) {
	return result, a.tx.Find(&result)
}

func (a jyhUserTagManyToManyUsersTx) Append(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a jyhUserTagManyToManyUsersTx) Replace(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a jyhUserTagManyToManyUsersTx) Delete(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a jyhUserTagManyToManyUsersTx) Clear() error {
	return a.tx.Clear()
}

func (a jyhUserTagManyToManyUsersTx) Count() int64 {
	return a.tx.Count()
}

type jyhUserTagDo struct{ gen.DO }

func (j jyhUserTagDo) Debug() *jyhUserTagDo {
	return j.withDO(j.DO.Debug())
}

func (j jyhUserTagDo) WithContext(ctx context.Context) *jyhUserTagDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jyhUserTagDo) ReadDB() *jyhUserTagDo {
	return j.Clauses(dbresolver.Read)
}

func (j jyhUserTagDo) WriteDB() *jyhUserTagDo {
	return j.Clauses(dbresolver.Write)
}

func (j jyhUserTagDo) Session(config *gorm.Session) *jyhUserTagDo {
	return j.withDO(j.DO.Session(config))
}

func (j jyhUserTagDo) Clauses(conds ...clause.Expression) *jyhUserTagDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jyhUserTagDo) Returning(value interface{}, columns ...string) *jyhUserTagDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jyhUserTagDo) Not(conds ...gen.Condition) *jyhUserTagDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jyhUserTagDo) Or(conds ...gen.Condition) *jyhUserTagDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jyhUserTagDo) Select(conds ...field.Expr) *jyhUserTagDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jyhUserTagDo) Where(conds ...gen.Condition) *jyhUserTagDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jyhUserTagDo) Order(conds ...field.Expr) *jyhUserTagDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jyhUserTagDo) Distinct(cols ...field.Expr) *jyhUserTagDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jyhUserTagDo) Omit(cols ...field.Expr) *jyhUserTagDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jyhUserTagDo) Join(table schema.Tabler, on ...field.Expr) *jyhUserTagDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jyhUserTagDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jyhUserTagDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jyhUserTagDo) RightJoin(table schema.Tabler, on ...field.Expr) *jyhUserTagDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jyhUserTagDo) Group(cols ...field.Expr) *jyhUserTagDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jyhUserTagDo) Having(conds ...gen.Condition) *jyhUserTagDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jyhUserTagDo) Limit(limit int) *jyhUserTagDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jyhUserTagDo) Offset(offset int) *jyhUserTagDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jyhUserTagDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jyhUserTagDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jyhUserTagDo) Unscoped() *jyhUserTagDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jyhUserTagDo) Create(values ...*jyhapp.JyhUserTag) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jyhUserTagDo) CreateInBatches(values []*jyhapp.JyhUserTag, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jyhUserTagDo) Save(values ...*jyhapp.JyhUserTag) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jyhUserTagDo) First() (*jyhapp.JyhUserTag, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserTag), nil
	}
}

func (j jyhUserTagDo) Take() (*jyhapp.JyhUserTag, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserTag), nil
	}
}

func (j jyhUserTagDo) Last() (*jyhapp.JyhUserTag, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserTag), nil
	}
}

func (j jyhUserTagDo) Find() ([]*jyhapp.JyhUserTag, error) {
	result, err := j.DO.Find()
	return result.([]*jyhapp.JyhUserTag), err
}

func (j jyhUserTagDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*jyhapp.JyhUserTag, err error) {
	buf := make([]*jyhapp.JyhUserTag, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jyhUserTagDo) FindInBatches(result *[]*jyhapp.JyhUserTag, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jyhUserTagDo) Attrs(attrs ...field.AssignExpr) *jyhUserTagDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jyhUserTagDo) Assign(attrs ...field.AssignExpr) *jyhUserTagDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jyhUserTagDo) Joins(fields ...field.RelationField) *jyhUserTagDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jyhUserTagDo) Preload(fields ...field.RelationField) *jyhUserTagDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jyhUserTagDo) FirstOrInit() (*jyhapp.JyhUserTag, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserTag), nil
	}
}

func (j jyhUserTagDo) FirstOrCreate() (*jyhapp.JyhUserTag, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhUserTag), nil
	}
}

func (j jyhUserTagDo) FindByPage(offset int, limit int) (result []*jyhapp.JyhUserTag, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jyhUserTagDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jyhUserTagDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jyhUserTagDo) Delete(models ...*jyhapp.JyhUserTag) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jyhUserTagDo) withDO(do gen.Dao) *jyhUserTagDo {
	j.DO = *do.(*gen.DO)
	return j
}
