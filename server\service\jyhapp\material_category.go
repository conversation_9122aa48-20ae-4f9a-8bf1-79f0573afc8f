package jyhapp

import (
	"errors"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
	"go.uber.org/zap"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	commonReq "github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/query"
	jyhReq "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/request"
	jyhRes "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/response"
)

type MaterialCategoryService struct{}

// Create 创建素材分类
func (s *MaterialCategoryService) Create(req jyhReq.MaterialCategoryCreate) (err error) {
	dbCategory := query.JyhMaterialCategory
	// 检查分类名称是否存在
	res := &jyhapp.JyhMaterialCategory{}
	err = dbCategory.Where(dbCategory.CatName.Eq(req.CatName)).Scan(&res)
	if err != nil {
		global.GVA_LOG.Error("查询素材分类失败", zap.Error(err))
		return err
	}

	if res.ID > 0 {
		return errors.New("分类名称重复")
	}

	// 检查父级分类是否存在
	if req.ParentID > 0 {
		_, err = dbCategory.Where(dbCategory.ID.Eq(req.ParentID)).First()
		if err != nil {
			return errors.New("父级分类不存在")
		}
	}

	category := &jyhapp.JyhMaterialCategory{
		CatName:     req.CatName,
		ParentID:    req.ParentID,
		Sort:        req.Sort,
		CatDesc:     req.CatDesc,
		IsActive:    req.IsActive,
		DouyinUrl:   req.DouyinUrl,
		Copywriting: req.Copywriting,
	}

	return dbCategory.Create(category)
}

// Update 更新素材分类
func (s *MaterialCategoryService) Update(req *jyhReq.MaterialCategoryUpdate) (err error) {
	dbCategory := query.JyhMaterialCategory

	res := &jyhapp.JyhMaterialCategory{}
	err = dbCategory.Where(dbCategory.CatName.Eq(req.CatName), dbCategory.ID.Neq(req.ID)).Scan(&res)
	if err != nil {
		return err
	}
	if res.ID > 0 {
		return errors.New("分类名称重复")
	}

	_, err = dbCategory.Where(dbCategory.ID.Eq(req.ID)).First()
	if err != nil {
		return errors.New("分类不存在")
	}

	// 检查父级分类是否存在且不能是自己的子分类
	if req.ParentID > 0 {
		if req.ParentID == req.ID {
			return errors.New("不能选择自己作为父级分类")
		}
		_, err = dbCategory.Where(dbCategory.ID.Eq(req.ParentID)).First()
		if err != nil {
			return errors.New("父级分类不存在")
		}
		// 检查是否选择了自己的子分类作为父级
		if err = s.checkParentIsChild(req.ID, req.ParentID); err != nil {
			return err
		}
	}

	// 更新分类信息
	updates := map[string]interface{}{
		"cat_name":    req.CatName,
		"parent_id":   req.ParentID,
		"sort":        req.Sort,
		"is_active":   req.IsActive,
		"cat_desc":    req.CatDesc,
		"douyin_url":  req.DouyinUrl,
		"copywriting": req.Copywriting,
	}

	_, err = dbCategory.Select(dbCategory.CatName, dbCategory.ParentID,
		dbCategory.Sort, dbCategory.IsActive, dbCategory.CatDesc, dbCategory.DouyinUrl, dbCategory.Copywriting).Where(dbCategory.ID.Eq(req.ID)).Updates(updates)
	return err
}

// Delete 删除素材分类
func (s *MaterialCategoryService) Delete(id uint) (err error) {
	dbCategory := query.JyhMaterialCategory

	// 检查是否存在子分类
	count, err := dbCategory.Where(dbCategory.ParentID.Eq(id)).Count()
	if err != nil {
		return err
	}
	if count > 0 {
		return errors.New("存在子分类，无法删除")
	}

	// 检查是否有关联的素材
	materialQuery := query.JyhMaterial
	materialCount, err := materialQuery.Where(materialQuery.CategoryID.Eq(id)).Count()
	if err != nil {
		return err
	}
	if materialCount > 0 {
		return errors.New("存在关联的素材，无法删除")
	}

	_, err = dbCategory.Where(dbCategory.ID.Eq(id)).Unscoped().Delete()
	return err
}

// GetDetail 获取分类详情
func (s *MaterialCategoryService) GetDetail(id uint) (category *jyhapp.JyhMaterialCategory, err error) {
	dbCategory := query.JyhMaterialCategory
	category, err = dbCategory.Where(dbCategory.ID.Eq(id)).First()
	return
}

// GetList 获取分类列表
func (s *MaterialCategoryService) GetList(req *jyhReq.MaterialCategorySearch, pageInfo *commonReq.PageInfo) (list []*jyhapp.JyhMaterialCategory, total int64, err error) {
	dbCategory := query.JyhMaterialCategory
	queryBuilder := dbCategory.WithContext(global.GVA_DB.Statement.Context)

	// 构建查询条件
	if req.CatName != "" {
		queryBuilder = queryBuilder.Where(dbCategory.CatName.Like("%" + req.CatName + "%"))
	}
	if req.ParentID != nil {
		queryBuilder = queryBuilder.Where(dbCategory.ParentID.Eq(*req.ParentID))
	}
	if req.IsActive {
		queryBuilder = queryBuilder.Where(dbCategory.IsActive.Is(req.IsActive))
	}

	// 获取总数
	total, err = queryBuilder.Count()
	if err != nil {
		return
	}

	if req.Page == 0 && req.PageSize == -1 {
		list, err = queryBuilder.Order(dbCategory.Sort).Find()
		return
	}

	// 分页查询
	if pageInfo != nil {
		queryBuilder = queryBuilder.Limit(pageInfo.PageSize).Offset((pageInfo.Page - 1) * pageInfo.PageSize)
	}
	list, err = queryBuilder.Order(dbCategory.Sort).Find()
	return
}

// checkParentIsChild 检查父级是否是自己的子分类
func (s *MaterialCategoryService) checkParentIsChild(id, parentID uint) error {
	dbCategory := query.JyhMaterialCategory

	parentCategory, err := dbCategory.Where(dbCategory.ID.Eq(parentID)).First()
	if err != nil {
		return err
	}

	// 递归检查父级的父级是否包含当前分类
	for parentCategory.ParentID > 0 {
		if parentCategory.ParentID == id {
			return errors.New("不能选择自己的子分类作为父级")
		}
		parentCategory, err = dbCategory.Where(dbCategory.ID.Eq(parentCategory.ParentID)).First()
		if err != nil {
			return err
		}
	}
	return nil
}

// 树形返回分类列表
func (s *MaterialCategoryService) GetTreeList() (list []*jyhRes.MaterialCategoryTree, err error) {
	dbCategory := query.JyhMaterialCategory
	queryBuilder := dbCategory.WithContext(global.GVA_DB.Statement.Context)

	// 获取所有分类
	categories, err := queryBuilder.Order(dbCategory.Sort).Find()
	if err != nil {
		return list, err
	}

	// 构建树形结构
	return s.getChildrenList(categories, 0), nil
}

// getChildrenList 递归构建树形结构
func (s *MaterialCategoryService) getChildrenList(categories []*jyhapp.JyhMaterialCategory, parentID uint) []*jyhRes.MaterialCategoryTree {
	tree := make([]*jyhRes.MaterialCategoryTree, 0)
	for _, category := range categories {
		if category.ParentID == parentID {

			node := &jyhRes.MaterialCategoryTree{
				ID:          category.ID,
				CatName:     category.CatName,
				ParentID:    category.ParentID,
				Sort:        category.Sort,
				IsActive:    category.IsActive,
				CatDesc:     category.CatDesc,
				DouyinUrl:   category.DouyinUrl,
				Copywriting: category.Copywriting,
				Children:    s.getChildrenList(categories, category.ID),
			}
			tree = append(tree, node)
		}
	}
	return tree
}

// GetListByParentId 获取分类列表通过parentsIds
func (s *MaterialCategoryService) GetListByParentId(req jyhReq.MaterialCategoryParentId) (list []jyhRes.MaterialCategoryParent, err error) {
	dbCategory := query.JyhMaterialCategory
	queryBuilder := dbCategory.WithContext(global.GVA_DB.Statement.Context)

	// 构建查询条件
	if len(req.ParentIds) == 0 {
		return nil, errors.New("parentIds不能为空")
	}

	queryBuilder = queryBuilder.Where(dbCategory.ParentID.In(req.ParentIds...))
	// 获取分类列表
	var categories []*jyhapp.JyhMaterialCategory
	err = queryBuilder.Order(dbCategory.Sort).Scan(&categories)
	if err != nil {
		global.GVA_LOG.Error("获取素材分类列表失败", zap.Error(err))
		return nil, err
	}

	//处理返回
	for _, parentId := range req.ParentIds {
		parent := &jyhRes.MaterialCategoryParent{
			ParentID: parentId,
			List:     make([]jyhRes.MaterialCategoryListResponse, 0),
		}

		for _, category := range categories {
			if category.ParentID == parentId {
				parent.List = append(parent.List, jyhRes.MaterialCategoryListResponse{
					ID:       category.ID,
					CatName:  category.CatName,
					ParentID: category.ParentID,
					Sort:     category.Sort,
					CatDesc:  category.CatDesc,
				})
			}
		}

		list = append(list, *parent)
	}

	return list, nil
}

// GetSubCategoryIDs 遍历获取所有子分类的ID
func (s *MaterialCategoryService) GetSubCategoryIDs(parentID uint) (ids []uint, err error) {
	dbCategory := query.JyhMaterialCategory
	queryBuilder := dbCategory.WithContext(global.GVA_DB.Statement.Context)

	// 获取所有子分类
	var categories []*jyhapp.JyhMaterialCategory
	err = queryBuilder.Select(dbCategory.ID, dbCategory.ParentID).
		Where(dbCategory.ParentID.Eq(parentID)).Scan(&categories)
	if err != nil {
		global.GVA_LOG.Error("获取子分类失败", zap.Error(err))
		return nil, err
	}

	// 递归获取子分类的ID
	for _, category := range categories {
		ids = append(ids, category.ID)
		subIDs, err := s.GetSubCategoryIDs(category.ID)
		if err != nil {
			return nil, err
		}
		ids = append(ids, subIDs...)
	}

	return ids, nil
}
