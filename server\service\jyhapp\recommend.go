package jyhapp

import (
	"errors"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/errno"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/query"
	jyhReq "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/request"
	"go.uber.org/zap"
	"gorm.io/gen"
)

type RecommendPositionService struct{}

// CreateRecommendPosition 创建推荐位
func (s *RecommendPositionService) CreateRecommendPosition(req jyhReq.RecommendPositionCreate) error {
	dbPosition := query.JyhRecommendPosition

	// 检查推荐位标识是否已存在
	res := &jyhapp.JyhRecommendPosition{}
	err := dbPosition.Where(dbPosition.PositionKey.Eq(req.PositionKey)).Scan(&res)
	if err != nil {
		global.GVA_LOG.Error("查询推荐位失败", zap.Error(err))
		return err
	}

	if res.ID > 0 {
		return errors.New("推荐位标识已存在")
	}

	// 创建推荐位
	position := &jyhapp.JyhRecommendPosition{
		Name:        req.Name,
		PositionKey: req.PositionKey,
		Description: req.Description,
		Width:       req.Width,
		Height:      req.Height,
		IsEnabled:   req.IsEnabled,
		MaxItems:    req.MaxItems,
	}

	return dbPosition.Create(position)
}

// UpdateRecommendPosition 更新推荐位
func (s *RecommendPositionService) UpdateRecommendPosition(req *jyhReq.RecommendPositionUpdate) error {
	dbPosition := query.JyhRecommendPosition

	// 检查推荐位是否存在
	_, err := dbPosition.Where(dbPosition.ID.Eq(req.ID)).First()
	if err != nil {
		return errors.New("推荐位不存在")
	}

	// 检查推荐位标识是否与其他记录冲突
	res := &jyhapp.JyhRecommendPosition{}
	err = dbPosition.Where(
		dbPosition.PositionKey.Eq(req.PositionKey),
		dbPosition.ID.Neq(req.ID),
	).Scan(&res)
	if err != nil {
		return err
	}
	if res.ID > 0 {
		return errors.New("推荐位标识已存在")
	}

	// 更新推荐位信息
	updates := map[string]interface{}{
		"name":         req.Name,
		"position_key": req.PositionKey,
		"description":  req.Description,
		"width":        req.Width,
		"height":       req.Height,
		"is_enabled":   req.IsEnabled,
		"max_items":    req.MaxItems,
	}

	_, err = dbPosition.Select(
		dbPosition.Name,
		dbPosition.PositionKey,
		dbPosition.Description,
		dbPosition.Width,
		dbPosition.Height,
		dbPosition.IsEnabled,
	).Where(dbPosition.ID.Eq(req.ID)).Updates(updates)
	return err
}

// DeleteRecommendPosition 删除推荐位
func (s *RecommendPositionService) DeleteRecommendPosition(id uint) error {
	dbPosition := query.JyhRecommendPosition
	dbItem := query.JyhRecommendItem

	// 检查是否有推荐项存在
	itemCount, err := dbItem.Where(dbItem.PositionID.Eq(id)).Count()
	if err != nil {
		return err
	}
	if itemCount > 0 {
		return errors.New("存在关联的推荐项，无法删除")
	}

	// 删除推荐位
	_, err = dbPosition.Where(dbPosition.ID.Eq(id)).Delete()
	return err
}

// GetRecommendPositionDetail 获取推荐位详情
func (s *RecommendPositionService) GetRecommendPositionDetail(id uint) (position *jyhapp.JyhRecommendPosition, err error) {
	dbPosition := query.JyhRecommendPosition
	position, err = dbPosition.Where(dbPosition.ID.Eq(id)).First()
	return
}

// GetRecommendPositionList 获取推荐位列表
func (s *RecommendPositionService) GetRecommendPositionList(req *jyhReq.RecommendPositionSearch) (list []*jyhapp.JyhRecommendPosition, total int64, err error) {
	dbPosition := query.JyhRecommendPosition
	queryBuilder := dbPosition.WithContext(global.GVA_DB.Statement.Context)

	// 构建查询条件
	if req.Name != "" {
		queryBuilder = queryBuilder.Where(dbPosition.Name.Like("%" + req.Name + "%"))
	}
	if req.PositionKey != "" {
		queryBuilder = queryBuilder.Where(dbPosition.PositionKey.Like("%" + req.PositionKey + "%"))
	}

	// 获取总数
	total, err = queryBuilder.Count()
	if err != nil {
		return
	}

	// 处理分页
	if req.Page == 0 && req.PageSize == -1 {
		list, err = queryBuilder.Order(dbPosition.ID).Find()
		return
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	list, err = queryBuilder.
		Limit(req.PageSize).
		Offset(offset).
		Order(dbPosition.ID).
		Find()

	return
}

// CreateRecommendItem 创建推荐内容项
func (s *RecommendPositionService) CreateRecommendItem(req jyhReq.RecommendItemCreate) error {
	dbItem := query.JyhRecommendItem
	dbPosition := query.JyhRecommendPosition

	// 验证推荐位是否存在
	if _, err := dbPosition.Where(dbPosition.ID.Eq(req.PositionID)).First(); err != nil {
		return errors.New("关联的推荐位不存在")
	}

	// 检查排序值是否冲突
	if req.Sort > 0 {
		count, err := dbItem.Where(
			dbItem.PositionID.Eq(req.PositionID),
			dbItem.Sort.Eq(req.Sort),
		).Count()
		if err != nil {
			return err
		}
		if count > 0 {
			return errors.New("该排序值已被使用")
		}
	}

	// 创建内容项
	item := &jyhapp.JyhRecommendItem{
		PositionID:   req.PositionID,
		Title:        req.Title,
		ContentType:  req.ContentType,
		ContentValue: req.ContentValue,
		LinkUrl:      req.LinkUrl,
		BgColor:      req.BgColor,
		TextColor:    req.TextColor,
		StartTime:    req.StartTime,
		EndTime:      req.EndTime,
		Sort:         req.Sort,
		Status:       req.Status,
	}

	return dbItem.Create(item)
}

// UpdateRecommendItem 更新推荐内容项
func (s *RecommendPositionService) UpdateRecommendItem(req *jyhReq.RecommendItemUpdate) error {
	dbItem := query.JyhRecommendItem
	dbPosition := query.JyhRecommendPosition

	// 验证内容项是否存在
	existingItem, err := dbItem.Where(dbItem.ID.Eq(req.ID)).First()
	if err != nil {
		return errors.New("推荐内容项不存在")
	}

	// 验证推荐位是否存在（如果更改了关联推荐位）
	if req.PositionID != existingItem.PositionID {
		if _, err := dbPosition.Where(dbPosition.ID.Eq(req.PositionID)).First(); err != nil {
			return errors.New("关联的推荐位不存在")
		}
	}

	// 检查排序值是否冲突
	if req.Sort > 0 && req.Sort != existingItem.Sort {
		count, err := dbItem.Where(
			dbItem.PositionID.Eq(req.PositionID),
			dbItem.Sort.Eq(req.Sort),
			dbItem.ID.Neq(req.ID),
		).Count()
		if err != nil {
			return err
		}
		if count > 0 {
			return errors.New("该排序值已被使用")
		}
	}

	// 更新时间范围有效性
	if req.StartTime.After(req.EndTime) {
		return errors.New("开始时间不能晚于结束时间")
	}

	// 更新内容项
	updates := map[string]interface{}{
		"position_id":   req.PositionID,
		"title":         req.Title,
		"content_type":  req.ContentType,
		"content_value": req.ContentValue,
		"link_url":      req.LinkUrl,
		"bg_color":      req.BgColor,
		"text_color":    req.TextColor,
		"start_time":    req.StartTime,
		"end_time":      req.EndTime,
		"sort":          req.Sort,
		"status":        req.Status,
	}

	_, err = dbItem.Select(
		dbItem.PositionID,
		dbItem.Title,
		dbItem.ContentType,
		dbItem.ContentValue,
		dbItem.LinkUrl,
		dbItem.BgColor,
		dbItem.TextColor,
		dbItem.StartTime,
		dbItem.EndTime,
		dbItem.Sort,
		dbItem.Status,
	).Where(dbItem.ID.Eq(req.ID)).Updates(updates)
	return err
}

// DeleteRecommendItem 删除推荐内容项
func (s *RecommendPositionService) DeleteRecommendItem(id uint) error {
	dbItem := query.JyhRecommendItem

	// 检查内容项是否存在
	if _, err := dbItem.Where(dbItem.ID.Eq(id)).First(); err != nil {
		return errors.New("推荐内容项不存在")
	}

	// 删除内容项（数据库有外键约束，会自动删除关联的展示记录）
	_, err := dbItem.Where(dbItem.ID.Eq(id)).Delete()
	return err
}

// GetRecommendItemDetail 获取推荐内容项详情
func (s *RecommendPositionService) GetRecommendItemDetail(id uint) (item *jyhapp.JyhRecommendItem, err error) {
	dbItem := query.JyhRecommendItem
	item, err = dbItem.Where(dbItem.ID.Eq(id)).First()
	if err == nil && item != nil {
		// 预加载关联的推荐位信息
		dbPosition := query.JyhRecommendPosition
		position, _ := dbPosition.Where(dbPosition.ID.Eq(item.PositionID)).First()
		item.Position = *position
	}
	return
}

// GetRecommendItemList 获取推荐内容项列表
func (s *RecommendPositionService) GetRecommendItemList(req *jyhReq.RecommendItemSearch) (list []*jyhapp.JyhRecommendItem, total int64, err error) {
	var (
		dbRecommendItem = query.JyhRecommendItem
		dbRecommend     = query.JyhRecommendPosition
		cons            []gen.Condition
	)
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)

	// 构建查询条件
	if req.PositionID > 0 {
		cons = append(cons, dbRecommendItem.PositionID.Eq(req.PositionID))
	}
	if req.Title != "" {
		cons = append(cons, dbRecommendItem.Title.Like("%"+req.Title+"%"))
	}
	if req.ContentType != "" {
		cons = append(cons, dbRecommendItem.ContentType.Eq(req.ContentType))
	}

	if req.StartTime != nil {
		cons = append(cons, dbRecommendItem.StartTime.Gte(*req.StartTime))
	}
	if req.EndTime != nil {
		cons = append(cons, dbRecommendItem.EndTime.Gte(*req.EndTime))
	}
	// PositionKey
	if req.PositionKey != "" {
		cons = append(cons, dbRecommend.PositionKey.Eq(req.PositionKey))
	}
	if req.Status > 0 {
		cons = append(cons, dbRecommendItem.Status.Eq(req.Status))
	}

	// 预加载关联数据
	db := dbRecommendItem.WithContext(global.GVA_DB.Statement.Context).
		Select(dbRecommendItem.ALL).
		LeftJoin(dbRecommend, dbRecommendItem.PositionID.EqCol(dbRecommend.ID)).
		Where(cons...).Order(dbRecommendItem.Sort)

	total, err = db.Count()
	if err != nil {
		global.GVA_LOG.Error("查询统计失败", zap.Error(err))
		return nil, 0, errno.QueryFailed.WithError(err)
	}

	// 处理分页
	if req.Page == 0 && req.PageSize == -1 {
		err = db.Scan(&list)
		return
	}

	// 分页处理
	err = db.Limit(limit).Offset(offset).Scan(&list)
	if err != nil {
		global.GVA_LOG.Error("查询失败", zap.Error(err))
		return nil, 0, errno.QueryFailed.WithError(err)
	}

	// 预加载推荐位信息
	/*if len(list) > 0 {
		positionIDs := make([]uint, 0, len(list))
		for _, item := range list {
			positionIDs = append(positionIDs, item.PositionID)
		}

		dbPosition := query.JyhRecommendPosition
		positions, _ := dbPosition.Where(dbPosition.ID.In(positionIDs...)).Find()
		positionMap := make(map[uint]jyhapp.JyhRecommendPosition)
		for _, pos := range positions {
			positionMap[pos.ID] = *pos
		}

		for i, item := range list {
			if pos, ok := positionMap[item.PositionID]; ok {
				list[i].Position = pos
			}
		}
	}*/

	return
}
