package jyhapp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"google.golang.org/genproto/googleapis/type/decimal"
)

// JyhCommission 佣金表
type JyhCommission struct {
	global.GVA_MODEL
	OrderID    uint            `gorm:"not null;index" json:"OrderId"`
	UserID     uint            `gorm:"not null;index;comment:获得佣金的代理" json:"userId"`
	FromUserID uint            `gorm:"not null;index;comment:下单用户" json:"fromUserId"`
	Amount     decimal.Decimal `gorm:"type:decimal(10,2)"`
	Level      uint            `gorm:"not null;comment:佣金层级" json:"level"`
	Status     string          `gorm:"type:enum('pending', 'paid');default:'pending'" json:"status"`

	// 关联关系
	Order    JyhOrder `gorm:"foreignKey:OrderID" json:"order"`
	User     JyhUser  `gorm:"foreignKey:UserID" json:"user"`
	FromUser JyhUser  `gorm:"foreignKey:FromUserID" json:"fromUser"`
}

// TableName JyhCommission 表名
func (JyhCommission) TableName() string {
	return "jyh_commission"
}

// JyhCommissionRate 佣金比例表
type JyhCommissionRate struct {
	Level uint    `gorm:"primaryKey;comment:层级" json:"level"`
	Rate  float64 `gorm:"type:decimal(5,4);not null;comment:佣金比例" json:"rate"`
}

// TableName JyhCommissionRate 表名
func (JyhCommissionRate) TableName() string {
	return "jyh_commission_rate"
}
