package jyhapp

import (
	"errors"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
	jyhReq "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/request"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"time"
)

type JyhArticleService struct{}

// CategoryCreate 创建文章分类
func (s *JyhArticleService) CategoryCreate(req jyhReq.CategoryCreate) error {
	db := global.GVA_DB

	// 检查分类名称是否重复
	var count int64
	if err := db.Model(&jyhapp.JyhArticleCategory{}).
		Where("name = ?", req.Name).
		Count(&count).Error; err != nil {
		global.GVA_LOG.Error("查询分类失败", zap.Error(err))
		return err
	}

	if count > 0 {
		return errors.New("分类名称已存在")
	}

	category := &jyhapp.JyhArticleCategory{
		Name:        req.Name,
		ParentID:    req.ParentID,
		Sort:        req.Sort,
		IsActive:    req.IsActive,
		Description: req.Description,
	}

	return db.Create(category).Error
}

// CategoryUpdate 更新文章分类
func (s *JyhArticleService) CategoryUpdate(req *jyhReq.CategoryUpdate) error {
	db := global.GVA_DB

	// 检查分类是否存在
	var category jyhapp.JyhArticleCategory
	if err := db.First(&category, req.ID).Error; err != nil {
		return errors.New("分类不存在")
	}

	// 检查名称是否与其他分类冲突
	var count int64
	if err := db.Model(&jyhapp.JyhArticleCategory{}).
		Where("name = ? AND id != ?", req.Name, req.ID).
		Count(&count).Error; err != nil {
		return err
	}

	if count > 0 {
		return errors.New("分类名称已存在")
	}

	// 更新分类
	updates := map[string]interface{}{
		"name":        req.Name,
		"parent_id":   req.ParentID,
		"sort":        req.Sort,
		"is_active":   req.IsActive,
		"description": req.Description,
	}

	return db.Model(&category).Updates(updates).Error
}

// CategoryDelete 删除文章分类
func (s *JyhArticleService) CategoryDelete(id uint) error {
	db := global.GVA_DB

	// 检查是否有关联文章
	var articleCount int64
	if err := db.Model(&jyhapp.JyhArticle{}).
		Where("category_id = ?", id).
		Count(&articleCount).Error; err != nil {
		return err
	}

	if articleCount > 0 {
		return errors.New("存在关联文章，无法删除")
	}

	// 检查是否有子分类
	var childCount int64
	if err := db.Model(&jyhapp.JyhArticleCategory{}).
		Where("parent_id = ?", id).
		Count(&childCount).Error; err != nil {
		return err
	}

	if childCount > 0 {
		return errors.New("存在子分类，无法删除")
	}

	return db.Delete(&jyhapp.JyhArticleCategory{}, id).Error
}

// CategoryGetDetail 获取分类详情
func (s *JyhArticleService) CategoryGetDetail(id uint) (*jyhapp.JyhArticleCategory, error) {
	db := global.GVA_DB

	var category jyhapp.JyhArticleCategory
	if err := db.Preload("Parent").First(&category, id).Error; err != nil {
		return nil, err
	}

	return &category, nil
}

// CategoryGetList 获取分类列表
func (s *JyhArticleService) CategoryGetList(req *jyhReq.CategorySearch) ([]*jyhapp.JyhArticleCategory, int64, error) {
	db := global.GVA_DB

	query := db.Model(&jyhapp.JyhArticleCategory{})

	// 添加查询条件
	if req.Name != "" {
		query = query.Where("name LIKE ?", "%"+req.Name+"%")
	}
	if req.ParentID != nil {
		query = query.Where("parent_id = ?", *req.ParentID)
	}
	if req.IsActive != nil {
		query = query.Where("is_active = ?", *req.IsActive)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页处理
	if req.Page > 0 && req.PageSize > 0 {
		offset := (req.Page - 1) * req.PageSize
		query = query.Offset(offset).Limit(req.PageSize)
	}

	// 排序
	query = query.Order("sort ASC, id DESC")

	// 查询结果
	var categories []*jyhapp.JyhArticleCategory
	if err := query.Preload("Children").Find(&categories).Error; err != nil {
		return nil, 0, err
	}

	return categories, total, nil
}

// CategoryGetTree 获取分类树
func (s *JyhArticleService) CategoryGetTree() ([]*jyhapp.JyhArticleCategory, error) {
	db := global.GVA_DB

	// 获取所有分类
	var allCategories []*jyhapp.JyhArticleCategory
	if err := db.Order("sort ASC").Find(&allCategories).Error; err != nil {
		return nil, err
	}

	// 构建树形结构
	categoryMap := make(map[uint]*jyhapp.JyhArticleCategory)
	for _, category := range allCategories {
		categoryMap[category.ID] = category
	}

	var rootCategories []*jyhapp.JyhArticleCategory
	for _, category := range allCategories {
		if category.ParentID == 0 {
			rootCategories = append(rootCategories, category)
			continue
		}

		if parent, ok := categoryMap[category.ParentID]; ok {
			parent.Children = append(parent.Children, *category)
		}
	}

	return rootCategories, nil
}

// Create 创建文章
func (s *JyhArticleService) Create(req jyhReq.ArticleCreate) error {
	db := global.GVA_DB

	// 验证分类是否存在
	var category jyhapp.JyhArticleCategory
	if err := db.First(&category, req.CategoryID).Error; err != nil {
		return errors.New("分类不存在")
	}

	article := &jyhapp.JyhArticle{
		Title:       req.Title,
		CategoryID:  req.CategoryID,
		Author:      req.Author,
		Summary:     req.Summary,
		Content:     req.Content,
		CoverImage:  req.CoverImage,
		Status:      req.Status,
		IsTop:       req.IsTop,
		PublishedAt: req.PublishedAt,
	}

	if article.Status == "published" && article.PublishedAt == nil {
		now := time.Now()
		article.PublishedAt = &now
	}

	return db.Create(article).Error
}

// Update 更新文章
func (s *JyhArticleService) Update(req *jyhReq.ArticleUpdate) error {
	db := global.GVA_DB

	// 获取文章
	var article jyhapp.JyhArticle
	if err := db.First(&article, req.ID).Error; err != nil {
		return errors.New("文章不存在")
	}

	// 验证分类是否存在
	var category jyhapp.JyhArticleCategory
	if err := db.First(&category, req.CategoryID).Error; err != nil {
		return errors.New("分类不存在")
	}

	// 更新文章
	updates := map[string]interface{}{
		"title":        req.Title,
		"category_id":  req.CategoryID,
		"author":       req.Author,
		"summary":      req.Summary,
		"content":      req.Content,
		"cover_image":  req.CoverImage,
		"status":       req.Status,
		"is_top":       req.IsTop,
		"published_at": req.PublishedAt,
	}

	// 如果状态变为已发布且发布时间为空，设置当前时间
	if req.Status == "published" && req.PublishedAt == nil {
		now := time.Now()
		updates["published_at"] = &now
	}

	return db.Model(&article).Updates(updates).Error
}

// Delete 删除文章
func (s *JyhArticleService) Delete(id uint) error {
	db := global.GVA_DB
	return db.Delete(&jyhapp.JyhArticle{}, id).Error
}

// GetDetail 获取文章详情
func (s *JyhArticleService) GetDetail(id uint) (*jyhapp.JyhArticle, error) {
	db := global.GVA_DB

	var article jyhapp.JyhArticle
	if err := db.Preload("Category").First(&article, id).Error; err != nil {
		return nil, err
	}

	// 增加浏览量
	if article.Status == "published" {
		go func() {
			db.Model(&article).Update("view_count", gorm.Expr("view_count + 1"))
		}()
	}

	return &article, nil
}

// GetList 获取文章列表
func (s *JyhArticleService) GetList(req *jyhReq.ArticleSearch) ([]*jyhapp.JyhArticle, int64, error) {
	db := global.GVA_DB

	query := db.Model(&jyhapp.JyhArticle{})

	// 添加查询条件
	if req.CategoryID != nil {
		query = query.Where("category_id = ?", *req.CategoryID)
	}
	if req.Title != "" {
		query = query.Where("title LIKE ?", "%"+req.Title+"%")
	}
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}
	if req.IsTop != nil {
		query = query.Where("is_top = ?", *req.IsTop)
	}
	if req.Author != "" {
		query = query.Where("author = ?", req.Author)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页处理
	if req.Page > 0 && req.PageSize > 0 {
		offset := (req.Page - 1) * req.PageSize
		query = query.Offset(offset).Limit(req.PageSize)
	}

	// 排序
	if req.IsTop != nil && *req.IsTop {
		query = query.Order("is_top DESC, published_at DESC")
	} else {
		query = query.Order("published_at DESC")
	}

	// 查询结果
	var articles []*jyhapp.JyhArticle
	if err := query.Preload("Category").Find(&articles).Error; err != nil {
		return nil, 0, err
	}

	return articles, total, nil
}

// Publish 发布文章
func (s *JyhArticleService) Publish(id uint) error {
	db := global.GVA_DB

	now := time.Now()
	return db.Model(&jyhapp.JyhArticle{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":       "published",
			"published_at": &now,
		}).Error
}

// Archive 归档文章
func (s *JyhArticleService) Archive(id uint) error {
	db := global.GVA_DB
	return db.Model(&jyhapp.JyhArticle{}).
		Where("id = ?", id).
		Update("status", "archived").Error
}

// SetTop 设置文章置顶
func (s *JyhArticleService) SetTop(id uint, isTop bool) error {
	db := global.GVA_DB
	return db.Model(&jyhapp.JyhArticle{}).
		Where("id = ?", id).
		Update("is_top", isTop).Error
}
