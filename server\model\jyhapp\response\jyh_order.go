package response

type JyhOrderStatisticsData struct {
	OrderDate         string  `json:"orderDate"`         // 订单日期
	TotalCount        int     `json:"totalCount"`        // 总订单数
	TotalAmount       float64 `json:"totalAmount"`       // 总金额
	TotalServiceFee   float64 `json:"totalServiceFee"`   // 总服务费
	TotalSettleAmount float64 `json:"totalSettleAmount"` // 总结算收入
	TotalAuthorFee    float64 `json:"totalAuthorFee"`    // 达人佣金（抖音平台可提现佣金）
	//TotalOrganizationFee float64 `json:"totalOrganizationFee"` // 机构佣金
	TotalJyhFee float64 `json:"totalJyhFee"` // 今音荟佣金（今音荟佣金）
}

type JyhOrderDetail struct {
	OrderDate            string  `json:"orderDate"`            // 订单日期
	TotalCount           int     `json:"totalCount"`           // 总订单数
	TotalAmount          float64 `json:"totalAmount"`          // 总金额
	TotalServiceFee      float64 `json:"totalServiceFee"`      // 总服务费
	TotalSettleAmount    float64 `json:"totalSettleAmount"`    // 总结算收入
	TotalAuthorFee       float64 `json:"totalAuthorFee"`       // 达人佣金（抖音平台可提现佣金）
	TotalOrganizationFee float64 `json:"totalOrganizationFee"` // 机构佣金
	TotalJyhFee          float64 `json:"totalJyhFee"`          // 今音荟佣金（今音荟佣金）
	UserName             string  `json:"userName"`             // 用户名
	AuthorUid            string  `json:"authorUid"`            // 达人抖音ID
}
