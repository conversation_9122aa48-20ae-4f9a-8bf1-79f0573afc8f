// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
)

func newJyhInviteRecord(db *gorm.DB, opts ...gen.DOOption) jyhInviteRecord {
	_jyhInviteRecord := jyhInviteRecord{}

	_jyhInviteRecord.jyhInviteRecordDo.UseDB(db, opts...)
	_jyhInviteRecord.jyhInviteRecordDo.UseModel(&jyhapp.JyhInviteRecord{})

	tableName := _jyhInviteRecord.jyhInviteRecordDo.TableName()
	_jyhInviteRecord.ALL = field.NewAsterisk(tableName)
	_jyhInviteRecord.ID = field.NewUint(tableName, "id")
	_jyhInviteRecord.CreatedAt = field.NewTime(tableName, "created_at")
	_jyhInviteRecord.UpdatedAt = field.NewTime(tableName, "updated_at")
	_jyhInviteRecord.DeletedAt = field.NewField(tableName, "deleted_at")
	_jyhInviteRecord.InviterID = field.NewUint(tableName, "inviter_id")
	_jyhInviteRecord.InviteeID = field.NewUint(tableName, "invitee_id")
	_jyhInviteRecord.CodeUsed = field.NewString(tableName, "code_used")
	_jyhInviteRecord.Inviter = jyhInviteRecordBelongsToInviter{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Inviter", "jyhapp.JyhUser"),
		Inviter: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Inviter.Inviter", "jyhapp.JyhUser"),
		},
		JyhUserExt: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Inviter.JyhUserExt", "jyhapp.JyhUserExt"),
		},
		Invitees: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Inviter.Invitees", "jyhapp.JyhUser"),
		},
		Tags: struct {
			field.RelationField
			Users struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Inviter.Tags", "jyhapp.JyhUserTag"),
			Users: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Inviter.Tags.Users", "jyhapp.JyhUser"),
			},
		},
	}

	_jyhInviteRecord.Invitee = jyhInviteRecordBelongsToInvitee{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Invitee", "jyhapp.JyhUser"),
	}

	_jyhInviteRecord.fillFieldMap()

	return _jyhInviteRecord
}

type jyhInviteRecord struct {
	jyhInviteRecordDo

	ALL       field.Asterisk
	ID        field.Uint
	CreatedAt field.Time
	UpdatedAt field.Time
	DeletedAt field.Field
	InviterID field.Uint
	InviteeID field.Uint
	CodeUsed  field.String
	Inviter   jyhInviteRecordBelongsToInviter

	Invitee jyhInviteRecordBelongsToInvitee

	fieldMap map[string]field.Expr
}

func (j jyhInviteRecord) Table(newTableName string) *jyhInviteRecord {
	j.jyhInviteRecordDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j jyhInviteRecord) As(alias string) *jyhInviteRecord {
	j.jyhInviteRecordDo.DO = *(j.jyhInviteRecordDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *jyhInviteRecord) updateTableName(table string) *jyhInviteRecord {
	j.ALL = field.NewAsterisk(table)
	j.ID = field.NewUint(table, "id")
	j.CreatedAt = field.NewTime(table, "created_at")
	j.UpdatedAt = field.NewTime(table, "updated_at")
	j.DeletedAt = field.NewField(table, "deleted_at")
	j.InviterID = field.NewUint(table, "inviter_id")
	j.InviteeID = field.NewUint(table, "invitee_id")
	j.CodeUsed = field.NewString(table, "code_used")

	j.fillFieldMap()

	return j
}

func (j *jyhInviteRecord) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *jyhInviteRecord) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 9)
	j.fieldMap["id"] = j.ID
	j.fieldMap["created_at"] = j.CreatedAt
	j.fieldMap["updated_at"] = j.UpdatedAt
	j.fieldMap["deleted_at"] = j.DeletedAt
	j.fieldMap["inviter_id"] = j.InviterID
	j.fieldMap["invitee_id"] = j.InviteeID
	j.fieldMap["code_used"] = j.CodeUsed

}

func (j jyhInviteRecord) clone(db *gorm.DB) jyhInviteRecord {
	j.jyhInviteRecordDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j jyhInviteRecord) replaceDB(db *gorm.DB) jyhInviteRecord {
	j.jyhInviteRecordDo.ReplaceDB(db)
	return j
}

type jyhInviteRecordBelongsToInviter struct {
	db *gorm.DB

	field.RelationField

	Inviter struct {
		field.RelationField
	}
	JyhUserExt struct {
		field.RelationField
	}
	Invitees struct {
		field.RelationField
	}
	Tags struct {
		field.RelationField
		Users struct {
			field.RelationField
		}
	}
}

func (a jyhInviteRecordBelongsToInviter) Where(conds ...field.Expr) *jyhInviteRecordBelongsToInviter {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a jyhInviteRecordBelongsToInviter) WithContext(ctx context.Context) *jyhInviteRecordBelongsToInviter {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a jyhInviteRecordBelongsToInviter) Session(session *gorm.Session) *jyhInviteRecordBelongsToInviter {
	a.db = a.db.Session(session)
	return &a
}

func (a jyhInviteRecordBelongsToInviter) Model(m *jyhapp.JyhInviteRecord) *jyhInviteRecordBelongsToInviterTx {
	return &jyhInviteRecordBelongsToInviterTx{a.db.Model(m).Association(a.Name())}
}

type jyhInviteRecordBelongsToInviterTx struct{ tx *gorm.Association }

func (a jyhInviteRecordBelongsToInviterTx) Find() (result *jyhapp.JyhUser, err error) {
	return result, a.tx.Find(&result)
}

func (a jyhInviteRecordBelongsToInviterTx) Append(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a jyhInviteRecordBelongsToInviterTx) Replace(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a jyhInviteRecordBelongsToInviterTx) Delete(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a jyhInviteRecordBelongsToInviterTx) Clear() error {
	return a.tx.Clear()
}

func (a jyhInviteRecordBelongsToInviterTx) Count() int64 {
	return a.tx.Count()
}

type jyhInviteRecordBelongsToInvitee struct {
	db *gorm.DB

	field.RelationField
}

func (a jyhInviteRecordBelongsToInvitee) Where(conds ...field.Expr) *jyhInviteRecordBelongsToInvitee {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a jyhInviteRecordBelongsToInvitee) WithContext(ctx context.Context) *jyhInviteRecordBelongsToInvitee {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a jyhInviteRecordBelongsToInvitee) Session(session *gorm.Session) *jyhInviteRecordBelongsToInvitee {
	a.db = a.db.Session(session)
	return &a
}

func (a jyhInviteRecordBelongsToInvitee) Model(m *jyhapp.JyhInviteRecord) *jyhInviteRecordBelongsToInviteeTx {
	return &jyhInviteRecordBelongsToInviteeTx{a.db.Model(m).Association(a.Name())}
}

type jyhInviteRecordBelongsToInviteeTx struct{ tx *gorm.Association }

func (a jyhInviteRecordBelongsToInviteeTx) Find() (result *jyhapp.JyhUser, err error) {
	return result, a.tx.Find(&result)
}

func (a jyhInviteRecordBelongsToInviteeTx) Append(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a jyhInviteRecordBelongsToInviteeTx) Replace(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a jyhInviteRecordBelongsToInviteeTx) Delete(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a jyhInviteRecordBelongsToInviteeTx) Clear() error {
	return a.tx.Clear()
}

func (a jyhInviteRecordBelongsToInviteeTx) Count() int64 {
	return a.tx.Count()
}

type jyhInviteRecordDo struct{ gen.DO }

func (j jyhInviteRecordDo) Debug() *jyhInviteRecordDo {
	return j.withDO(j.DO.Debug())
}

func (j jyhInviteRecordDo) WithContext(ctx context.Context) *jyhInviteRecordDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jyhInviteRecordDo) ReadDB() *jyhInviteRecordDo {
	return j.Clauses(dbresolver.Read)
}

func (j jyhInviteRecordDo) WriteDB() *jyhInviteRecordDo {
	return j.Clauses(dbresolver.Write)
}

func (j jyhInviteRecordDo) Session(config *gorm.Session) *jyhInviteRecordDo {
	return j.withDO(j.DO.Session(config))
}

func (j jyhInviteRecordDo) Clauses(conds ...clause.Expression) *jyhInviteRecordDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jyhInviteRecordDo) Returning(value interface{}, columns ...string) *jyhInviteRecordDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jyhInviteRecordDo) Not(conds ...gen.Condition) *jyhInviteRecordDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jyhInviteRecordDo) Or(conds ...gen.Condition) *jyhInviteRecordDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jyhInviteRecordDo) Select(conds ...field.Expr) *jyhInviteRecordDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jyhInviteRecordDo) Where(conds ...gen.Condition) *jyhInviteRecordDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jyhInviteRecordDo) Order(conds ...field.Expr) *jyhInviteRecordDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jyhInviteRecordDo) Distinct(cols ...field.Expr) *jyhInviteRecordDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jyhInviteRecordDo) Omit(cols ...field.Expr) *jyhInviteRecordDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jyhInviteRecordDo) Join(table schema.Tabler, on ...field.Expr) *jyhInviteRecordDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jyhInviteRecordDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jyhInviteRecordDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jyhInviteRecordDo) RightJoin(table schema.Tabler, on ...field.Expr) *jyhInviteRecordDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jyhInviteRecordDo) Group(cols ...field.Expr) *jyhInviteRecordDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jyhInviteRecordDo) Having(conds ...gen.Condition) *jyhInviteRecordDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jyhInviteRecordDo) Limit(limit int) *jyhInviteRecordDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jyhInviteRecordDo) Offset(offset int) *jyhInviteRecordDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jyhInviteRecordDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jyhInviteRecordDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jyhInviteRecordDo) Unscoped() *jyhInviteRecordDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jyhInviteRecordDo) Create(values ...*jyhapp.JyhInviteRecord) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jyhInviteRecordDo) CreateInBatches(values []*jyhapp.JyhInviteRecord, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jyhInviteRecordDo) Save(values ...*jyhapp.JyhInviteRecord) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jyhInviteRecordDo) First() (*jyhapp.JyhInviteRecord, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhInviteRecord), nil
	}
}

func (j jyhInviteRecordDo) Take() (*jyhapp.JyhInviteRecord, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhInviteRecord), nil
	}
}

func (j jyhInviteRecordDo) Last() (*jyhapp.JyhInviteRecord, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhInviteRecord), nil
	}
}

func (j jyhInviteRecordDo) Find() ([]*jyhapp.JyhInviteRecord, error) {
	result, err := j.DO.Find()
	return result.([]*jyhapp.JyhInviteRecord), err
}

func (j jyhInviteRecordDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*jyhapp.JyhInviteRecord, err error) {
	buf := make([]*jyhapp.JyhInviteRecord, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jyhInviteRecordDo) FindInBatches(result *[]*jyhapp.JyhInviteRecord, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jyhInviteRecordDo) Attrs(attrs ...field.AssignExpr) *jyhInviteRecordDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jyhInviteRecordDo) Assign(attrs ...field.AssignExpr) *jyhInviteRecordDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jyhInviteRecordDo) Joins(fields ...field.RelationField) *jyhInviteRecordDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jyhInviteRecordDo) Preload(fields ...field.RelationField) *jyhInviteRecordDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jyhInviteRecordDo) FirstOrInit() (*jyhapp.JyhInviteRecord, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhInviteRecord), nil
	}
}

func (j jyhInviteRecordDo) FirstOrCreate() (*jyhapp.JyhInviteRecord, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhInviteRecord), nil
	}
}

func (j jyhInviteRecordDo) FindByPage(offset int, limit int) (result []*jyhapp.JyhInviteRecord, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jyhInviteRecordDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jyhInviteRecordDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jyhInviteRecordDo) Delete(models ...*jyhapp.JyhInviteRecord) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jyhInviteRecordDo) withDO(do gen.Dao) *jyhInviteRecordDo {
	j.DO = *do.(*gen.DO)
	return j
}
