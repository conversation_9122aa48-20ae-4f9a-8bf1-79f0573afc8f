package jyhapp

import (
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	jyhappReq "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type JyhUserLiveRoomApi struct{}

// CreateJyhUserLiveRoom 创建用户直播房间
// @Tags JyhUserLiveRoom
// @Summary 创建用户直播房间
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body jyhappReq.CreateJyhUserLiveRoomReq true "直播房间信息"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /admin/live/create [post]
func (jyhUserLiveRoomApi *JyhUserLiveRoomApi) CreateJyhUserLiveRoom(c *gin.Context) {
	var req jyhappReq.CreateJyhUserLiveRoomReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 从JWT中获取用户ID
	userID := utils.GetUserID(c)

	if res, err := jyhUserLiveRoomService.CreateUserLiveRoom(userID, req); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
}

// DeleteJyhUserLiveRoom 删除用户直播房间
// @Tags JyhUserLiveRoom
// @Summary 删除用户直播房间
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "直播房间ID"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /admin/live/delete/{id} [delete]
func (jyhUserLiveRoomApi *JyhUserLiveRoomApi) DeleteJyhUserLiveRoom(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		return
	}
	if err := jyhUserLiveRoomService.DeleteJyhUserLiveRoom(uint(id)); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// UpdateJyhUserLiveRoomStatus 更新用户直播房间状态
// @Tags JyhUserLiveRoom
// @Summary 更新用户直播房间状态
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id query uint true "直播房间ID"
// @Param status query string true "状态"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /admin/live/updateStatus [put]
func (jyhUserLiveRoomApi *JyhUserLiveRoomApi) UpdateJyhUserLiveRoomStatus(c *gin.Context) {
	idStr := c.Query("id")
	status := c.Query("status")

	if idStr == "" || status == "" {
		response.FailWithMessage("参数不能为空", c)
		return
	}

	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("ID格式错误", c)
		return
	}

	if err := jyhUserLiveRoomService.UpdateUserLiveRoomStatus(uint(id), status); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindJyhUserLiveRoom 获取用户直播房间详情
// @Tags JyhUserLiveRoom
// @Summary 获取用户直播房间详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "直播房间ID"
// @Success 200 {object} response.Response{data=jyhapp.JyhUserLiveRoom,msg=string} "获取成功"
// @Router /admin/live/detail/{id} [get]
func (jyhUserLiveRoomApi *JyhUserLiveRoomApi) FindJyhUserLiveRoom(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		return
	}
	if rejyhUserLiveRoom, err := jyhUserLiveRoomService.GetJyhUserLiveRoom(uint(id)); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
	} else {
		response.OkWithData(rejyhUserLiveRoom, c)
	}
}

// GetJyhUserLiveRoomList 获取用户直播房间列表
// @Tags JyhUserLiveRoom
// @Summary 获取用户直播房间列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query jyhappReq.JyhUserLiveRoomSearch true "查询参数"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /admin/live/list [get]
func (jyhUserLiveRoomApi *JyhUserLiveRoomApi) GetJyhUserLiveRoomList(c *gin.Context) {
	var pageInfo jyhappReq.JyhUserLiveRoomSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := jyhUserLiveRoomService.LiveRoomInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetUserAvailableRoom 获取用户可用的直播房间
// @Tags JyhUserLiveRoom
// @Summary 获取用户可用的直播房间
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=jyhapp.JyhUserLiveRoom,msg=string} "获取成功"
// @Router /admin/live/available [get]
func (jyhUserLiveRoomApi *JyhUserLiveRoomApi) GetUserAvailableRoom(c *gin.Context) {
	// 从JWT中获取用户ID
	userID := utils.GetUserID(c)
	if room, err := jyhUserLiveRoomService.GetUserAvailableRoom(userID); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
	} else if room == nil {
		response.OkWithData(nil, c) // 没有可用房间
	} else {
		// 更新最后使用时间
		_ = jyhUserLiveRoomService.UpdateLastUsedTime(userID)
		response.OkWithData(room, c)
	}
}
