package service

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/customerservice/model"
	"gorm.io/gorm"
)

type DistributeService struct{}

// SmartAssign 智能分配客服
func (s *DistributeService) SmartAssign(userID uint, userTags []uint) (*model.SysService, error) {
	var service model.SysService

	// 如果用户有标签，优先匹配有相同标签的客服
	if len(userTags) > 0 {
		// 查找最高优先级的规则
		rule, err := s.FindBestRuleForTags(userTags)
		if err == nil && rule != nil {
			// 根据规则查找指定的客服
			assignedService, err := s.FindServiceByRule(rule.ID)
			if err == nil && assignedService != nil {
				// 检查指定客服是否可用
				if assignedService.Online == 1 &&
					assignedService.WorkStatus == model.WorkStatusAvailable &&
					assignedService.CurrentUserCount < rule.MaxUsers {
					return assignedService, nil
				}
			}

			// 如果指定客服不可用，从规则关联的客服中选择
			services, err := s.FindServicesByRule(rule.ID)
			if err == nil && len(services) > 0 {
				for _, srv := range services {
					if srv.Online == 1 &&
						srv.WorkStatus == model.WorkStatusAvailable &&
						srv.CurrentUserCount < rule.MaxUsers {
						return &srv, nil
					}
				}
			}

			// 如果规则关联的客服都不可用，回退到标签匹配
			query := global.GVA_DB.Table("sys_service").
				Select("sys_service.*").
				Joins("LEFT JOIN sys_service_tag_relation ON sys_service.id = sys_service_tag_relation.service_id").
				Where("sys_service.online = 1 AND sys_service.work_status = ?", model.WorkStatusAvailable).
				Where("sys_service_tag_relation.tag_id IN ?", userTags).
				Where("sys_service.current_user_count < ?", rule.MaxUsers).
				Order("sys_service.current_user_count ASC")

			if err := query.First(&service).Error; err == nil {
				return &service, nil
			}
		}
	}

	// 没有匹配标签的客服或没有标签，使用默认规则分配客服
	defaultRule, err := s.GetDefaultRule()
	if err == nil && defaultRule != nil {
		// 优先从默认规则关联的客服中选择
		services, err := s.FindServicesByRule(defaultRule.ID)
		if err == nil && len(services) > 0 {
			for _, srv := range services {
				if srv.Online == 1 &&
					srv.WorkStatus == model.WorkStatusAvailable &&
					srv.CurrentUserCount < defaultRule.MaxUsers {
					return &srv, nil
				}
			}
		}

		// 如果默认规则没有关联客服，使用通用逻辑
		err = global.GVA_DB.Where("online = 1 AND work_status = ?", model.WorkStatusAvailable).
			Where("current_user_count < ?", defaultRule.MaxUsers).
			Order("current_user_count ASC").First(&service).Error
	} else {
		// 如果没有默认规则，使用基础逻辑
		err = global.GVA_DB.Where("online = 1 AND work_status = ?", model.WorkStatusAvailable).
			Where("current_user_count < max_user_count").
			Order("current_user_count ASC").First(&service).Error
	}

	if err != nil {
		// 如果没有可用客服，找一个在线的客服
		err = global.GVA_DB.Where("online = 1").
			Order("current_user_count ASC").First(&service).Error
	}

	return &service, err
}

// FindBestRuleForTags 为标签找到最佳规则
func (s *DistributeService) FindBestRuleForTags(tagIDs []uint) (*model.SysServiceDistributeRule, error) {
	var rule model.SysServiceDistributeRule

	// 查找与标签关联的规则，按优先级排序
	err := global.GVA_DB.Table("sys_service_distribute_rule").
		Select("sys_service_distribute_rule.*").
		Joins("LEFT JOIN sys_service_rule_tag_relation ON sys_service_distribute_rule.id = sys_service_rule_tag_relation.rule_id").
		Where("sys_service_rule_tag_relation.tag_id IN ? AND sys_service_distribute_rule.status = ?", tagIDs, model.RuleStatusEnabled).
		Order("sys_service_distribute_rule.priority ASC").
		First(&rule).Error

	if err != nil {
		return nil, err
	}

	return &rule, nil
}

// GetDefaultRule 获取默认规则
func (s *DistributeService) GetDefaultRule() (*model.SysServiceDistributeRule, error) {
	var rule model.SysServiceDistributeRule
	err := global.GVA_DB.Where("is_default = ? AND status = ?", true, model.RuleStatusEnabled).
		First(&rule).Error

	if err != nil {
		return nil, err
	}

	return &rule, nil
}

// CreateDistributeRule 创建分发规则
func (s *DistributeService) CreateDistributeRule(ruleName string, priority int, maxUsers int, workTimeStart, workTimeEnd string, isDefault bool) (*model.SysServiceDistributeRule, error) {
	// 如果设置为默认规则，需要先取消其他默认规则
	if isDefault {
		global.GVA_DB.Model(&model.SysServiceDistributeRule{}).
			Where("is_default = ?", true).
			Update("is_default", false)
	}

	rule := &model.SysServiceDistributeRule{
		RuleName:      ruleName,
		Priority:      priority,
		MaxUsers:      maxUsers,
		WorkTimeStart: workTimeStart,
		WorkTimeEnd:   workTimeEnd,
		Status:        model.RuleStatusEnabled,
		IsDefault:     isDefault,
	}

	err := global.GVA_DB.Create(rule).Error
	return rule, err
}

// BindRuleToTags 绑定规则到标签
func (s *DistributeService) BindRuleToTags(ruleID uint, tagIDs []uint) error {
	// 先删除现有的关联
	global.GVA_DB.Where("rule_id = ?", ruleID).Delete(&model.SysServiceRuleTagRelation{})

	// 添加新的关联
	for _, tagID := range tagIDs {
		relation := &model.SysServiceRuleTagRelation{
			RuleID: ruleID,
			TagID:  tagID,
		}
		if err := global.GVA_DB.Create(relation).Error; err != nil {
			return err
		}
	}

	return nil
}

// GetRulesByTag 获取标签关联的规则
func (s *DistributeService) GetRulesByTag(tagID uint) ([]model.SysServiceDistributeRule, error) {
	var rules []model.SysServiceDistributeRule
	err := global.GVA_DB.Table("sys_service_distribute_rule").
		Select("sys_service_distribute_rule.*").
		Joins("LEFT JOIN sys_service_rule_tag_relation ON sys_service_distribute_rule.id = sys_service_rule_tag_relation.rule_id").
		Where("sys_service_rule_tag_relation.tag_id = ? AND sys_service_distribute_rule.status = ?", tagID, model.RuleStatusEnabled).
		Order("sys_service_distribute_rule.priority ASC").
		Find(&rules).Error
	return rules, err
}

// GetDistributeRules 获取分发规则列表
func (s *DistributeService) GetDistributeRules(ruleName string, status uint) ([]model.SysServiceDistributeRule, error) {
	var rules []model.SysServiceDistributeRule
	query := global.GVA_DB.Model(&model.SysServiceDistributeRule{})

	if ruleName != "" {
		query = query.Where("rule_name LIKE ?", "%"+ruleName+"%")
	}
	if status > 0 {
		query = query.Where("status = ?", status)
	}

	err := query.Order("priority ASC").Find(&rules).Error
	return rules, err
}

// UpdateDistributeRule 更新分发规则
func (s *DistributeService) UpdateDistributeRule(ruleID uint, updates map[string]interface{}) error {
	updates["update_time"] = time.Now().Unix()

	// 如果设置为默认规则，需要先取消其他默认规则
	if isDefault, exists := updates["is_default"]; exists && isDefault.(bool) {
		global.GVA_DB.Model(&model.SysServiceDistributeRule{}).
			Where("id != ? AND is_default = ?", ruleID, true).
			Update("is_default", false)
	}

	return global.GVA_DB.Model(&model.SysServiceDistributeRule{}).
		Where("id = ?", ruleID).Updates(updates).Error
}

// DeleteDistributeRule 删除分发规则
func (s *DistributeService) DeleteDistributeRule(ruleID uint) error {
	// 使用事务删除规则及其关联
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 先删除标签关联
		if err := tx.Where("rule_id = ?", ruleID).Delete(&model.SysServiceRuleTagRelation{}).Error; err != nil {
			return err
		}

		// 删除客服关联
		if err := tx.Where("rule_id = ?", ruleID).Delete(&model.SysServiceRuleServiceRelation{}).Error; err != nil {
			return err
		}

		// 删除规则
		return tx.Delete(&model.SysServiceDistributeRule{}, ruleID).Error
	})
}

// BindServiceTags 绑定客服标签
func (s *DistributeService) BindServiceTags(serviceID int64, tagIDs []uint) error {
	// 先删除现有的标签关系
	global.GVA_DB.Where("service_id = ?", serviceID).Delete(&model.SysServiceTagRelation{})

	// 添加新的标签关系
	for _, tagID := range tagIDs {
		relation := &model.SysServiceTagRelation{
			ServiceID: serviceID,
			TagID:     tagID,
		}
		global.GVA_DB.Create(relation)
	}

	return nil
}

// GetServiceTags 获取客服的标签
func (s *DistributeService) GetServiceTags(serviceID int64) ([]jyhapp.JyhUserTag, error) {
	var tags []jyhapp.JyhUserTag
	err := global.GVA_DB.Table("jyh_user_tag").
		Select("jyh_user_tag.*").
		Joins("LEFT JOIN sys_service_tag_relation ON jyh_user_tag.id = sys_service_tag_relation.tag_id").
		Where("sys_service_tag_relation.service_id = ?", serviceID).
		Find(&tags).Error
	return tags, err
}

// GetAvailableTags 获取可用的标签列表
func (s *DistributeService) GetAvailableTags() ([]jyhapp.JyhUserTag, error) {
	var tags []jyhapp.JyhUserTag
	err := global.GVA_DB.Where("status = 1").Order("sort ASC, id ASC").Find(&tags).Error
	return tags, err
}

// GetServicesByTag 获取有指定标签的客服列表
func (s *DistributeService) GetServicesByTag(tagID uint) ([]model.SysService, error) {
	var services []model.SysService
	err := global.GVA_DB.Table("sys_service").
		Select("sys_service.*").
		Joins("LEFT JOIN sys_service_tag_relation ON sys_service.id = sys_service_tag_relation.service_id").
		Where("sys_service_tag_relation.tag_id = ?", tagID).
		Find(&services).Error
	return services, err
}

// CheckWorkTime 检查客服工作时间
func (s *DistributeService) CheckWorkTime(serviceID int64) bool {
	var service model.SysService
	err := global.GVA_DB.Where("id = ?", serviceID).First(&service).Error
	if err != nil {
		return false
	}

	// 获取客服标签
	tags, err := s.GetServiceTags(serviceID)
	if err != nil {
		return false
	}

	if len(tags) == 0 {
		// 没有标签，使用默认规则
		defaultRule, err := s.GetDefaultRule()
		if err != nil {
			return true // 没有默认规则，默认可以工作
		}
		return s.isInWorkTime(defaultRule.WorkTimeStart, defaultRule.WorkTimeEnd)
	}

	// 获取标签对应的规则
	var tagIDs []uint
	for _, tag := range tags {
		tagIDs = append(tagIDs, tag.ID)
	}

	rule, err := s.FindBestRuleForTags(tagIDs)
	if err != nil {
		return true // 没有规则，默认可以工作
	}

	return s.isInWorkTime(rule.WorkTimeStart, rule.WorkTimeEnd)
}

// isInWorkTime 检查当前时间是否在工作时间内
func (s *DistributeService) isInWorkTime(startTime, endTime string) bool {
	now := time.Now()
	currentTime := now.Format("15:04")

	return startTime <= currentTime && currentTime <= endTime
}

// GetServiceLoad 获取客服负载信息
func (s *DistributeService) GetServiceLoad(serviceID int64) (map[string]interface{}, error) {
	var service model.SysService
	err := global.GVA_DB.Where("id = ?", serviceID).First(&service).Error
	if err != nil {
		return nil, err
	}

	loadInfo := map[string]interface{}{
		"service_id":         service.Id,
		"current_user_count": service.CurrentUserCount,
		"max_user_count":     service.MaxUserCount,
		"load_rate":          float64(service.CurrentUserCount) / float64(service.MaxUserCount) * 100,
		"work_status":        service.WorkStatus,
		"online":             service.Online,
	}

	return loadInfo, nil
}

// FindServiceByRule 根据规则查找首选客服（优先级最高的客服）
func (s *DistributeService) FindServiceByRule(ruleID uint) (*model.SysService, error) {
	var service model.SysService
	err := global.GVA_DB.Table("sys_service").
		Select("sys_service.*").
		Joins("LEFT JOIN sys_service_rule_service_relation ON sys_service.id = sys_service_rule_service_relation.service_id").
		Where("sys_service_rule_service_relation.rule_id = ?", ruleID).
		Order("sys_service_rule_service_relation.priority ASC").
		First(&service).Error

	if err != nil {
		return nil, err
	}
	return &service, nil
}

// FindServicesByRule 根据规则查找所有关联的客服
func (s *DistributeService) FindServicesByRule(ruleID uint) ([]model.SysService, error) {
	var services []model.SysService
	err := global.GVA_DB.Table("sys_service").
		Select("sys_service.*").
		Joins("LEFT JOIN sys_service_rule_service_relation ON sys_service.id = sys_service_rule_service_relation.service_id").
		Where("sys_service_rule_service_relation.rule_id = ?", ruleID).
		Order("sys_service_rule_service_relation.priority ASC").
		Find(&services).Error

	return services, err
}

// BindRuleToServices 绑定规则到客服
func (s *DistributeService) BindRuleToServices(ruleID uint, serviceIDs []int64) error {
	// 先删除现有的关联
	global.GVA_DB.Where("rule_id = ?", ruleID).Delete(&model.SysServiceRuleServiceRelation{})

	// 添加新的关联
	for i, serviceID := range serviceIDs {
		relation := &model.SysServiceRuleServiceRelation{
			RuleID:    ruleID,
			ServiceID: serviceID,
			Priority:  i + 1, // 按顺序设置优先级
		}
		if err := global.GVA_DB.Create(relation).Error; err != nil {
			return err
		}
	}

	return nil
}

// GetServicesByRule 获取规则关联的客服列表
func (s *DistributeService) GetServicesByRule(ruleID uint) ([]model.SysService, error) {
	var services []model.SysService
	err := global.GVA_DB.Table("sys_service").
		Select("sys_service.*").
		Joins("LEFT JOIN sys_service_rule_service_relation ON sys_service.id = sys_service_rule_service_relation.service_id").
		Where("sys_service_rule_service_relation.rule_id = ?", ruleID).
		Order("sys_service_rule_service_relation.priority ASC").
		Find(&services).Error
	return services, err
}
