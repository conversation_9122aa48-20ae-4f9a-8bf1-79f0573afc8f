package jyhapp

import (
	"context"
	"errors"
	"time"

	"github.com/xtulnx/jkit-go/jtime"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/query"
	jyhReq "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/request"
	jyhResp "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/response"
	"go.uber.org/zap"
	"gorm.io/gen"
)

type JyhUserBenefitService struct{}

// ===== 权益类型管理（预设数据，极少外部接口） =====

// GetBenefitList 获取权益类型列表（主要用于等级配置时选择）
func (s *JyhUserBenefitService) GetBenefitList(req *jyhReq.BenefitListReq) ([]*jyhResp.BenefitListItem, int64, error) {
	db := query.JyhUserBenefit
	q := db.WithContext(context.Background())

	// 条件查询
	if req.Key != "" {
		q = q.Where(db.Key.Like("%" + req.Key + "%"))
	}
	if req.Name != "" {
		q = q.Where(db.Name.Like("%" + req.Name + "%"))
	}
	if req.Description != "" {
		q = q.Where(db.Description.Like("%" + req.Description + "%"))
	}

	// 获取总数
	total, err := q.Count()
	if err != nil {
		global.GVA_LOG.Error("获取权益类型列表总数失败", zap.Error(err))
		return nil, 0, err
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	benefits, err := q.Offset(offset).Limit(req.PageSize).Order(db.CreatedAt.Desc()).Find()
	if err != nil {
		global.GVA_LOG.Error("获取权益类型列表失败", zap.Error(err))
		return nil, 0, err
	}

	// 构建响应数据
	var result []*jyhResp.BenefitListItem
	for _, benefit := range benefits {
		item := &jyhResp.BenefitListItem{
			ID:          benefit.ID,
			Key:         benefit.Key,
			Name:        benefit.Name,
			Description: benefit.Description,
			CreatedAt:   benefit.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:   benefit.UpdatedAt.Format("2006-01-02 15:04:05"),
		}
		result = append(result, item)
	}

	return result, total, nil
}

// ===== 会员等级管理（主要管理界面，包含权益配置） =====

// GetUserShipLevelList 获取会员等级列表
func (s *JyhUserBenefitService) GetUserShipLevelList(req *jyhReq.UserShipLevelListReq, isUserEndpoint ...bool) ([]*jyhResp.UserShipLevelListItem, int64, error) {
	db := query.JyhUserShipLevel
	q := db.WithContext(context.Background())

	// 如果是用户端接口，过滤隐藏的等级
	if len(isUserEndpoint) > 0 && isUserEndpoint[0] {
		q = q.Where(db.Hide.Is(false))
	}

	// 条件查询
	if req.Name != "" {
		q = q.Where(db.Name.Like("%" + req.Name + "%"))
	}
	if req.Code != "" {
		q = q.Where(db.Code.Like("%" + req.Code + "%"))
	}

	// 获取总数
	total, err := q.Count()
	if err != nil {
		global.GVA_LOG.Error("获取会员等级列表总数失败", zap.Error(err))
		return nil, 0, err
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	levels, err := q.Offset(offset).Limit(req.PageSize).Order(db.Sort, db.CreatedAt.Desc()).Find()
	if err != nil {
		global.GVA_LOG.Error("获取会员等级列表失败", zap.Error(err))
		return nil, 0, err
	}

	// 构建响应数据
	var result []*jyhResp.UserShipLevelListItem
	for _, level := range levels {
		item := &jyhResp.UserShipLevelListItem{
			ID:                     level.ID,
			Name:                   level.Name,
			Code:                   level.Code,
			Icon:                   level.Icon,
			PriceCents:             level.PriceCents,
			DurationDays:           level.DurationDays,
			Sort:                   level.Sort,
			Description:            level.Description,
			CreatedAt:              level.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:              level.UpdatedAt.Format("2006-01-02 15:04:05"),
			Hide:                   level.Hide,
			CommissionRate:         level.CommissionRate,
			PlatformCommissionRate: level.PlatformCommissionRate,
		}
		result = append(result, item)
	}

	return result, total, nil
}

// CreateUserShipLevel 创建会员等级（包含权益配置，事务处理）
func (s *JyhUserBenefitService) CreateUserShipLevel(req *jyhReq.UserShipLevelCreateReq) error {
	return TryTransaction(func(tx *query.Query) error {
		levelDb := tx.JyhUserShipLevel
		benefitMappingDb := tx.JyhUserShipLevelBenefit
		benefitDb := tx.JyhUserBenefit

		// 检查Code是否已存在
		exists, err := levelDb.Where(levelDb.Code.Eq(req.Code)).Count()
		if err != nil {
			global.GVA_LOG.Error("检查等级编码失败", zap.Error(err))
			return err
		}
		if exists > 0 {
			return errors.New("等级编码已存在")
		}

		// 检查Name是否已存在
		exists, err = levelDb.Where(levelDb.Name.Eq(req.Name)).Count()
		if err != nil {
			global.GVA_LOG.Error("检查等级名称失败", zap.Error(err))
			return err
		}
		if exists > 0 {
			return errors.New("等级名称已存在")
		}

		// 验证权益配置中的权益是否存在
		for _, benefitConfig := range req.Benefits {
			exists, err := benefitDb.Where(benefitDb.ID.Eq(benefitConfig.BenefitID)).Count()
			if err != nil {
				global.GVA_LOG.Error("检查权益存在性失败", zap.Error(err))
				return err
			}
			if exists == 0 {
				return errors.New("权益类型不存在")
			}
		}

		// 创建会员等级
		level := &jyhapp.JyhUserShipLevel{
			Name:                   req.Name,
			Code:                   req.Code,
			Icon:                   req.Icon,
			PriceCents:             req.PriceCents,
			DurationDays:           req.DurationDays,
			Sort:                   req.Sort,
			Description:            req.Description,
			Hide:                   req.Hide,
			CommissionRate:         req.CommissionRate,
			PlatformCommissionRate: req.PlatformCommissionRate,
		}

		err = levelDb.Create(level)
		if err != nil {
			global.GVA_LOG.Error("创建会员等级失败", zap.Error(err))
			return err
		}

		// 创建等级权益映射
		mapping := make([]*jyhapp.JyhUserShipLevelBenefit, 0)
		for _, benefitConfig := range req.Benefits {
			mapping = append(mapping, &jyhapp.JyhUserShipLevelBenefit{
				LevelID:   level.ID,
				BenefitID: benefitConfig.BenefitID,
				Value:     benefitConfig.Value,
				Condition: benefitConfig.Condition,
			})

		}
		err = benefitMappingDb.CreateInBatches(mapping, len(mapping))
		if err != nil {
			global.GVA_LOG.Error("创建等级权益映射失败", zap.Error(err))
			return err
		}
		return nil
	})
}

// GetUserShipLevelDetail 获取会员等级详情
func (s *JyhUserBenefitService) GetUserShipLevelDetail(id uint) (*jyhResp.UserShipLevelDetailResp, error) {
	db := query.JyhUserShipLevel
	benefitMappingDb := query.JyhUserShipLevelBenefit
	benefitDb := query.JyhUserBenefit

	// 获取等级基本信息
	level, err := db.Where(db.ID.Eq(id)).First()
	if err != nil {
		return nil, errors.New("会员等级不存在")
	}

	// 获取关联的权益
	mappings, err := benefitMappingDb.Where(benefitMappingDb.LevelID.Eq(id)).Find()
	if err != nil {
		global.GVA_LOG.Error("获取等级权益映射失败", zap.Error(err))
		return nil, err
	}

	var benefits []jyhResp.LevelBenefitDetailItem
	for _, mapping := range mappings {
		// 获取权益信息
		benefit, err := benefitDb.Where(benefitDb.ID.Eq(mapping.BenefitID)).First()
		if err != nil {
			continue
		}

		item := jyhResp.LevelBenefitDetailItem{
			ID:          mapping.ID,
			LevelID:     mapping.LevelID,
			BenefitID:   mapping.BenefitID,
			Value:       mapping.Value,
			Condition:   mapping.Condition,
			BenefitKey:  benefit.Key,
			BenefitName: benefit.Name,
			CreatedAt:   mapping.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:   mapping.UpdatedAt.Format("2006-01-02 15:04:05"),
		}
		benefits = append(benefits, item)
	}

	resp := &jyhResp.UserShipLevelDetailResp{
		ID:                     level.ID,
		Name:                   level.Name,
		Code:                   level.Code,
		Icon:                   level.Icon,
		PriceCents:             level.PriceCents,
		DurationDays:           level.DurationDays,
		Sort:                   level.Sort,
		Description:            level.Description,
		Benefits:               benefits,
		CreatedAt:              level.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:              level.UpdatedAt.Format("2006-01-02 15:04:05"),
		Hide:                   level.Hide,
		CommissionRate:         level.CommissionRate,
		PlatformCommissionRate: level.PlatformCommissionRate,
	}

	return resp, nil
}

// UpdateUserShipLevel 更新会员等级（包含权益配置，事务处理）
func (s *JyhUserBenefitService) UpdateUserShipLevel(req *jyhReq.UserShipLevelUpdateReq) error {
	return TryTransaction(func(tx *query.Query) error {
		levelDb := tx.JyhUserShipLevel
		benefitMappingDb := tx.JyhUserShipLevelBenefit
		benefitDb := tx.JyhUserBenefit

		// 检查等级是否存在
		_, err := levelDb.Where(levelDb.ID.Eq(req.ID)).First()
		if err != nil {
			return errors.New("会员等级不存在")
		}

		// 检查Code是否与其他记录重复
		exists, err := levelDb.Where(levelDb.Code.Eq(req.Code), levelDb.ID.Neq(req.ID)).Count()
		if err != nil {
			global.GVA_LOG.Error("检查等级编码失败", zap.Error(err))
			return err
		}
		if exists > 0 {
			return errors.New("等级编码已存在")
		}

		// 检查Name是否与其他记录重复
		exists, err = levelDb.Where(levelDb.Name.Eq(req.Name), levelDb.ID.Neq(req.ID)).Count()
		if err != nil {
			global.GVA_LOG.Error("检查等级名称失败", zap.Error(err))
			return err
		}
		if exists > 0 {
			return errors.New("等级名称已存在")
		}

		// 验证权益配置中的权益是否存在
		for _, benefitConfig := range req.Benefits {
			exists, err := benefitDb.Where(benefitDb.ID.Eq(benefitConfig.BenefitID)).Count()
			if err != nil {
				global.GVA_LOG.Error("检查权益存在性失败", zap.Error(err))
				return err
			}
			if exists == 0 {
				return errors.New("权益类型不存在")
			}
		}

		// 更新等级基本信息
		updates := map[string]interface{}{
			"name":                     req.Name,
			"code":                     req.Code,
			"icon":                     req.Icon,
			"price_cents":              req.PriceCents,
			"duration_days":            req.DurationDays,
			"sort":                     req.Sort,
			"description":              req.Description,
			"hide":                     req.Hide,
			"commission_rate":          req.CommissionRate,
			"platform_commission_rate": req.PlatformCommissionRate,
		}

		_, err = levelDb.Where(levelDb.ID.Eq(req.ID)).Updates(updates)
		if err != nil {
			global.GVA_LOG.Error("更新会员等级失败", zap.Error(err))
			return err
		}

		// 删除原有的权益映射
		_, err = benefitMappingDb.Where(benefitMappingDb.LevelID.Eq(req.ID)).Delete()
		if err != nil {
			global.GVA_LOG.Error("删除原有权益映射失败", zap.Error(err))
			return err
		}

		// 重新创建权益映射
		for _, benefitConfig := range req.Benefits {
			mapping := &jyhapp.JyhUserShipLevelBenefit{
				LevelID:   req.ID,
				BenefitID: benefitConfig.BenefitID,
				Value:     benefitConfig.Value,
				Condition: benefitConfig.Condition,
			}

			err = benefitMappingDb.Create(mapping)
			if err != nil {
				global.GVA_LOG.Error("创建权益映射失败", zap.Error(err))
				return err
			}
		}

		return nil
	})
}

// DeleteUserShipLevel 删除会员等级
func (s *JyhUserBenefitService) DeleteUserShipLevel(id uint) error {
	return TryTransaction(func(tx *query.Query) error {
		levelDb := tx.JyhUserShipLevel
		userLevelDb := tx.JyhUserLevel
		benefitMappingDb := tx.JyhUserShipLevelBenefit

		// 检查是否被用户使用
		exists, err := userLevelDb.Where(userLevelDb.LevelID.Eq(id)).Count()
		if err != nil {
			global.GVA_LOG.Error("检查等级使用情况失败", zap.Error(err))
			return err
		}
		if exists > 0 {
			return errors.New("该会员等级正在被用户使用，无法删除")
		}

		// 删除等级权益映射
		_, err = benefitMappingDb.Where(benefitMappingDb.LevelID.Eq(id)).Delete()
		if err != nil {
			global.GVA_LOG.Error("删除等级权益映射失败", zap.Error(err))
			return err
		}

		// 删除等级
		_, err = levelDb.Where(levelDb.ID.Eq(id)).Delete()
		if err != nil {
			global.GVA_LOG.Error("删除会员等级失败", zap.Error(err))
			return err
		}

		return nil
	})
}

// ===== 用户等级记录管理（用户开通等级） =====

// UserLevelJoinResult 用户等级JOIN查询结果结构体
type UserLevelJoinResult struct {
	// 用户等级记录字段
	ID        uint      `gorm:"column:id"`
	UserID    uint      `gorm:"column:user_id"`
	LevelID   uint      `gorm:"column:level_id"`
	OrderID   *uint     `gorm:"column:order_id"`
	StartAt   time.Time `gorm:"column:start_at"`
	EndAt     time.Time `gorm:"column:end_at"`
	Status    string    `gorm:"column:status"`
	CreatedAt time.Time `gorm:"column:created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at"`

	// 用户信息字段
	Username      string    `gorm:"column:username"`
	Phone         string    `gorm:"column:phone"`
	UserCreatedAt time.Time `gorm:"column:user_created_at"`
	UserType      string    `gorm:"column:user_type"`
	UserStatus    uint      `gorm:"column:user_status"`

	// 等级信息字段
	LevelName    string `gorm:"column:level_name"`
	LevelCode    string `gorm:"column:level_code"`
	PriceCents   uint64 `gorm:"column:price_cents"`
	DurationDays uint   `gorm:"column:duration_days"`
}

// GetUserLevelList 获取用户等级记录列表（合并原待审核列表功能，使用JOIN优化）
func (s *JyhUserBenefitService) GetUserLevelList(req *jyhReq.UserLevelListReq) ([]*jyhResp.UserLevelListItem, int64, error) {
	db := query.JyhUserLevel
	userDb := query.JyhUser
	levelDb := query.JyhUserShipLevel

	q := db.WithContext(context.Background())
	// 构建条件数组
	var conditions []gen.Condition
	{
		// 基础条件过滤
		if req.UserID > 0 {
			conditions = append(conditions, db.UserID.Eq(req.UserID))
		}
		if req.LevelID > 0 {
			conditions = append(conditions, db.LevelID.Eq(req.LevelID))
		}
		if req.Status != "" {
			conditions = append(conditions, db.Status.Eq(req.Status))
		}

		// 时间范围筛选
		if startTime := jtime.Str2Date(req.StartTime); !startTime.IsZero() {
			conditions = append(conditions, db.CreatedAt.Gte(startTime))
		}
		if endTime := jtime.Str2Date(req.EndTime); !endTime.IsZero() {
			conditions = append(conditions, db.CreatedAt.Lte(endTime))
		}
	}
	dao := q.Where(conditions...)
	// 获取总数
	total, err := dao.Count()
	if err != nil {
		global.GVA_LOG.Error("获取用户等级记录列表总数失败", zap.Error(err))
		return nil, 0, err
	}
	offset := (req.Page - 1) * req.PageSize
	var joinResults []UserLevelJoinResult
	err = q.Select(
		// 用户等级记录字段
		db.ID, db.UserID, db.LevelID, db.OrderID, db.StartAt, db.EndAt,
		db.Status, db.CreatedAt, db.UpdatedAt,
		// 用户信息字段
		userDb.Username, userDb.Phone,
		userDb.CreatedAt.As("user_created_at"),
		userDb.UserType.As("user_type"),
		userDb.Status.As("user_status"),
		// 等级信息字段
		levelDb.Name.As("level_name"),
		levelDb.Code.As("level_code"),
		levelDb.PriceCents,
		levelDb.DurationDays,
	).LeftJoin(userDb, db.UserID.EqCol(userDb.ID)).
		LeftJoin(levelDb, db.LevelID.EqCol(levelDb.ID)).
		Where(conditions...).
		Offset(offset).Limit(req.PageSize).
		Order(db.CreatedAt.Desc()).
		Scan(&joinResults)

	if err != nil {
		global.GVA_LOG.Error("获取用户等级记录列表失败", zap.Error(err))
		return nil, 0, err
	}

	// 构建响应数据
	result := make([]*jyhResp.UserLevelListItem, 0, len(joinResults))
	now := time.Now()

	for _, joinResult := range joinResults {
		// 计算等待审核天数（仅待审核状态时有值）
		waitingDays := 0
		if joinResult.Status == "pending" {
			waitingDays = int(now.Sub(joinResult.CreatedAt).Hours() / 24)
		}
		item := &jyhResp.UserLevelListItem{
			ID:            joinResult.ID,
			UserID:        joinResult.UserID,
			LevelID:       joinResult.LevelID,
			OrderID:       joinResult.OrderID,
			StartAt:       joinResult.StartAt.Format("2006-01-02 15:04:05"),
			EndAt:         joinResult.EndAt.Format("2006-01-02 15:04:05"),
			Status:        joinResult.Status,
			ApplyTime:     joinResult.CreatedAt.Format("2006-01-02 15:04:05"),
			WaitingDays:   waitingDays,
			CreatedAt:     joinResult.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:     joinResult.UpdatedAt.Format("2006-01-02 15:04:05"),
			Username:      joinResult.Username,
			Phone:         HideIdent(joinResult.Phone),
			UserCreatedAt: joinResult.UserCreatedAt.Format("2006-01-02 15:04:05"),
			UserType:      joinResult.UserType,
			UserStatus:    joinResult.UserStatus,
			LevelName:     joinResult.LevelName,
			LevelCode:     joinResult.LevelCode,
			PriceCents:    joinResult.PriceCents,
			DurationDays:  joinResult.DurationDays,
		}
		result = append(result, item)
	}

	return result, total, nil
}

// CreateUserLevel 用户开通等级（创建用户等级记录并快照权益）
func (s *JyhUserBenefitService) CreateUserLevel(req *jyhReq.UserLevelReq) error {
	return TryTransaction(func(tx *query.Query) error {
		userLevelDb := tx.JyhUserLevel

		// 判断是否已开通或正在申请中
		// 检查是否有相同等级的有效记录（active状态或pending状态）
		existingCount, err := userLevelDb.Where(
			userLevelDb.UserID.Eq(req.UserID),
			userLevelDb.LevelID.Eq(req.LevelID),
			userLevelDb.Status.In("active", "pending"),
		).Count()
		if err != nil {
			global.GVA_LOG.Error("检查用户等级记录失败", zap.Error(err))
			return err
		}
		if existingCount > 0 {
			return errors.New("该等级已开通或正在审核中，请勿重复申请")
		}

		// 设置初始状态为待审核
		status := "pending"
		// 创建用户等级记录
		userLevel := &jyhapp.JyhUserLevel{
			UserID:  req.UserID,
			LevelID: req.LevelID,
			//OrderID: req.OrderID,
			StartAt: time.Now(),
			EndAt:   time.Now().AddDate(1, 0, 0),
			Status:  status,
		}
		err = userLevelDb.Create(userLevel)
		if err != nil {
			global.GVA_LOG.Error("创建用户等级记录失败", zap.Error(err))
			return err
		}

		// 创建用户权益快照（快照开通时的权益配置）
		err = s.CreateUserBenefitSnapshotTx(tx, userLevel.ID, req.UserID, req.LevelID, userLevel.StartAt, userLevel.EndAt)
		if err != nil {
			global.GVA_LOG.Error("创建用户权益快照失败", zap.Error(err))
			return err
		}

		return nil
	})
}

// CreateUserBenefitSnapshotTx 在事务中创建用户权益快照
func (s *JyhUserBenefitService) CreateUserBenefitSnapshotTx(tx *query.Query, userLevelID, userID, levelID uint, startAt, endAt time.Time) error {
	benefitMappingDb := tx.JyhUserShipLevelBenefit
	benefitDb := tx.JyhUserBenefit
	snapshotDb := tx.JyhUserBenefitSnapshot

	// 获取该等级的所有权益配置
	mappings, err := benefitMappingDb.Where(benefitMappingDb.LevelID.Eq(levelID)).Find()
	if err != nil {
		return err
	}

	// 为每个权益创建快照（快照开通时的权益配置）
	for _, mapping := range mappings {
		// 获取权益详情
		benefit, err := benefitDb.Where(benefitDb.ID.Eq(mapping.BenefitID)).First()
		if err != nil {
			continue
		}

		// 创建快照
		snapshot := &jyhapp.JyhUserBenefitSnapshot{
			UserID:      userID,
			UserLevelID: userLevelID,
			LevelID:     levelID,
			BenefitID:   mapping.BenefitID,
			Value:       mapping.Value,     // 快照开通时的权益值
			Condition:   mapping.Condition, // 快照开通时的条件
			StartAt:     startAt,
			EndAt:       endAt,
			BenefitKey:  benefit.Key,
			BenefitName: benefit.Name,
		}

		err = snapshotDb.Create(snapshot)
		if err != nil {
			global.GVA_LOG.Error("创建用户权益快照失败", zap.Error(err))
			continue
		}
	}

	return nil
}

// UpdateUserLevel 更新用户等级记录
func (s *JyhUserBenefitService) UpdateUserLevel(req *jyhReq.UserLevelReq) error {
	db := query.JyhUserLevel

	// 检查记录是否存在
	_, err := db.Where(db.ID.Eq(req.ID)).First()
	if err != nil {
		return errors.New("用户等级记录不存在")
	}

	// 更新记录
	updates := map[string]interface{}{
		"user_id":  req.UserID,
		"level_id": req.LevelID,
		"order_id": req.OrderID,
		"start_at": req.StartAt,
		"end_at":   req.EndAt,
		"status":   req.Status,
	}

	_, err = db.Where(db.ID.Eq(req.ID)).Updates(updates)
	if err != nil {
		global.GVA_LOG.Error("更新用户等级记录失败", zap.Error(err))
		return err
	}

	return nil
}

// AuditUserLevel 审核用户等级记录
func (s *JyhUserBenefitService) AuditUserLevel(req *jyhReq.UserLevelAuditReq) error {
	return TryTransaction(func(tx *query.Query) error {
		userLevelDb := tx.JyhUserLevel
		levelDb := tx.JyhUserShipLevel
		snapshotDb := tx.JyhUserBenefitSnapshot

		// 检查记录是否存在
		userLevel, err := userLevelDb.Where(userLevelDb.ID.Eq(req.ID)).First()
		if err != nil {
			return errors.New("用户等级记录不存在")
		}

		// 检查当前状态是否为待审核
		if userLevel.Status != "pending" {
			return errors.New("该记录不是待审核状态，无法审核")
		}

		// 验证审核状态
		if req.Status != "active" && req.Status != "rejected" {
			return errors.New("无效的审核状态，只能设置为 active 或 rejected")
		}

		// 准备更新数据
		updates := map[string]interface{}{
			"status": req.Status,
		}

		// 如果审核通过，计算正确的开始时间和结束时间
		if req.Status == "active" {
			// 获取等级信息
			level, err := levelDb.Where(levelDb.ID.Eq(userLevel.LevelID)).First()
			if err != nil {
				return errors.New("会员等级不存在")
			}

			// 设置开始时间为当前时间
			startAt := time.Now()
			var endAt time.Time

			// 根据等级有效期计算结束时间
			if level.DurationDays == 0 {
				// 0表示永久有效，设置为一个很远的日期
				endAt = time.Date(2099, 12, 31, 23, 59, 59, 0, time.Local)
			} else {
				// 按天数计算结束时间
				endAt = startAt.AddDate(0, 0, int(level.DurationDays))
			}

			updates["start_at"] = startAt
			updates["end_at"] = endAt

			// 更新权益快照的时间
			_, err = snapshotDb.Where(snapshotDb.UserLevelID.Eq(req.ID)).Updates(map[string]interface{}{
				"start_at": startAt,
				"end_at":   endAt,
			})
			if err != nil {
				global.GVA_LOG.Error("更新权益快照时间失败", zap.Error(err))
				return err
			}
		}

		// 更新用户等级记录
		_, err = userLevelDb.Where(userLevelDb.ID.Eq(req.ID)).Updates(updates)
		if err != nil {
			global.GVA_LOG.Error("审核用户等级记录失败", zap.Error(err))
			return err
		}

		// 如果是拒绝审核，删除相关的权益快照
		if req.Status == "rejected" {
			_, err = snapshotDb.Where(snapshotDb.UserLevelID.Eq(req.ID)).Delete()
			if err != nil {
				global.GVA_LOG.Error("删除权益快照失败", zap.Error(err))
				return err
			}
		}

		return nil
	})
}

// DeleteUserLevel 删除用户等级记录
func (s *JyhUserBenefitService) DeleteUserLevel(id uint) error {
	return TryTransaction(func(tx *query.Query) error {
		userLevelDb := tx.JyhUserLevel
		snapshotDb := tx.JyhUserBenefitSnapshot

		// 删除相关的权益快照
		_, err := snapshotDb.Where(snapshotDb.UserLevelID.Eq(id)).Delete()
		if err != nil {
			global.GVA_LOG.Error("删除用户权益快照失败", zap.Error(err))
			return err
		}

		// 删除用户等级记录
		_, err = userLevelDb.Where(userLevelDb.ID.Eq(id)).Delete()
		if err != nil {
			global.GVA_LOG.Error("删除用户等级记录失败", zap.Error(err))
			return err
		}

		return nil
	})
}

// ===== 用户权益快照管理（主要用于查询） =====

// GetUserBenefitSnapshotList 获取用户权益快照列表
func (s *JyhUserBenefitService) GetUserBenefitSnapshotList(req *jyhReq.UserBenefitSnapshotListReq) ([]*jyhResp.UserBenefitSnapshotListItem, int64, error) {
	db := query.JyhUserBenefitSnapshot
	userDb := query.JyhUser
	levelDb := query.JyhUserShipLevel

	q := db.WithContext(context.Background())

	// 条件查询
	if req.UserID > 0 {
		q = q.Where(db.UserID.Eq(req.UserID))
	}
	if req.LevelID > 0 {
		q = q.Where(db.LevelID.Eq(req.LevelID))
	}
	if req.BenefitID > 0 {
		q = q.Where(db.BenefitID.Eq(req.BenefitID))
	}

	// 获取总数
	total, err := q.Count()
	if err != nil {
		global.GVA_LOG.Error("获取用户权益快照列表总数失败", zap.Error(err))
		return nil, 0, err
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	snapshots, err := q.Offset(offset).Limit(req.PageSize).Order(db.CreatedAt.Desc()).Find()
	if err != nil {
		global.GVA_LOG.Error("获取用户权益快照列表失败", zap.Error(err))
		return nil, 0, err
	}

	// 构建响应数据
	var result []*jyhResp.UserBenefitSnapshotListItem
	for _, snapshot := range snapshots {
		// 获取用户信息
		user, _ := userDb.Where(userDb.ID.Eq(snapshot.UserID)).First()
		// 获取等级信息
		level, _ := levelDb.Where(levelDb.ID.Eq(snapshot.LevelID)).First()

		item := &jyhResp.UserBenefitSnapshotListItem{
			ID:          snapshot.ID,
			UserID:      snapshot.UserID,
			UserLevelID: snapshot.UserLevelID,
			LevelID:     snapshot.LevelID,
			BenefitID:   snapshot.BenefitID,
			Value:       snapshot.Value,
			Condition:   snapshot.Condition,
			StartAt:     snapshot.StartAt.Format("2006-01-02 15:04:05"),
			EndAt:       snapshot.EndAt.Format("2006-01-02 15:04:05"),
			BenefitKey:  snapshot.BenefitKey,
			BenefitName: snapshot.BenefitName,
			CreatedAt:   snapshot.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:   snapshot.UpdatedAt.Format("2006-01-02 15:04:05"),
		}

		if user != nil {
			item.Username = user.Username
			item.Phone = HideIdent(user.Phone)
		}
		if level != nil {
			item.LevelName = level.Name
		}

		result = append(result, item)
	}

	return result, total, nil
}
