package jyhapp

import (
	jyhappRes "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	jyhappReq "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/request"
	jyhappService "github.com/flipped-aurora/gin-vue-admin/server/service/jyhapp"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
)

type LiveApi struct{}

// GetChannelDetailList 获取频道详细信息列表
// @Tags Live
// @Summary 获取频道详细信息列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body jyhappService.GetChannelDetailListReq true "查询参数"
// @Success 200 {object} response.Response{data=jyhappService.GetChannelDetailListResp,msg=string} "获取成功"
// @Router /live/channels [get]
func (l *LiveApi) GetChannelDetailList(c *gin.Context) {
	var req jyhappService.GetChannelDetailListReq
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	polyv := global.GVA_CONFIG.Polyv
	appId := polyv.AppId
	secretKey := polyv.SecretKey
	privateKey := polyv.PrivateKey
	publicKey := polyv.PublicKey
	userId := polyv.UserId
	liveService := jyhappService.NewPolyvLiveService(appId, secretKey, userId, privateKey, publicKey)
	resp, err := liveService.GetChannelDetailList(&req)
	if err != nil {
		global.GVA_LOG.Error("获取频道列表失败", zap.Error(err))
		response.FailWithMessage("获取频道列表失败: "+err.Error(), c)
		return
	}

	// 记录日志
	global.GVA_LOG.Info("用户查询频道列表",
		zap.Int("page", req.Page),
		zap.Int("page_size", req.PageSize),
		zap.String("status", req.Status),
		zap.String("keyword", req.Keyword))

	response.OkWithData(resp, c)
}

// StartLive 用户自行开播
// @Tags Live
// @Summary 用户自行开播
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body jyhappReq.CreateJyhUserLiveRoomReq true "开播参数"
// @Success 200 {object} response.Response{msg=string} "开播成功"
// @Router /live/start_live [post]
func (l *LiveApi) StartLive(c *gin.Context) {
	var req jyhappReq.CreateJyhUserLiveRoomReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	// 从JWT中获取用户ID
	userID := utils.GetUserID(c)
	// 检查用户开播权限
	user, err := userService.GetJyhUser(userID)
	if err != nil {
		global.GVA_LOG.Error("获取用户信息失败", zap.Error(err), zap.Uint("userID", userID))
		response.FailWithMessage("获取用户信息失败", c)
		return
	}
	// 验证开播权限
	if !user.CanLiveStream {
		global.GVA_LOG.Warn("用户无开播权限", zap.Uint("userID", userID))
		response.FailWithMessage("您暂无开播权限，请联系管理员", c)
		return
	}
	// 调用用户直播房间服务创建或获取直播房间
	liveRoom, err := jyhUserLiveRoomService.CreateUserLiveRoom(userID, req)
	if err != nil {
		global.GVA_LOG.Error("创建直播房间失败", zap.Error(err), zap.Uint("userID", userID))
		response.FailWithMessage("开播失败: "+err.Error(), c)
		return
	}

	// 记录开播日志
	global.GVA_LOG.Info("用户开播成功",
		zap.Uint("userID", userID),
		zap.Int64("channelId", liveRoom.ChannelId),
		zap.String("channelName", liveRoom.ChannelName))

	response.OkWithDetailed(liveRoom, "开播成功", c)
}

// UserLiveRoom 获取当前用户的直播房间
// @Tags JyhUserLiveRoom
// @Summary 获取当前用户的直播房间
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=jyhappRes.CreateJyhUserLiveRoomResponse,msg=string} "获取成功"
// @Router /live/live_room [get]
func (l *LiveApi) UserLiveRoom(c *gin.Context) {
	// 从JWT中获取用户ID
	userID := utils.GetUserID(c)

	// 检查用户开播权限
	user, err := userService.GetJyhUser(userID)
	if err != nil {
		global.GVA_LOG.Error("获取用户信息失败", zap.Error(err), zap.Uint("userID", userID))
		response.FailWithMessage("获取用户信息失败", c)
		return
	}

	// 验证开播权限
	if !user.CanLiveStream {
		response.FailWithMessage("您暂无开播权限，请联系管理员", c)
		return
	}
	liveRoom := &jyhappRes.CreateJyhUserLiveRoomResponse{}
	// 获取用户当前的直播房间
	liveRoom, err = jyhUserLiveRoomService.UserLiveRoom(userID)
	if err != nil {
		global.GVA_LOG.Error("获取用户直播房间失败", zap.Error(err), zap.Uint("userID", userID))
		response.FailWithMessage("获取直播房间失败: "+err.Error(), c)
		return
	}

	// 如果没有直播房间
	if liveRoom == nil {
		response.FailWithMessage("您还没有直播房间，请先创建", c)
		return
	}

	// 返回直播房间信息
	response.OkWithDetailed(map[string]interface{}{
		"has_permission": true,
		"has_room":       true,
		"room":           liveRoom,
	}, "获取成功", c)
}

// GetLiveAuthInfo 获取直播加密参数
// @Tags Live
// @Summary 获取直播加密参数
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=jyhappRes.EncryptedAuthInfo,msg=string} "获取成功"
// @Router /live/auth_info [get]
func (l *LiveApi) GetLiveAuthInfo(c *gin.Context) {
	// 从JWT中获取用户ID
	userID := utils.GetUserID(c)
	// 创建保利威服务实例并生成认证信息
	polyv := global.GVA_CONFIG.Polyv
	userId := polyv.UserId
	liveService := jyhappService.NewPolyvLiveService(polyv.AppId, polyv.SecretKey, userId, polyv.PrivateKey, polyv.PublicKey)

	authInfo, err := liveService.EncryptAuthInfo()
	if err != nil {
		global.GVA_LOG.Error("生成认证信息失败", zap.Error(err), zap.Uint("userID", userID))
		response.FailWithMessage("生成认证信息失败: "+err.Error(), c)
		return
	}

	// 记录日志
	global.GVA_LOG.Info("用户获取直播加密参数成功", zap.Uint("userID", userID))

	response.OkWithDetailed(authInfo, "获取成功", c)
}
