package test

import (
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/customerservice/model"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/customerservice/service"
)

// TestTransferWorkflow 测试完整的转接工作流程
func TestTransferWorkflow() {
	fmt.Println("=== 客服转接功能测试 ===")

	transferService := &service.TransferService{}

	// 测试场景1：技术问题转接
	fmt.Println("\n--- 场景1：技术问题转接 ---")
	techTransferReq := &service.TransferRequest{
		TicketID:      1,
		FromServiceID: 1,  // 普通客服
		ToServiceID:   10, // 技术专员
		UserID:        1001,
		Reason:        "用户遇到技术问题，需要技术支持处理",
		Priority:      3, // 高优先级
		TransferType:  1, // 手动转接
	}

	transfer1, err := transferService.CreateTransferRequest(techTransferReq)
	if err != nil {
		log.Printf("创建技术转接失败: %v", err)
	} else {
		fmt.Printf("技术转接创建成功: ID=%d, 过期时间=%d\n", transfer1.ID, transfer1.ExpireTime)
	}

	// 测试场景2：VIP客户转接
	fmt.Println("\n--- 场景2：VIP客户转接 ---")
	vipTransferReq := &service.TransferRequest{
		TicketID:      2,
		FromServiceID: 2,  // 普通客服
		ToServiceID:   20, // VIP专属客服
		UserID:        2001,
		Reason:        "VIP客户需要高级客服专属服务",
		Priority:      3, // 高优先级
		TransferType:  2, // 智能转接
	}

	transfer2, err := transferService.CreateTransferRequest(vipTransferReq)
	if err != nil {
		log.Printf("创建VIP转接失败: %v", err)
	} else {
		fmt.Printf("VIP转接创建成功: ID=%d\n", transfer2.ID)
	}

	// 测试场景3：接受转接
	fmt.Println("\n--- 场景3：接受转接 ---")
	if transfer1 != nil {
		err = transferService.AcceptTransfer(transfer1.ID)
		if err != nil {
			log.Printf("接受转接失败: %v", err)
		} else {
			fmt.Printf("转接 %d 已被接受\n", transfer1.ID)
		}
	}

	// 测试场景4：拒绝转接
	fmt.Println("\n--- 场景4：拒绝转接 ---")
	if transfer2 != nil {
		err = transferService.RejectTransfer(transfer2.ID, "当前负载过高，无法接收新用户")
		if err != nil {
			log.Printf("拒绝转接失败: %v", err)
		} else {
			fmt.Printf("转接 %d 已被拒绝\n", transfer2.ID)
		}
	}

	// 测试场景5：批量转接
	fmt.Println("\n--- 场景5：批量转接 ---")
	ticketIDs := []uint{3, 4, 5}
	err = transferService.BatchTransfer(ticketIDs, 3, 30, "负载均衡转接")
	if err != nil {
		log.Printf("批量转接失败: %v", err)
	} else {
		fmt.Printf("批量转接成功，转接了 %d 个工单\n", len(ticketIDs))
	}
}

// TestTransferStatistics 测试转接统计功能
func TestTransferStatistics() {
	fmt.Println("\n=== 转接统计测试 ===")

	transferService := &service.TransferService{}

	// 获取客服转接统计
	serviceID := int64(1)
	stats, err := transferService.GetTransferStatistics(serviceID)
	if err != nil {
		log.Printf("获取转接统计失败: %v", err)
		return
	}

	fmt.Printf("客服 %d 的转接统计:\n", serviceID)
	statsJSON, _ := json.MarshalIndent(stats, "", "  ")
	fmt.Println(string(statsJSON))

	// 获取待处理转接
	pendingTransfers, err := transferService.GetPendingTransfers(serviceID)
	if err != nil {
		log.Printf("获取待处理转接失败: %v", err)
		return
	}

	fmt.Printf("\n客服 %d 的待处理转接数量: %d\n", serviceID, len(pendingTransfers))
	for _, transfer := range pendingTransfers {
		fmt.Printf("  - 转接ID: %d, 来源客服: %d, 原因: %s\n",
			transfer.ID, transfer.FromServiceID, transfer.Reason)
	}
}

// TestAvailableServices 测试可转接客服查询
func TestAvailableServices() {
	fmt.Println("\n=== 可转接客服查询测试 ===")

	transferService := &service.TransferService{}

	currentServiceID := int64(1)
	userTags := []uint{1, 2} // 假设用户有VIP和技术支持标签

	services, err := transferService.GetAvailableServices(currentServiceID, userTags)
	if err != nil {
		log.Printf("获取可转接客服失败: %v", err)
		return
	}

	fmt.Printf("当前客服 %d 可转接的客服列表:\n", currentServiceID)
	for _, service := range services {
		fmt.Printf("  - 客服ID: %d, 昵称: %s, 在线: %d, 当前负载: %d/%d\n",
			service.Id, service.Nickname, service.Online,
			service.CurrentUserCount, service.MaxUserCount)
	}
}

// TestTransferExpiration 测试转接过期处理
func TestTransferExpiration() {
	fmt.Println("\n=== 转接过期处理测试 ===")

	transferService := &service.TransferService{}

	// 创建一个即将过期的转接请求
	expireTransferReq := &service.TransferRequest{
		TicketID:      99,
		FromServiceID: 1,
		ToServiceID:   2,
		UserID:        9999,
		Reason:        "测试过期转接",
		Priority:      2,
		TransferType:  1,
	}

	transfer, err := transferService.CreateTransferRequest(expireTransferReq)
	if err != nil {
		log.Printf("创建测试转接失败: %v", err)
		return
	}

	fmt.Printf("创建测试转接: ID=%d, 过期时间=%d\n", transfer.ID, transfer.ExpireTime)

	// 模拟时间过去，手动设置过期时间为过去的时间
	pastTime := time.Now().Add(-10 * time.Minute).Unix()
	global.GVA_DB.Model(&model.SysServiceTransfer{}).
		Where("id = ?", transfer.ID).
		Update("expire_time", pastTime)

	// 执行过期清理
	err = transferService.CleanupExpiredTransfers()
	if err != nil {
		log.Printf("清理过期转接失败: %v", err)
		return
	}

	fmt.Println("过期转接清理完成")

	// 检查转接状态
	var updatedTransfer model.SysServiceTransfer
	global.GVA_DB.Where("id = ?", transfer.ID).First(&updatedTransfer)
	fmt.Printf("转接状态更新为: %d (5表示已过期)\n", updatedTransfer.Status)
}

// TestTransferCancel 测试转接取消功能
func TestTransferCancel() {
	fmt.Println("\n=== 转接取消测试 ===")

	transferService := &service.TransferService{}

	// 创建一个转接请求
	cancelTransferReq := &service.TransferRequest{
		TicketID:      88,
		FromServiceID: 1,
		ToServiceID:   2,
		UserID:        8888,
		Reason:        "测试取消转接",
		Priority:      2,
		TransferType:  1,
	}

	transfer, err := transferService.CreateTransferRequest(cancelTransferReq)
	if err != nil {
		log.Printf("创建测试转接失败: %v", err)
		return
	}

	fmt.Printf("创建测试转接: ID=%d\n", transfer.ID)

	// 取消转接
	err = transferService.CancelTransfer(transfer.ID, "用户问题已解决，无需转接")
	if err != nil {
		log.Printf("取消转接失败: %v", err)
		return
	}

	fmt.Printf("转接 %d 已取消\n", transfer.ID)

	// 验证状态
	var canceledTransfer model.SysServiceTransfer
	global.GVA_DB.Where("id = ?", transfer.ID).First(&canceledTransfer)
	fmt.Printf("转接状态: %d, 取消原因: %s\n",
		canceledTransfer.Status, canceledTransfer.CancelReason)
}

// TestTransferValidation 测试转接验证逻辑
func TestTransferValidation() {
	fmt.Println("\n=== 转接验证测试 ===")

	transferService := &service.TransferService{}

	// 测试各种验证场景
	testCases := []struct {
		name        string
		fromService int64
		toService   int64
		expectError bool
		description string
	}{
		{
			name:        "正常转接",
			fromService: 1,
			toService:   2,
			expectError: false,
			description: "两个在线客服之间的正常转接",
		},
		{
			name:        "自己转给自己",
			fromService: 1,
			toService:   1,
			expectError: true,
			description: "不能转接给自己",
		},
		{
			name:        "目标客服离线",
			fromService: 1,
			toService:   999, // 假设不存在的客服
			expectError: true,
			description: "目标客服不存在或离线",
		},
	}

	for _, tc := range testCases {
		fmt.Printf("\n--- %s ---\n", tc.name)
		fmt.Printf("描述: %s\n", tc.description)

		canTransfer, msg := transferService.CanTransfer(tc.fromService, tc.toService)
		fmt.Printf("可以转接: %t, 消息: %s\n", canTransfer, msg)

		if tc.expectError && canTransfer {
			fmt.Printf("❌ 预期应该失败，但验证通过了\n")
		} else if !tc.expectError && !canTransfer {
			fmt.Printf("❌ 预期应该成功，但验证失败了\n")
		} else {
			fmt.Printf("✅ 验证结果符合预期\n")
		}
	}
}

// RunAllTransferTests 运行所有转接测试
func RunAllTransferTests() {
	fmt.Println("开始运行客服转接功能测试...")

	TestTransferWorkflow()
	TestTransferStatistics()
	TestAvailableServices()
	TestTransferExpiration()
	TestTransferCancel()
	TestTransferValidation()

	fmt.Println("\n所有转接测试完成！")
}
