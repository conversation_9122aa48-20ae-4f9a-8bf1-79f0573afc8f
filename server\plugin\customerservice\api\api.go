package api

import (
	"crypto/md5"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"path/filepath"
	"sort"
	"strconv"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
	sysModel "github.com/flipped-aurora/gin-vue-admin/server/plugin/customerservice/model"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/customerservice/service"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/customerservice/service/ws"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/customerservice/tools"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.uber.org/zap"
)

type CustomerServiceApi struct{}

func (cus *CustomerServiceApi) ServeWs(ctx *gin.Context) {
	ws.WsServe(ctx)
}

func (cus *CustomerServiceApi) ServeWsForKefu(ctx *gin.Context) {
	ws.ServeWsForKefu(ctx)
}

func (cus *CustomerServiceApi) HandleTransfer(c *gin.Context) {
	var transferReq struct {
		SessionID     uint   `json:"session_id"` // SysServiceRecord表的ID
		FromServiceID uint   `json:"from_service_id"`
		ToServiceID   uint   `json:"to_service_id"`
		UserID        uint   `json:"user_id"`
		Reason        string `json:"reason"`
	}
	if err := c.ShouldBindJSON(&transferReq); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}

	// 检查是否可以转接
	transferService := &service.TransferService{}
	if canTransfer, msg := transferService.CanTransfer(int64(transferReq.FromServiceID), int64(transferReq.ToServiceID)); !canTransfer {
		response.FailWithMessage(msg, c)
		return
	}

	// 构建转接请求
	req := &service.TransferRequest{
		SessionID:     transferReq.SessionID,
		FromServiceID: int64(transferReq.FromServiceID),
		ToServiceID:   int64(transferReq.ToServiceID),
		UserID:        transferReq.UserID,
		Reason:        transferReq.Reason,
		Priority:      2, // 默认中等优先级
		TransferType:  1, // 手动转接
	}

	// 创建转接记录
	transfer, err := transferService.CreateTransferRequest(req)
	if err != nil {
		response.FailWithMessage("创建转接记录失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(transfer, "转接申请已发送", c)
}

// GetKefuInfo 获取客服信息
// 根据用户ID获取分配的客服信息，支持智能分配和历史记录查找
func (cus *CustomerServiceApi) GetKefuInfo(c *gin.Context) {
	// 1. 获取用户ID和可能存在的客服ID
	userID, serviceIDFromContext := cus.extractUserAndServiceID(c)

	// 2. 确定客服ID
	serviceID, err := cus.determineServiceID(userID, serviceIDFromContext)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 3. 获取客服详细信息
	serviceInfo, err := cus.getServiceInfo(serviceID)
	if err != nil {
		response.FailWithMessage("获取客服信息失败", c)
		return
	}

	response.OkWithDetailed(serviceInfo, "获取成功", c)
}

// extractUserAndServiceID 提取用户ID和客服ID
func (cus *CustomerServiceApi) extractUserAndServiceID(c *gin.Context) (uint, interface{}) {
	userID, _ := c.Get("jwt_user_id")
	serviceIDFromContext, _ := c.Get("service_id")

	// 转换用户ID为uint类型
	var uid uint
	if userID != nil {
		if uidStr := fmt.Sprintf("%v", userID); uidStr != "" {
			if parsedUID, err := strconv.ParseUint(uidStr, 10, 32); err == nil {
				uid = uint(parsedUID)
			}
		}
	}

	return uid, serviceIDFromContext
}

// determineServiceID 确定要使用的客服ID
func (cus *CustomerServiceApi) determineServiceID(userID uint, serviceIDFromContext interface{}) (int64, error) {
	// 如果上下文中已有客服ID，直接使用
	if serviceIDFromContext != nil {
		serviceIDStr := fmt.Sprintf("%v", serviceIDFromContext)
		if serviceID, err := strconv.ParseInt(serviceIDStr, 10, 64); err == nil {
			return serviceID, nil
		}
	}

	// 尝试从历史记录中获取客服ID
	if serviceID, found := cus.getServiceFromHistory(userID); found {
		return serviceID, nil
	}

	// 使用智能分配获取客服ID
	return cus.assignServiceForUser(userID)
}

// getServiceFromHistory 从历史记录中获取客服ID
func (cus *CustomerServiceApi) getServiceFromHistory(userID uint) (int64, bool) {
	var recordData sysModel.SysServiceRecord
	result := global.GVA_DB.Where("uid = ?", userID).
		Order("update_time DESC").
		Limit(1).
		Find(&recordData)

	if result.RowsAffected > 0 && result.Error == nil {
		return recordData.ServiceId, true
	}

	return 0, false
}

// assignServiceForUser 为用户分配客服
func (cus *CustomerServiceApi) assignServiceForUser(userID uint) (int64, error) {
	// 获取用户标签
	userTags := getUserTags(userID)

	// 使用智能分配
	assignedService, err := distributeService.SmartAssign(userID, userTags)
	if err != nil {
		return 0, fmt.Errorf("暂无可用客服")
	}

	// 如果智能分配返回有效的客服ID
	if assignedService != nil && assignedService.Id > 0 {
		return assignedService.Id, nil
	}

	// 回退到默认客服分配
	return cus.getFallbackService()
}

// getFallbackService 获取回退客服（最后可用的客服）
func (cus *CustomerServiceApi) getFallbackService() (int64, error) {
	var serviceID int64
	result := global.GVA_DB.Model(&sysModel.SysService{}).
		Select("id").
		Where("status = ?", 1).
		Order("add_time DESC").
		Limit(1).
		Scan(&serviceID)

	if result.Error != nil {
		return 0, fmt.Errorf("获取客服信息失败")
	}

	return serviceID, nil
}

// getServiceInfo 获取客服详细信息
func (cus *CustomerServiceApi) getServiceInfo(serviceID int64) (*sysModel.SysService, error) {
	var serviceData sysModel.SysService
	result := global.GVA_DB.Select("id,uid,online,avatar,nickname,add_time,status").
		Where("id = ?", serviceID).
		Where("status = ?", 1).
		Order("add_time DESC").
		First(&serviceData)

	if result.Error != nil {
		return nil, result.Error
	}

	return &serviceData, nil
}

func (cus *CustomerServiceApi) SendMsg(c *gin.Context) {
	var msgJson ws.Message
	if jsErr := c.ShouldBindJSON(&msgJson); jsErr != nil {
		fmt.Println(jsErr)
		response.FailWithMessage("参数有误-1", c)
		return
	}
	fromIdStr := msgJson.Sender
	toIdStr := msgJson.Receiver
	content := msgJson.Content
	isKf := msgJson.IsKf
	msgTypeStr := msgJson.MsgType
	if content == "" || fromIdStr == "" || toIdStr == "" || msgTypeStr == "" {
		response.FailWithMessage("参数有误-2", c)
		return
	}
	toId, err_1 := strconv.ParseInt(toIdStr, 10, 64)
	fromId, err_2 := strconv.ParseInt(fromIdStr, 10, 64)
	msgType, err_3 := strconv.ParseInt(msgTypeStr, 10, 64)
	if err_1 != nil || err_2 != nil || err_3 != nil {
		response.FailWithMessage("参数有误", c)
		return
	}
	//限流
	if !tools.LimitFreqSingle("send_message:"+c.ClientIP(), 1, 2) {
		response.FailWithMessage("发送频率过快", c)
		return
	}
	var kfInfo sysModel.SysService
	//var userInfo sysModel.SysTestUser
	var userInfo jyhapp.JyhUser
	var err, err2 error
	if isKf == 1 {
		err = global.GVA_DB.Where("id = ?", fromId).First(&kfInfo).Error
		err2 = global.GVA_DB.Where("id = ?", toId).First(&userInfo).Error

	} else if isKf == 0 {
		err = global.GVA_DB.Where("id = ?", toId).First(&kfInfo).Error
		err2 = global.GVA_DB.Where("id = ?", fromId).First(&userInfo).Error
	}
	if err != nil || err2 != nil {
		response.FailWithMessage("获取失败-1", c)
		return
	}

	ser := service.ServiceGroupApp
	cErr := ser.CreateMsg(kfInfo, userInfo, msgType, content, strconv.FormatInt(isKf, 10))
	if cErr != nil {
		response.FailWithMessage("发送失败", c)
		return
	}

	// 更新会话状态为活跃
	if isKf == 1 {
		// 客服发送消息，更新会话状态
		updateSessionStatus(uint(kfInfo.Id), uint(userInfo.ID), "active")
	}
	message := ws.Message{
		Sender:    fromIdStr,
		Receiver:  toIdStr,
		Content:   content,
		MsgType:   msgTypeStr,
		Role:      "kf",
		Timestamp: time.Now().Unix(),
	}
	var key string
	if isKf == 1 {
		//查找指定用户广播消息
		key = "user" + toIdStr
		message.AvatarUrl = kfInfo.Avatar
		message.Nickname = kfInfo.Nickname
	} else if isKf == 0 {
		//查找指定客服广播消息
		key = "kf" + toIdStr
		message.Role = "user"
		message.AvatarUrl = userInfo.Avatar
		message.Nickname = userInfo.Username
	}
	conn, ok := ws.Manager.Clients[key]
	if conn != nil && ok {
		sendMsg := ws.TypeMsg{
			Type: "message",
			Data: message,
		}
		str, _ := json.Marshal(sendMsg)
		conn.Send <- str

		if isKf == 0 {
			//客服给用户发送自动回复消息
			var autoReply sysModel.SysServiceReply
			autoContent := ""
			var autoMsgType int64
			aErr := global.GVA_DB.Where("is_complete = ? AND `status` = ? AND keyword = ?", 1, 1, content).First(&autoReply).Error
			fmt.Println(aErr)
			if aErr == nil {
				fmt.Println(autoReply)
				autoContent = autoReply.Content
				autoMsgType = autoReply.ReplyType
			} else {
				aErr = global.GVA_DB.Where("is_complete = ? AND `status` = ? AND keyword LIKE ?", 0, 1, "%"+content+"%").First(&autoReply).Error
				if aErr == nil {
					autoContent = autoReply.Content
					autoMsgType = autoReply.ReplyType
				}
			}
			if autoContent != "" {
				if autoMsgType == 2 {
					autoMsgType = 3 //图片
				}
				aErr = ser.CreateMsg(kfInfo, userInfo, autoMsgType, autoContent, "1")
				if aErr == nil {
					autoUidStr := strconv.FormatUint(uint64(userInfo.ID), 10)
					message.Sender = strconv.FormatInt(kfInfo.Id, 10)
					message.Receiver = autoUidStr
					message.MsgType = strconv.FormatInt(autoMsgType, 10)
					message.Content = autoContent
					message.IsKf = 1
					message.Role = "kf"
					message.AvatarUrl = kfInfo.Avatar
					message.Nickname = kfInfo.Nickname
					sendMsg.Data = message
					autoStr, _ := json.Marshal(sendMsg)
					kfConn, isOk := ws.Manager.Clients["user"+autoUidStr]
					if kfConn != nil && isOk {
						kfConn.Send <- autoStr
					}
				}
			}
		}
	}

	response.OkWithDetailed(nil, "发送成功", c)
}

func (cus *CustomerServiceApi) GetMsgList(c *gin.Context) {

	uid, ok := c.Get("jwt_user_id")          //jwt里解出的
	jwtServiceId, ok2 := c.Get("service_id") //jwt里解出的
	if !ok2 {
		//gva-shop前端用户连接请求消息列表
		jwtServiceId = c.Query("kf_id")
	}
	if !ok {
		//后台客服连接请求消息列表
		uid = c.Query("uid")
	}
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	if pageSize > 20 {
		pageSize = 20
	}
	offset := pageSize * (page - 1)
	var total int64
	var list []sysModel.SysServiceMsg
	db := global.GVA_DB.Model(&sysModel.SysServiceMsg{}).Where("uid = ?", uid).Where("service_id = ?", jwtServiceId)
	db.Count(&total)
	err := db.Limit(pageSize).Offset(offset).Order("add_time desc").Find(&list).Error
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}
	if len(list) > 0 {
		sort.Slice(list, func(i, j int) bool {
			return list[i].AddTime < list[j].AddTime
		})
		for k, v := range list {
			decoded, _ := base64.StdEncoding.DecodeString(v.Content)
			v.Content = string(decoded)
			list[k] = v
		}
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     page,
		PageSize: pageSize,
	}, "获取成功", c)
}

func (cus *CustomerServiceApi) GetMsgUser(c *gin.Context) {
	kfId, ok2 := c.Get("service_id")
	if !ok2 {
		//gva-shop前端用户连接请求消息列表
		kfId = c.Query("kf_id")
	}
	var list []sysModel.SysServiceRecord
	err := global.GVA_DB.Where("service_id=?", kfId).Find(&list).Error
	if err != nil {
		response.FailWithMessage("获取失败", c)
		return
	}
	if len(list) > 0 {
		//判断用户在线状况
		for k, v := range list {
			userKey := "user" + strconv.FormatInt(v.Uid, 10)
			isClent, ok := ws.Manager.Clients[userKey]
			if ok && isClent != nil {
				v.Online = 1
			} else {
				v.Online = 0
			}
			decoded, _ := base64.StdEncoding.DecodeString(v.Message)
			v.Message = string(decoded)
			//查找未读消息数
			var noCount int64
			global.GVA_DB.Model(&sysModel.SysServiceMsg{}).
				Where("is_view=?", 0).
				Where("is_kf=?", 0).
				Where("service_id=?", kfId).
				Where("uid=?", v.Uid).Count(&noCount)
			v.NoRead = noCount
			v.AddTimeStr = tools.FormatTimestamp(v.UpdateTime)
			if v.MessageType == 3 {
				v.Message = "[图片]"
			}
			list[k] = v
		}
		sort.Slice(list, func(i, j int) bool {
			if list[i].Online != list[j].Online {
				return list[i].Online > list[j].Online
			}
			return list[i].AddTime > list[j].AddTime
		})
	}
	response.OkWithDetailed(list, "获取成功", c)
}

func (cus *CustomerServiceApi) SetMsgView(c *gin.Context) {
	kfId, _ := c.Get("service_id")
	uid := c.Query("uid")
	global.GVA_DB.Model(&sysModel.SysServiceMsg{}).
		Where(map[string]interface{}{"is_kf": 0, "service_id": kfId, "is_view": 0, "uid": uid}).
		Update("is_view", 1)
	response.Ok(c)
}

func (cus *CustomerServiceApi) UploadFile(c *gin.Context) {
	file, err := c.FormFile("file")
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	extension := filepath.Ext(file.Filename)
	newUUID := uuid.New().String()
	hash := md5.Sum([]byte("gva-service" + newUUID))
	md5Pwd := hex.EncodeToString(hash[:])
	filename := md5Pwd + extension
	if err := c.SaveUploadedFile(file, "./uploads/file/"+filename); err != nil {
		response.FailWithMessage("上传失败-2", c)
		return

	}
	//ser := service.ServiceGroupApp
	//url := ser.GetUrlHost(c)
	response.OkWithDetailed("uploads/file/"+filename, "获取成功", c)
	return
}

func (cus *CustomerServiceApi) GetTestMsgList(c *gin.Context) {
	uid := c.Query("uid")
	serviceId := c.Query("service_id")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	if pageSize > 20 {
		pageSize = 20
	}
	offset := pageSize * (page - 1)
	var total int64
	var list []sysModel.SysServiceMsg
	global.GVA_DB.Model(&sysModel.SysServiceMsg{}).Where("uid=?", uid).Where("service_id=?", serviceId).Count(&total)
	err := global.GVA_DB.Where("uid=?", uid).Where("service_id=?", serviceId).Limit(pageSize).Offset(offset).Order("add_time desc").Find(&list).Error
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}
	if len(list) > 0 {
		sort.Slice(list, func(i, j int) bool {
			return list[i].AddTime < list[j].AddTime
		})
		for k, v := range list {
			decoded, _ := base64.StdEncoding.DecodeString(v.Content)
			v.Content = string(decoded)
			list[k] = v
		}
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     page,
		PageSize: pageSize,
	}, "获取成功", c)
}

func (cus *CustomerServiceApi) GetUserInfo(c *gin.Context) {
	//userID := utils.GetUserID(c)
	userID, ok := c.Get("jwt_user_id")
	if !ok {
		//后台客服连接请求
		userID = c.Query("uid")
	}
	var clientUser jyhapp.JyhUser
	result := global.GVA_DB.Omit("password").Where("id = ?", userID).Where("deleted_at IS NULL").First(&clientUser)
	if result.Error != nil {
		response.FailWithMessage("获取用户信息失败", c)
		return
	}
	response.OkWithDetailed(clientUser, "获取成功", c)
}

func (cus *CustomerServiceApi) GetServiceScript(c *gin.Context) {
	rType := c.Query("type")
	db := global.GVA_DB.Model(&sysModel.SysServiceScript{})
	if rType == "1" {
		serviceId, ok := c.Get("service_id")
		if serviceId != "" && ok {
			db = db.Where("service_id=?", serviceId)
		}
	} else {
		db = db.Where("service_id=?", 0)
	}
	var list []sysModel.SysServiceScript
	err := db.Order("add_time desc").Limit(20).Offset(0).Find(&list).Error
	if err != nil {
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(list, "获取成功", c)
}

// ==================== 辅助函数 ====================

// getUserTags 获取用户标签
func getUserTags(userID uint) []uint {
	var tags []uint
	var user jyhapp.JyhUser
	err := global.GVA_DB.Where("id = ?", userID).First(&user).Error
	if err == nil {
		if user.ID > 0 {
			var relation []jyhapp.JyhUserTagRelation
			err = global.GVA_DB.Where("user_id = ?", userID).Find(&relation).Error
			if err == nil {
				for _, rel := range relation {
					tags = append(tags, rel.TagID)
				}
			}
		}

	}
	return tags
}

// updateSessionStatus 更新会话状态
func updateSessionStatus(serviceID uint, userID uint, status string) {
	// 更新客服记录表中的会话状态
	global.GVA_DB.Model(&sysModel.SysServiceRecord{}).
		Where("service_id = ? AND uid = ?", serviceID, userID).
		Update("status", status)
}

// notifyTransfer 通知转接
func notifyTransfer(transfer *sysModel.SysServiceTransfer) {
	// 通过 WebSocket 通知目标客服
	transferMsg := ws.TypeMsg{
		Type: "transfer",
		Data: map[string]interface{}{
			"transfer_id":  transfer.ID,
			"from_service": transfer.FromServiceID,
			"to_service":   transfer.ToServiceID,
			"user_id":      transfer.UserID,
			"reason":       transfer.Reason,
			"status":       "pending",
		},
	}

	// 发送给目标客服
	ws.Manager.Broadcast <- transferMsg
}
