// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
)

func newJyhMaterialClaimRecord(db *gorm.DB, opts ...gen.DOOption) jyhMaterialClaimRecord {
	_jyhMaterialClaimRecord := jyhMaterialClaimRecord{}

	_jyhMaterialClaimRecord.jyhMaterialClaimRecordDo.UseDB(db, opts...)
	_jyhMaterialClaimRecord.jyhMaterialClaimRecordDo.UseModel(&jyhapp.JyhMaterialClaimRecord{})

	tableName := _jyhMaterialClaimRecord.jyhMaterialClaimRecordDo.TableName()
	_jyhMaterialClaimRecord.ALL = field.NewAsterisk(tableName)
	_jyhMaterialClaimRecord.ID = field.NewUint(tableName, "id")
	_jyhMaterialClaimRecord.UserID = field.NewUint(tableName, "user_id")
	_jyhMaterialClaimRecord.MaterialID = field.NewUint(tableName, "material_id")
	_jyhMaterialClaimRecord.ClaimType = field.NewString(tableName, "claim_type")
	_jyhMaterialClaimRecord.Quantity = field.NewInt(tableName, "quantity")
	_jyhMaterialClaimRecord.ClaimedAt = field.NewTime(tableName, "claimed_at")
	_jyhMaterialClaimRecord.IsUploaded = field.NewUint(tableName, "is_uploaded")
	_jyhMaterialClaimRecord.User = jyhMaterialClaimRecordBelongsToUser{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("User", "jyhapp.JyhUser"),
		Inviter: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("User.Inviter", "jyhapp.JyhUser"),
		},
		JyhUserExt: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("User.JyhUserExt", "jyhapp.JyhUserExt"),
		},
		Invitees: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("User.Invitees", "jyhapp.JyhUser"),
		},
		Tags: struct {
			field.RelationField
			Users struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("User.Tags", "jyhapp.JyhUserTag"),
			Users: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("User.Tags.Users", "jyhapp.JyhUser"),
			},
		},
	}

	_jyhMaterialClaimRecord.Material = jyhMaterialClaimRecordBelongsToMaterial{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Material", "jyhapp.JyhMaterial"),
		Category: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Material.Category", "jyhapp.JyhMaterialCategory"),
		},
		Files: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Material.Files", "jyhapp.JyhMaterialFile"),
		},
	}

	_jyhMaterialClaimRecord.fillFieldMap()

	return _jyhMaterialClaimRecord
}

type jyhMaterialClaimRecord struct {
	jyhMaterialClaimRecordDo

	ALL        field.Asterisk
	ID         field.Uint
	UserID     field.Uint
	MaterialID field.Uint
	ClaimType  field.String
	Quantity   field.Int
	ClaimedAt  field.Time
	IsUploaded field.Uint
	User       jyhMaterialClaimRecordBelongsToUser

	Material jyhMaterialClaimRecordBelongsToMaterial

	fieldMap map[string]field.Expr
}

func (j jyhMaterialClaimRecord) Table(newTableName string) *jyhMaterialClaimRecord {
	j.jyhMaterialClaimRecordDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j jyhMaterialClaimRecord) As(alias string) *jyhMaterialClaimRecord {
	j.jyhMaterialClaimRecordDo.DO = *(j.jyhMaterialClaimRecordDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *jyhMaterialClaimRecord) updateTableName(table string) *jyhMaterialClaimRecord {
	j.ALL = field.NewAsterisk(table)
	j.ID = field.NewUint(table, "id")
	j.UserID = field.NewUint(table, "user_id")
	j.MaterialID = field.NewUint(table, "material_id")
	j.ClaimType = field.NewString(table, "claim_type")
	j.Quantity = field.NewInt(table, "quantity")
	j.ClaimedAt = field.NewTime(table, "claimed_at")
	j.IsUploaded = field.NewUint(table, "is_uploaded")

	j.fillFieldMap()

	return j
}

func (j *jyhMaterialClaimRecord) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *jyhMaterialClaimRecord) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 9)
	j.fieldMap["id"] = j.ID
	j.fieldMap["user_id"] = j.UserID
	j.fieldMap["material_id"] = j.MaterialID
	j.fieldMap["claim_type"] = j.ClaimType
	j.fieldMap["quantity"] = j.Quantity
	j.fieldMap["claimed_at"] = j.ClaimedAt
	j.fieldMap["is_uploaded"] = j.IsUploaded

}

func (j jyhMaterialClaimRecord) clone(db *gorm.DB) jyhMaterialClaimRecord {
	j.jyhMaterialClaimRecordDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j jyhMaterialClaimRecord) replaceDB(db *gorm.DB) jyhMaterialClaimRecord {
	j.jyhMaterialClaimRecordDo.ReplaceDB(db)
	return j
}

type jyhMaterialClaimRecordBelongsToUser struct {
	db *gorm.DB

	field.RelationField

	Inviter struct {
		field.RelationField
	}
	JyhUserExt struct {
		field.RelationField
	}
	Invitees struct {
		field.RelationField
	}
	Tags struct {
		field.RelationField
		Users struct {
			field.RelationField
		}
	}
}

func (a jyhMaterialClaimRecordBelongsToUser) Where(conds ...field.Expr) *jyhMaterialClaimRecordBelongsToUser {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a jyhMaterialClaimRecordBelongsToUser) WithContext(ctx context.Context) *jyhMaterialClaimRecordBelongsToUser {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a jyhMaterialClaimRecordBelongsToUser) Session(session *gorm.Session) *jyhMaterialClaimRecordBelongsToUser {
	a.db = a.db.Session(session)
	return &a
}

func (a jyhMaterialClaimRecordBelongsToUser) Model(m *jyhapp.JyhMaterialClaimRecord) *jyhMaterialClaimRecordBelongsToUserTx {
	return &jyhMaterialClaimRecordBelongsToUserTx{a.db.Model(m).Association(a.Name())}
}

type jyhMaterialClaimRecordBelongsToUserTx struct{ tx *gorm.Association }

func (a jyhMaterialClaimRecordBelongsToUserTx) Find() (result *jyhapp.JyhUser, err error) {
	return result, a.tx.Find(&result)
}

func (a jyhMaterialClaimRecordBelongsToUserTx) Append(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a jyhMaterialClaimRecordBelongsToUserTx) Replace(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a jyhMaterialClaimRecordBelongsToUserTx) Delete(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a jyhMaterialClaimRecordBelongsToUserTx) Clear() error {
	return a.tx.Clear()
}

func (a jyhMaterialClaimRecordBelongsToUserTx) Count() int64 {
	return a.tx.Count()
}

type jyhMaterialClaimRecordBelongsToMaterial struct {
	db *gorm.DB

	field.RelationField

	Category struct {
		field.RelationField
	}
	Files struct {
		field.RelationField
	}
}

func (a jyhMaterialClaimRecordBelongsToMaterial) Where(conds ...field.Expr) *jyhMaterialClaimRecordBelongsToMaterial {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a jyhMaterialClaimRecordBelongsToMaterial) WithContext(ctx context.Context) *jyhMaterialClaimRecordBelongsToMaterial {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a jyhMaterialClaimRecordBelongsToMaterial) Session(session *gorm.Session) *jyhMaterialClaimRecordBelongsToMaterial {
	a.db = a.db.Session(session)
	return &a
}

func (a jyhMaterialClaimRecordBelongsToMaterial) Model(m *jyhapp.JyhMaterialClaimRecord) *jyhMaterialClaimRecordBelongsToMaterialTx {
	return &jyhMaterialClaimRecordBelongsToMaterialTx{a.db.Model(m).Association(a.Name())}
}

type jyhMaterialClaimRecordBelongsToMaterialTx struct{ tx *gorm.Association }

func (a jyhMaterialClaimRecordBelongsToMaterialTx) Find() (result *jyhapp.JyhMaterial, err error) {
	return result, a.tx.Find(&result)
}

func (a jyhMaterialClaimRecordBelongsToMaterialTx) Append(values ...*jyhapp.JyhMaterial) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a jyhMaterialClaimRecordBelongsToMaterialTx) Replace(values ...*jyhapp.JyhMaterial) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a jyhMaterialClaimRecordBelongsToMaterialTx) Delete(values ...*jyhapp.JyhMaterial) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a jyhMaterialClaimRecordBelongsToMaterialTx) Clear() error {
	return a.tx.Clear()
}

func (a jyhMaterialClaimRecordBelongsToMaterialTx) Count() int64 {
	return a.tx.Count()
}

type jyhMaterialClaimRecordDo struct{ gen.DO }

func (j jyhMaterialClaimRecordDo) Debug() *jyhMaterialClaimRecordDo {
	return j.withDO(j.DO.Debug())
}

func (j jyhMaterialClaimRecordDo) WithContext(ctx context.Context) *jyhMaterialClaimRecordDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jyhMaterialClaimRecordDo) ReadDB() *jyhMaterialClaimRecordDo {
	return j.Clauses(dbresolver.Read)
}

func (j jyhMaterialClaimRecordDo) WriteDB() *jyhMaterialClaimRecordDo {
	return j.Clauses(dbresolver.Write)
}

func (j jyhMaterialClaimRecordDo) Session(config *gorm.Session) *jyhMaterialClaimRecordDo {
	return j.withDO(j.DO.Session(config))
}

func (j jyhMaterialClaimRecordDo) Clauses(conds ...clause.Expression) *jyhMaterialClaimRecordDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jyhMaterialClaimRecordDo) Returning(value interface{}, columns ...string) *jyhMaterialClaimRecordDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jyhMaterialClaimRecordDo) Not(conds ...gen.Condition) *jyhMaterialClaimRecordDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jyhMaterialClaimRecordDo) Or(conds ...gen.Condition) *jyhMaterialClaimRecordDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jyhMaterialClaimRecordDo) Select(conds ...field.Expr) *jyhMaterialClaimRecordDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jyhMaterialClaimRecordDo) Where(conds ...gen.Condition) *jyhMaterialClaimRecordDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jyhMaterialClaimRecordDo) Order(conds ...field.Expr) *jyhMaterialClaimRecordDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jyhMaterialClaimRecordDo) Distinct(cols ...field.Expr) *jyhMaterialClaimRecordDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jyhMaterialClaimRecordDo) Omit(cols ...field.Expr) *jyhMaterialClaimRecordDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jyhMaterialClaimRecordDo) Join(table schema.Tabler, on ...field.Expr) *jyhMaterialClaimRecordDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jyhMaterialClaimRecordDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jyhMaterialClaimRecordDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jyhMaterialClaimRecordDo) RightJoin(table schema.Tabler, on ...field.Expr) *jyhMaterialClaimRecordDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jyhMaterialClaimRecordDo) Group(cols ...field.Expr) *jyhMaterialClaimRecordDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jyhMaterialClaimRecordDo) Having(conds ...gen.Condition) *jyhMaterialClaimRecordDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jyhMaterialClaimRecordDo) Limit(limit int) *jyhMaterialClaimRecordDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jyhMaterialClaimRecordDo) Offset(offset int) *jyhMaterialClaimRecordDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jyhMaterialClaimRecordDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jyhMaterialClaimRecordDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jyhMaterialClaimRecordDo) Unscoped() *jyhMaterialClaimRecordDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jyhMaterialClaimRecordDo) Create(values ...*jyhapp.JyhMaterialClaimRecord) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jyhMaterialClaimRecordDo) CreateInBatches(values []*jyhapp.JyhMaterialClaimRecord, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jyhMaterialClaimRecordDo) Save(values ...*jyhapp.JyhMaterialClaimRecord) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jyhMaterialClaimRecordDo) First() (*jyhapp.JyhMaterialClaimRecord, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterialClaimRecord), nil
	}
}

func (j jyhMaterialClaimRecordDo) Take() (*jyhapp.JyhMaterialClaimRecord, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterialClaimRecord), nil
	}
}

func (j jyhMaterialClaimRecordDo) Last() (*jyhapp.JyhMaterialClaimRecord, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterialClaimRecord), nil
	}
}

func (j jyhMaterialClaimRecordDo) Find() ([]*jyhapp.JyhMaterialClaimRecord, error) {
	result, err := j.DO.Find()
	return result.([]*jyhapp.JyhMaterialClaimRecord), err
}

func (j jyhMaterialClaimRecordDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*jyhapp.JyhMaterialClaimRecord, err error) {
	buf := make([]*jyhapp.JyhMaterialClaimRecord, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jyhMaterialClaimRecordDo) FindInBatches(result *[]*jyhapp.JyhMaterialClaimRecord, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jyhMaterialClaimRecordDo) Attrs(attrs ...field.AssignExpr) *jyhMaterialClaimRecordDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jyhMaterialClaimRecordDo) Assign(attrs ...field.AssignExpr) *jyhMaterialClaimRecordDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jyhMaterialClaimRecordDo) Joins(fields ...field.RelationField) *jyhMaterialClaimRecordDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jyhMaterialClaimRecordDo) Preload(fields ...field.RelationField) *jyhMaterialClaimRecordDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jyhMaterialClaimRecordDo) FirstOrInit() (*jyhapp.JyhMaterialClaimRecord, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterialClaimRecord), nil
	}
}

func (j jyhMaterialClaimRecordDo) FirstOrCreate() (*jyhapp.JyhMaterialClaimRecord, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhMaterialClaimRecord), nil
	}
}

func (j jyhMaterialClaimRecordDo) FindByPage(offset int, limit int) (result []*jyhapp.JyhMaterialClaimRecord, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jyhMaterialClaimRecordDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jyhMaterialClaimRecordDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jyhMaterialClaimRecordDo) Delete(models ...*jyhapp.JyhMaterialClaimRecord) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jyhMaterialClaimRecordDo) withDO(do gen.Dao) *jyhMaterialClaimRecordDo {
	j.DO = *do.(*gen.DO)
	return j
}
