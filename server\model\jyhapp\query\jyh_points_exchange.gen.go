// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
)

func newJyhPointsExchange(db *gorm.DB, opts ...gen.DOOption) jyhPointsExchange {
	_jyhPointsExchange := jyhPointsExchange{}

	_jyhPointsExchange.jyhPointsExchangeDo.UseDB(db, opts...)
	_jyhPointsExchange.jyhPointsExchangeDo.UseModel(&jyhapp.JyhPointsExchange{})

	tableName := _jyhPointsExchange.jyhPointsExchangeDo.TableName()
	_jyhPointsExchange.ALL = field.NewAsterisk(tableName)
	_jyhPointsExchange.ID = field.NewUint(tableName, "id")
	_jyhPointsExchange.UserID = field.NewUint(tableName, "user_id")
	_jyhPointsExchange.MaterialID = field.NewUint(tableName, "material_id")
	_jyhPointsExchange.Quantity = field.NewInt(tableName, "quantity")
	_jyhPointsExchange.PointsTotal = field.NewInt(tableName, "points_total")
	_jyhPointsExchange.ExchangeCode = field.NewString(tableName, "exchange_code")
	_jyhPointsExchange.Status = field.NewString(tableName, "status")
	_jyhPointsExchange.ExchangeTime = field.NewTime(tableName, "exchange_time")
	_jyhPointsExchange.CompletedAt = field.NewTime(tableName, "completed_at")
	_jyhPointsExchange.Address = field.NewField(tableName, "address")
	_jyhPointsExchange.Remark = field.NewString(tableName, "remark")
	_jyhPointsExchange.CreatedAt = field.NewTime(tableName, "created_at")
	_jyhPointsExchange.UpdatedAt = field.NewTime(tableName, "updated_at")
	_jyhPointsExchange.User = jyhPointsExchangeBelongsToUser{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("User", "jyhapp.JyhUser"),
		Inviter: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("User.Inviter", "jyhapp.JyhUser"),
		},
		JyhUserExt: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("User.JyhUserExt", "jyhapp.JyhUserExt"),
		},
		Invitees: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("User.Invitees", "jyhapp.JyhUser"),
		},
		Tags: struct {
			field.RelationField
			Users struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("User.Tags", "jyhapp.JyhUserTag"),
			Users: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("User.Tags.Users", "jyhapp.JyhUser"),
			},
		},
	}

	_jyhPointsExchange.Material = jyhPointsExchangeBelongsToMaterial{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Material", "jyhapp.JyhMaterial"),
		Category: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Material.Category", "jyhapp.JyhMaterialCategory"),
		},
		Files: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Material.Files", "jyhapp.JyhMaterialFile"),
		},
	}

	_jyhPointsExchange.fillFieldMap()

	return _jyhPointsExchange
}

type jyhPointsExchange struct {
	jyhPointsExchangeDo

	ALL          field.Asterisk
	ID           field.Uint
	UserID       field.Uint
	MaterialID   field.Uint
	Quantity     field.Int
	PointsTotal  field.Int
	ExchangeCode field.String
	Status       field.String
	ExchangeTime field.Time
	CompletedAt  field.Time
	Address      field.Field
	Remark       field.String
	CreatedAt    field.Time
	UpdatedAt    field.Time
	User         jyhPointsExchangeBelongsToUser

	Material jyhPointsExchangeBelongsToMaterial

	fieldMap map[string]field.Expr
}

func (j jyhPointsExchange) Table(newTableName string) *jyhPointsExchange {
	j.jyhPointsExchangeDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j jyhPointsExchange) As(alias string) *jyhPointsExchange {
	j.jyhPointsExchangeDo.DO = *(j.jyhPointsExchangeDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *jyhPointsExchange) updateTableName(table string) *jyhPointsExchange {
	j.ALL = field.NewAsterisk(table)
	j.ID = field.NewUint(table, "id")
	j.UserID = field.NewUint(table, "user_id")
	j.MaterialID = field.NewUint(table, "material_id")
	j.Quantity = field.NewInt(table, "quantity")
	j.PointsTotal = field.NewInt(table, "points_total")
	j.ExchangeCode = field.NewString(table, "exchange_code")
	j.Status = field.NewString(table, "status")
	j.ExchangeTime = field.NewTime(table, "exchange_time")
	j.CompletedAt = field.NewTime(table, "completed_at")
	j.Address = field.NewField(table, "address")
	j.Remark = field.NewString(table, "remark")
	j.CreatedAt = field.NewTime(table, "created_at")
	j.UpdatedAt = field.NewTime(table, "updated_at")

	j.fillFieldMap()

	return j
}

func (j *jyhPointsExchange) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *jyhPointsExchange) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 15)
	j.fieldMap["id"] = j.ID
	j.fieldMap["user_id"] = j.UserID
	j.fieldMap["material_id"] = j.MaterialID
	j.fieldMap["quantity"] = j.Quantity
	j.fieldMap["points_total"] = j.PointsTotal
	j.fieldMap["exchange_code"] = j.ExchangeCode
	j.fieldMap["status"] = j.Status
	j.fieldMap["exchange_time"] = j.ExchangeTime
	j.fieldMap["completed_at"] = j.CompletedAt
	j.fieldMap["address"] = j.Address
	j.fieldMap["remark"] = j.Remark
	j.fieldMap["created_at"] = j.CreatedAt
	j.fieldMap["updated_at"] = j.UpdatedAt

}

func (j jyhPointsExchange) clone(db *gorm.DB) jyhPointsExchange {
	j.jyhPointsExchangeDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j jyhPointsExchange) replaceDB(db *gorm.DB) jyhPointsExchange {
	j.jyhPointsExchangeDo.ReplaceDB(db)
	return j
}

type jyhPointsExchangeBelongsToUser struct {
	db *gorm.DB

	field.RelationField

	Inviter struct {
		field.RelationField
	}
	JyhUserExt struct {
		field.RelationField
	}
	Invitees struct {
		field.RelationField
	}
	Tags struct {
		field.RelationField
		Users struct {
			field.RelationField
		}
	}
}

func (a jyhPointsExchangeBelongsToUser) Where(conds ...field.Expr) *jyhPointsExchangeBelongsToUser {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a jyhPointsExchangeBelongsToUser) WithContext(ctx context.Context) *jyhPointsExchangeBelongsToUser {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a jyhPointsExchangeBelongsToUser) Session(session *gorm.Session) *jyhPointsExchangeBelongsToUser {
	a.db = a.db.Session(session)
	return &a
}

func (a jyhPointsExchangeBelongsToUser) Model(m *jyhapp.JyhPointsExchange) *jyhPointsExchangeBelongsToUserTx {
	return &jyhPointsExchangeBelongsToUserTx{a.db.Model(m).Association(a.Name())}
}

type jyhPointsExchangeBelongsToUserTx struct{ tx *gorm.Association }

func (a jyhPointsExchangeBelongsToUserTx) Find() (result *jyhapp.JyhUser, err error) {
	return result, a.tx.Find(&result)
}

func (a jyhPointsExchangeBelongsToUserTx) Append(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a jyhPointsExchangeBelongsToUserTx) Replace(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a jyhPointsExchangeBelongsToUserTx) Delete(values ...*jyhapp.JyhUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a jyhPointsExchangeBelongsToUserTx) Clear() error {
	return a.tx.Clear()
}

func (a jyhPointsExchangeBelongsToUserTx) Count() int64 {
	return a.tx.Count()
}

type jyhPointsExchangeBelongsToMaterial struct {
	db *gorm.DB

	field.RelationField

	Category struct {
		field.RelationField
	}
	Files struct {
		field.RelationField
	}
}

func (a jyhPointsExchangeBelongsToMaterial) Where(conds ...field.Expr) *jyhPointsExchangeBelongsToMaterial {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a jyhPointsExchangeBelongsToMaterial) WithContext(ctx context.Context) *jyhPointsExchangeBelongsToMaterial {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a jyhPointsExchangeBelongsToMaterial) Session(session *gorm.Session) *jyhPointsExchangeBelongsToMaterial {
	a.db = a.db.Session(session)
	return &a
}

func (a jyhPointsExchangeBelongsToMaterial) Model(m *jyhapp.JyhPointsExchange) *jyhPointsExchangeBelongsToMaterialTx {
	return &jyhPointsExchangeBelongsToMaterialTx{a.db.Model(m).Association(a.Name())}
}

type jyhPointsExchangeBelongsToMaterialTx struct{ tx *gorm.Association }

func (a jyhPointsExchangeBelongsToMaterialTx) Find() (result *jyhapp.JyhMaterial, err error) {
	return result, a.tx.Find(&result)
}

func (a jyhPointsExchangeBelongsToMaterialTx) Append(values ...*jyhapp.JyhMaterial) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a jyhPointsExchangeBelongsToMaterialTx) Replace(values ...*jyhapp.JyhMaterial) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a jyhPointsExchangeBelongsToMaterialTx) Delete(values ...*jyhapp.JyhMaterial) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a jyhPointsExchangeBelongsToMaterialTx) Clear() error {
	return a.tx.Clear()
}

func (a jyhPointsExchangeBelongsToMaterialTx) Count() int64 {
	return a.tx.Count()
}

type jyhPointsExchangeDo struct{ gen.DO }

func (j jyhPointsExchangeDo) Debug() *jyhPointsExchangeDo {
	return j.withDO(j.DO.Debug())
}

func (j jyhPointsExchangeDo) WithContext(ctx context.Context) *jyhPointsExchangeDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jyhPointsExchangeDo) ReadDB() *jyhPointsExchangeDo {
	return j.Clauses(dbresolver.Read)
}

func (j jyhPointsExchangeDo) WriteDB() *jyhPointsExchangeDo {
	return j.Clauses(dbresolver.Write)
}

func (j jyhPointsExchangeDo) Session(config *gorm.Session) *jyhPointsExchangeDo {
	return j.withDO(j.DO.Session(config))
}

func (j jyhPointsExchangeDo) Clauses(conds ...clause.Expression) *jyhPointsExchangeDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jyhPointsExchangeDo) Returning(value interface{}, columns ...string) *jyhPointsExchangeDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jyhPointsExchangeDo) Not(conds ...gen.Condition) *jyhPointsExchangeDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jyhPointsExchangeDo) Or(conds ...gen.Condition) *jyhPointsExchangeDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jyhPointsExchangeDo) Select(conds ...field.Expr) *jyhPointsExchangeDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jyhPointsExchangeDo) Where(conds ...gen.Condition) *jyhPointsExchangeDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jyhPointsExchangeDo) Order(conds ...field.Expr) *jyhPointsExchangeDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jyhPointsExchangeDo) Distinct(cols ...field.Expr) *jyhPointsExchangeDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jyhPointsExchangeDo) Omit(cols ...field.Expr) *jyhPointsExchangeDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jyhPointsExchangeDo) Join(table schema.Tabler, on ...field.Expr) *jyhPointsExchangeDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jyhPointsExchangeDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jyhPointsExchangeDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jyhPointsExchangeDo) RightJoin(table schema.Tabler, on ...field.Expr) *jyhPointsExchangeDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jyhPointsExchangeDo) Group(cols ...field.Expr) *jyhPointsExchangeDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jyhPointsExchangeDo) Having(conds ...gen.Condition) *jyhPointsExchangeDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jyhPointsExchangeDo) Limit(limit int) *jyhPointsExchangeDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jyhPointsExchangeDo) Offset(offset int) *jyhPointsExchangeDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jyhPointsExchangeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jyhPointsExchangeDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jyhPointsExchangeDo) Unscoped() *jyhPointsExchangeDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jyhPointsExchangeDo) Create(values ...*jyhapp.JyhPointsExchange) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jyhPointsExchangeDo) CreateInBatches(values []*jyhapp.JyhPointsExchange, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jyhPointsExchangeDo) Save(values ...*jyhapp.JyhPointsExchange) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jyhPointsExchangeDo) First() (*jyhapp.JyhPointsExchange, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhPointsExchange), nil
	}
}

func (j jyhPointsExchangeDo) Take() (*jyhapp.JyhPointsExchange, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhPointsExchange), nil
	}
}

func (j jyhPointsExchangeDo) Last() (*jyhapp.JyhPointsExchange, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhPointsExchange), nil
	}
}

func (j jyhPointsExchangeDo) Find() ([]*jyhapp.JyhPointsExchange, error) {
	result, err := j.DO.Find()
	return result.([]*jyhapp.JyhPointsExchange), err
}

func (j jyhPointsExchangeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*jyhapp.JyhPointsExchange, err error) {
	buf := make([]*jyhapp.JyhPointsExchange, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jyhPointsExchangeDo) FindInBatches(result *[]*jyhapp.JyhPointsExchange, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jyhPointsExchangeDo) Attrs(attrs ...field.AssignExpr) *jyhPointsExchangeDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jyhPointsExchangeDo) Assign(attrs ...field.AssignExpr) *jyhPointsExchangeDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jyhPointsExchangeDo) Joins(fields ...field.RelationField) *jyhPointsExchangeDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jyhPointsExchangeDo) Preload(fields ...field.RelationField) *jyhPointsExchangeDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jyhPointsExchangeDo) FirstOrInit() (*jyhapp.JyhPointsExchange, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhPointsExchange), nil
	}
}

func (j jyhPointsExchangeDo) FirstOrCreate() (*jyhapp.JyhPointsExchange, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhPointsExchange), nil
	}
}

func (j jyhPointsExchangeDo) FindByPage(offset int, limit int) (result []*jyhapp.JyhPointsExchange, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jyhPointsExchangeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jyhPointsExchangeDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jyhPointsExchangeDo) Delete(models ...*jyhapp.JyhPointsExchange) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jyhPointsExchangeDo) withDO(do gen.Dao) *jyhPointsExchangeDo {
	j.DO = *do.(*gen.DO)
	return j
}
