package jyhapp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// JyhUserTag 用户标签表
type JyhUserTag struct {
	global.GVA_MODEL
	Name        string    `json:"name" gorm:"column:name;comment:标签名称"`
	Description string    `json:"description" gorm:"column:description;comment:标签描述"`
	Color       string    `json:"color" gorm:"column:color;comment:标签颜色;default:#409EFF"`
	Status      uint      `json:"status" gorm:"column:status;comment:状态(1:启用 0:禁用);default:1"`
	Sort        uint      `json:"sort" gorm:"column:sort;comment:排序;default:0"`
	Users       []JyhUser `json:"users" gorm:"many2many:jyh_user_tag_relation;"`
}

// JyhUserTagRelation 用户标签关系表
type JyhUserTagRelation struct {
	ID     uint `gorm:"primarykey" json:"ID"` // 主键ID
	UserID uint `json:"userId" gorm:"column:user_id;comment:用户ID;not null"`
	TagID  uint `json:"tagId" gorm:"column:tag_id;comment:标签ID;not null"`
}

// TableName 设置表名
func (JyhUserTag) TableName() string {
	return "jyh_user_tag"
}

// TableName 设置表名
func (JyhUserTagRelation) TableName() string {
	return "jyh_user_tag_relation"
}
