package response

import (
	"github.com/shopspring/decimal"
	"gorm.io/datatypes"
)

// ===== 权益类型相关 =====

// BenefitListItem 权益类型列表项
type BenefitListItem struct {
	ID          uint   `json:"id"`          // 权益ID
	Key         string `json:"key"`         // 唯一英文编码
	Name        string `json:"name"`        // 展示名
	Description string `json:"description"` // 运营备注
	CreatedAt   string `json:"created_at"`  // 创建时间
	UpdatedAt   string `json:"updated_at"`  // 更新时间
}

// BenefitDetailResp 权益类型详情响应
type BenefitDetailResp struct {
	ID          uint   `json:"id"`          // 权益ID
	Key         string `json:"key"`         // 唯一英文编码
	Name        string `json:"name"`        // 展示名
	Description string `json:"description"` // 运营备注
	CreatedAt   string `json:"created_at"`  // 创建时间
	UpdatedAt   string `json:"updated_at"`  // 更新时间
}

// ===== 会员等级相关 =====

// UserShipLevelListItem 会员等级列表项
type UserShipLevelListItem struct {
	ID                     uint            `json:"id"`                       // 等级ID
	Name                   string          `json:"name"`                     // 会员等级名称
	Code                   string          `json:"code"`                     // 内部唯一编码
	Icon                   string          `json:"icon"`                     // 图标
	PriceCents             uint64          `json:"price_cents"`              // 官方售价(单位分)
	DurationDays           uint            `json:"duration_days"`            // 有效期(天)
	Sort                   int             `json:"sort"`                     // 前端展示排序
	Description            string          `json:"description"`              // 等级描述
	CreatedAt              string          `json:"created_at"`               // 创建时间
	UpdatedAt              string          `json:"updated_at"`               // 更新时间
	Hide                   bool            `json:"hide"`                     // 是否隐藏（仅管理员可见）
	CommissionRate         decimal.Decimal `json:"commission_rate"`          // 佣金比例（0-1之间的小数，0表示不支持佣金）
	PlatformCommissionRate decimal.Decimal `json:"platform_commission_rate"` // 平台佣金比例（0-1之间的小数，0表示不支持平台佣金）
}

// UserShipLevelDetailResp 会员等级详情响应
type UserShipLevelDetailResp struct {
	ID                     uint                     `json:"id"`                       // 等级ID
	Name                   string                   `json:"name"`                     // 会员等级名称
	Code                   string                   `json:"code"`                     // 内部唯一编码
	Icon                   string                   `json:"icon"`                     // 图标
	PriceCents             uint64                   `json:"price_cents"`              // 官方售价(单位分)
	DurationDays           uint                     `json:"duration_days"`            // 有效期(天)
	Sort                   int                      `json:"sort"`                     // 前端展示排序
	Description            string                   `json:"description"`              // 等级描述
	Benefits               []LevelBenefitDetailItem `json:"benefits"`                 // 关联的权益列表
	CreatedAt              string                   `json:"created_at"`               // 创建时间
	UpdatedAt              string                   `json:"updated_at"`               // 更新时间
	Hide                   bool                     `json:"hide"`                     // 是否隐藏（仅管理员可见）
	CommissionRate         decimal.Decimal          `json:"commission_rate"`          // 佣金比例（0-1之间的小数，0表示不支持佣金）
	PlatformCommissionRate decimal.Decimal          `json:"platform_commission_rate"` // 平台佣金比例（0-1之间的小数，0表示不支持平台佣金）
}

// ===== 等级-权益映射相关 =====

// LevelBenefitListItem 等级权益映射列表项
type LevelBenefitListItem struct {
	ID          uint            `json:"id"`           // 映射ID
	LevelID     uint            `json:"level_id"`     // 所属会员等级ID
	BenefitID   uint            `json:"benefit_id"`   // 权益类型ID
	Value       decimal.Decimal `json:"value"`        // 权益值
	Condition   datatypes.JSON  `json:"condition"`    // 附加条件
	LevelName   string          `json:"level_name"`   // 会员等级名称
	BenefitKey  string          `json:"benefit_key"`  // 权益唯一键
	BenefitName string          `json:"benefit_name"` // 权益名称
	CreatedAt   string          `json:"created_at"`   // 创建时间
	UpdatedAt   string          `json:"updated_at"`   // 更新时间
}

// LevelBenefitDetailItem 等级权益映射详情项
type LevelBenefitDetailItem struct {
	ID          uint            `json:"id"`           // 映射ID
	LevelID     uint            `json:"level_id"`     // 所属会员等级ID
	BenefitID   uint            `json:"benefit_id"`   // 权益类型ID
	Value       decimal.Decimal `json:"value"`        // 权益值
	Condition   datatypes.JSON  `json:"condition"`    // 附加条件
	BenefitKey  string          `json:"benefit_key"`  // 权益唯一键
	BenefitName string          `json:"benefit_name"` // 权益名称
	CreatedAt   string          `json:"created_at"`   // 创建时间
	UpdatedAt   string          `json:"updated_at"`   // 更新时间
}

// ===== 用户等级记录相关 =====

// UserLevelListItem 用户等级记录列表项
type UserLevelListItem struct {
	ID            uint   `json:"id"`              // 记录ID
	UserID        uint   `json:"user_id"`         // 用户ID
	LevelID       uint   `json:"level_id"`        // 会员等级ID
	OrderID       *uint  `json:"order_id"`        // 对应的订单ID
	StartAt       string `json:"start_at"`        // 生效时间
	EndAt         string `json:"end_at"`          // 过期时间
	Status        string `json:"status"`          // 状态
	Username      string `json:"username"`        // 用户名
	Phone         string `json:"phone"`           // 手机号（脱敏）
	LevelName     string `json:"level_name"`      // 等级名称
	LevelCode     string `json:"level_code"`      // 等级编码
	PriceCents    uint64 `json:"price_cents"`     // 等级价格（分）
	DurationDays  uint   `json:"duration_days"`   // 有效期天数
	ApplyTime     string `json:"apply_time"`      // 申请时间
	WaitingDays   int    `json:"waiting_days"`    // 等待审核天数（仅待审核状态时有值）
	UserCreatedAt string `json:"user_created_at"` // 用户注册时间
	UserType      string `json:"user_type"`       // 用户类型
	UserStatus    uint   `json:"user_status"`     // 用户状态
	CreatedAt     string `json:"created_at"`      // 创建时间
	UpdatedAt     string `json:"updated_at"`      // 更新时间
}

// UserLevelDetailResp 用户等级记录详情响应
type UserLevelDetailResp struct {
	ID        uint   `json:"id"`         // 记录ID
	UserID    uint   `json:"user_id"`    // 用户ID
	LevelID   uint   `json:"level_id"`   // 会员等级ID
	OrderID   *uint  `json:"order_id"`   // 对应的订单ID
	StartAt   string `json:"start_at"`   // 生效时间
	EndAt     string `json:"end_at"`     // 过期时间
	Status    string `json:"status"`     // 状态
	Username  string `json:"username"`   // 用户名
	Phone     string `json:"phone"`      // 手机号
	LevelName string `json:"level_name"` // 等级名称
	CreatedAt string `json:"created_at"` // 创建时间
	UpdatedAt string `json:"updated_at"` // 更新时间
}

// ===== 用户权益快照相关 =====

// UserBenefitSnapshotListItem 用户权益快照列表项
type UserBenefitSnapshotListItem struct {
	ID          uint            `json:"id"`            // 快照ID
	UserID      uint            `json:"user_id"`       // 用户ID
	UserLevelID uint            `json:"user_level_id"` // 对应 jyh_user_level.id
	LevelID     uint            `json:"level_id"`      // 来源会员等级ID
	BenefitID   uint            `json:"benefit_id"`    // 权益类型ID
	Value       decimal.Decimal `json:"value"`         // 具体权益值
	Condition   datatypes.JSON  `json:"condition"`     // 触发条件
	StartAt     string          `json:"start_at"`      // 生效时间
	EndAt       string          `json:"end_at"`        // 失效时间
	BenefitKey  string          `json:"benefit_key"`   // 冗余: 权益key
	BenefitName string          `json:"benefit_name"`  // 冗余: 权益名称
	Username    string          `json:"username"`      // 用户名
	Phone       string          `json:"phone"`         // 手机号
	LevelName   string          `json:"level_name"`    // 等级名称
	CreatedAt   string          `json:"created_at"`    // 创建时间
	UpdatedAt   string          `json:"updated_at"`    // 更新时间
}

// UserBenefitSnapshotDetailResp 用户权益快照详情响应
type UserBenefitSnapshotDetailResp struct {
	ID          uint            `json:"id"`            // 快照ID
	UserID      uint            `json:"user_id"`       // 用户ID
	UserLevelID uint            `json:"user_level_id"` // 对应 jyh_user_level.id
	LevelID     uint            `json:"level_id"`      // 来源会员等级ID
	BenefitID   uint            `json:"benefit_id"`    // 权益类型ID
	Value       decimal.Decimal `json:"value"`         // 具体权益值
	Condition   datatypes.JSON  `json:"condition"`     // 触发条件
	StartAt     string          `json:"start_at"`      // 生效时间
	EndAt       string          `json:"end_at"`        // 失效时间
	BenefitKey  string          `json:"benefit_key"`   // 冗余: 权益key
	BenefitName string          `json:"benefit_name"`  // 冗余: 权益名称
	Username    string          `json:"username"`      // 用户名
	Phone       string          `json:"phone"`         // 手机号
	LevelName   string          `json:"level_name"`    // 等级名称
	CreatedAt   string          `json:"created_at"`    // 创建时间
	UpdatedAt   string          `json:"updated_at"`    // 更新时间
}

// UserBenefitSummaryResp 用户权益汇总响应
type UserBenefitSummaryResp struct {
	UserID      uint                          `json:"user_id"`      // 用户ID
	Username    string                        `json:"username"`     // 用户名
	Phone       string                        `json:"phone"`        // 手机号
	Benefits    []UserBenefitSnapshotListItem `json:"benefits"`     // 当前权益列表
	ActiveLevel *UserLevelListItem            `json:"active_level"` // 当前生效的等级
}
