package jyhapp

import (
	"errors"
	"fmt"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/errno"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/query"
	jyhReq "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/request"
	jyhRes "github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/response"
	"go.uber.org/zap"
	"gorm.io/gen"
	"gorm.io/gorm"
	"strings"
	"time"
)

type MaterialService struct{}

// Create 创建素材
func (s *MaterialService) Create(req *jyhReq.MaterialCreate) (err error) {
	if req.Name == "" {
		return errno.BadRequest.WithMsg("素材名称不能为空")
	}
	if len(req.TagIds) == 0 {
		return errno.QueryFailed.WithMsg("至少需要选择一个标签")
	}
	if len(req.Files) == 0 {
		return errno.QueryFailed.WithMsg("至少需要上传一个素材文件")
	}
	err = query.Use(global.GVA_DB).Transaction(func(tx *query.Query) (err error) {
		// 检查素材名称是否重复
		var dbMaterial = tx.JyhMaterial
		cnt, err := dbMaterial.Where(dbMaterial.Name.Eq(req.Name)).Count()
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return errno.QueryFailed.WithMsgAndError("查询素材失败", err)
		} else if cnt > 0 {
			return errno.Conflict.WithMsg("素材名称重复")
		}

		var material = &jyhapp.JyhMaterial{
			Name:             req.Name,
			CategoryID:       req.CategoryID,
			Description:      req.Description,
			Copywriting:      req.Copywriting,
			DouyinProductUrl: req.DouyinProductUrl,
			MusicUrl:         req.MusicUrl,
			FileUrl:          req.FileUrl,
			//IsFree:           true,
			//Status:           1,
		}

		return s.CreateInner(tx, material, req.TagIds, req.Files)
	})
	return
}

func (s *MaterialService) CreateInner(tx *query.Query, material *jyhapp.JyhMaterial, reqTagIds []uint, reqFiles []jyhReq.MaterialFile) (err error) {
	var (
		dbMaterial = tx.JyhMaterial
	)
	err = dbMaterial.Create(material)
	if err != nil {
		return errno.FailedUpdate.WithMsgAndError("创建素材失败", err)
	}

	// 处理标签
	tags := make([]*jyhapp.JyhMaterialTag, 0)
	for _, tagID := range reqTagIds {
		tags = append(tags, &jyhapp.JyhMaterialTag{
			MaterialID: material.ID,
			TagID:      tagID,
		})
	}
	// 批量插入标签
	if len(tags) > 0 {
		err = tx.JyhMaterialTag.CreateInBatches(tags, len(tags))
		if err != nil {
			return errno.FailedUpdate.WithMsgAndError("创建素材标签失败", err)
		}
	}

	// 处理文件
	files := make([]*jyhapp.JyhMaterialFile, 0)
	for _, file := range reqFiles {
		files = append(files, &jyhapp.JyhMaterialFile{
			MaterialID: material.ID,
			FileUrl:    file.FileUrl,
			FileType:   file.FileType,
			FileName:   file.FileName,
		})
	}
	// 批量插入文件
	if len(files) > 0 {
		err = tx.JyhMaterialFile.CreateInBatches(files, len(files))
		if err != nil {
			return errno.FailedUpdate.WithMsgAndError("创建素材文件失败", err)
		}
	}
	return
}

// Delete 删除素材
func (s *MaterialService) Delete(id uint) error {

	err := query.Use(global.GVA_DB).Transaction(func(tx *query.Query) (err error) {
		var (
			dbMaterial     = tx.JyhMaterial
			dbMaterialTag  = tx.JyhMaterialTag
			dbMaterialFile = tx.JyhMaterialFile
		)

		// 删除关联的标签
		_, err = dbMaterialTag.Where(dbMaterialTag.MaterialID.Eq(id)).Delete()
		if err != nil {
			return errors.New("删除素材标签失败: " + err.Error())
		}

		// 删除关联的文件
		_, err = dbMaterialFile.Where(dbMaterialFile.MaterialID.Eq(id)).Delete()
		if err != nil {
			return errors.New("删除素材文件失败: " + err.Error())
		}

		// 删除素材
		_, err = dbMaterial.Where(dbMaterial.ID.Eq(id)).Delete()
		if err != nil {
			return errors.New("删除素材失败: " + err.Error())
		}

		return err
	})
	return err
}

// Update 更新素材分类
func (s *MaterialService) Update(req *jyhReq.MaterialUpdate) (err error) {
	if req.Name == "" {
		return errno.BadRequest.WithMsg("素材名称不能为空")
	}
	if len(req.TagIds) == 0 {
		return errno.QueryFailed.WithMsg("至少需要选择一个标签")
	}
	if len(req.Files) == 0 {
		return errno.QueryFailed.WithMsg("至少需要上传一个素材文件")
	}

	err = query.Use(global.GVA_DB).Transaction(func(tx *query.Query) (err error) {
		// 检查素材名称是否重复
		var dbMaterial = tx.JyhMaterial
		if req.Name != "" {
			cnt, err := dbMaterial.Where(dbMaterial.Name.Eq(req.Name), dbMaterial.ID.Neq(req.ID)).Count()
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return errno.QueryFailed.WithMsgAndError("查询素材失败", err)
			} else if cnt > 0 {
				return errno.Conflict.WithMsg("素材名称重复")
			}
		}

		var material = &jyhapp.JyhMaterial{
			Name:             req.Name,
			CategoryID:       req.CategoryID,
			Description:      req.Description,
			Copywriting:      req.Copywriting,
			DouyinProductUrl: req.DouyinProductUrl,
			MusicUrl:         req.MusicUrl,
			FileUrl:          req.FileUrl,
			//Status:           req.Status,
		}
		material.ID = req.ID

		return s.UpdateInner(tx, material, req.TagIds, req.Files)
	})
	return
}

func (s *MaterialService) UpdateInner(tx *query.Query, material *jyhapp.JyhMaterial, reqTagIds []uint, reqFiles []jyhReq.MaterialFile) (err error) {
	var (
		dbMaterial     = tx.JyhMaterial
		dbMaterialTag  = tx.JyhMaterialTag
		dbMaterialFile = tx.JyhMaterialFile
	)

	cnt, err := dbMaterial.Where(dbMaterial.ID.Eq(material.ID)).Count()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return errno.QueryFailed.WithMsgAndError("查询素材失败", err)
	} else if cnt == 0 {
		return errno.QueryNotFound.WithMsg("素材不存在")
	}

	_, err = dbMaterial.Where(dbMaterial.ID.Eq(material.ID)).Updates(material)
	if err != nil {
		return errno.FailedUpdate.WithMsgAndError("更新素材失败", err)
	}
	// 删除旧的标签
	_, err = dbMaterialTag.Where(dbMaterialTag.MaterialID.Eq(material.ID)).Delete()
	if err != nil {
		return errors.New("删除旧素材标签失败: " + err.Error())
	}
	// 删除旧的文件
	_, err = dbMaterialFile.Where(dbMaterialFile.MaterialID.Eq(material.ID)).Delete()
	if err != nil {
		return errors.New("删除旧素材文件失败: " + err.Error())
	}
	// 处理标签
	tags := make([]*jyhapp.JyhMaterialTag, 0)
	for _, tagID := range reqTagIds {
		tags = append(tags, &jyhapp.JyhMaterialTag{
			MaterialID: material.ID,
			TagID:      tagID,
		})
	}
	// 批量插入标签
	if len(tags) > 0 {
		err = tx.JyhMaterialTag.CreateInBatches(tags, len(tags))
		if err != nil {
			return errors.New("更新素材标签失败: " + err.Error())
		}
	}

	// 处理文件
	files := make([]*jyhapp.JyhMaterialFile, 0)
	for _, file := range reqFiles {
		files = append(files, &jyhapp.JyhMaterialFile{
			MaterialID: material.ID,
			FileUrl:    file.FileUrl,
			FileType:   file.FileType,
			FileName:   file.FileName,
		})
	}
	// 批量插入文件
	if len(files) > 0 {
		err = tx.JyhMaterialFile.CreateInBatches(files, len(files))
		if err != nil {
			return errors.New("更新素材文件失败: " + err.Error())
		}
	}
	return err
}

// getlist 素材列表
func (s *MaterialService) GetList(req *jyhReq.MaterialSearch) (list []*jyhRes.MaterialResponse, total int64, err error) {
	dbMaterial := query.JyhMaterial
	dbMaterialCategory := query.JyhMaterialCategory
	queryBuilder := dbMaterial.WithContext(global.GVA_DB.Statement.Context).
		LeftJoin(dbMaterialCategory, dbMaterial.CategoryID.EqCol(dbMaterialCategory.ID))

	if req.Name != "" {
		queryBuilder = queryBuilder.Where(dbMaterial.Name.Like("%" + req.Name + "%"))
	}

	if req.Status != nil {
		queryBuilder = queryBuilder.Where(dbMaterial.Status.Eq(*req.Status))
	}

	if req.IsSend != nil {
		queryBuilder = queryBuilder.Where(dbMaterial.IsSend.Is(true))
	}

	if req.CategoryID > 0 {
		// 优化成获取分类ID的所有子分类
		subCategoryIDs, _ := gSrv.GetSubCategoryIDs(req.CategoryID)
		if len(subCategoryIDs) > 0 {
			subCategoryIDs = append(subCategoryIDs, req.CategoryID) // 包含自身分类
			queryBuilder = queryBuilder.Where(dbMaterial.CategoryID.In(subCategoryIDs...))
		} else {
			queryBuilder = queryBuilder.Where(dbMaterial.CategoryID.Eq(req.CategoryID))
		}

	}
	if req.Type != "" {
		queryBuilder = queryBuilder.Where(dbMaterial.Type.Eq(req.Type))
	}

	// 获取总数
	total, err = queryBuilder.Count()
	if err != nil {
		return
	}

	if req.Page == 0 && req.PageSize == -1 {
		err = queryBuilder.Select(dbMaterial.ALL, dbMaterialCategory.CatName).
			Scan(&list)
		return
	}

	// 获取分页，只查询基本字段，不包含关联数据
	err = queryBuilder.Select(dbMaterial.ALL, dbMaterialCategory.CatName).
		Order(dbMaterial.ID.Desc()).Limit(req.PageSize).Offset((req.Page - 1) * req.PageSize).Scan(&list)

	return
}

// GetDetail 获取素材详情
func (s *MaterialService) GetDetail(id uint) (material *jyhRes.MaterialResponse, err error) {
	var (
		dbMaterial         = query.JyhMaterial
		dbMaterialCategory = query.JyhMaterialCategory
		dbMaterialTags     = query.JyhMaterialTag
		dbMaterialFiles    = query.JyhMaterialFile
		dbTags             = query.JyhTag
		dbMaterialUser     = query.JyhMaterialUser
	)

	err = dbMaterial.
		Select(dbMaterial.ALL, dbMaterialCategory.CatName, dbMaterial.ID.As("material_id"),
			dbMaterialUser.Status.As("is_claimed"), dbMaterialUser.IsRecognized).
		LeftJoin(dbMaterialCategory, dbMaterial.CategoryID.EqCol(dbMaterialCategory.ID)).
		LeftJoin(dbMaterialUser, dbMaterialUser.MaterialID.EqCol(dbMaterial.ID)).
		Where(dbMaterial.ID.Eq(id)).Scan(&material)
	if err != nil {
		global.GVA_LOG.Error("查询素材详情失败", zap.Error(err))
		return nil, errno.QueryFailed.WithError(err)
	}

	if material != nil {
		// 获取素材标签
		err = dbMaterialTags.
			Select(dbMaterialTags.TagID, dbTags.Name).
			LeftJoin(dbTags, dbMaterialTags.TagID.EqCol(dbTags.ID)).
			Where(dbMaterialTags.MaterialID.Eq(id)).
			Scan(&material.TagList)

		// 获取素材文件
		err = dbMaterialFiles.
			Select(dbMaterialFiles.FileUrl, dbMaterialFiles.FileType, dbMaterialFiles.IsPrimary, dbMaterialFiles.FileName).
			Where(dbMaterialFiles.MaterialID.Eq(id)).
			Scan(&material.Files)
	}
	return
}

// GetMaterialsByCategoryAndTags 通过分类ID和标签获取素材  // 废弃掉了
func (s *MaterialService) GetMaterialsByCategoryAndTags(req jyhReq.MaterialFilterReq) (list []jyhRes.MaterialDataResponse, total int64, err error) {

	var (
		dbMaterial            = query.JyhMaterial
		dbTags                = query.JyhTag
		dbMaterialTags        = query.JyhMaterialTag
		dbFiles               = query.JyhMaterialFile
		cons                  []gen.Condition
		dbMaterialClaimRecord = query.JyhMaterialClaimRecord
	)

	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)

	// 初始化查询条件
	cons = append(cons, dbMaterial.Status.Eq(jyhapp.MaterialStatusActive)) // 只查询状态为1的素材
	if req.CategoryID > 0 {
		cons = append(cons, dbMaterial.CategoryID.Eq(req.CategoryID))
	}
	if req.TagIDs != nil && len(req.TagIDs) > 0 {
		cons = append(cons, dbMaterialTags.TagID.In(req.TagIDs...))
	}
	// 预加载关联数据
	db := dbMaterial.
		LeftJoin(dbMaterialTags, dbMaterial.ID.EqCol(dbMaterialTags.MaterialID)).
		LeftJoin(dbTags, dbMaterialTags.TagID.EqCol(dbTags.ID)).
		LeftJoin(dbFiles, dbMaterial.ID.EqCol(dbFiles.MaterialID)).
		Select(
			dbMaterial.ID.As("material_id"),
			dbMaterial.Name,
			dbMaterial.Description,
			dbMaterial.Copywriting,
			dbMaterial.DouyinProductUrl,
			dbMaterial.MusicUrl,
			dbMaterial.FileUrl,
			dbMaterial.CreatedAt,
		).Where(cons...).Group(dbMaterialTags.TagID).Order(dbMaterial.ID.Desc())

	total, err = db.Count()
	if err != nil {
		global.GVA_LOG.Error("查询统计失败", zap.Error(err))
		return nil, 0, errno.QueryFailed.WithError(err)
	}

	// 分页处理
	err = db.Limit(limit).Offset(offset).Scan(&list)
	if err != nil {
		global.GVA_LOG.Error("查询失败", zap.Error(err))
		return nil, 0, errno.QueryFailed.WithError(err)
	}

	// 遍历获取标签列表和首图
	for i := range list {
		// 获取标签列表
		var tags []jyhRes.TagsResponse
		err = dbTags.Select(dbTags.ID.As("tag_id"), dbTags.Name).
			Join(dbMaterialTags, dbMaterialTags.TagID.EqCol(dbTags.ID)).
			Where(dbMaterialTags.MaterialID.Eq(list[i].MaterialId)).
			Scan(&tags)
		if err != nil {
			//global.GVA_LOG.Error("查询标签失败", zap.Error(err))
			//return nil, 0, errno.QueryFailed.WithError(err)
		}
		list[i].TagList = tags

		// 获取首图
		var primaryFile *jyhapp.JyhMaterialFile
		primaryFile, err = dbFiles.Select(dbFiles.FileUrl, dbFiles.FileType).
			Where(dbFiles.MaterialID.Eq(list[i].MaterialId), dbFiles.IsPrimary.Is(true), dbFiles.FileType.Eq(jyhapp.FileTypeImage)).
			First()
		if err != nil {
			//global.GVA_LOG.Error("查询首图失败", zap.Error(err))
			//return nil, 0, errno.QueryFailed.WithError(err)
		}

		// 如果没有首图，则给默认图
		if primaryFile == nil {

		} else {
			list[i].FileUrl = primaryFile.FileUrl
		}

		// 素材领取状态
		var claimRecord *jyhapp.JyhMaterialClaimRecord
		dbMaterialClaimRecord.Where(dbMaterialClaimRecord.UserID.Eq(req.UserID), dbMaterialClaimRecord.MaterialID.Eq(list[i].MaterialId)).Scan(&claimRecord)
		if claimRecord != nil {
			if claimRecord.IsUploaded == jyhapp.MaterialRecognitionSuccess {
				list[i].Status = jyhapp.MaterialClaimed
			} else {
				list[i].Status = jyhapp.MaterialClaimedNotRecognized
			}
		} else {
			list[i].Status = jyhapp.MaterialNotClaimed
		}
	}

	return list, total, nil
}

// MaterialClaim 领取素材
func (s *MaterialService) MaterialClaim(req jyhReq.MaterialClaim) (err error) {
	err = query.Use(global.GVA_DB).Transaction(func(tx *query.Query) (err error) {
		return s.claimMaterial(tx, req)
	})
	return
}

func (s *MaterialService) claimMaterial(tx *query.Query, req jyhReq.MaterialClaim) (err error) {
	dbMaterial := tx.JyhMaterial
	dbMaterialUser := tx.JyhMaterialUser

	// 检查素材是否存在
	_, err = dbMaterial.Where(dbMaterial.ID.Eq(req.MaterialID), dbMaterial.Status.Eq(jyhapp.MaterialStatusActive)).First()
	if err != nil {
		return errors.New("素材不存在或已下架")
	}

	// 检查用户是否已经领取过该素材
	existingRecord, err := dbMaterialUser.Where(dbMaterialUser.UserID.Eq(req.UserID),
		dbMaterialUser.MaterialID.Eq(req.MaterialID)).First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return errors.New("领取失败，未给用户下发素材")
	}

	if existingRecord.Status == jyhapp.MaterialClaimed {
		return errors.New("您已领取过该素材，请勿重复领取")
	}

	// 领取前技术会判断下是不是第2次领取，第2次要上传他第1次抖音已经发过的作品来识别， 然后识别结果会弹框确认。
	recognizedRecord, err := dbMaterialUser.Where(dbMaterialUser.UserID.Eq(req.UserID),
		dbMaterialUser.Status.Eq(jyhapp.MaterialClaimed), dbMaterialUser.IsRecognized.Eq(jyhapp.MaterialRecognitionUnknown)).Count()
	if recognizedRecord > 0 {
		return errors.New("您已领取过素材，请上传抖音作品进行识别后再领取")
	}

	// 更新用户素材关联表的状态为已领取
	_, err = dbMaterialUser.Where(dbMaterialUser.UserID.Eq(req.UserID), dbMaterialUser.MaterialID.Eq(req.MaterialID)).Updates(map[string]interface{}{
		"status":     jyhapp.MaterialClaimed,
		"claimed_at": time.Now(),
	})
	if err != nil {
		global.GVA_LOG.Error("更新用户素材状态失败: ", zap.Error(err))
		return errors.New("更新用户素材状态失败")
	}

	// 领取素材时，发送积分
	if err := gSrv.AddPoints(req.UserID, "claim_material", "领取素材", fmt.Sprintf("领取素材ID: %d", req.MaterialID)); err != nil {
		global.GVA_LOG.Warn("添加素材积分失败",
			zap.Uint("userID", req.UserID),
			zap.Uint("materialID", req.MaterialID),
			zap.Error(err))
	}

	return nil
}

// SendToUser 素材下发给用户
func (s *MaterialService) SendToUser(req jyhReq.MaterialSendToUser) (err error) {

	// 检查用户参数是否传
	if len(req.UserIDs) == 0 {
		return errors.New("用户ID不能为空")
	}
	if len(req.MaterialIDs) == 0 {
		return errors.New("素材ID不能为空")
	}

	// 判断素材ID和用户ID是否一样长度
	if len(req.UserIDs) != len(req.MaterialIDs) {
		return errors.New("素材和用户数量不一致")
	}

	// 判断是否为自定义素材
	if req.Type == jyhapp.CustomMaterialTypeUser && req.MaterialIDs[0] == 0 {
		return errors.New("自定义素材ID不能为空")
	}

	err = query.Use(global.GVA_DB).Transaction(func(tx *query.Query) (err error) {
		dbMaterial := tx.JyhMaterial

		// 检查素材是否存在
		_, err = dbMaterial.Where(dbMaterial.ID.In(req.MaterialIDs...), dbMaterial.IsSend.Is(false)).First()
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("素材不存在, 或已被下发")
		}

		// 遍历用户ID，逐个下发素材
		for _, userID := range req.UserIDs {
			// 下发素材给用户
			err = s.sendMaterialToUser(tx, userID, req.MaterialIDs[0])
			if err != nil {
				return err
			}
		}
		// 如果下发成功，更新素材状态为已下发
		_, err = dbMaterial.Where(dbMaterial.ID.In(req.MaterialIDs...)).Updates(map[string]interface{}{
			"is_send": true,
		})
		if err != nil {
			global.GVA_LOG.Error("更新素材状态为已下发失败", zap.Error(err))
			return errors.New("更新素材状态为已下发失败")
		}

		// 更新自定义素材的状态
		if req.Type == jyhapp.CustomMaterialTypeUser && req.MaterialIDs[0] > 0 {
			dbCustomMaterial := tx.JyhMaterialCustom
			_, err = dbCustomMaterial.Where(dbCustomMaterial.ID.Eq(req.MaterialIDs[0])).Updates(map[string]interface{}{
				"is_send":     true,
				"send_at":     time.Now(),
				"material_id": req.MaterialIDs[0], // 关联下发的素材ID
			})
			if err != nil {
				global.GVA_LOG.Error("更新自定义素材状态为已下发失败", zap.Error(err))
				return errors.New("更新自定义素材状态为已下发失败")
			}
		}

		// 记录下发日志 @todo
		return err
	})

	return err
}

func (s *MaterialService) sendMaterialToUser(tx *query.Query, userID, materialID uint) error {
	dbMaterialUser := tx.JyhMaterialUser

	// 检查用户是否已经领取过该素材
	existingRecord, err := dbMaterialUser.Where(dbMaterialUser.UserID.Eq(userID),
		dbMaterialUser.MaterialID.Eq(materialID)).First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		global.GVA_LOG.Error("查询下发记录失败", zap.Error(err))
		return errors.New("查询下发记录失败")
	}
	if existingRecord != nil {
		return errors.New("用户已领取过该素材")
	}

	// 创建下发记录
	record := &jyhapp.JyhMaterialUser{
		MaterialID: materialID,
		UserID:     userID,
		SendAt:     time.Now(),
	}
	err = dbMaterialUser.Omit(dbMaterialUser.ClaimedAt).Create(record)
	if err != nil {
		global.GVA_LOG.Error("创建下发记录失败", zap.Error(err))
		return errors.New("创建下发记录失败")
	}

	return nil
}

// GetUserMaterials 获取用户素材列表
func (s *MaterialService) GetUserMaterials(req *jyhReq.UserMaterialSearch) (list []*jyhRes.MaterialDataResponse, total int64, err error) {
	var (
		dbMaterialUser = query.JyhMaterialUser
		dbMaterial     = query.JyhMaterial
		dbMaterialFile = query.JyhMaterialFile
		dbMaterialTags = query.JyhMaterialTag
		dbTags         = query.JyhTag
		cons           []gen.Condition
	)

	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)

	// 初始化查询条件
	cons = append(cons, dbMaterialUser.UserID.Eq(req.UserID))
	if req.CategoryID > 0 {
		// 优化成获取分类ID的所有子分类
		subCategoryIDs, _ := gSrv.GetSubCategoryIDs(req.CategoryID)
		if len(subCategoryIDs) > 0 {
			subCategoryIDs = append(subCategoryIDs, req.CategoryID) // 包含自身分类
			cons = append(cons, dbMaterial.CategoryID.In(subCategoryIDs...))
		} else {
			cons = append(cons, dbMaterial.CategoryID.Eq(req.CategoryID))
		}
	}
	if req.Keyword != "" {
		cons = append(cons, dbMaterial.Name.Like("%"+req.Keyword+"%"))
	}
	if req.TagIDs != nil && len(req.TagIDs) > 0 {
		cons = append(cons, dbMaterialTags.TagID.In(req.TagIDs...))
	}

	db := dbMaterialUser.
		Select(
			dbMaterial.ID.As("material_id"),
			dbMaterial.Name,
			dbMaterial.Description,
			dbMaterial.Copywriting,
			dbMaterial.DouyinProductUrl,
			dbMaterial.MusicUrl,
			dbMaterial.FileUrl,
			dbMaterial.CreatedAt,
			dbMaterialUser.Status,       // 用户领取状态
			dbMaterialUser.ClaimedAt,    // 领取时间
			dbMaterialUser.IsRecognized, // 是否已识别
			dbMaterialUser.ID.As("material_user_id"),
		).
		LeftJoin(dbMaterial, dbMaterialUser.MaterialID.EqCol(dbMaterial.ID)).
		LeftJoin(dbMaterialTags, dbMaterial.ID.EqCol(dbMaterialTags.MaterialID)).
		LeftJoin(dbTags, dbMaterialTags.TagID.EqCol(dbTags.ID)).
		Where(cons...).Group(dbMaterial.ID).Order(dbMaterialUser.ID.Desc())

	total, err = db.Count()
	if err != nil {
		global.GVA_LOG.Error("查询记录失败", zap.Error(err))
		return nil, 0, errno.QueryFailed.WithError(err)
	}

	// 分页处理
	err = db.Limit(limit).Offset(offset).Scan(&list)
	if err != nil {
		global.GVA_LOG.Error("查询记录失败", zap.Error(err))
		return nil, 0, errno.QueryFailed.WithError(err)
	}

	// 遍历获取标签列表和首图
	for i := range list {
		// 获取标签列表
		var tags []jyhRes.TagsResponse
		err = dbTags.Select(dbTags.ID.As("tag_id"), dbTags.Name).
			Join(dbMaterialTags, dbMaterialTags.TagID.EqCol(dbTags.ID)).
			Where(dbMaterialTags.MaterialID.Eq(list[i].MaterialId)).
			Scan(&tags)

		list[i].TagList = tags

		// 获取首图
		var primaryFile *jyhapp.JyhMaterialFile
		primaryFile, err = dbMaterialFile.Select(dbMaterialFile.FileUrl, dbMaterialFile.FileType).
			Where(dbMaterialFile.MaterialID.Eq(list[i].MaterialId), dbMaterialFile.IsPrimary.Is(true), dbMaterialFile.FileType.Eq(jyhapp.FileTypeImage)).
			First()

		// 如果没有首图，则给默认图
		if primaryFile == nil {

		} else {
			list[i].FileUrl = primaryFile.FileUrl
		}
	}

	return list, total, nil
}

// CustomMaterial 定制素材
func (s *MaterialService) CustomMaterial(req jyhReq.CustomMaterialReq) (err error) {
	if req.UserID == 0 {
		return errors.New("用户ID不能为空")
	}

	if len(req.MaterialUrl) == 0 {
		return errors.New("素材文件链接不能为空")
	}

	var (
		dbMaterialCustom = query.JyhMaterialCustom
	)

	material := &jyhapp.JyhMaterialCustom{
		UserID:      req.UserID,
		MaterialUrl: req.MaterialUrl,
	}

	err = dbMaterialCustom.Omit(dbMaterialCustom.SendAt).Create(material)
	if err != nil {
		return errors.New("创建定制素材失败: " + err.Error())
	}

	return
}

// GetCustomMaterials 获取定制素材列表
func (s *MaterialService) GetCustomMaterials(req jyhReq.CustomMaterialSearch) (list []*jyhRes.CustomMaterialResponse, total int64, err error) {
	var (
		dbMaterialCustom = query.JyhMaterialCustom
		dbUser           = query.JyhUser
		cons             []gen.Condition
	)

	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)

	if req.UserID > 0 {
		cons = append(cons, dbMaterialCustom.UserID.Eq(req.UserID))
	}

	db := dbMaterialCustom.
		Select(dbMaterialCustom.ALL, dbUser.Username).
		LeftJoin(dbUser, dbMaterialCustom.UserID.EqCol(dbUser.ID)).
		Where(cons...)
	// 获取总数
	total, err = db.Count()
	if err != nil {
		global.GVA_LOG.Error("查询自定义素材统计失败", zap.Error(err))
		return nil, 0, errno.QueryFailed.WithError(err)
	}

	// 分页处理
	err = db.Limit(limit).Offset(offset).Scan(&list)
	if err != nil {
		global.GVA_LOG.Error("查询自定义素材失败", zap.Error(err))
		return nil, 0, errno.QueryFailed.WithError(err)
	}
	return
}

// GetSendRecords 获取发放记录
func (s *MaterialService) GetSendRecords(req *jyhReq.UserMaterialSendRecordSearch) (list []*jyhRes.MaterialSendRecordResponse, total int64, err error) {
	var (
		dbMaterialUser     = query.JyhMaterialUser
		dbMaterial         = query.JyhMaterial
		dbMaterialCategory = query.JyhMaterialCategory
		dbMaterialTags     = query.JyhMaterialTag
		dbTags             = query.JyhTag
		dbUser             = query.JyhUser
		cons               []gen.Condition
	)

	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)

	// 初始化查询条件
	if req.UserID > 0 {
		cons = append(cons, dbMaterialUser.UserID.Eq(req.UserID))
	}
	if req.CategoryID > 0 {
		// 优化成获取分类ID的所有子分类
		subCategoryIDs, _ := gSrv.GetSubCategoryIDs(req.CategoryID)
		if len(subCategoryIDs) > 0 {
			subCategoryIDs = append(subCategoryIDs, req.CategoryID) // 包含自身分类
			cons = append(cons, dbMaterial.CategoryID.In(subCategoryIDs...))
		} else {
			cons = append(cons, dbMaterial.CategoryID.Eq(req.CategoryID))
		}
	}
	if req.MaterialName != "" {
		cons = append(cons, dbMaterial.Name.Like("%"+req.MaterialName+"%"))
	}
	if req.TagIDs != nil && len(req.TagIDs) > 0 {
		cons = append(cons, dbMaterialTags.TagID.In(req.TagIDs...))
	}

	db := dbMaterialUser.
		Select(
			dbMaterial.ID.As("material_id"),
			dbMaterial.Name,
			dbMaterialCategory.ID.As("category_id"),
			dbMaterialCategory.CatName,
			dbMaterial.FileUrl,
			dbMaterialUser.SendAt,
			dbMaterialUser.ClaimedAt,
			dbMaterialUser.Status,
			dbUser.Username,
			dbMaterialCategory.DouyinUrl,
			dbMaterialCategory.Copywriting,
		).
		LeftJoin(dbMaterial, dbMaterialUser.MaterialID.EqCol(dbMaterial.ID)).
		LeftJoin(dbMaterialCategory, dbMaterial.CategoryID.EqCol(dbMaterialCategory.ID)).
		LeftJoin(dbUser, dbMaterialUser.UserID.EqCol(dbUser.ID)).
		LeftJoin(dbMaterialTags, dbMaterial.ID.EqCol(dbMaterialTags.MaterialID)).
		LeftJoin(dbTags, dbMaterialTags.TagID.EqCol(dbTags.ID)).
		Where(cons...).Group(dbMaterial.ID).Order(dbMaterialUser.ID.Desc())

	total, err = db.Count()
	if err != nil {
		global.GVA_LOG.Error("查询下放记录失败", zap.Error(err))
		return nil, 0, errno.QueryFailed.WithError(err)
	}

	// 分页处理
	err = db.Limit(limit).Offset(offset).Scan(&list)
	if err != nil {
		global.GVA_LOG.Error("查询下放记录失败", zap.Error(err))
		return nil, 0, errno.QueryFailed.WithError(err)
	}

	// 遍历获取标签列表和首图
	for i := range list {
		// 获取标签列表
		var tags []jyhRes.TagsResponse
		err = dbTags.Select(dbTags.ID.As("tag_id"), dbTags.Name).
			Join(dbMaterialTags, dbMaterialTags.TagID.EqCol(dbTags.ID)).
			Where(dbMaterialTags.MaterialID.Eq(list[i].MaterialId)).
			Scan(&tags)

		list[i].TagList = tags
	}

	return list, total, nil
}

// UploadAndRecognize 上传抖音链接并识别
func (s *MaterialService) UploadAndRecognize(req jyhReq.MaterialUploadAndRecognize) (err error) {
	if req.UserID == 0 {
		return errors.New("用户ID不能为空")
	}

	if req.MaterialUserID == 0 {
		return errors.New("素材用户关联ID不能为空")
	}

	if len(req.FileUrl) == 0 {
		return errors.New("上传的抖音链接不能为空")
	}

	// 判断文件链接是否符合要求
	if !strings.HasPrefix(req.FileUrl, "https://") && !strings.HasPrefix(req.FileUrl, "http://") {
		return errors.New("上传的链接格式不正确，请使用有效的抖音链接")
	}

	err = query.Use(global.GVA_DB).Transaction(func(tx *query.Query) (err error) {
		dbMaterialUser := tx.JyhMaterialUser
		dbMaterialUpload := tx.JyhMaterialUpload

		// 检查素材用户关联记录是否存在
		record, err := dbMaterialUser.Where(dbMaterialUser.ID.Eq(req.MaterialUserID), dbMaterialUser.UserID.Eq(req.UserID)).First()
		if err != nil {
			return errors.New("素材用户关联记录不存在")
		}

		if record.IsRecognized == jyhapp.MaterialRecognitionSuccess {
			return errors.New("素材已识别成功，请勿重复识别")
		}

		// 判断用户是否已经上传过抖音链接
		existingUpload, err := dbMaterialUpload.Where(dbMaterialUpload.MaterialUserID.Eq(req.MaterialUserID), dbMaterialUpload.Status.Eq(0)).First()
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			global.GVA_LOG.Error("查询素材上传记录失败", zap.Error(err))
			return errors.New("查询素材上传记录失败")
		}
		if existingUpload != nil {
			return errors.New("您已上传过该抖音链接，请勿重复上传")
		}

		// 先保存，异步识别素材图片
		uploadRecord := &jyhapp.JyhMaterialUpload{
			MaterialUserID: req.MaterialUserID,
			FileUrl:        req.FileUrl,
			UploadTime:     time.Now(),
		}

		err = dbMaterialUpload.Save(uploadRecord)
		if err != nil {
			global.GVA_LOG.Error("保存素材上传记录失败", zap.Error(err))
			return errors.New("保存素材上传记录失败")
		}

		// 同步识别抖音链接数据

		return err
	})

	return err
}

// GetMaterialRuleConfig 获取素材规则配置
func (s *MaterialService) GetMaterialRuleConfig(req *jyhReq.MaterialRuleConfigSearch) (list []*jyhRes.MaterialRuleConfigResponse, total int64, err error) {
	var (
		dbMaterialRule = query.JyhMaterialRuleConfig
		cons           []gen.Condition
	)

	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)

	// 初始化查询条件

	db := dbMaterialRule.
		Where(cons...).Order(dbMaterialRule.ID.Desc())

	total, err = db.Count()
	if err != nil {
		global.GVA_LOG.Error("查询素材配置规则失败", zap.Error(err))
		return nil, 0, errno.QueryFailed.WithError(err)
	}

	// 分页处理
	err = db.Limit(limit).Offset(offset).Scan(&list)
	if err != nil {
		global.GVA_LOG.Error("查询素材配置规则失败", zap.Error(err))
		return nil, 0, errno.QueryFailed.WithError(err)
	}

	return list, total, nil
}

// CreateMaterialRuleConfig 配置素材规则
func (s *MaterialService) CreateMaterialRuleConfig(req jyhReq.MaterialRuleConfigCreate) (err error) {
	var dbMaterialRule = query.JyhMaterialRuleConfig

	if req.MinFans < 0 || req.MaxFans < 0 {
		return errno.BadRequest.WithMsg("粉丝数不能小于0")
	}
	if req.MinFans > req.MaxFans {
		return errno.BadRequest.WithMsg("最小粉丝数不能大于最大粉丝数")
	}
	if req.MinValidFans < 0 || req.MaxValidFans < 0 {
		return errno.BadRequest.WithMsg("有效粉丝数不能小于0")
	}
	if req.MinValidFans > req.MaxValidFans {
		return errno.BadRequest.WithMsg("最小有效粉丝数不能大于最大有效粉丝数")
	}
	if req.MaterialCount <= 0 {
		return errno.BadRequest.WithMsg("素材总数量必须大于0")
	}
	if req.MaterialCount != (req.StreamCount + req.VideoCount + req.ImageCount) {
		return errno.BadRequest.WithMsg("素材总数量必须等于拉流、视频和图文的数量之和")
	}

	var materialRule = &jyhapp.JyhMaterialRuleConfig{
		MinFans:       req.MinFans,
		MaxFans:       req.MaxFans,
		MinValidFans:  req.MinValidFans,
		MaxValidFans:  req.MaxValidFans,
		FrequencyType: req.FrequencyType,
		SelectionType: req.SelectionType,
		MaterialCount: req.MaterialCount,
		StreamCount:   req.StreamCount,
		VideoCount:    req.VideoCount,
		ImageCount:    req.ImageCount,
		Remark:        req.Remark,
	}
	err = dbMaterialRule.Create(materialRule)
	if err != nil {
		global.GVA_LOG.Error("创建素材规则配置失败", zap.Error(err))
		return errno.FailedUpdate.WithError(err)
	}
	return nil
}

// UpdateMaterialRuleConfig 更新素材规则配置
func (s *MaterialService) UpdateMaterialRuleConfig(req jyhReq.MaterialRuleConfigUpdate) (err error) {
	if req.ID == 0 {
		return errno.BadRequest.WithMsg("素材规则配置ID不能为空")
	}

	var dbMaterialRule = query.JyhMaterialRuleConfig

	if req.MinFans < 0 || req.MaxFans < 0 {
		return errno.BadRequest.WithMsg("粉丝数不能小于0")
	}
	if req.MinFans > req.MaxFans {
		return errno.BadRequest.WithMsg("最小粉丝数不能大于最大粉丝数")
	}
	if req.MinValidFans < 0 || req.MaxValidFans < 0 {
		return errno.BadRequest.WithMsg("有效粉丝数不能小于0")
	}
	if req.MinValidFans > req.MaxValidFans {
		return errno.BadRequest.WithMsg("最小有效粉丝数不能大于最大有效粉丝数")
	}
	if req.MaterialCount <= 0 {
		return errno.BadRequest.WithMsg("素材总数量必须大于0")
	}
	if req.MaterialCount != (req.StreamCount + req.VideoCount + req.ImageCount) {
		return errno.BadRequest.WithMsg("素材总数量必须等于拉流、视频和图文的数量之和")
	}

	var materialRule = &jyhapp.JyhMaterialRuleConfig{
		MinFans:       req.MinFans,
		MaxFans:       req.MaxFans,
		MinValidFans:  req.MinValidFans,
		MaxValidFans:  req.MaxValidFans,
		FrequencyType: req.FrequencyType,
		SelectionType: req.SelectionType,
		MaterialCount: req.MaterialCount,
		StreamCount:   req.StreamCount,
		VideoCount:    req.VideoCount,
		ImageCount:    req.ImageCount,
		Remark:        req.Remark,
	}

	_, err = dbMaterialRule.Where(dbMaterialRule.ID.Eq(req.ID)).Updates(materialRule)
	if err != nil {
		global.GVA_LOG.Error("更新素材规则配置失败", zap.Error(err))
		return errno.FailedUpdate.WithError(err)
	}
	return nil
}

// DeleteMaterialRuleConfig 删除素材
func (s *MaterialService) DeleteMaterialRuleConfig(id uint) (err error) {
	err = query.Use(global.GVA_DB).Transaction(func(tx *query.Query) (err error) {
		var (
			dbMaterialRule = query.JyhMaterialRuleConfig
		)

		// 检查素材规则是否存在
		cnt, err := dbMaterialRule.Where(dbMaterialRule.ID.Eq(id)).Count()
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return errno.QueryFailed.WithMsgAndError("查询素材配置规则失败", err)
		} else if cnt == 0 {
			return errno.QueryNotFound.WithMsg("素材素材配置规则不存在")
		}

		_, err = dbMaterialRule.Where(dbMaterialRule.ID.Eq(id)).Delete()
		if err != nil {
			return errors.New("删除素材配置规则失败: " + err.Error())
		}

		return err
	})
	return err
}

// GetMaterialRuleConfigByID 获取素材规则配置详情
func (s *MaterialService) GetMaterialRuleConfigByID(id uint) (config *jyhRes.MaterialRuleConfigResponse, err error) {
	var (
		dbMaterialRule = query.JyhMaterialRuleConfig
	)

	err = dbMaterialRule.Where(dbMaterialRule.ID.Eq(id)).Limit(1).Scan(&config)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errno.QueryNotFound.WithMsg("素材规则配置不存在")
		}
		return nil, errno.QueryFailed.WithError(err)
	}

	return config, nil
}
