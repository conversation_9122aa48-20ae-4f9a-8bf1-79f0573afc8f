{
    "days": 20,
    "scene": "media_pull"
    "followers": 1000,
    "daily_qty": 5,
    "scene": "media_pull"
}

### 字段解释
days:天数（前xx天）
daily_qty:每日数量
scene:场景
followers:粉丝数

### scene 场景
media_pull 拉流
car_follow 挂车

## 示例
| `condition` 示例                                          | 场景解释             |
| ------------------------------------------------------- | ---------------- |
| `{"days":20,"scene":"media_pull"}`                      | 前 20 天拉流素材       |
| `{"followers":1000,"scene":"car_follow"}`               | 挂车满 1000 粉丝      |
| `{"followers":3000,"daily_qty":5,"scene":"media_pull"}` | 3000 粉丝 & 每日 5 条 |
|{
  "target_level_id": 12,   // 12 = membership_levels.id
  "expire_days": 30,
  "price_cents": 0关联的权益等级
}| 

## 邀请码权益自动生成功能

### 功能说明
当用户等级生效时，系统会自动检查该等级是否配置了 `referral_code_once`（单个推荐码兑换权）权益。如果满足以下条件，系统将自动为用户生成免费邀请码：

1. 等级配置了 `referral_code_once` 权益类型
2. 权益条件中 `price_cents = 0`（表示免费权益）
3. 权益条件中包含有效的 `target_level_id`（目标等级ID）

### 权益配置示例

#### 等级权益映射配置
```json
{
  "level_id": 10,          // 当前用户等级ID（如：星辰达人）
  "benefit_id": 5,         // referral_code_once 权益类型ID
  "value": 6.0000,         // 生成邀请码数量（6个）
  "condition": {
    "target_level_id": 12, // 目标等级ID（如：炬火达人）
    "expire_days": 30,     // 邀请码有效期（30天）
    "price_cents": 0       // 免费权益标识
  }
}
```

#### 付费权益配置示例
```json
{
  "level_id": 10,
  "benefit_id": 5,
  "value": 1.0000,         // 可购买1个邀请码
  "condition": {
    "target_level_id": 8,  // 目标等级ID（如：流星达人）
    "expire_days": 90,     // 邀请码有效期（90天）
    "price_cents": 11900   // 购买价格119元（付费权益不会自动生成）
  }
}
```

### 业务流程

1. **用户注册/升级**：用户使用邀请码注册或通过其他方式获得新等级
2. **权益检查**：系统检查新等级是否配置了 `referral_code_once` 权益
3. **条件判断**：
   - 如果 `price_cents = 0`：自动生成对应数量的免费邀请码
   - 如果 `price_cents > 0`：记录为可购买权益，用户需要付费购买
4. **邀请码生成**：
   - 类型：`user`（用户生成）
   - 数量：根据 `value` 字段决定
   - 目标等级：`target_level_id` 指定的等级
   - 有效期：`expire_days` 指定的天数
   - 价格：0（免费权益生成的邀请码）

### 数据库影响

#### jyh_invite_code 表新增记录
```sql
INSERT INTO jyh_invite_code (
  type, user_id, code, level_id, sale_price, 
  status, is_used, expired_at, created_at
) VALUES (
  'user',           -- 用户权益生成
  123,              -- 用户ID
  'ABC12345',       -- 8位随机邀请码
  12,               -- target_level_id（目标等级）
  0,                -- 免费邀请码
  0,                -- 未使用状态
  false,            -- 未使用
  '2024-02-01',     -- 过期时间
  NOW()             -- 创建时间
);
```

### 配置权益类型

需要在权益类型表中添加 `referral_code_once` 权益：

```sql
INSERT INTO jyh_user_benefit (key, name, description) 
VALUES ('referral_code_once', '单个推荐码兑换权', '用户可生成指定等级的邀请码');
```

### 使用场景

**星辰达人等级示例**：
- 用户开通"星辰达人"等级
- 系统检测到该等级配置了免费的 `referral_code_once` 权益
- 自动生成6个"炬火达人"等级的邀请码（有效期30天）
- 用户可以将这些邀请码分享给他人，被邀请者使用后直接获得"炬火达人"等级

### 日志记录

系统会详细记录邀请码生成过程：
- 权益检查结果
- 邀请码生成成功/失败
- 生成的邀请码信息
- 错误原因（如果有）

这样确保了邀请码权益系统的可追踪性和可维护性。

