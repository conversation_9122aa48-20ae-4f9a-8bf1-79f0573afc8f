// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
)

func newJyhTag(db *gorm.DB, opts ...gen.DOOption) jyhTag {
	_jyhTag := jyhTag{}

	_jyhTag.jyhTagDo.UseDB(db, opts...)
	_jyhTag.jyhTagDo.UseModel(&jyhapp.JyhTag{})

	tableName := _jyhTag.jyhTagDo.TableName()
	_jyhTag.ALL = field.NewAsterisk(tableName)
	_jyhTag.ID = field.NewUint(tableName, "id")
	_jyhTag.Name = field.NewString(tableName, "name")
	_jyhTag.Type = field.NewString(tableName, "type")
	_jyhTag.CreatedAt = field.NewTime(tableName, "created_at")

	_jyhTag.fillFieldMap()

	return _jyhTag
}

type jyhTag struct {
	jyhTagDo

	ALL       field.Asterisk
	ID        field.Uint
	Name      field.String
	Type      field.String
	CreatedAt field.Time

	fieldMap map[string]field.Expr
}

func (j jyhTag) Table(newTableName string) *jyhTag {
	j.jyhTagDo.UseTable(newTableName)
	return j.updateTableName(newTableName)
}

func (j jyhTag) As(alias string) *jyhTag {
	j.jyhTagDo.DO = *(j.jyhTagDo.As(alias).(*gen.DO))
	return j.updateTableName(alias)
}

func (j *jyhTag) updateTableName(table string) *jyhTag {
	j.ALL = field.NewAsterisk(table)
	j.ID = field.NewUint(table, "id")
	j.Name = field.NewString(table, "name")
	j.Type = field.NewString(table, "type")
	j.CreatedAt = field.NewTime(table, "created_at")

	j.fillFieldMap()

	return j
}

func (j *jyhTag) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := j.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (j *jyhTag) fillFieldMap() {
	j.fieldMap = make(map[string]field.Expr, 4)
	j.fieldMap["id"] = j.ID
	j.fieldMap["name"] = j.Name
	j.fieldMap["type"] = j.Type
	j.fieldMap["created_at"] = j.CreatedAt
}

func (j jyhTag) clone(db *gorm.DB) jyhTag {
	j.jyhTagDo.ReplaceConnPool(db.Statement.ConnPool)
	return j
}

func (j jyhTag) replaceDB(db *gorm.DB) jyhTag {
	j.jyhTagDo.ReplaceDB(db)
	return j
}

type jyhTagDo struct{ gen.DO }

func (j jyhTagDo) Debug() *jyhTagDo {
	return j.withDO(j.DO.Debug())
}

func (j jyhTagDo) WithContext(ctx context.Context) *jyhTagDo {
	return j.withDO(j.DO.WithContext(ctx))
}

func (j jyhTagDo) ReadDB() *jyhTagDo {
	return j.Clauses(dbresolver.Read)
}

func (j jyhTagDo) WriteDB() *jyhTagDo {
	return j.Clauses(dbresolver.Write)
}

func (j jyhTagDo) Session(config *gorm.Session) *jyhTagDo {
	return j.withDO(j.DO.Session(config))
}

func (j jyhTagDo) Clauses(conds ...clause.Expression) *jyhTagDo {
	return j.withDO(j.DO.Clauses(conds...))
}

func (j jyhTagDo) Returning(value interface{}, columns ...string) *jyhTagDo {
	return j.withDO(j.DO.Returning(value, columns...))
}

func (j jyhTagDo) Not(conds ...gen.Condition) *jyhTagDo {
	return j.withDO(j.DO.Not(conds...))
}

func (j jyhTagDo) Or(conds ...gen.Condition) *jyhTagDo {
	return j.withDO(j.DO.Or(conds...))
}

func (j jyhTagDo) Select(conds ...field.Expr) *jyhTagDo {
	return j.withDO(j.DO.Select(conds...))
}

func (j jyhTagDo) Where(conds ...gen.Condition) *jyhTagDo {
	return j.withDO(j.DO.Where(conds...))
}

func (j jyhTagDo) Order(conds ...field.Expr) *jyhTagDo {
	return j.withDO(j.DO.Order(conds...))
}

func (j jyhTagDo) Distinct(cols ...field.Expr) *jyhTagDo {
	return j.withDO(j.DO.Distinct(cols...))
}

func (j jyhTagDo) Omit(cols ...field.Expr) *jyhTagDo {
	return j.withDO(j.DO.Omit(cols...))
}

func (j jyhTagDo) Join(table schema.Tabler, on ...field.Expr) *jyhTagDo {
	return j.withDO(j.DO.Join(table, on...))
}

func (j jyhTagDo) LeftJoin(table schema.Tabler, on ...field.Expr) *jyhTagDo {
	return j.withDO(j.DO.LeftJoin(table, on...))
}

func (j jyhTagDo) RightJoin(table schema.Tabler, on ...field.Expr) *jyhTagDo {
	return j.withDO(j.DO.RightJoin(table, on...))
}

func (j jyhTagDo) Group(cols ...field.Expr) *jyhTagDo {
	return j.withDO(j.DO.Group(cols...))
}

func (j jyhTagDo) Having(conds ...gen.Condition) *jyhTagDo {
	return j.withDO(j.DO.Having(conds...))
}

func (j jyhTagDo) Limit(limit int) *jyhTagDo {
	return j.withDO(j.DO.Limit(limit))
}

func (j jyhTagDo) Offset(offset int) *jyhTagDo {
	return j.withDO(j.DO.Offset(offset))
}

func (j jyhTagDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *jyhTagDo {
	return j.withDO(j.DO.Scopes(funcs...))
}

func (j jyhTagDo) Unscoped() *jyhTagDo {
	return j.withDO(j.DO.Unscoped())
}

func (j jyhTagDo) Create(values ...*jyhapp.JyhTag) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Create(values)
}

func (j jyhTagDo) CreateInBatches(values []*jyhapp.JyhTag, batchSize int) error {
	return j.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (j jyhTagDo) Save(values ...*jyhapp.JyhTag) error {
	if len(values) == 0 {
		return nil
	}
	return j.DO.Save(values)
}

func (j jyhTagDo) First() (*jyhapp.JyhTag, error) {
	if result, err := j.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhTag), nil
	}
}

func (j jyhTagDo) Take() (*jyhapp.JyhTag, error) {
	if result, err := j.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhTag), nil
	}
}

func (j jyhTagDo) Last() (*jyhapp.JyhTag, error) {
	if result, err := j.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhTag), nil
	}
}

func (j jyhTagDo) Find() ([]*jyhapp.JyhTag, error) {
	result, err := j.DO.Find()
	return result.([]*jyhapp.JyhTag), err
}

func (j jyhTagDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*jyhapp.JyhTag, err error) {
	buf := make([]*jyhapp.JyhTag, 0, batchSize)
	err = j.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (j jyhTagDo) FindInBatches(result *[]*jyhapp.JyhTag, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return j.DO.FindInBatches(result, batchSize, fc)
}

func (j jyhTagDo) Attrs(attrs ...field.AssignExpr) *jyhTagDo {
	return j.withDO(j.DO.Attrs(attrs...))
}

func (j jyhTagDo) Assign(attrs ...field.AssignExpr) *jyhTagDo {
	return j.withDO(j.DO.Assign(attrs...))
}

func (j jyhTagDo) Joins(fields ...field.RelationField) *jyhTagDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Joins(_f))
	}
	return &j
}

func (j jyhTagDo) Preload(fields ...field.RelationField) *jyhTagDo {
	for _, _f := range fields {
		j = *j.withDO(j.DO.Preload(_f))
	}
	return &j
}

func (j jyhTagDo) FirstOrInit() (*jyhapp.JyhTag, error) {
	if result, err := j.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhTag), nil
	}
}

func (j jyhTagDo) FirstOrCreate() (*jyhapp.JyhTag, error) {
	if result, err := j.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*jyhapp.JyhTag), nil
	}
}

func (j jyhTagDo) FindByPage(offset int, limit int) (result []*jyhapp.JyhTag, count int64, err error) {
	result, err = j.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = j.Offset(-1).Limit(-1).Count()
	return
}

func (j jyhTagDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = j.Count()
	if err != nil {
		return
	}

	err = j.Offset(offset).Limit(limit).Scan(result)
	return
}

func (j jyhTagDo) Scan(result interface{}) (err error) {
	return j.DO.Scan(result)
}

func (j jyhTagDo) Delete(models ...*jyhapp.JyhTag) (result gen.ResultInfo, err error) {
	return j.DO.Delete(models)
}

func (j *jyhTagDo) withDO(do gen.Dao) *jyhTagDo {
	j.DO = *do.(*gen.DO)
	return j
}
