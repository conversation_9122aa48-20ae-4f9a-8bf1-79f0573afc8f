package jyhapp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/shopspring/decimal"
	"time"
)

// JyhMaterialCategory 素材分类表
type JyhMaterialCategory struct {
	global.GVA_MODEL
	CatName     string `gorm:"type:varchar(100);not null;uniqueIndex;comment:分类名称;" json:"catName"`
	CatDesc     string `gorm:"type:varchar(255);comment:分类描述;" json:"catDesc"`
	ParentID    uint   `gorm:"default:0;comment:父级分类ID" json:"parentID"`
	Sort        int    `gorm:"default:0;comment:排序权重" json:"sort"`
	IsActive    bool   `gorm:"default:true;comment:是否启用" json:"isActive"`
	DouyinUrl   string `gorm:"type:varchar(500);comment:抖音链接" json:"douyinUrl"`
	Copywriting string `gorm:"type:varchar(255);comment:备注" json:"copywriting"`
}

// TableName JyhMaterialCategory 素材分类表
func (JyhMaterialCategory) TableName() string {
	return "jyh_material_category"
}

// JyhMaterial 素材表
type JyhMaterial struct {
	global.GVA_MODEL
	Name             string `gorm:"type:varchar(255);not null;comment:素材标题" json:"name"`
	Description      string `gorm:"type:text;comment:素材描述" json:"description"`
	FileUrl          string `gorm:"type:varchar(500);not null;comment:素材首图" json:"fileUrl"`
	CategoryID       uint   `gorm:"not null;index" json:"categoryId"`
	Copywriting      string `gorm:"type:text;comment:推荐文案" json:"copywriting"`
	DouyinProductUrl string `gorm:"type:varchar(500);comment:抖音商品链接" json:"douyinProductUrl"`
	MusicUrl         string `gorm:"type:varchar(500);comment:背景音乐链接" json:"musicUrl"`

	//统计字段
	ViewCount     int             `gorm:"default:0" json:"viewCount"`
	DownloadCount int             `gorm:"default:0" json:"downloadCount"`
	Price         decimal.Decimal `gorm:"type:decimal(10,2);default:0.00" json:"price"`
	IsFree        bool            `gorm:"default:true" json:"isFree"`
	Status        uint            `gorm:"default:1;comment:素材状态 1-正常 2-下架" json:"status"` // 状态
	IsSend        bool            `gorm:"default:false;comment:是否已下发" json:"isSend"`      // 是否已下发

	// 素材类型（拉流，视频挂车，图文挂车）
	Type string `gorm:"type:enum('stream','video','image');default:'video';comment:素材类型" json:"type"` // 素材类型: 拉流，视频挂车，图文挂车
	// 需要今音豆（类积分）
	PointsRequired int `gorm:"default:0;not null" json:"points_required"`

	// 关联关系
	Category JyhMaterialCategory `gorm:"foreignKey:CategoryID" json:"category"`
	//Tags     []JyhMaterialTag    `gorm:"foreignKey:MaterialID" json:"tags"`
	Files []JyhMaterialFile `gorm:"foreignKey:MaterialID" json:"files"`
}

// TableName JyhMaterial 素材表表名
func (JyhMaterial) TableName() string {
	return "jyh_material"
}

// JyhMaterialClaimRecord 素材领取记录
type JyhMaterialClaimRecord struct {
	ID         uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	UserID     uint      `gorm:"not null;index;comment:用户ID" json:"userID"`
	MaterialID uint      `gorm:"not null;index;comment:素材ID" json:"materialID"`
	ClaimType  string    `gorm:"type:enum('free','paid');default:'free'" json:"claimType"`
	Quantity   int       `gorm:"default:1" json:"quantity"`
	ClaimedAt  time.Time `gorm:"not null" json:"claimedAt"`
	IsUploaded uint      `gorm:"default:0;comment:是否上传抖音并识别 0 未识别，1，成功，2 失败" json:"isUploaded"`

	User     JyhUser     `gorm:"foreignKey:UserID" json:"user"`
	Material JyhMaterial `gorm:"foreignKey:MaterialID" json:"material"`
}

// TableName JyhMaterialClaimRecord 素材领取记录表名
func (JyhMaterialClaimRecord) TableName() string {
	return "jyh_material_claim_record"
}

// JyhMaterialFile 素材文件表
type JyhMaterialFile struct {
	global.GVA_MODEL
	MaterialID uint   `gorm:"not null;index;comment:素材ID" json:"materialID"`
	FileUrl    string `gorm:"type:varchar(500);not null" json:"fileUrl"`
	FileType   string `gorm:"type:enum('image','video','music','document');default:'image'" json:"fileType"`
	IsPrimary  bool   `gorm:"default:false;comment:是否主展示文件" json:"isPrimary"`
	Sort       int    `gorm:"default:0" json:"sort"`
	FileName   string `gorm:"type:varchar(150);not null;comment:文件名称" json:"fileName"` // 文件名称
}

// TableName JyhMaterialFile 素材文件表
func (JyhMaterialFile) TableName() string {
	return "jyh_material_file"
}

// JyhMaterialTag 素材标签关联表
type JyhMaterialTag struct {
	MaterialID uint `gorm:"not null;index;comment:素材ID" json:"materialId"`
	TagID      uint `gorm:"not null;index;comment:标签ID" json:"tagId"`
}

// TableName JyhMaterialTag 素材标签关联表
func (JyhMaterialTag) TableName() string {
	return "jyh_material_tag"
}

// JyhMaterialUser 素材与用户关联表
type JyhMaterialUser struct {
	ID           uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	MaterialID   uint      `gorm:"not null;uniqueIndex:uniq_material_user;comment:素材ID" json:"materialId"`
	UserID       uint      `gorm:"not null;uniqueIndex:uniq_material_user;comment:用户ID" json:"userId"`
	SendAt       time.Time `gorm:"not null;comment:发放时间" json:"sendAt"`                                          // 下发时间
	ClaimedAt    time.Time `gorm:"comment:领取时间" json:"claimedAt"`                                                // 领取时间
	Status       uint      `gorm:"not null;default:0;index;comment:状态 0-未领取 1-已领取" json:"status"`                // 状态
	IsRecognized uint      `gorm:"not null;default:0;index;comment:是否已识别素材 0 未识别，1，成功，2 失败" json:"isRecognized"` // 是否已识别素材
}

// TableName JyhMaterialUser 素材与用户关联表
func (JyhMaterialUser) TableName() string {
	return "jyh_material_user"
}

// JyhMaterialCustom 定制素材
type JyhMaterialCustom struct {
	Model1
	UserID      uint      `gorm:"not null;index;comment:用户ID" json:"userId"`
	MaterialUrl string    `gorm:"type:varchar(500);not null;comment:素材链接" json:"materialUrl"`
	IsSend      bool      `gorm:"default:false;comment:是否已下发" json:"isSend"` // 是否已下发
	SendAt      time.Time `gorm:"comment:下发时间" json:"sendAt"`                // 下发时间
	MaterialID  uint      `gorm:"default:0;comment:素材ID" json:"materialId"`  // 素材ID
}

// TableName JyhMaterialCustom 定制素材表名
func (JyhMaterialCustom) TableName() string {
	return "jyh_material_custom"
}

// JyhMaterialUpload 用户领取的素材需要上传截图
type JyhMaterialUpload struct {
	Model1
	MaterialUserID uint      `gorm:"not null;index;comment:素材用户ID" json:"materialUserId"`        // 素材用户ID
	FileUrl        string    `gorm:"type:varchar(500);not null;comment:上传的截图URL" json:"fileUrl"` // 上传的截图URL
	UploadTime     time.Time `gorm:"not null;comment:上传时间" json:"uploadTime"`                    // 上传时间
	Status         uint      `gorm:"default:0;comment:状态 0 未识别，1，成功，2 失败" json:"status"`         // 状态
	Ret            string    `gorm:"type:text;comment:识别结果" json:"ret"`                          // 识别结果
}

// TableName JyhMaterialUpload 用户领取的素材需要上传截图表名
func (JyhMaterialUpload) TableName() string {
	return "jyh_material_upload"
}

// JyhMaterialRuleConfig 自动发放规则配置
type JyhMaterialRuleConfig struct {
	ID            uint   `gorm:"column:id;type:int unsigned;primaryKey;autoIncrement;comment:主键" json:"id"`
	MinFans       int    `gorm:"column:min_fans;type:int;not null;comment:最小粉丝数（闭区间）" json:"minFans"`
	MaxFans       int    `gorm:"column:max_fans;type:int;not null;comment:最大粉丝数（闭区间）" json:"maxFans"`
	MinValidFans  int    `gorm:"column:min_valid_fans;type:int;not null;comment:最小有效粉丝数（闭区间）" json:"minValidFans"`
	MaxValidFans  int    `gorm:"column:max_valid_fans;type:int;not null;comment:最大有效粉丝数（闭区间）" json:"maxValidFans"`
	FrequencyType string `gorm:"column:frequency_type;type:enum('daily','weekly');not null;comment:推送频率类型" json:"frequencyType"`
	SelectionType string `gorm:"column:selection_type;type:enum('fixed','flexible');not null;default:'fixed';comment:素材形式选择方式,固定或组合" json:"selectionType"`
	MaterialCount int    `gorm:"column:material_count;type:int;default:0;not null;comment:素材数量（每天/每周）" json:"materialCount"`
	StreamCount   int    `gorm:"column:stream_count;type:int(11);default:0;not null;comment:拉流素材数量" json:"streamCount"`
	VideoCount    int    `gorm:"column:video_count;type:int(11);default:0;not null;comment:视频挂车数量" json:"videoCount"`
	ImageCount    int    `gorm:"column:image_count;type:int(11);default:0;not null;comment:图文挂车数量" json:"imageCount"`
	Remark        string `gorm:"column:remark;type:varchar(255);comment:备注说明" json:"remark"`
}

// TableName JyhMaterialRuleConfig 指定表名（可选）
func (JyhMaterialRuleConfig) TableName() string {
	return "jyh_material_rule_config"
}
